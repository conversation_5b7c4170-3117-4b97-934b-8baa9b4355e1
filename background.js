let automationState = {
    version: '1.0.0',
    isRunning: false,
    windowId: null,
    currentFile: null,
    files: [],
    settings: null,
    totalFiles: 0,
    processedFiles: []
};

let isProcessingFile = false;

let activeProgressTabId = null;

let notificationState = {
    completionNotificationShown: false,
    completionTimestamp: null,
    isAutomationTrulyComplete: false,
    lastQueueLength: 0
};

function resetNotificationState() {
    notificationState = {
        completionNotificationShown: false,
        completionTimestamp: null,
        isAutomationTrulyComplete: false,
        lastQueueLength: 0
    };
}

async function ensureNotificationPermissions() {
    try {
        return new Promise((resolve) => {
            chrome.permissions.contains({
                permissions: ['notifications']
            }, (hasPermission) => {
                if (hasPermission) {
                    resolve(true);
                } else {
                    resolve(false);
                }
            });
        });
    } catch (error) {
        return false;
    }
}

(async () => {
    try {
        const hasPermission = await ensureNotificationPermissions();
    } catch (error) {
    }
})();

function createFileObject(file, index) {
    return {
        index: index,
        filename: file.filename || '',
        blobUrl: file.blobUrl || null,
        base64: file.base64 || null,
        status: 'pending'
    };
}

function isValidFileObject(file) {
    return file && 
           typeof file === 'object' && 
           typeof file.filename === 'string' &&
           typeof file.index === 'number' &&
           typeof file.status === 'string' &&
           (file.blobUrl !== undefined || file.base64 !== undefined);
}

function createCleanState() {
    return {
        version: '1.0.0',
        isRunning: false,
        windowId: null,
        currentFile: null,
        files: [],
        settings: null,
        totalFiles: 0,
        processedFiles: []
    };
}

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    const handleMessage = async () => {
        try {
            switch (request.action) {
                case 'startAutomation':
                    await handleStartAutomation(request);
                    return { success: true };
                    
                case 'getAutomationState':
                    try {
                        const data = await safeStorageGet(['automationFiles']);
                        const stateWithFileData = { ...automationState };
                        if (stateWithFileData.currentFile && data.automationFiles) {
                            const filename = stateWithFileData.currentFile.filename;
                            if (filename && data.automationFiles[filename]) {
                                stateWithFileData.currentFile = {
                                    ...stateWithFileData.currentFile,
                                    blobUrl: data.automationFiles[filename]
                                };
                            }
                        }
                        return { success: true, state: stateWithFileData };
                    } catch (error) {
                        return { success: true, state: automationState };
                    }
                    
                case 'processNextFile':
                    await handleProcessNextFile(request, sender);
                    return { success: true };
                
                case 'markTabCompleted':
                    await handleMarkTabCompleted(request, sender);
                    return { success: true };
                
                case 'playCompletionSound':
                    try {
                        const isFinalTab = request.isLastFile === true || request.isFinalTab === true;
                        const now = Date.now();
                        const recentCompletionWindow = 60000;
                        const isDuplicateNotificationRequest = 
                            notificationState.completionTimestamp && 
                            (now - notificationState.completionTimestamp < recentCompletionWindow);
                        
                        if (isFinalTab && !notificationState.completionNotificationShown && !isDuplicateNotificationRequest) {
                            notificationState.isAutomationTrulyComplete = true;
                            notificationState.completionTimestamp = now;
                            
                            await safeCreateNotification(
                                'Snap Automation Complete',
                                'All files have been processed successfully!',
                                2,
                                5
                            );
                            
                            notificationState.completionNotificationShown = true;
                        } else if (isFinalTab) {
                            if (notificationState.completionNotificationShown) {
                            } else if (isDuplicateNotificationRequest) {
                            }
                        }
                        
                        if (activeProgressTabId) {
                            await chrome.tabs.sendMessage(activeProgressTabId, { 
                                action: 'playCompletionSound',
                                isFinalTab: isFinalTab  
                            });
                        } else {
                            if (automationState && automationState.windowId) {
                                const windowTabs = await chrome.tabs.query({ windowId: automationState.windowId, active: true });
                                if (windowTabs && windowTabs.length > 0) {
                                    await chrome.tabs.sendMessage(windowTabs[0].id, { 
                                        action: 'playCompletionSound',
                                        isFinalTab: isFinalTab
                                    });
                                }
                            }
                        }
                    } catch (error) {
                    }
                    return { success: true };
                
                case 'updateAutomationProgress':
                    try {
                        if (request.isTabCompleted && sender.tab && sender.tab.id === activeProgressTabId) {
                            activeProgressTabId = null;
                        }
                        
                        if (!activeProgressTabId && !request.isTabCompleted && sender.tab) {
                            activeProgressTabId = sender.tab.id;
                        }
                        
                        if (activeProgressTabId) {
                            try {
                                await chrome.tabs.sendMessage(activeProgressTabId, {
                                    action: 'updateAutomationProgress',
                                    status: request.status,
                                    filename: request.filename,
                                    currentFile: request.currentFile,
                                    totalFiles: request.totalFiles,
                                    isTabCompleted: request.isTabCompleted,
                                    updateProgressOnly: request.updateProgressOnly,
                                    updateProgressBar: request.updateProgressBar,
                                    forceProgressUpdate: request.forceProgressUpdate,
                                    queueProgress: request.queueProgress,
                                    statusInfo: request.statusInfo,
                                    completed: request.completed
                                });
                            } catch (error) {
                                activeProgressTabId = null;
                            }
                        }
                        return { success: true };
                    } catch (error) {
                        return { success: false, error: error.message };
                    }
                
                case 'setActiveProgressTab':
                    if (sender.tab) {
                        if (activeProgressTabId && activeProgressTabId !== sender.tab.id) {
                            try {
                                await chrome.tabs.sendMessage(activeProgressTabId, {
                                    action: 'updateAutomationProgress',
                                    isTabCompleted: true,
                                    forceHide: true
                                });
                            } catch (error) {
                            }
                        }
                        
                        activeProgressTabId = sender.tab.id;
                        return { 
                            success: true, 
                            activeProgressTabId: activeProgressTabId,
                            automationState: automationState
                        };
                    }
                    
                    return { success: false, error: 'No tab ID provided' };
                
                case 'hideAllProgressUI':
                    try {
                        if (activeProgressTabId) {
                            try {
                                await chrome.tabs.sendMessage(activeProgressTabId, {
                                    action: 'updateAutomationProgress',
                                    isTabCompleted: true,
                                    forceHide: true
                                });
                            } catch (error) {
                            }
                            
                            activeProgressTabId = null;
                        } else {
                            const allTabs = await chrome.tabs.query({});
                            
                            for (const tab of allTabs) {
                                try {
                                    await chrome.tabs.sendMessage(tab.id, {
                                        action: 'updateAutomationProgress',
                                        isTabCompleted: true,
                                        forceHide: true
                                    });
                                } catch (error) {
                                }
                            }
                        }
                        
                        return { success: true, message: 'Progress UI hidden' };
                    } catch (error) {
                        return { success: false, error: error.message };
                    }
                
                case 'extractDesignBase64':
                    return await handleExtractDesignBase64(sender);
                
                case 'showErrorNotification':
                    try {
                        await safeCreateNotification(
                            'Snap Automation Error',
                            `Error processing ${request.filename || 'file'}: ${request.error || 'Unknown error'}`,
                            2
                        );
                        return { success: true };
                    } catch (error) {
                        return { success: false, error: error.message };
                    }
                
                default:
                    return { success: false, error: 'Unknown action' };
            }
        } catch (error) {
            return { success: false, error: error.message };
        }
    };

    handleMessage().then(response => {
        try {
            sendResponse(response);
        } catch (error) {
        }
    });

    return true;
});

async function safeStorageGet(keys) {
    return new Promise((resolve, reject) => {
        try {
            chrome.storage.local.get(keys, (result) => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve(result);
                }
            });
        } catch (error) {
            reject(error);
        }
    });
}

async function safeStorageSet(items) {
    return new Promise((resolve, reject) => {
        try {
            chrome.storage.local.set(items, () => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve();
                }
            });
        } catch (error) {
            reject(error);
        }
    });
}

async function safeStorageRemove(keys) {
    return new Promise((resolve, reject) => {
        try {
            chrome.storage.local.remove(keys, () => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve();
                }
            });
        } catch (error) {
            reject(error);
        }
    });
}

async function verifyNotificationPermissionSilently() {
    return new Promise((resolve) => {
        try {
            const testId = 'snap-perm-test-' + Date.now();
            chrome.notifications.create(testId, {
                type: 'basic',
                iconUrl: chrome.runtime.getURL('Icon.png'),
                title: '', 
                message: '',
                priority: -2,
                silent: true,
                requireInteraction: false
            }, (notificationId) => {
                chrome.notifications.clear(notificationId);
                resolve(true);
            });
        } catch (error) {
            resolve(false);
        }
    });
}

async function safeCreateNotification(title, message, priority = 1, autoClearSeconds = 0) {
    try {
        const hasPermission = await verifyNotificationPermissionSilently();
        
        if (!hasPermission) {
            return false;
        }
        
        const uniqueId = 'snap-notification-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        
        await chrome.notifications.create(uniqueId, {
            type: 'basic',
            iconUrl: chrome.runtime.getURL('Icon.png'),
            title: title,
            message: message,
            priority: priority,
            requireInteraction: autoClearSeconds === 0 && priority > 1
        });
        
        if (autoClearSeconds > 0) {
            setTimeout(() => {
                chrome.notifications.clear(uniqueId);
            }, autoClearSeconds * 1000);
        }
        
        return true;
    } catch (error) {
        return false;
    }
}

async function handleStartAutomation(request) {
    try {
        resetNotificationState();
        await ensureNotificationPermissions();
        
        if (automationState.isRunning) {
            if (automationState.windowId) {
                try {
                    const existingWindow = await chrome.windows.get(automationState.windowId);
                    if (!existingWindow) {
                        console.log("");
                        automationState = createCleanState();
                    }
                } catch (error) {
                    console.log("");
                    automationState = createCleanState();
                    await safeStorageRemove(['automationState', 'automationQueue', 'automationFiles']);
                }
            } else {
                console.log("");
                automationState = createCleanState();
                await safeStorageRemove(['automationState', 'automationQueue', 'automationFiles']);
            }
            
            if (automationState.isRunning) {
                throw new Error('Automation already running');
            }
        }

        if (!Array.isArray(request?.files) || request.files.length === 0) {
            throw new Error('Invalid request data - missing files');
        }

        const mappedFiles = request.files.map((file, index) => createFileObject(file, index));
        
        const firstFile = mappedFiles[0];
        if (!isValidFileObject(firstFile)) {
            throw new Error('Invalid first file structure');
        }

        const initialState = {
            ...createCleanState(),
            isRunning: true,
            files: mappedFiles,
            currentFile: firstFile,
            settings: { ...(request.settings || {}), useNativeUploader: request.settings?.useNativeUploader === true },
            totalFiles: mappedFiles.length
        };

        console.log('', JSON.stringify({
            useNativeUploader: initialState.settings?.useNativeUploader,
            requestSettingsValue: request.settings?.useNativeUploader
        }));

        const queueState = {
            settings: request.settings || {},
            remainingFiles: mappedFiles.slice(1),
            totalFiles: mappedFiles.length,
            currentIndex: 0,
            currentFile: firstFile,
            processedFiles: []
        };

        const window = await chrome.windows.create({
            url: `https://merch.amazon.com/designs/new?Snap=0-${encodeURIComponent(firstFile.filename)}`,
            type: 'normal',
            state: 'maximized'
        });

        initialState.windowId = window.id;

        await safeStorageRemove(['automationState', 'automationQueue']);

        await safeStorageSet({ 
            automationState: initialState,
            automationQueue: queueState,
            automationFiles: {
                [firstFile.filename]: firstFile.blobUrl || firstFile.base64,
                ...Object.fromEntries(mappedFiles.slice(1).map(f => [f.filename, f.blobUrl || f.base64]))
            }
        });

        automationState = initialState;

        try {
            await safeCreateNotification(
                'Snap Automation Started',
                `Processing ${initialState.totalFiles} files`,
                1
            );
        } catch (error) {
        }

        return { success: true };

    } catch (error) {
        throw error;
    }
}

function revokeBlobUrl(url) {
    if (url && typeof url === 'string' && url.startsWith('blob:')) {
        try {
            URL.revokeObjectURL(url);
        } catch (error) {
        }
    }
}

async function handleMarkTabCompleted(request, sender) {
    try {
        const data = await safeStorageGet(['automationState', 'automationQueue']);
        
        if (!data?.automationState) {
            throw new Error('Missing automation state');
        }
        
        if (data.automationState.currentFile && 
            data.automationState.currentFile.filename === request.filename) {
            
            if (data.automationState.currentFile.blobUrl) {
                revokeBlobUrl(data.automationState.currentFile.blobUrl);
            }
            
            const updatedState = {
                ...data.automationState,
                currentFile: {
                    ...data.automationState.currentFile,
                    status: 'completed',
                    completedAt: Date.now(),
                    completionStatus: request.status || 'completed',
                    error: request.error || null
                }
            };
            
            await safeStorageSet({ automationState: updatedState });
            automationState = updatedState;
            
            return { success: true };
        } else {
            return { success: false, error: 'File mismatch' };
        }
    } catch (error) {
        return { success: false, error: error.message };
    }
}

async function handleProcessNextFile(request, sender) {
    try {
        const data = await safeStorageGet(['automationState']);
        
        if (data?.automationState?.currentFile) {
            const currentFile = data.automationState.currentFile;
            
            if (currentFile.status !== 'completed') {
                let attempts = 0;
                const maxAttempts = 20;
                
                while (attempts < maxAttempts) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    const updatedData = await safeStorageGet(['automationState']);
                    
                    if (updatedData?.automationState?.currentFile?.status === 'completed') {
                        break;
                    }
                    
                    attempts++;
                    
                    if (attempts >= maxAttempts) {
                        return { 
                            success: false, 
                            error: 'Current file not completed after maximum wait time' 
                        };
                    }
                }
            }
        }
        
        const fullData = await safeStorageGet(['automationQueue', 'automationState', 'automationFiles']);

        if (!fullData?.automationQueue || !fullData?.automationState || !fullData?.automationFiles) {
            throw new Error('Missing automation data');
        }

        if (fullData.automationState.currentFile?.status !== 'completed') {
            return { 
                success: false, 
                error: 'Current file not completed after verification' 
            };
        }

        const currentState = {
            ...createCleanState(),
            ...fullData.automationState,
            isRunning: true
        };

        const remainingFiles = Array.isArray(fullData.automationQueue.remainingFiles) ? 
            fullData.automationQueue.remainingFiles.map(file => ({
                index: file.index || 0,
                filename: file.filename || '',
                blobUrl: fullData.automationFiles[file.filename] || null,
                status: file.status || 'pending'
            })) : [];

        notificationState.lastQueueLength = remainingFiles.length;
        
        if (currentState.currentFile) {
            const processedFile = {
                ...currentState.currentFile,
                status: 'completed'
            };

            if (processedFile.blobUrl) {
                revokeBlobUrl(processedFile.blobUrl);
            }

            if (!currentState.processedFiles) {
                currentState.processedFiles = [];
            }
            
            if (!currentState.processedFiles.some(f => f.filename === processedFile.filename)) {
                currentState.processedFiles.push(processedFile);
            }

            const { [processedFile.filename]: removedData, ...remainingFilesData } = fullData.automationFiles;
            await safeStorageSet({ automationFiles: remainingFilesData });
        }

        if (remainingFiles.length === 0) {
            notificationState.isAutomationTrulyComplete = true;
            notificationState.completionTimestamp = Date.now();
            
            const finalState = {
                ...currentState,
                isRunning: false,
                currentFile: currentState.currentFile ? {
                    ...currentState.currentFile,
                    status: 'completed'
                } : null
            };

            await Promise.all([
                safeStorageRemove(['automationQueue', 'automationFiles']),
                safeStorageSet({ automationState: finalState })
            ]);
            
            automationState = finalState;
            
            await completeAutomation();
            return { success: true, completed: true };
        }

        const nextFile = remainingFiles[0];
        const nextFileIndex = (fullData.automationQueue.currentIndex || 0) + 1;

        const nextFileState = {
            index: nextFileIndex,
            filename: nextFile.filename,
            blobUrl: fullData.automationFiles[nextFile.filename] || null,
            status: 'pending'
        };

        const updatedQueue = {
            settings: fullData.automationQueue.settings || {},
            remainingFiles: remainingFiles.slice(1),
            totalFiles: fullData.automationQueue.totalFiles || remainingFiles.length + 1,
            currentIndex: nextFileIndex,
            currentFile: nextFileState,
            processedFiles: currentState.processedFiles || []
        };

        const updatedState = {
            ...currentState,
            isRunning: true,
            currentFile: nextFileState
        };

        await safeStorageSet({ 
            automationQueue: updatedQueue,
            automationState: updatedState
        });

        automationState = updatedState;

        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const newTab = await chrome.tabs.create({
            windowId: updatedState.windowId,
            url: `https://merch.amazon.com/designs/new?Snap=${nextFileIndex}-${encodeURIComponent(nextFileState.filename)}`,
            active: true
        });
        
        activeProgressTabId = newTab.id;
        
        if (sender?.tab?.id) {
            try {
                await chrome.tabs.sendMessage(sender.tab.id, {
                    action: 'updateAutomationProgress',
                    isTabCompleted: true,
                    forceHide: true
                });
            } catch (error) {
            }
        }

        return { success: true };

    } catch (error) {
        throw error;
    }
}

async function completeAutomation() {
    try {
        notificationState.isAutomationTrulyComplete = true;
        notificationState.completionTimestamp = Date.now();

        automationState.isRunning = false;

        if (automationState?.files && Array.isArray(automationState.files)) {
            automationState.files.forEach(file => {
                if (file.blobUrl) {
                    revokeBlobUrl(file.blobUrl);
                }
            });
        }

        const completionState = {
            ...createCleanState(),
            windowId: automationState?.windowId || null,
            currentFile: automationState?.currentFile ? {
                ...automationState.currentFile,
                status: 'completed'
            } : null,
            files: Array.isArray(automationState?.files) ? 
                automationState.files.map(file => ({ ...file })) : [],
            settings: automationState?.settings || null,
            totalFiles: automationState?.totalFiles || 0,
            processedFiles: Array.isArray(automationState?.processedFiles) ? 
                automationState.processedFiles.map(file => ({ ...file })) : []
        };

        await safeStorageSet({ automationState: completionState });
        automationState = completionState;
        
        try {
            if (!notificationState.completionNotificationShown && notificationState.isAutomationTrulyComplete) {
                await safeCreateNotification(
                    'Snap Automation Complete',
                    `All ${completionState.processedFiles.length} files have been processed successfully!`,
                    2,
                    5
                );
                notificationState.completionNotificationShown = true;
            }
            
            if (activeProgressTabId) {
                try {
                    await chrome.tabs.sendMessage(activeProgressTabId, { 
                        action: 'playCompletionSound',
                        isFinalTab: true
                    });
                } catch (error) {
                    if (automationState && automationState.windowId) {
                        const windowTabs = await chrome.tabs.query({ windowId: automationState.windowId, active: true });
                        if (windowTabs && windowTabs.length > 0) {
                            await chrome.tabs.sendMessage(windowTabs[0].id, { 
                                action: 'playCompletionSound',
                                isFinalTab: true
                            });
                        }
                    }
                }
            } else {
                if (automationState && automationState.windowId) {
                    const windowTabs = await chrome.tabs.query({ windowId: automationState.windowId, active: true });
                    if (windowTabs && windowTabs.length > 0) {
                        await chrome.tabs.sendMessage(windowTabs[0].id, { 
                            action: 'playCompletionSound',
                            isFinalTab: true
                        });
                    }
                }
            }
        } catch (error) {
        }

        try {
            if (activeProgressTabId) {
                const finalStatuses = [
                    { status: 'Uploading...', index: 0 },
                    { status: 'Processing Products', index: 1 },
                    { status: 'Processing Design', index: 2 },
                    { status: 'Actions: Text Swap', index: 18 },
                    { status: 'Actions: Copy EN to All', index: 19 },
                    { status: 'Actions: Save to Drafts', index: 20 },
                    { status: 'Completed', index: 21 }
                ];
                
                for (let i = 0; i < finalStatuses.length; i++) {
                    const currentStatus = finalStatuses[i];
                    const isLastStatus = i === finalStatuses.length - 1;
                    
                    const statusInfo = {
                        status: currentStatus.status,
                        statusIndex: currentStatus.index,
                        totalStatuses: 22,
                        isCompleted: isLastStatus
                    };
                    
                    if (isLastStatus) {
                        statusInfo.forceExact100Percent = true;
                    }
                    
                    try {
                        await chrome.tabs.sendMessage(activeProgressTabId, { 
                            action: 'updateAutomationProgress',
                            status: currentStatus.status,
                            currentFile: completionState.totalFiles,
                            totalFiles: completionState.totalFiles,
                            completed: isLastStatus,
                            isTabCompleted: false,
                            updateProgressOnly: true,
                            updateProgressBar: isLastStatus,
                            forceProgressUpdate: isLastStatus,
                            forceExact100Percent: isLastStatus,
                            statusInfo: statusInfo
                        });
                    } catch (error) {
                        break;
                    }
                    
                    if (!isLastStatus) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                    } else {
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                }
                
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                try {
                    await chrome.tabs.sendMessage(activeProgressTabId, {
                        action: 'updateAutomationProgress',
                        isTabCompleted: true,
                        forceHide: true
                    });
                } catch (error) {
                }
                
                activeProgressTabId = null;
            }
        } catch (error) {
        }

        await new Promise(resolve => setTimeout(resolve, 5000));
        
        try {
            await safeStorageRemove(['automationQueue']);
            
            const storageData = await safeStorageGet(['automationFiles']);
            if (storageData.automationFiles) {
                await safeStorageRemove(['automationFiles']);
            }
            
            setTimeout(() => {
                if (notificationState.isAutomationTrulyComplete) {
                    resetNotificationState();
                }
            }, 120000);
        } catch (error) {
        }
        
        return { success: true, completed: true };
    } catch (error) {
        throw error;
    }
}

chrome.windows.onRemoved.addListener(async (windowId) => {
    console.log();
    
    if (windowId === automationState.windowId) {
        console.log("");
        
        if (automationState?.files && Array.isArray(automationState.files)) {
            automationState.files.forEach(file => {
                if (file.blobUrl) {
                    revokeBlobUrl(file.blobUrl);
                }
            });
        }
        
        automationState = createCleanState();
        isProcessingFile = false;
        activeProgressTabId = null;
        resetNotificationState();
        
        try {
            await safeStorageRemove(['automationState', 'automationQueue', 'automationFiles']);
            console.log("");
        } catch (error) {
            console.error("");
        }
    }
});

function handleExtractDesignBase64(sender) {
    return chrome.scripting.executeScript(
        {
            target: { tabId: sender.tab.id },
            func: () => {
                try {
                    const designElement = document.querySelector("img.artwork.ng-star-inserted");
                    if (designElement) {
                        const canvas = document.createElement("canvas");
                        canvas.width = designElement.naturalWidth;
                        canvas.height = designElement.naturalHeight;
                        const ctx = canvas.getContext("2d");
                        ctx.drawImage(designElement, 0, 0);
                        return { success: true, dataURL: canvas.toDataURL("image/png") };
                    } else {
                        return { success: false, error: "Design element not found" };
                    }
                } catch (error) {
                    return { success: false, error: error.message };
                }
            },
            world: "MAIN",
        },
        (results) => {
            if (chrome.runtime.lastError) {
                return { success: false, error: chrome.runtime.lastError.message };
            }
            if (!results || !results[0] || !results[0].result.success) {
                return { success: false, error: results[0]?.result?.error || "Unknown error" };
            }
            return { success: true, dataURL: results[0].result.dataURL };
        }
    );
}

chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url?.includes('merch.amazon.com/designs/new')) {
        if (isProcessingFile) {
            return;
        }
        chrome.storage.local.get(['automationState', 'automationQueue'], async (data) => {
            try {
                if (!data.automationState?.isRunning) {
                    return;
                }
                if (data.automationState.windowId !== tab.windowId) {
                    return;
                }
                isProcessingFile = true;
                await new Promise(resolve => setTimeout(resolve, 2000));
                chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    func: async () => {
                        try {
                            const uploadContainer = await new Promise((resolve, reject) => {
                                let attempts = 0;
                                const maxAttempts = 30;
                                
                                const checkElement = () => {
                                    attempts++;
                                    const element = document.querySelector('.snap-upload-container');
                                    if (element) {
                                        resolve(element);
                                    } else if (attempts >= maxAttempts) {
                                        reject(new Error('Upload container not found after 15 seconds'));
                                    } else {
                                        setTimeout(checkElement, 500);
                                    }
                                };
                                checkElement();
                            });

                            await new Promise(resolve => setTimeout(resolve, 2000));
                            
                            return { success: true };
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    }
                }, async (results) => {
                    try {
                    } catch (error) {
                    } finally {
                        isProcessingFile = false;
                    }
                });
            } catch (error) {
                isProcessingFile = false;
            }
        });
    }
});

chrome.runtime.onSuspend.addListener(() => {
    try {
        resetNotificationState();
        isProcessingFile = false;
        activeProgressTabId = null;
    } catch (error) {
    }
});
