!function(){
    function initBulkUploadToggle() {
        if (!document.body.contains(document.querySelector("div.container-fluid"))) {
            return setTimeout(initBulkUploadToggle, 500);
        }

        const containerSelector = ".row.mb-4 > .col-3:not(.p-base)";
        const container = document.querySelector("div.container-fluid").querySelector(containerSelector);
        
        if (!container) {
            return setTimeout(initBulkUploadToggle, 500);
        }

        if (!container.querySelector("#dynamic-align-toggle-button")) {
            return setTimeout(initBulkUploadToggle, 300);
        }
        
        if (container.querySelector("#bulk-upload-toggle-button")) {
            return;
        }

        const toggleContainer = document.createElement("div");
        toggleContainer.id = "bulk-upload-toggle-button";
        toggleContainer.style.cssText = `
            display: inline-flex;
            align-items: center;
            cursor: pointer;
            margin-top: 5px;
        `;

        const toggleSwitch = document.createElement("div");
        toggleSwitch.style.cssText = `
            width: 36px;
            height: 18px;
            border-radius: 18px;
            background-color: rgb(207, 212, 212);
            position: relative;
            transition: background-color 0.3s;
            margin-right: 10px;
        `;

        const toggleKnob = document.createElement("span");
        toggleKnob.style.cssText = `
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: white;
            position: absolute;
            top: 3px;
            left: 3px;
            transition: transform 0.3s;
        `;
        toggleSwitch.appendChild(toggleKnob);

        const toggleLabel = document.createElement("span");
        toggleLabel.style.cssText = `
            font-size: 14px;
            color: rgb(100, 100, 100);
        `;

        function updateToggleVisuals(switchEl, knobEl, labelEl) {
            let bulkUploadOn = localStorage.getItem("bulkUploadOn");
            if (bulkUploadOn === null) {
                bulkUploadOn = "true";
                localStorage.setItem("bulkUploadOn", bulkUploadOn);
            }

            if (bulkUploadOn === "true") {
                switchEl.style.backgroundColor = "#470CED";
                knobEl.style.transform = "translateX(18px)";
                labelEl.textContent = "Bulk Upload On";
                labelEl.style.color = "rgb(0, 0, 0)";
                showSnapBulkUploadButton();
            } else {
                switchEl.style.backgroundColor = "rgb(207, 212, 212)";
                knobEl.style.transform = "translateX(0)";
                labelEl.textContent = "Bulk Upload Off";
                labelEl.style.color = "rgb(100, 100, 100)";
                hideSnapBulkUploadButton();
            }
        }

        function toggleBulkUploadState(switchEl, knobEl, labelEl) {
            let bulkUploadOn = localStorage.getItem("bulkUploadOn") === "true";
            bulkUploadOn = !bulkUploadOn;
            localStorage.setItem("bulkUploadOn", bulkUploadOn.toString());
            
            if (bulkUploadOn) {
                switchEl.style.backgroundColor = "#470CED";
                knobEl.style.transform = "translateX(18px)";
                labelEl.textContent = "Bulk Upload On";
                labelEl.style.color = "rgb(0, 0, 0)";
                showSnapBulkUploadButton();
            } else {
                switchEl.style.backgroundColor = "rgb(207, 212, 212)";
                knobEl.style.transform = "translateX(0)";
                labelEl.textContent = "Bulk Upload Off";
                labelEl.style.color = "rgb(100, 100, 100)";
                hideSnapBulkUploadButton();
            }
        }
        
        updateToggleVisuals(toggleSwitch, toggleKnob, toggleLabel);

        toggleContainer.addEventListener("click", function() {
            toggleBulkUploadState(toggleSwitch, toggleKnob, toggleLabel);
        });

        toggleContainer.appendChild(toggleSwitch);
        toggleContainer.appendChild(toggleLabel);

        const dynamicAlignToggle = container.querySelector("#dynamic-align-toggle-button");
        if (dynamicAlignToggle && dynamicAlignToggle.nextSibling) {
            container.insertBefore(toggleContainer, dynamicAlignToggle.nextSibling);
        } else {
            container.appendChild(toggleContainer);
        }
    }

    function hideSnapBulkUploadButton() {
        const existingStyle = document.getElementById("bulk-upload-toggle-style");
        if (!existingStyle) {
            const style = document.createElement("style");
            style.id = "bulk-upload-toggle-style";
            style.textContent = `
                #snap-bulk-upload-btn {
                    display: none !important;
                    visibility: hidden !important;
                    opacity: 0 !important;
                    pointer-events: none !important;
                }
            `;
            document.head.appendChild(style);
        }
    }

    function showSnapBulkUploadButton() {
        const style = document.getElementById("bulk-upload-toggle-style");
        if (style) {
            style.remove();
        }
    }

    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", initBulkUploadToggle);
    } else {
        initBulkUploadToggle();
    }

    const observer = new MutationObserver((mutations) => {
        mutations.forEach(mutation => {
            if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
                initBulkUploadToggle();
            }
        });
    });
    
    observer.observe(document.body, { 
        childList: true, 
        subtree: true 
    });

    const bulkUploadOn = localStorage.getItem("bulkUploadOn");
    if (bulkUploadOn === "true" || bulkUploadOn === null) {
        showSnapBulkUploadButton();
    } else {
        hideSnapBulkUploadButton();
    }
}(); 