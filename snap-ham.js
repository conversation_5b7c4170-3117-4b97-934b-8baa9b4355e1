(() => {
  const fontStyle = document.createElement('style');
  fontStyle.textContent = `
    @font-face {
      font-family: 'Amazon Ember';
      src: url('${chrome.runtime.getURL('fonts/AmazonEmber_Regular.ttf')}') format('truetype');
      font-weight: normal;
      font-style: normal;
      font-display: swap;
    }
    
    @font-face {
      font-family: 'Amazon Ember';
      src: url('${chrome.runtime.getURL('fonts/AmazonEmber_Bold.ttf')}') format('truetype');
      font-weight: bold;
      font-style: normal;
      font-display: swap;
    }
    
    @font-face {
      font-family: 'Amazon Ember';
      src: url('${chrome.runtime.getURL('fonts/Amazon-Ember-Medium.ttf')}') format('truetype');
      font-weight: 500;
      font-style: normal;
      font-display: swap;
    }
    
    .snap-hammer-element {
      font-family: 'Amazon Ember' !important;
    }
  `;
  document.head.appendChild(fontStyle);

  const countries = [
    'Afghanistan', 'Albania', 'Algeria', 'American Samoa', 'Andorra', 'Anguilla', 'Antarctica', 
    'Antigua And Barbuda', 'Argentina', 'Armenia', 'Aruba', 'Australia', 'Austria', 'Azerbaijan', 
    'Bahamas', 'Bahrain', 'Bangladesh', 'Barbados', 'Belarus', 'Belgium', 'Belize', 'Benin', 
    'Bermuda', 'Bhutan', 'Bolivia', 'Bosnia and Herzegovina', 'Botswana', 'Bouvet Island', 
    'Brazil', 'British Indian Ocean Territory', 'Brunei Darussalam', 'Bulgaria', 'Burkina Faso', 
    'Burundi', 'Cambodia', 'Cameroon', 'Canada', 'Cape Verde', 'Cayman Islands', 
    'Central African Republic', 'Chad', 'Chile', 'China', 'Christmas Island', 
    'Cocos (Keeling) Islands', 'Colombia', 'Comoros', 'Congo', 'Democratic Republic of the Congo', 
    'Cook Islands', 'Costa Rica', "Cote d'Ivoire", 'Croatia', 'Cyprus', 'Czech Republic', 
    'Denmark', 'Djibouti', 'Dominica', 'Dominican Republic', 'East Timor', 'Ecuador', 'Egypt', 
    'El Salvador', 'Equatorial Guinea', 'Eritrea', 'Estonia', 'Ethiopia', 'Falkland Islands', 
    'Faroe Islands', 'Fiji', 'Finland', 'France', 'French Guiana', 'French Polynesia', 
    'French Southern Territories', 'Gabon', 'Gambia', 'Georgia', 'Germany', 'Ghana', 'Gibraltar', 
    'Greece', 'Greenland', 'Grenada', 'Guadeloupe', 'Guam', 'Guatemala', 'Guinea', 
    'Guinea-Bissau', 'Guyana', 'Haiti', 'Heard and Mc Donald Islands', 'Honduras', 
    'Hong Kong, China', 'Hungary', 'Iceland', 'India', 'Indonesia', 'Ireland', 'Israel', 'Italy', 
    'Jamaica', 'Japan', 'Jordan', 'Kazakhstan', 'Kenya', 'Kiribati', 'Korea (South)', 'Kuwait', 
    'Kyrgyzstan', 'Lao People\'s Democratic Republic', 'Latvia', 'Lebanon', 'Lesotho', 'Liberia', 
    'Liechtenstein', 'Lithuania', 'Luxembourg', 'Macau', 'Macedonia', 'Madagascar', 'Malawi', 
    'Malaysia', 'Maldives', 'Mali', 'Malta', 'Marshall Islands', 'Martinique', 'Mauritania', 
    'Mauritius', 'Mayotte', 'Mexico', 'Micronesia, Federated States of', 'Moldova, Republic of', 
    'Mongolia', 'Montserrat', 'Morocco', 'Mozambique', 'Myanmar', 'Namibia', 'Nauru', 'Nepal', 
    'Netherlands', 'Netherlands Antilles', 'New Caledonia', 'New Zealand', 'Nicaragua', 'Niger', 
    'Nigeria', 'Niue', 'Norfolk Island', 'Northern Mariana Islands', 'Norway', 'Oman', 'Pakistan', 
    'Palau', 'Panama', 'Papua New Guinea', 'Paraguay', 'Peru', 'Philippines', 'Pitcairn', 
    'Poland', 'Portugal', 'Puerto Rico', 'Qatar', 'Reunion', 'Romania', 'Russia', 'Rwanda', 
    'Saint Kitts and Nevis', 'Saint Lucia', 'Saint Vincent and the Grenadines', 
    'Samoa (Independent)', 'San Marino', 'Sao Tome and Principe', 'Saudi Arabia', 'Senegal', 
    'Seychelles', 'Sierra Leone', 'Singapore', 'Slovakia', 'Slovenia', 'Solomon Islands', 
    'Somalia', 'South Africa', 'South Georgia and the South Sandwich Islands', 'Spain', 
    'Sri Lanka', 'St. Helena', 'St. Pierre and Miquelon', 'Suriname', 
    'Svalbard and Jan Mayen Islands', 'Swaziland', 'Sweden', 'Switzerland', 'Taiwan', 
    'Tajikistan', 'Tanzania', 'Thailand', 'Togo', 'Tokelau', 'Tonga', 'Trinidad and Tobago', 
    'Tunisia', 'Turkey', 'Turkmenistan', 'Turks and Caicos Islands', 'Tuvalu', 'Uganda', 
    'Ukraine', 'United Arab Emirates', 'United Kingdom', 'United States', 
    'United States Minor Outlying Islands', 'Uruguay', 'Uzbekistan', 'Vanuatu', 
    'Vatican City State (Holy See)', 'Venezuela', 'Vietnam', 'Virgin Islands (British)', 
    'Virgin Islands (U.S.)', 'Wallis and Futuna Islands', 'Western Sahara', 'Yemen', 
    'Yugoslavia', 'Zambia', 'Zimbabwe'
  ];

  const snapHammer = document.createElement('div');
  snapHammer.setAttribute('data-tooltip', 'Drag listings here to report');
  snapHammer.style.cssText = `
    position: fixed;
    bottom: 80px;
    right: 80px;
    z-index: 999997;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: #000000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    transition: transform 0.2s ease-out;
    cursor: pointer;
  `;

  const tooltipStyle = document.createElement('style');
  tooltipStyle.textContent = `
    [data-tooltip] {
      position: relative;
    }

    [data-tooltip]:before {
      content: attr(data-tooltip);
      position: absolute;
      bottom: calc(100% + 8px);
      left: 50%;
      transform: translateX(-50%);
      padding: 4px 12px;
      background-color: #1F2937;
      color: white;
      font-size: 12px;
      font-family: 'Amazon Ember';
      font-weight: 500;
      white-space: nowrap;
      border-radius: 999px;
      pointer-events: none;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.2s ease, visibility 0.2s ease;
      z-index: 9;
    }

    [data-tooltip]:after {
      content: '';
      position: absolute;
      bottom: calc(100% + 4px);
      left: 50%;
      transform: translateX(-50%);
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-top: 5px solid #1F2937;
      pointer-events: none;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.2s ease, visibility 0.2s ease;
      z-index: 9;
    }

    [data-tooltip]:hover:before,
    [data-tooltip]:hover:after {
      opacity: 1;
      visibility: visible;
    }
  `;
  document.head.appendChild(tooltipStyle);

  const img = document.createElement('img');
  const iconUrl = chrome.runtime.getURL('assets/hammer-ic.svg');
  img.src = iconUrl;
  img.style.cssText = `
    width: 36px;
    height: 36px;
    pointer-events: none;
    transition: transform 0.2s ease-out;
  `;
  snapHammer.appendChild(img);

  const counter = document.createElement('div');
  counter.style.cssText = `
    position: absolute;
    top: 0px;
    right: 0px;
    background: #FA583A;
    color: white;
    border: 4px solid white;
    border-radius: 24px;
    padding: 2px 8px;
    font-family: 'Amazon Ember';
    font-weight: bold;
    font-size: 16px;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
  `;
  counter.textContent = '0';
  snapHammer.appendChild(counter);

  const overlay = document.createElement('div');
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999998;
    display: none;
  `;

  const snapHammerPopup = document.createElement('div');
  snapHammerPopup.id = 'snapHammerPopup';
  snapHammerPopup.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 994px;
    height: 96vh;
    background: #F7F8FA;
    border-radius: 28px;
    padding: 40px;
    z-index: 999999;
    display: none;
    flex-direction: column;
    gap: 24px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.1);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #DCE0E5 transparent;
    scroll-behavior: smooth;
    overscroll-behavior: contain;
  `;

  const scrollStyles = document.createElement('style');
  scrollStyles.textContent = `
    #snapHammerPopup::-webkit-scrollbar,
    .validation-section::-webkit-scrollbar,
    #confirmProductsSection::-webkit-scrollbar {
      width: 8px;
    }
    
    #snapHammerPopup::-webkit-scrollbar-track,
    .validation-section::-webkit-scrollbar-track,
    #confirmProductsSection::-webkit-scrollbar-track {
      background: transparent;
      margin: 10px 0;
    }
    
    #snapHammerPopup::-webkit-scrollbar-thumb,
    .validation-section::-webkit-scrollbar-thumb,
    #confirmProductsSection::-webkit-scrollbar-thumb {
      background-color: #DCE0E5;
      border-radius: 20px;
      border: 2px solid #F7F8FA;
    }
    
    #snapHammerPopup::-webkit-scrollbar-thumb:hover,
    .validation-section::-webkit-scrollbar-thumb:hover,
    #confirmProductsSection::-webkit-scrollbar-thumb:hover {
      background-color: #C7CDD5;
    }
    
    @media screen and (max-height: 700px) {
      #snapHammerPopup {
        height: 96vh;
        top: 48%;
      }
      
      .validation-section {
        height: 96vh;
        top: 48%;
      }
      
      #confirmProductsSection {
        height: 96vh;
        top: 48%;
      }
    }
    
    /* Ensure all popup sections have proper sizing */
    #snapHammerPopup > div {
      max-width: 100%;
    }
    
    #asinsTableContainer, #validateSection, #validationSection, #confirmReportSection, #confirmProductsSection {
      height: calc(96vh - 180px);
      overflow-y: auto;
    }
    
    #asinsTableContainer::-webkit-scrollbar,
    #validateSection::-webkit-scrollbar,
    #validationSection::-webkit-scrollbar,
    #confirmReportSection::-webkit-scrollbar,
    #confirmProductsSection::-webkit-scrollbar {
      width: 8px;
    }
    
    #asinsTableContainer::-webkit-scrollbar-track,
    #validateSection::-webkit-scrollbar-track,
    #validationSection::-webkit-scrollbar-track,
    #confirmReportSection::-webkit-scrollbar-track,
    #confirmProductsSection::-webkit-scrollbar-track {
      background: transparent;
      margin: 10px 0;
    }
    
    #asinsTableContainer::-webkit-scrollbar-thumb,
    #validateSection::-webkit-scrollbar-thumb,
    #validationSection::-webkit-scrollbar-thumb,
    #confirmReportSection::-webkit-scrollbar-thumb,
    #confirmProductsSection::-webkit-scrollbar-thumb {
      background-color: #DCE0E5;
      border-radius: 20px;
      border: 2px solid #F7F8FA;
    }
    
    #asinsTableContainer::-webkit-scrollbar-thumb:hover,
    #validateSection::-webkit-scrollbar-thumb:hover,
    #validationSection::-webkit-scrollbar-thumb:hover,
    #confirmReportSection::-webkit-scrollbar-thumb:hover,
    #confirmProductsSection::-webkit-scrollbar-thumb:hover {
      background-color: #C7CDD5;
    }
  `;
  document.head.appendChild(scrollStyles);

  const header = document.createElement('div');
  header.className = 'listings-top';
  header.style.cssText = `
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
  `;

  const leftContainer = document.createElement('div');
  leftContainer.className = 'listings-left';
  leftContainer.style.cssText = `
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 16px;
    margin-right: auto;
    padding-right: 48px;
  `;

  const headerIcon = document.createElement('img');
  headerIcon.src = chrome.runtime.getURL('assets/Snap-hammer-ic.svg');
  headerIcon.style.cssText = `
    width: 36px;
    height: 36px;
    flex-shrink: 0;
  `;

  const textContainer = document.createElement('div');
  textContainer.style.cssText = `
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0px;
    width: 100%;
  `;

  const mainTitle = document.createElement('h2');
  mainTitle.textContent = 'Snap Hammer';
  mainTitle.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 700;
    font-size: 20px;
    color: #000000;
    margin: 0;
    line-height: 1.197;
    width: 100%;
  `;

  textContainer.appendChild(mainTitle);

  leftContainer.appendChild(headerIcon);
  leftContainer.appendChild(textContainer);

  const rightSection = document.createElement('div');
  rightSection.className = 'listings-right';
  rightSection.style.cssText = `
    display: flex;
    align-items: center;
    gap: 24px;
  `;

  const stepper = document.createElement('div');
  stepper.style.cssText = `
    display: flex;
    align-items: center;
    gap: 16px;
  `;

  const listingSteps = [
    { number: '01', text: 'Add Infringing ASINs', state: 'active' },
    { number: '02', text: 'Validate your ASIN', state: 'inactive' },
    { number: '03', text: 'Confirm & Report', state: 'inactive' }
  ];

  listingSteps.forEach((step, index) => {
    const stepContainer = document.createElement('div');
    stepContainer.style.cssText = `
      display: flex;
      align-items: center;
      gap: 8px;
    `;

    const numberCircle = document.createElement('div');
    numberCircle.style.cssText = `
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: 'Amazon Ember';
      font-weight: 500;
      font-size: 12px;
      background: ${step.state === 'active' ? '#470CED' : '#E9EBF2'};
      color: ${step.state === 'active' ? 'white' : '#606F95'};
    `;
    numberCircle.textContent = step.number;
    stepContainer.appendChild(numberCircle);

    const stepText = document.createElement('span');
    stepText.style.cssText = `
      font-family: 'Amazon Ember';
      font-weight: 500;
      font-size: 14px;
      color: ${step.state === 'active' ? '#470CED' : '#606F95'};
    `;
    stepText.textContent = step.text;
    stepContainer.appendChild(stepText);

    if (index < listingSteps.length - 1) {
      const separator = document.createElement('div');
      separator.style.cssText = `
        width: 24px;
        height: 1px;
        background: #E9EBF2;
        margin: 0 4px;
      `;
      stepContainer.appendChild(separator);
    }

    stepper.appendChild(stepContainer);
  });

  const closeButton = document.createElement('button');
  closeButton.style.cssText = `
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 24px;
  `;

  const closeIcon = document.createElement('img');
  closeIcon.src = chrome.runtime.getURL('assets/close-ham-popup-ic.svg');
  closeIcon.style.cssText = `
    width: 24px;
    height: 24px;
  `;

  closeButton.appendChild(closeIcon);
  rightSection.appendChild(stepper);
  rightSection.appendChild(closeButton);

  header.appendChild(leftContainer);
  header.appendChild(rightSection);

  const tabBar = document.createElement('div');
  tabBar.style.cssText = `
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 0;
  `;

  const tabButtons = document.createElement('div');
  tabButtons.style.cssText = `
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2px;
    background: #E9EBF2;
    border-radius: 6px;
    padding: 2px;
    width: 50%;
  `;

  const listingsTab = document.createElement('button');
  listingsTab.style.cssText = `
    position: relative;
    background: white;
    border: 1px solid #DCE0E5;
    border-radius: 4px;
    cursor: pointer;
    padding: 0;
    margin: 0;
  `;

  const historyTab = document.createElement('button');
  historyTab.style.cssText = `
    position: relative;
    background: transparent;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    padding: 0;
    margin: 0;
  `;

  const listingsInner = document.createElement('div');
  listingsInner.style.cssText = `
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-family: 'Amazon Ember';
    font-weight: 700;
    font-size: 14px;
    color: #470CED;
    pointer-events: none;
  `;

  const historyInner = document.createElement('div');
  historyInner.style.cssText = `
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 14px;
    color: #606F95;
    pointer-events: none;
  `;

  const listingsIcon = document.createElement('img');
  listingsIcon.src = chrome.runtime.getURL('assets/products-options-ic.svg');
  listingsIcon.style.cssText = `
    width: 16px;
    height: 16px;
    pointer-events: none;
  `;

  const listingsText = document.createElement('span');
  listingsText.textContent = 'Listings to Report';
  listingsText.style.cssText = `
    pointer-events: none;
  `;

  const historyIcon = document.createElement('img');
  historyIcon.src = chrome.runtime.getURL('assets/history-ic.svg');
  historyIcon.style.cssText = `
    width: 16px;
    height: 16px;
    pointer-events: none;
  `;

  const historyText = document.createElement('span');
  historyText.textContent = 'Report History';
  historyText.style.cssText = `
    pointer-events: none;
  `;

  listingsInner.appendChild(listingsIcon);
  listingsInner.appendChild(listingsText);
  historyInner.appendChild(historyIcon);
  historyInner.appendChild(historyText);

  listingsTab.appendChild(listingsInner);
  historyTab.appendChild(historyInner);

  listingsTab.style.height = '30px';
  historyTab.style.height = '30px';

  const content = document.createElement('div');
  content.style.cssText = `
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    overflow-y: auto;
    margin-top: 0;
    gap: 0;
    flex: 1;
  `;

  const actionsBar = document.createElement('div');
  actionsBar.style.cssText = `
    display: flex;
    gap: 8px;
    align-items: center;
  `;

  const deleteButton = document.createElement('button');
  deleteButton.setAttribute('data-tooltip', 'Delete selected');
  deleteButton.style.cssText = `
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    height: 36px;
    width: 36px;
    background: #FF391F;
    border: none;
    border-radius: 999px;
    cursor: not-allowed;
    opacity: 0.5;
    pointer-events: none;
    transition: all 0.2s ease;
    position: relative;
    overflow: visible;
  `;
  deleteButton.disabled = true;
  const clearIcon = document.createElement('img');
  clearIcon.src = chrome.runtime.getURL('assets/clear.svg');
  clearIcon.style.cssText = `
    width: 16px;
    height: 16px;
    filter: brightness(0) invert(1);
  `;
  deleteButton.appendChild(clearIcon);

  const downloadButton = document.createElement('button');
  downloadButton.setAttribute('data-tooltip', 'Download selected');
  downloadButton.style.cssText = `
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    height: 36px;
    width: 36px;
    background: #470CED;
    border: none;
    border-radius: 999px;
    cursor: not-allowed;
    opacity: 0.5;
    pointer-events: none;
    transition: all 0.2s ease;
    position: relative;
    overflow: visible;
  `;
  downloadButton.disabled = true;

  const downloadIcon = document.createElement('img');
  downloadIcon.src = chrome.runtime.getURL('assets/download-ic.svg');
  downloadIcon.style.cssText = `
    width: 16px;
    height: 16px;
    filter: brightness(0) invert(1);
  `;
  downloadButton.appendChild(downloadIcon);

  const searchWrapper = document.createElement('div');
  searchWrapper.style.cssText = `
    position: relative;
    display: inline-block;
    width: 220px;
  `;

  const searchInput = document.createElement('input');
  searchInput.placeholder = 'Search for ASIN, listings...';
  searchInput.style.cssText = `
    width: 100%;
    height: 36px;
    padding: 8px 36px 8px 12px;
    border: 1px solid #E4E4E7;
    border-radius: 999px;
    font-family: 'Amazon Ember';
    font-size: 14px;
    color: #09090B;
    outline: none;
  `;

  const clearSearch = document.createElement('button');
  clearSearch.style.cssText = `
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    display: none;
    width: 16px;
    height: 16px;
  `;

  const searchClearIcon = document.createElement('img');
  searchClearIcon.src = chrome.runtime.getURL('assets/close-ham-popup-ic.svg');
  searchClearIcon.style.cssText = `
    width: 16px;
    height: 16px;
    opacity: 0.5;
    transition: opacity 0.2s ease;
  `;

  clearSearch.appendChild(searchClearIcon);
  clearSearch.addEventListener('mouseenter', () => {
    searchClearIcon.style.opacity = '1';
  });

  clearSearch.addEventListener('mouseleave', () => {
    searchClearIcon.style.opacity = '0.5';
  });

  function filterRows(searchTerm) {
    const rows = tableBody.querySelectorAll('div[style*="grid"]');
    let hasVisibleRows = false;

    rows.forEach(row => {
      const asinCell = row.querySelector('div:last-child').textContent;
      const productCell = row.querySelector('div:nth-child(3)').textContent;
      const matchesSearch = (
        asinCell.toLowerCase().includes(searchTerm.toLowerCase()) ||
        productCell.toLowerCase().includes(searchTerm.toLowerCase())
      );

      row.style.display = matchesSearch ? 'grid' : 'none';
      if (matchesSearch) hasVisibleRows = true;
    });

    const entireTable = table;
    const listingsContent = document.querySelector('.listings-tab-content');

    if (!hasVisibleRows && searchTerm) {
      if (entireTable) {
        entireTable.style.display = 'none';
      }

      deleteButton.style.display = 'none';
      downloadButton.style.display = 'none';

      let noResults = listingsContent.querySelector('.no-results');
      if (!noResults) {
        noResults = document.createElement('div');
        noResults.className = 'no-results';
        noResults.style.cssText = `
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 16px;
          font-family: 'Amazon Ember';
          font-size: 14px;
          color: #606F95;
          text-align: center;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 100%;
          padding: 0 40px;
        `;
        
        const noResultsImg = document.createElement('img');
        noResultsImg.src = chrome.runtime.getURL('assets/no-results-img.svg');
        noResultsImg.style.cssText = `
          width: 80px;
          height: 80px;
          margin-bottom: 8px;
        `;
        
        const noResultsText = document.createElement('p');
        noResultsText.style.cssText = `
          font-family: 'Amazon Ember';
          font-size: 14px;
          color: #606F95;
          text-align: center;
          margin: 0;
        `;
        noResultsText.textContent = `No results found for "${searchTerm}"`;
        
        noResults.appendChild(noResultsImg);
        noResults.appendChild(noResultsText);
        
        listingsContent.appendChild(noResults);
      } else {
        const noResultsText = noResults.querySelector('p');
        if (noResultsText) {
          noResultsText.textContent = `No results found for "${searchTerm}"`;
        }
        noResults.style.display = 'flex';
      }

      const reportButton = document.getElementById('report-button');
      if (reportButton) {
        reportButton.style.display = 'none';
      }
    } else {
      if (entireTable) {
        entireTable.style.display = 'block';
      }

      deleteButton.style.display = 'flex';
      downloadButton.style.display = 'flex';

      const noResults = listingsContent.querySelector('.no-results');
      if (noResults) {
        noResults.style.display = 'none';
      }

      const reportButton = document.getElementById('report-button');
      if (reportButton) {
        reportButton.style.display = 'flex';
      }
    }
  }

  searchInput.addEventListener('input', (e) => {
    const searchTerm = e.target.value;
    clearSearch.style.display = searchTerm ? 'block' : 'none';
    filterRows(searchTerm);
  });

  clearSearch.addEventListener('click', () => {
    searchInput.value = '';
    clearSearch.style.display = 'none';
    filterRows('');
    searchInput.focus();
  });

  searchWrapper.appendChild(searchInput);
  searchWrapper.appendChild(clearSearch);

  actionsBar.appendChild(deleteButton);
  actionsBar.appendChild(downloadButton);
  actionsBar.appendChild(searchWrapper);

  const table = document.createElement('div');
  table.style.cssText = `
    width: 100%;
    max-height: 688px;
    background: white;
    border: 1px solid #E4E4E7;
    border-radius: 8px;
    overflow: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  `;

  const tableHeader = document.createElement('div');
  tableHeader.className = 'header-div';
  tableHeader.style.cssText = `
    display: grid;
    grid-template-columns: 48px 100px 378px 130px 120px 120px;
    gap: 0;
    border-bottom: 1px solid #E4E4E7;
    width: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: sticky;
    top: 0;
    background: white;
    z-index: 1;
  `;

  const checkboxHeader = document.createElement('div');
  checkboxHeader.style.cssText = `
    padding: 12px 16px 10px;
    display: flex;
    align-items: center;
    height: 52px;
  `;
  const headerCheckbox = document.createElement('input');
  headerCheckbox.type = 'checkbox';
  headerCheckbox.style.cssText = `
    min-width: 20px;
    min-height: 20px;
    width: 20px;
    height: 20px;
    border: 1.5px solid #E4E4E7;
    border-radius: 4px;
    appearance: none;
    cursor: pointer;
    position: relative;
    background: white;
    transition: all 0.2s ease;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
  `;

  const selectedCheckboxes = new Set();

  headerCheckbox.addEventListener('change', function() {
    if (fetchQueue.isFetchingActive) {
      return;
    }
    
    const visibleCheckboxes = Array.from(tableBody.querySelectorAll('input[type="checkbox"]')).filter(checkbox => {
        const row = checkbox.closest('div[style*="grid"]');
        return row && row.style.display !== 'none';
    });
    const isChecked = this.checked;

    selectedCheckboxes.clear();

    if (this.checked) {
        this.style.backgroundColor = '#470CED';
        this.style.borderColor = '#470CED';
        this.style.backgroundImage = "url(\"data:image/svg+xml,%3Csvg width='10' height='8' viewBox='0 0 10 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 1L3.5 6.5L1 4' stroke='white' stroke-width='1.6666' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\")";
        this.style.backgroundRepeat = 'no-repeat';
        this.style.backgroundPosition = 'center';
    } else {
        this.style.backgroundColor = 'white';
        this.style.borderColor = '#E4E4E7';
        this.style.backgroundImage = 'none';
    }

    visibleCheckboxes.forEach(checkbox => {
        checkbox.checked = isChecked;
        if (isChecked) {
            checkbox.style.backgroundColor = '#470CED';
            checkbox.style.borderColor = '#470CED';
            checkbox.style.backgroundImage = "url(\"data:image/svg+xml,%3Csvg width='10' height='8' viewBox='0 0 10 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 1L3.5 6.5L1 4' stroke='white' stroke-width='1.6666' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\")";
            checkbox.style.backgroundRepeat = 'no-repeat';
            checkbox.style.backgroundPosition = 'center';
            selectedCheckboxes.add(checkbox);
        } else {
            checkbox.style.backgroundColor = 'white';
            checkbox.style.borderColor = '#E4E4E7';
            checkbox.style.backgroundImage = 'none';
        }
    });

    updateButtonStates(selectedCheckboxes.size);
  });

  checkboxHeader.appendChild(headerCheckbox);

  const marketplaceHeader = document.createElement('div');
  marketplaceHeader.style.cssText = `
    padding: 16px 16px 16px 0;
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 14px;
    color: #606F95;
    display: flex;
    align-items: center;
    height: 100%;
  `;
  marketplaceHeader.textContent = 'Marketplace';

  const productHeader = document.createElement('div');
  productHeader.style.cssText = `
    padding: 16px 16px 16px 0;
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 14px;
    color: #606F95;
    display: flex;
    align-items: center;
  `;

  const productSortButton = document.createElement('div');
  productSortButton.style.cssText = `
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
  `;
  productSortButton.textContent = 'Listing';
  const productAscending = document.createElement('img');
  productAscending.src = chrome.runtime.getURL('assets/Ascending.svg');
  productAscending.style.cssText = `
    width: 16px;
    height: 16px;
    display: block;
  `;
  const productDescending = document.createElement('img');
  productDescending.src = chrome.runtime.getURL('assets/Descending.svg');
  productDescending.style.cssText = `
    width: 16px;
    height: 16px;
    display: none;
  `;

  const publishedHeader = document.createElement('div');
  publishedHeader.style.cssText = `
    padding: 16px 16px 16px 0;
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 14px;
    color: #606F95;
    display: flex;
    align-items: center;
  `;

  const publishedSortButton = document.createElement('div');
  publishedSortButton.style.cssText = `
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
  `;
  publishedSortButton.textContent = 'Published';
  const publishedAscending = document.createElement('img');
  publishedAscending.src = chrome.runtime.getURL('assets/Ascending.svg');
  publishedAscending.style.cssText = `
    width: 16px;
    height: 16px;
    display: block;
  `;
  const publishedDescending = document.createElement('img');
  publishedDescending.src = chrome.runtime.getURL('assets/Descending.svg');
  publishedDescending.style.cssText = `
    width: 16px;
    height: 16px;
    display: none;
  `;

  publishedSortButton.appendChild(publishedAscending);
  publishedSortButton.appendChild(publishedDescending);
  publishedHeader.appendChild(publishedSortButton);

  const asinHeader = document.createElement('div');
  asinHeader.style.cssText = `
    padding: 16px 16px 16px 0;
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 14px;
    color: #606F95;
    display: flex;
    align-items: center;
    width: 100%;
  `;

  const asinSortButton = document.createElement('div');
  asinSortButton.style.cssText = `
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
  `;

  const asinHeaderContent = document.createElement('div');
  asinHeaderContent.style.cssText = `
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
  `;

  const asinCounter = document.createElement('div');
  asinCounter.style.cssText = `
    padding: 4px 8px;
    margin-left: 16px;
    background: #470CED;
    border-radius: 4px;
    color: white;
    font-family: 'Amazon Ember';
    font-size: 14px;
    font-weight: 500;
  `;
  asinCounter.textContent = '0/50';

  asinSortButton.textContent = 'ASIN';
  const asinAscending = document.createElement('img');
  asinAscending.src = chrome.runtime.getURL('assets/Ascending.svg');
  asinAscending.style.cssText = `
    width: 16px;
    height: 16px;
    display: block;
  `;
  const asinDescending = document.createElement('img');
  asinDescending.src = chrome.runtime.getURL('assets/Descending.svg');
  asinDescending.style.cssText = `
    width: 16px;
    height: 16px;
    display: none;
  `;

  asinSortButton.appendChild(asinAscending);
  asinSortButton.appendChild(asinDescending);
  
  asinHeaderContent.appendChild(asinSortButton);
  asinHeaderContent.appendChild(asinCounter);
  asinHeader.appendChild(asinHeaderContent);

  let productSortState = 'asc'; 
  let asinSortState = null; 
  let publishedSortState = null; 

  function getBSRValue(bsr) {
    if (bsr === 'N/A' || !bsr || bsr === 'Loading...') {
      return { value: Number.MAX_SAFE_INTEGER, isNA: true };
    }
    
    const match = bsr.match(/[\d,]+/);
    if (!match) {
      return { value: Number.MAX_SAFE_INTEGER, isNA: true };
    }
    
    return { 
      value: parseInt(match[0].replace(/,/g, ''), 10),
      isNA: false 
    };
  }

  function getPublishedDate(dateStr) {
    if (!dateStr || dateStr === 'N/A' || dateStr === 'Loading...') {
      return new Date(0);
    }
    return new Date(dateStr);
  }

  function getBSRValueForSorting(bsr) {
    if (!bsr || bsr === 'N/A' || bsr === 'Loading...' || bsr === 'Error') {
        return Number.MAX_SAFE_INTEGER;
    }
    let value = 0;
    let foundDigit = false;
    for (let i = 0; i < bsr.length; i++) {
        const ch = bsr.charCodeAt(i);
        if (ch >= 48 && ch <= 57) {
            foundDigit = true;
            value = value * 10 + (ch - 48);
        }
    }
    return foundDigit ? value : Number.MAX_SAFE_INTEGER;
  }

  function sortRows() {
    const rows = Array.from(tableBody.children);
    
    if (productSortState === 'asc' || productSortState === 'desc') {
      rows.sort((a, b) => {
        const productA = a.querySelector('div:nth-child(3) a').textContent;
        const productB = b.querySelector('div:nth-child(3) a').textContent;
        return productSortState === 'asc' ? 
          productA.localeCompare(productB) : 
          productB.localeCompare(productA);
      });
    } else if (asinSortState === 'asc' || asinSortState === 'desc') {
      rows.sort((a, b) => {
        const asinA = a.querySelector('div:nth-child(6)').textContent;
        const asinB = b.querySelector('div:nth-child(6)').textContent;
        return asinSortState === 'asc' ? 
          asinA.localeCompare(asinB) : 
          asinB.localeCompare(asinA);
      });
    } else if (publishedSortState === 'asc' || publishedSortState === 'desc') {
      rows.sort((a, b) => {
        const dateA = getPublishedDate(a.querySelector('div:nth-child(4)').textContent);
        const dateB = getPublishedDate(b.querySelector('div:nth-child(4)').textContent);
        return publishedSortState === 'asc' ? 
          dateA - dateB : 
          dateB - dateA;
      });
    } else if (bsrSortState === 'asc' || bsrSortState === 'desc') {
      rows.sort((a, b) => {
        const asinA = a.querySelector('div:nth-child(6)').textContent;
        const asinB = b.querySelector('div:nth-child(6)').textContent;
        const bsrA = productBSRs.get(asinA) || 'N/A';
        const bsrB = productBSRs.get(asinB) || 'N/A';
        
        const numA = bsrA === 'N/A' ? Number.MAX_SAFE_INTEGER : parseInt(bsrA, 10);
        const numB = bsrB === 'N/A' ? Number.MAX_SAFE_INTEGER : parseInt(bsrB, 10);
        
        if (bsrSortState === 'asc') {
          return numA - numB;
        } else {
          return numB - numA;
        }
      });
    }
    
    tableBody.innerHTML = '';
    rows.forEach(row => tableBody.appendChild(row));
  }

  publishedSortButton.addEventListener('click', () => {
    if (publishedSortState === 'asc') {
      publishedSortState = 'desc';
      publishedAscending.style.display = 'none';
      publishedDescending.style.display = 'block';
    } else {
      publishedSortState = 'asc';
      publishedAscending.style.display = 'block';
      publishedDescending.style.display = 'none';
    }
    
    productSortState = null;
    asinSortState = null;

    productAscending.style.display = 'block';
    productDescending.style.display = 'none';
    asinAscending.style.display = 'block';
    asinDescending.style.display = 'none';

    sortRows();
  });

  productSortButton.addEventListener('click', () => {
    if (productSortState === 'asc') {
      productSortState = 'desc';
      productAscending.style.display = 'none';
      productDescending.style.display = 'block';
    } else {
      productSortState = 'asc';
      productAscending.style.display = 'block';
      productDescending.style.display = 'none';
    }

    publishedSortState = null;
    asinSortState = null;

    publishedAscending.style.display = 'block';
    publishedDescending.style.display = 'none';
    asinAscending.style.display = 'block';
    asinDescending.style.display = 'none';

    sortRows();
  });

  asinSortButton.addEventListener('click', () => {
    if (asinSortState === 'asc') {
      asinSortState = 'desc';
      asinAscending.style.display = 'none';
      asinDescending.style.display = 'block';
    } else {
      asinSortState = 'asc';
      asinAscending.style.display = 'block';
      asinDescending.style.display = 'none';
    }

    productSortState = null;
    publishedSortState = null;

    productAscending.style.display = 'block';
    productDescending.style.display = 'none';
    publishedAscending.style.display = 'block';
    publishedDescending.style.display = 'none';
    asinAscending.style.display = 'block';
    asinDescending.style.display = 'none';

    sortRows();
  });

  productHeader.appendChild(productSortButton);
  productSortButton.appendChild(productAscending);
  productSortButton.appendChild(productDescending);

  publishedHeader.appendChild(publishedSortButton);
  publishedSortButton.appendChild(publishedAscending);
  publishedSortButton.appendChild(publishedDescending);

  const bsrHeader = document.createElement('div');
  bsrHeader.style.cssText = `
    padding: 16px 16px 16px 0;
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 14px;
    color: #606F95;
    display: flex;
    align-items: center;
  `;

  const bsrSortButton = document.createElement('div');
  bsrSortButton.style.cssText = `
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
  `;
  bsrSortButton.textContent = 'BSR';
  const bsrAscending = document.createElement('img');
  bsrAscending.src = chrome.runtime.getURL('assets/Ascending.svg');
  bsrAscending.style.cssText = `
    width: 16px;
    height: 16px;
    display: block;
  `;
  const bsrDescending = document.createElement('img');
  bsrDescending.src = chrome.runtime.getURL('assets/Descending.svg');
  bsrDescending.style.cssText = `
    width: 16px;
    height: 16px;
    display: none;
  `;

  bsrSortButton.appendChild(bsrAscending);
  bsrSortButton.appendChild(bsrDescending);
  bsrHeader.appendChild(bsrSortButton);

  tableHeader.appendChild(checkboxHeader);     
  tableHeader.appendChild(marketplaceHeader);  
  tableHeader.appendChild(productHeader);      
  tableHeader.appendChild(publishedHeader);    
  tableHeader.appendChild(bsrHeader);          
  tableHeader.appendChild(asinHeader);        

  const tableBody = document.createElement('div');
  tableBody.style.cssText = `
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: calc(660px - 52px);
  `;

  table.appendChild(tableHeader);
  table.appendChild(tableBody);

  const listingsEmptyState = document.createElement('div');
  listingsEmptyState.style.cssText = `
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
  `;

  const emptyStateIcon = document.createElement('img');
  emptyStateIcon.src = chrome.runtime.getURL('assets/no-profile-img.svg');
  emptyStateIcon.style.cssText = `
    width: 80px;
    height: 91px;
  `;

  const emptyStateWrapper = document.createElement('div');
  emptyStateWrapper.style.cssText = `
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0;
  `;

  const emptyStateTitle = document.createElement('h3');
  emptyStateTitle.textContent = 'No listings added yet';
  emptyStateTitle.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 700;
    font-size: 16px;
    color: #606F95;
    margin: 0;
  `;

  const emptyStateText = document.createElement('p');
  emptyStateText.textContent = 'Drag listings from the Amazon search results into the\ncircle at the bottom-right to start reporting.';
  emptyStateText.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 400;
    font-size: 14px;
    color: #606F95;
    margin: 0;
    text-align: center;
    white-space: pre-line;
  `;

  const pasteButton = document.createElement('button');
  pasteButton.id = 'paste-btn';
  pasteButton.style.cssText = `
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #470CED;
    border: none;
    border-radius: 6px;
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 14px;
    color: white;
    cursor: pointer;
    margin-top: 24px;
    transition: background 0.2s ease;
  `;
  const pasteIcon = document.createElement('img');
  pasteIcon.src = chrome.runtime.getURL('assets/paste-asin-ic.svg');
  pasteIcon.style.cssText = `
    width: 16px;
    filter: brightness(0) invert(1);
  `;
  pasteButton.appendChild(pasteIcon);
  pasteButton.appendChild(document.createTextNode('Click to paste ASINs or (Ctrl/CMD+V)'));

  pasteButton.addEventListener('mouseenter', () => {
    pasteButton.style.background = '#3D0BCE';
  });
  
  pasteButton.addEventListener('mouseleave', () => {
    pasteButton.style.background = '#470CED';
  });

  pasteButton.addEventListener('click', async () => {
    if (isListingsTabActive) {
      handlePaste();
    }
  });

  emptyStateWrapper.appendChild(emptyStateTitle);
  emptyStateWrapper.appendChild(emptyStateText);

  listingsEmptyState.appendChild(emptyStateIcon);
  listingsEmptyState.appendChild(emptyStateWrapper);
  listingsEmptyState.appendChild(pasteButton);

  const historyEmptyState = document.createElement('div');
  historyEmptyState.style.cssText = `
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
  `;

  const historyEmptyIcon = document.createElement('img');
  historyEmptyIcon.src = chrome.runtime.getURL('assets/soon-img.svg');
  historyEmptyIcon.style.cssText = `
    width: 80px;
    height: 91px;
  `;

  const historyEmptyWrapper = document.createElement('div');
  historyEmptyWrapper.style.cssText = `
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0;
  `;

  const historyEmptyTitle = document.createElement('h3');
  historyEmptyTitle.textContent = 'Report History is coming soon.';
  historyEmptyTitle.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 700;
    font-size: 16px;
    color: #606F95;
    margin: 0;
  `;

  const historyEmptyText = document.createElement('p');
  historyEmptyText.textContent = "You'll be able to track the reported listings right here.";
  historyEmptyText.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 400;
    font-size: 14px;
    color: #606F95;
    margin: 0;
    text-align: center;
  `;

  historyEmptyWrapper.appendChild(historyEmptyTitle);
  historyEmptyWrapper.appendChild(historyEmptyText);

  historyEmptyState.appendChild(historyEmptyIcon);
  historyEmptyState.appendChild(historyEmptyWrapper);

  const listingsContent = document.createElement('div');
  listingsContent.className = 'listings-tab-content';
  listingsContent.style.cssText = `
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow-y: auto;
    flex: 1;
    width: 100%;
    height: 100%;
  `;

  const historyContent = document.createElement('div');
  historyContent.style.cssText = `
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow-y: auto;
    flex: 1;
    width: 100%;
    height: 100%;
  `;

  listingsContent.appendChild(listingsEmptyState);
  historyContent.appendChild(historyEmptyState);

  header.appendChild(closeButton);

  tabButtons.appendChild(listingsTab);
  tabButtons.appendChild(historyTab);

  tabBar.appendChild(tabButtons);
  tabBar.appendChild(actionsBar);

  snapHammerPopup.appendChild(header);
  snapHammerPopup.appendChild(tabBar);
  snapHammerPopup.appendChild(content);

  const tipContainer = document.createElement('div');
  tipContainer.className = 'tip-container';
  tipContainer.style.cssText = `
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin: 16px 40px;
  `;

  const automationTip = document.createElement('div');
  automationTip.className = 'automation-tip';
  automationTip.style.cssText = `
    display: inline-flex;
    align-items: center;
    background: rgba(0, 122, 255, 0.1);
    border-radius: 4px;
    padding: 4px 10px 4px 10px;
    margin-left: 0px;
    width: fit-content;
  `;

  const tipIcon = document.createElement('div');
  tipIcon.className = 'tip-icon';
  tipIcon.style.cssText = `
    width: 24px;
    height: 24px;
    min-width: 24px;
    min-height: 24px;
    background: #470CED;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    z-index: 2;
  `;

  const tipIconImg = document.createElement('img');
  tipIconImg.src = chrome.runtime.getURL('assets/apply.svg');
  tipIconImg.style.cssText = `
    width: 14px;
    height: 14px;
    filter: brightness(0) invert(1);
  `;
  tipIcon.appendChild(tipIconImg);

  const tipTextSpan = document.createElement('span');
  tipTextSpan.style.cssText = `
    font-family: "Amazon Ember";
    font-size: 12px;
    font-weight: 500;
    color: #470CED !important;
    line-height: 16px;
    position: relative;
    display: inline-block;
    padding: 0;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    box-sizing: border-box;
    transform: translateX(0);
    opacity: 1;
    transition: transform 0.3s cubic-bezier(0.0, 0, 0.2, 1), opacity 0.3s cubic-bezier(0.0, 0, 0.2, 1);
  `;

  function getMarketplaceDomain(marketplace) {
    const url = window.location.href;
    const amazonDomains = {
      'amazon.com': 'com',
      'amazon.co.uk': 'co.uk',
      'amazon.de': 'de',
      'amazon.fr': 'fr',
      'amazon.it': 'it',
      'amazon.es': 'es',
      'amazon.co.jp': 'co.jp'
    };

    for (const domain in amazonDomains) {
      if (url.includes(domain)) {
        return amazonDomains[domain];
      }
    }

    const domainMap = {
      'US': 'com',
      'UK': 'co.uk',
      'DE': 'de',
      'FR': 'fr',
      'IT': 'it',
      'ES': 'es',
      'JP': 'co.jp'
    };

    return domainMap[marketplace] || 'com'; 
  }

  const tipMessages = [
    `<strong>IMPORTANT*</strong> Sign in to amazon.${getMarketplaceDomain(getCurrentMarketplace())} first, then switch the website language to <strong>English (EN)</strong> before you start the reporting process.`
  ];

  tipTextSpan.innerHTML = tipMessages[0];

  document.addEventListener('marketplaceChanged', () => {
    tipTextSpan.innerHTML = `<strong>IMPORTANT*</strong> Sign in to amazon.${getMarketplaceDomain(getCurrentMarketplace())} first, then switch the website language to <strong>English (EN)</strong> before you start the reporting process.`;
  });

  let lastUrl = window.location.href;
  const observer = new MutationObserver(() => {
    if (lastUrl !== window.location.href) {
      lastUrl = window.location.href;
      tipTextSpan.innerHTML = `<strong>IMPORTANT*</strong> Sign in to amazon.${getMarketplaceDomain(getCurrentMarketplace())} first, then switch the website language to <strong>English (EN)</strong> before you start the reporting process.`;
    }
  });

  observer.observe(document, { subtree: true, childList: true });

  automationTip.appendChild(tipTextSpan);

  tipContainer.appendChild(tipIcon);
  tipContainer.appendChild(automationTip);

  snapHammerPopup.appendChild(tipContainer);

  const reportButton = document.createElement('button');
  reportButton.id = 'report-button'; 
  reportButton.style.cssText = `
    position: absolute;
    bottom: 40px;
    left: 40px;
    width: calc(100% - 80px);
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: #470CED;
    border: none;
    border-radius: 6px;
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 14px;
    color: white;
    cursor: pointer;
    pointer-events: auto;
    transition: all 0.2s ease;
    z-index: 10;
    padding: 0;
  `;
  reportButton.disabled = false;

  const applyIcon = document.createElement('img');
  applyIcon.src = chrome.runtime.getURL('assets/apply.svg');
  applyIcon.style.cssText = `
    width: 20px;
    height: 20px;
    filter: brightness(0) invert(1);
  `;

  reportButton.appendChild(applyIcon);
  reportButton.appendChild(document.createTextNode('Report selected listings'));

  reportButton.addEventListener('mouseenter', () => {
    if (!reportButton.disabled) {
      reportButton.style.background = '#2A00A0';
    }
  });

  reportButton.addEventListener('mouseleave', () => {
    if (!reportButton.disabled) {
      reportButton.style.background = '#470CED';
    }
  });

  reportButton.addEventListener('click', () => {
    if (!reportButton.disabled) {
      snapHammerPopup.style.display = 'none';
      validationSection.style.display = 'flex';
    }
  });

  snapHammerPopup.appendChild(reportButton); 

  document.body.appendChild(overlay);
  document.body.appendChild(snapHammerPopup);

  const confirmProductsSection = document.createElement('div');
  confirmProductsSection.id = 'confirmProductsSection';
  confirmProductsSection.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 994px;
    height: 96vh;
    background: #F7F8FA;
    border-radius: 28px;
    padding: 40px;
    z-index: 999999;
    display: none;
    flex-direction: column;
    gap: 32px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.1);
    overflow-y: auto;
    overflow-x: hidden;
    box-sizing: border-box;
    scrollbar-width: thin;
    scrollbar-color: #DCE0E5 transparent;
    scroll-behavior: smooth;
    overscroll-behavior: contain;
  `;

  const asinDiv = document.createElement('div');
  asinDiv.className = 'Asin-div';
  asinDiv.style.cssText = `
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
  `;

  const asinTitle = document.createElement('div');
  asinTitle.textContent = 'What is your ASIN?';
  asinTitle.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 700;
    font-size: 16px;
    color: #606F95;
    line-height: 1.197;
  `;

  const asinInput = document.createElement('input');
  asinInput.type = 'text';
  asinInput.placeholder = 'Enter your ASIN here';
  asinInput.id = 'originalasininput';
  asinInput.className = 'asin-input';
  asinInput.style.cssText = `
    width: 230px;
    height: 40px;
    padding: 13px 12px;
    background: #FFFFFF;
    border: 1.5px solid #DCE0E5;
    border-radius: 4px;
    font-family: 'Amazon Ember';
    font-size: 14px;
    line-height: 14px;
    color: #1F2937;
    transition: all 0.2s ease;
    outline: none;
    box-shadow: none;
    -webkit-appearance: none;
  `;

  asinInput.addEventListener('focus', () => {
    if (!asinInput.classList.contains('error')) {
      asinInput.style.borderColor = '#470CED';
      asinInput.style.borderWidth = '1.5px';
    }
  });

  asinInput.addEventListener('blur', () => {
    if (!asinInput.classList.contains('error')) {
      asinInput.style.borderColor = '#DCE0E5';
      asinInput.style.borderWidth = '1.5px';
    }
  });

  asinInput.setError = (isError) => {
    if (isError) {
      asinInput.classList.add('error');
      asinInput.style.borderColor = '#FF3920';
      asinInput.style.borderWidth = '1px';
    } else {
      asinInput.classList.remove('error');
      asinInput.style.borderColor = '#DCE0E5';
      asinInput.style.borderWidth = '1.5px';
    }
  };

  const style = document.createElement('style');
  style.textContent = `
    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus {
      -webkit-box-shadow: 0 0 0 30px white inset !important;
      -webkit-text-fill-color: #1F2937 !important;
      transition: background-color 5000s ease-in-out 0s;
    }
  `;
  document.head.appendChild(style);

  const backButton = document.createElement('button');
  backButton.id = 'back-btn';
  backButton.className = 'back-btn';
  backButton.style.cssText = `
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    width: 122px;
    height: 36px;
    background: #470CED;
    border: none;
    border-radius: 6px;
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 14px;
    color: #F8FAFC;
    cursor: pointer;
  `;

  const backIcon = document.createElement('img');
  backIcon.src = chrome.runtime.getURL('assets/back-ic.svg');
  backIcon.style.cssText = `
    width: 20px;
    height: 20px;
  `;

  const backText = document.createElement('span');
  backText.textContent = 'Back';
  backText.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 14px;
    color: #F8FAFC;
    line-height: 1.714;
  `;

  backButton.appendChild(backIcon);
  backButton.appendChild(backText);

  asinDiv.appendChild(asinTitle);

  asinDiv.appendChild(backButton);

  const issueCardsContainer = document.createElement('div');
  issueCardsContainer.className = 'issue-cards-container';
  issueCardsContainer.style.cssText = `
    display: flex;
    flex-direction: column;
    gap: 16px;
  `;

  const issueTitle = document.createElement('div');
  issueTitle.textContent = 'What best describes your issue?';
  issueTitle.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 700;
    font-size: 16px;
    color: #606F95;
    line-height: 1.197;
  `;

  const issueCardsWrapper = document.createElement('div');
  issueCardsWrapper.style.cssText = `
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    width: 914px;
  `;

  function createIssueCard(title, description, iconSrc, state = 'normal') {

    let normalIconSrc = iconSrc;
    let activeIconSrc = '';

    if (title === 'Stolen Artwork') {
      activeIconSrc = 'assets/stolen-artwork-active-img.svg';
    } else if (title === 'Copied Design') {
      activeIconSrc = 'assets/copied-design-active-img.svg';
    } else if (title === 'Copied Listing') {
      activeIconSrc = 'assets/copied-listing-active-img.svg';
    }

    const card = document.createElement('div');

    let specificClass = '';
    if (title === 'Stolen Artwork') {
      specificClass = 'stolen-artwork-card';
    } else if (title === 'Copied Design') {
      specificClass = 'copied-design-card';
    } else if (title === 'Copied Listing') {
      specificClass = 'copied-listing-card';
    }
    card.className = `issue-card ${state} ${specificClass}`.trim();
    card.style.cssText = `
      width: 882px;
      box-sizing: border-box;
      background: ${state === 'selected' ? '#FFFFFF' : state === 'hover' ? 'rgba(96, 111, 149, 0.01)' : '#FFFFFF'};
      border: ${state === 'selected' ? '1.5px solid #470CED' : state === 'hover' ? 'none' : '1.5px solid #E2E8F0'};
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
    `;

    const cardInner = document.createElement('div');
    cardInner.style.cssText = `
      display: flex;
      flex-direction: column;
      align-self: stretch;
      gap: 16px;
      padding: 24px;
    `;

    const topRow = document.createElement('div');
    topRow.style.cssText = `
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      gap: 16px;
      width: 100%;
    `;

    const leftContent = document.createElement('div');
    leftContent.style.cssText = `
      display: flex;
      align-items: flex-start;
      gap: 16px;
    `;

    const iconContainer = document.createElement('div');
    iconContainer.style.cssText = `
      width: 48px;
      height: 48px;
    `;

    const icon = document.createElement('img');
    icon.src = chrome.runtime.getURL(state === 'selected' ? activeIconSrc : normalIconSrc);
    icon.className = 'card-icon';
    icon.style.cssText = `
      width: 48px;
      height: 48px;
    `;

    icon.dataset.normalIcon = normalIconSrc;
    icon.dataset.activeIcon = activeIconSrc;

    const checkbox = document.createElement('img');
    checkbox.src = chrome.runtime.getURL('assets/ham-checkbox.svg');
    checkbox.style.cssText = `
      width: 24px;
      height: 24px;
      filter: ${state === 'selected' ? 'none' : 'brightness(0) saturate(100%) invert(93%) sepia(5%) saturate(851%) hue-rotate(178deg) brightness(83%) contrast(90%)'};
    `;

    iconContainer.appendChild(icon);
    leftContent.appendChild(iconContainer);
    topRow.appendChild(leftContent);
    topRow.appendChild(checkbox);

    const titleEl = document.createElement('div');
    titleEl.style.cssText = `
      font-family: 'Amazon Ember';
      font-weight: 700;
      font-size: 18px;
      color: ${state === 'selected' ? '#470CED' : '#606F95'};
      letter-spacing: -1.5%;
      line-height: 1.197;
      text-align: left;
    `;
    titleEl.textContent = title;

    const descriptionEl = document.createElement('div');
    descriptionEl.style.cssText = `
      font-family: 'Amazon Ember';
      font-weight: 500;
      font-size: 12px;
      color: ${state === 'selected' ? '#470CED' : '#606F95'};
      line-height: 1.666;
      text-align: left;
    `;
    descriptionEl.textContent = description;

    cardInner.appendChild(topRow);
    const textContainer = document.createElement('div');
    textContainer.style.cssText = `
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
      width: 100%;
    `;
    textContainer.appendChild(titleEl);
    textContainer.appendChild(descriptionEl);
    cardInner.appendChild(textContainer);
    card.appendChild(cardInner);

    card.addEventListener('mouseenter', () => {
      if (!card.classList.contains('selected')) {
        card.style.background = 'rgba(96, 111, 149, 0.05)';
      }
    });

    card.addEventListener('mouseleave', () => {
      if (!card.classList.contains('selected')) {
        card.style.background = '#FFFFFF';
      }
    });

    card.addEventListener('click', () => {

      document.querySelectorAll('.issue-card').forEach(otherCard => {

        otherCard.classList.remove('selected');
        otherCard.style.background = '#FFFFFF';
        otherCard.style.border = '1.5px solid #E2E8F0';

        const title = otherCard.querySelector('.issue-card > div > div:last-child > div:first-child');
        if (title) {
          title.style.color = '#606F95';
        }

        const description = otherCard.querySelector('.issue-card > div > div:last-child > div:last-child');
        if (description) {
          description.style.color = '#606F95';
        }

        const checkbox = otherCard.querySelector('.issue-card > div > div:first-child > img:last-child');
        if (checkbox) {
          checkbox.style.filter = 'brightness(0) saturate(100%) invert(93%) sepia(5%) saturate(851%) hue-rotate(178deg) brightness(83%) contrast(90%)';
        }

        const cardIcon = otherCard.querySelector('.card-icon');
        if (cardIcon && cardIcon.dataset.normalIcon) {
          cardIcon.src = chrome.runtime.getURL(cardIcon.dataset.normalIcon);
        }
      });

      card.classList.add('selected');
      card.style.background = '#FFFFFF';
      card.style.border = '1.5px solid #470CED';
      checkbox.style.filter = 'none';
      titleEl.style.color = '#470CED';
      descriptionEl.style.color = '#470CED';

      if (icon.dataset.activeIcon) {
        icon.src = chrome.runtime.getURL(icon.dataset.activeIcon);
      }

      window.selectedIssueType = title;
      console.log();

      formValidation.cardSelected = true;
      updateConfirmButtonState();
      saveFormData();
    });

    return card;
  }

  const stolenArtworkCard = createIssueCard(
    'Stolen Artwork',
    'Your exact artwork was re-uploaded',
    'assets/stolen-artwork-img.svg',
    'normal' 
  );

  const copiedDesignCard = createIssueCard(
    'Copied Design',
    'Your exact design layout was copied',
    'assets/copied-design-img.svg'
  );

  const copiedListingCard = createIssueCard(
    'Copied Listing',
    'Your exact product details were duplicated',
    'assets/copied-listing-img.svg'
  );

  issueCardsWrapper.appendChild(stolenArtworkCard);
  issueCardsWrapper.appendChild(copiedDesignCard);
  issueCardsWrapper.appendChild(copiedListingCard);

  issueCardsContainer.appendChild(issueTitle);
  issueCardsContainer.appendChild(issueCardsWrapper);

  const primaryContactContainer = document.createElement('div');
  primaryContactContainer.style.cssText = `
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 915px;
    height: fit-content;
    padding: 0;
  `;

  const titleSection = document.createElement('div');
  titleSection.style.cssText = `
    display: flex;
    flex-direction: column;
    gap: 2px;
    width: 215px;
    height: 37px;
  `;

  const titleHeading = document.createElement('div');
  titleHeading.textContent = 'Primary contact information';
  titleHeading.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 700;
    font-size: 16px;
    line-height: 1.197;
    color: #606F95;
    white-space: nowrap;
  `;

  const titleSubheading = document.createElement('div');
  titleSubheading.textContent = 'This is the contact Amazon will use.';
  titleSubheading.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 12px;
    line-height: 1.666;
    color: #606F95;
    white-space: nowrap;
  `;

  titleSection.appendChild(titleHeading);
  titleSection.appendChild(titleSubheading);

  const firstFormRow = document.createElement('div');
  firstFormRow.style.cssText = `
    display: flex;
    gap: 16px;
    width: 100%;
  `;

  const secondFormRow = document.createElement('div');
  secondFormRow.style.cssText = `
    display: flex;
    gap: 16px;
    width: 100%;
  `;

  const thirdFormRow = document.createElement('div');
  thirdFormRow.style.cssText = `
    display: flex;
    gap: 16px;
    width: 100%;
  `;

  firstFormRow.appendChild(createFormField('First Name', 'Enter your first name'));
  firstFormRow.appendChild(createFormField('Last Name', 'Enter your last name'));
  firstFormRow.appendChild(createFormField('Company', 'Enter company name', true));
  firstFormRow.appendChild(createFormField('Address Line 1', 'Enter address'));

  secondFormRow.appendChild(createFormField('Address Line 2', 'Enter address', true));
  secondFormRow.appendChild(createFormField('Country/Region', 'Country/Region', false, true));
  secondFormRow.appendChild(createFormField('State', 'Enter state', true));
  secondFormRow.appendChild(createFormField('City', 'Enter city'));

  thirdFormRow.appendChild(createFormField('ZIP Code', 'Enter ZIP code'));
  thirdFormRow.appendChild(createFormField('Phone Number', 'Enter phone number'));

  primaryContactContainer.appendChild(titleSection);
  primaryContactContainer.appendChild(firstFormRow);
  primaryContactContainer.appendChild(secondFormRow);
  primaryContactContainer.appendChild(thirdFormRow);

  thirdFormRow.style.marginBottom = '0';

  function createFormField(label, placeholder, isOptional = false, hasDropdown = false) {
    const container = document.createElement('div');
    container.style.cssText = `
      display: flex;
      flex-direction: column;
      width: 216.5px;
    `;

    let divId = '';
    let inputId = '';

    if (label === 'First Name') {
      divId = 'first-name-div';
      inputId = 'first-name-input';
    } else if (label === 'Last Name') {
      divId = 'last-name-div';
      inputId = 'last-name-input';
    } else if (label === 'Company') {
      divId = 'company-div';
      inputId = 'company-input';
    } else if (label === 'Address Line 1') {
      divId = 'address-1-div';
      inputId = 'address-1-input';
    } else if (label === 'Address Line 2') {
      divId = 'address-2-div';
      inputId = 'address-2-input';
    } else if (label === 'Country/Region') {
      divId = 'country-dropdown-div';
      inputId = 'country-dropdown';
    } else if (label === 'State') {
      divId = 'state-div';
      inputId = 'state-input';
    } else if (label === 'City') {
      divId = 'city-div';
      inputId = 'city-input';
    } else if (label === 'ZIP Code') {
      divId = 'zipcode-div';
      inputId = 'zip-code-input';
    } else if (label === 'Phone Number') {
      divId = 'phone-number-div';
      inputId = 'phone-number-input';
    } else if (label === 'Contact Name') {
      divId = 'contact-name-div';
      inputId = 'contact-name-input';
    } else if (label === 'Contact E-mail') {
      divId = 'contact-email-div';
      inputId = 'contact-email-input';
    }

    container.id = divId;
    container.className = divId;

    const labelContainer = document.createElement('div');
    labelContainer.style.cssText = `
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 20px;
      margin-bottom: 8px;
    `;

    const labelText = document.createElement('div');
    labelText.textContent = label;
    labelText.style.cssText = `
      font-family: 'Amazon Ember';
      font-weight: 500;
      font-size: 14px;
      line-height: 10px;
      color: #606F95;
    `;

    labelContainer.appendChild(labelText);

    if (isOptional) {
      const optionalBadge = document.createElement('div');
      optionalBadge.textContent = 'Optional';
      optionalBadge.style.cssText = `
        padding: 0.5px 8.5px;
        background: rgba(0, 122, 255, 0.1);
        border-radius: 2px;
        font-family: 'Amazon Ember';
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        color: #007AFF;
        margin-left: auto;
      `;
      labelContainer.appendChild(optionalBadge);
    }

    if (hasDropdown && label === 'Country/Region') {

      const dropdownContainer = document.createElement('div');
      dropdownContainer.className = 'select-dropdown snap-profile-dropdown';
      dropdownContainer.id = inputId;
      dropdownContainer.style.cssText = `
        position: relative;
        min-width: 216px;
        width: 216px;
        cursor: pointer;
        user-select: none;
      `;

      const dropdownHeader = document.createElement('div');
      dropdownHeader.className = 'dropdown-header';
      dropdownHeader.style.cssText = `
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 0;
        height: 40px;
        border: 1.5px solid #DCE0E5;
        border-radius: 4px;
        background: white;
        box-sizing: border-box;
        transition: border-color 0.2s ease;
      `;

      const headerText = document.createElement('span');
      headerText.textContent = placeholder;
      headerText.style.cssText = `
        padding-left: 12px;
        font-family: 'Amazon Ember';
        font-size: 13px;
        color: #1F2937;
      `;

      const dropdownIcon = document.createElement('img');
      dropdownIcon.src = chrome.runtime.getURL('assets/dropdown-ic.svg');
      dropdownIcon.style.cssText = `
        margin-right: 12px;
      `;

      dropdownHeader.appendChild(headerText);
      dropdownHeader.appendChild(dropdownIcon);

      const dropdownMenu = document.createElement('div');
      dropdownMenu.className = 'dropdown-menu';

      dropdownMenu.style.cssText = `
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: white;
        border: 1px solid #DCE0E5;
        border-radius: 4px;
        margin-top: 4px;
        z-index: 1000;
        display: none;
      `;

      const searchContainer = document.createElement('div');
      searchContainer.className = 'search-container';
      searchContainer.style.cssText = `
        display: flex;
        align-items: center;
        padding: 8px 12px;
        border-bottom: 1px solid #E9EBEF;
      `;

      const searchIcon = document.createElement('div');
      searchIcon.className = 'search-icon';
      searchIcon.style.cssText = `
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        margin-right: 8px;
      `;

      searchIcon.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M7.33333 12.6667C10.2789 12.6667 12.6667 10.2789 12.6667 7.33333C12.6667 4.38781 10.2789 2 7.33333 2C4.38781 2 2 4.38781 2 7.33333C2 10.2789 4.38781 12.6667 7.33333 12.6667Z" stroke="#470CED" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M14 14L11.1 11.1" stroke="#470CED" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      `;

      const searchInput = document.createElement('input');
      searchInput.type = 'text';
      searchInput.className = 'search-input';
      searchInput.placeholder = 'Search countries...';
      searchInput.style.cssText = `
        width: 100%;
        border: none;
        outline: none;
        font-size: 13px;
        color: #1F2937;
        padding: 0;
        background: transparent;
        font-family: 'Amazon Ember';
        box-shadow: none;
      `;

      searchContainer.appendChild(searchIcon);
      searchContainer.appendChild(searchInput);

      const dropdownList = document.createElement('div');
      dropdownList.className = 'dropdown-list';
      dropdownList.style.cssText = `
        max-height: 200px;
        overflow-y: auto;
      `;

      function renderCountries(searchTerm = '') {
        dropdownList.innerHTML = '';
        const filteredCountries = countries.filter(country => 
          country.toLowerCase().includes(searchTerm.toLowerCase())
        );

        filteredCountries.forEach(country => {
          const item = document.createElement('div');
          item.className = 'dropdown-item';
          item.textContent = country;
          item.style.cssText = `
            padding: 8px 12px;
            cursor: pointer;
            font-family: 'Amazon Ember';
            font-size: 13px;
            color: #1F2937;
          `;

          if (country === headerText.textContent) {
            item.style.fontWeight = '700';
            item.style.color = '#470CED';
          }

          item.addEventListener('mouseover', () => {
            item.style.backgroundColor = '#F3F4F6';
          });

          item.addEventListener('mouseout', () => {
            item.style.backgroundColor = 'transparent';
          });

          item.addEventListener('click', () => {
            headerText.textContent = country;
            dropdownMenu.style.display = 'none'; 
            dropdownContainer.classList.remove('focused');
            dropdownHeader.style.borderColor = '#DCE0E5';
            dropdownHeader.style.borderWidth = '1.5px';

            renderCountries(searchInput.value);
          });

          dropdownList.appendChild(item);
        });
      }

      searchInput.addEventListener('input', (e) => {
        renderCountries(e.target.value);
      });

      dropdownHeader.addEventListener('click', () => {
        const isHidden = dropdownMenu.style.display === 'none';
        dropdownMenu.style.display = isHidden ? 'block' : 'none';
        dropdownContainer.classList.toggle('focused');
        if (isHidden) {
          dropdownHeader.style.borderColor = '#470CED';
          dropdownHeader.style.borderWidth = '1.5px';
        } else {
          dropdownHeader.style.borderColor = '#DCE0E5';
          dropdownHeader.style.borderWidth = '1.5px';
          searchInput.value = '';
          renderCountries();
        }
      });

      document.addEventListener('click', (e) => {
        if (!dropdownContainer.contains(e.target) && !dropdownHeader.contains(e.target)) {
          dropdownMenu.style.display = 'none';
          dropdownContainer.classList.remove('focused');
          dropdownHeader.style.borderColor = '#DCE0E5';
          dropdownHeader.style.borderWidth = '1.5px';
        }
      });

      window.addEventListener('focus', () => {
        setTimeout(() => {
          if (dropdownContainer.classList.contains('focused') && !dropdownMenu.contains(document.activeElement)) {
            dropdownMenu.style.display = 'none';
            dropdownContainer.classList.remove('focused');
            dropdownHeader.style.borderColor = '#DCE0E5';
            dropdownHeader.style.borderWidth = '1.5px';
          }
        }, 500);
      });

      document.addEventListener('focusout', (e) => {
        if (!dropdownContainer.contains(e.relatedTarget)) {
          dropdownMenu.style.display = 'none';
          dropdownContainer.classList.remove('focused');
          dropdownHeader.style.borderColor = '#DCE0E5';
          dropdownHeader.style.borderWidth = '1.5px';
        }
      });

      dropdownMenu.style.display = 'none';
      dropdownContainer.classList.remove('focused');

      dropdownMenu.appendChild(searchContainer);
      dropdownMenu.appendChild(dropdownList);
      dropdownContainer.appendChild(dropdownHeader);
      dropdownContainer.appendChild(dropdownMenu);

      renderCountries();

      container.appendChild(labelContainer);
      container.appendChild(dropdownContainer);
      return container;
    }

    const input = document.createElement('input');
    input.type = 'text';
    input.placeholder = placeholder;
    input.id = inputId;
    input.className = inputId;
    input.style.cssText = `
      height: 40px;
      padding: 0 12px;
      border: 1.5px solid #DCE0E5;
      border-radius: 4px;
      font-family: 'Amazon Ember';
      font-size: 13px;
      color: #1F2937;
      box-sizing: border-box;
      outline: none;
      transition: border-color 0.2s ease;
      -webkit-appearance: none;
      box-shadow: none;
    `;

    input.addEventListener('focus', () => {
      if (!input.classList.contains('error')) {
        input.style.borderColor = '#470CED';
        input.style.borderWidth = '1.5px';
      }
    });

    input.addEventListener('blur', () => {
      if (!input.classList.contains('error')) {
        input.style.borderColor = '#DCE0E5';
        input.style.borderWidth = '1.5px';
      }
    });

    input.setError = (isError) => {
      if (isError) {
        input.classList.add('error');
        input.style.borderColor = '#FF3920';
        input.style.borderWidth = '1px';
      } else {
        input.classList.remove('error');
        input.style.borderColor = '#DCE0E5';
        input.style.borderWidth = '1.5px';
      }
    };

    container.appendChild(labelContainer);
    container.appendChild(input);
    return container;
  }

  const secondaryContactContainer = document.createElement('div');
  secondaryContactContainer.style.cssText = `
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 100%;
  `;

  const secondaryContactTitle = document.createElement('div');
  secondaryContactTitle.style.cssText = `
    display: flex;
    flex-direction: column;
    gap: 2px;
    width: 100%;
  `;

  const secondaryContactHeading = document.createElement('div');
  secondaryContactHeading.textContent = 'Secondary contact information';
  secondaryContactHeading.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 700;
    font-size: 16px;
    line-height: 1.197;
    color: #606F95;
    width: 100%;
  `;

  const secondaryContactSubheading = document.createElement('div');
  secondaryContactSubheading.textContent = 'This contact information will be shared with the party you\'re reporting.';
  secondaryContactSubheading.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 12px;
    line-height: 1.666;
    color: #606F95;
    width: 100%;
  `;

  secondaryContactTitle.appendChild(secondaryContactHeading);
  secondaryContactTitle.appendChild(secondaryContactSubheading);

  const secondaryContactForm = document.createElement('div');
  secondaryContactForm.style.cssText = `
    display: flex;
    gap: 16px;
    width: 100%;
    height: 66px;
  `;

  secondaryContactForm.appendChild(createFormField('Contact Name', 'Enter contact name'));
  secondaryContactForm.appendChild(createFormField('Contact E-mail', 'Enter contact e-mail'));

  secondaryContactContainer.appendChild(secondaryContactTitle);
  secondaryContactContainer.appendChild(secondaryContactForm);

  const confirmButtonContainer = document.createElement('div');
  confirmButtonContainer.className = 'confirm-report-btn';
  confirmButtonContainer.style.cssText = `
    position: static;
    width: 100%;
    margin-top: 24px;
    z-index: 10;
  `;

  const confirmButton = document.createElement('button');
  confirmButton.style.cssText = `
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 0;
    background: #470CED;
    border: none;
    border-radius: 6px;
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 14px;
    color: white;
    line-height: 1;
    cursor: pointer;
    height: 48px;
    width: 100%;
    transition: all 0.2s ease;
  `;

  const confirmIcon = document.createElement('img');
  confirmIcon.src = chrome.runtime.getURL('assets/apply.svg');
  confirmIcon.style.cssText = `
    width: 20px;
    height: 20px;
    filter: brightness(0) invert(1);
  `;

  confirmButton.appendChild(confirmIcon);
  confirmButton.appendChild(document.createTextNode('Confirm and start reporting'));

  confirmButton.addEventListener('mouseenter', () => {
    confirmButton.style.background = '#2A00A0';
  });

  confirmButton.addEventListener('mouseleave', () => {
    confirmButton.style.background = '#470CED';
  });

  confirmButton.addEventListener('mouseleave', () => {
    confirmButton.style.background = '#470CED';
  });

  confirmButton.addEventListener('click', async () => {
    try {

      const selectedCard = document.querySelector('.issue-card.selected');
      const selectedIssueType = selectedCard ? selectedCard.querySelector('div > div:last-child > div:first-child').textContent : 'Stolen Artwork';

      const formData = {
        ReportedASINs: Array.from(selectedCheckboxes).map(checkbox => {
          const row = checkbox.closest('div[style*="grid"]');
          return row.querySelector('div:nth-child(6)').textContent.trim();
        }).join(', '),
        IssueSelected: window.selectedIssueType || 'Stolen Artwork', 
        originalAsin: asinInput.value.trim(),
        originalAsinDate: await chrome.storage.local.get('originalAsinData').then(result => {
          if (result.originalAsinData) {
            const data = JSON.parse(result.originalAsinData);
            return data.date || 'N/A';
          }
          return 'N/A';
        }),
        BrandName: await chrome.storage.local.get('originalAsinData').then(result => {
          if (result.originalAsinData) {
            const data = JSON.parse(result.originalAsinData);
            return data.brand || 'N/A';
          }
          return 'N/A';
        }),
        PrimaryInfo: {
          firstName: document.querySelector('#first-name-input').value,
          lastName: document.querySelector('#last-name-input').value,
          company: document.querySelector('#company-input').value || '',
          addressLine1: document.querySelector('#address-1-input').value,
          addressLine2: document.querySelector('#address-2-input').value || '',
          city: document.querySelector('#city-input').value,
          state: document.querySelector('#state-input').value || '',
          zipCode: document.querySelector('#zip-code-input').value,
          country: document.querySelector('.dropdown-header span').textContent,
          phoneNumber: document.querySelector('#phone-number-input').value
        },
        SecInfo: {
          contactName: document.querySelector('#contact-name-input').value,
          contactEmail: document.querySelector('#contact-email-input').value
        }
      };

      console.log();

      if (window.handleReport) {
        await window.handleReport(formData);

        chrome.notifications.create({
          type: 'basic',
          iconUrl: chrome.runtime.getURL('assets/hammer-ic.svg'),
          title: 'Report Automation Started',
          message: 'The report automation has been launched successfully.'
        });

        hidePopup();

        selectedCheckboxes.clear();

        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
          checkbox.checked = false;
          checkbox.style.backgroundColor = 'white';
          checkbox.style.borderColor = '#E4E4E7';
          checkbox.style.backgroundImage = 'none';
        });

        const headerCheckbox = document.querySelector('.header-div input[type="checkbox"]');
        if (headerCheckbox) {
          headerCheckbox.checked = false;
          headerCheckbox.style.backgroundColor = 'white';
          headerCheckbox.style.borderColor = '#E4E4E7';
          headerCheckbox.style.backgroundImage = 'none';
        }

        deleteButton.style.opacity = '0.5';
        deleteButton.style.cursor = 'not-allowed';
        deleteButton.style.pointerEvents = 'none';
        deleteButton.disabled = true;

        downloadButton.style.opacity = '0.5';
        downloadButton.style.cursor = 'not-allowed';
        downloadButton.style.pointerEvents = 'none';
        downloadButton.disabled = true;
      } else {
        console.error('');
        chrome.notifications.create({
          type: 'basic',
          iconUrl: chrome.runtime.getURL('assets/hammer-ic.svg'),
          title: 'Error',
          message: 'Could not start report automation. Please try again.'
        });
      }
    } catch (error) {
      console.error();
      chrome.notifications.create({
        type: 'basic',
        iconUrl: chrome.runtime.getURL('assets/hammer-ic.svg'),
        title: 'Error',
        message: 'An error occurred while starting the report automation. Please try again.'
      });
    }
  });

  confirmButtonContainer.appendChild(confirmButton);

  const topDiv = document.createElement('div');
  topDiv.className = 'confirm-top';
  topDiv.style.cssText = `
    width: 100%;
    height: 36px;
    margin-bottom: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  `;

  const leftDiv = document.createElement('div');
  leftDiv.className = 'left-div';
  leftDiv.style.cssText = `
    display: flex;
    align-items: center;
    height: 100%;
  `;

  leftDiv.appendChild(backButton);

  const rightDiv = document.createElement('div');
  rightDiv.className = 'right-div';
  rightDiv.style.cssText = `
    display: flex;
    align-items: center;
    gap: 24px;
    height: 100%;
  `;

  const confirmStepper = document.createElement('div');
  confirmStepper.style.cssText = `
    display: flex;
    align-items: center;
    gap: 16px;
  `;

  const confirmSteps = [
    { number: '01', text: 'Add Infringing ASINs', state: 'completed' },
    { number: '02', text: 'Validate your ASIN', state: 'completed' },
    { number: '03', text: 'Confirm & Report', state: 'active' }
  ];

  confirmSteps.forEach((step, index) => {
    const stepContainer = document.createElement('div');
    stepContainer.style.cssText = `
      display: flex;
      align-items: center;
      gap: 8px;
    `;

    if (step.state === 'completed') {
      const checkIcon = document.createElement('img');
      checkIcon.src = chrome.runtime.getURL('assets/loaded.svg');
      checkIcon.style.cssText = `
        width: 24px;
        height: 24px;
      `;
      stepContainer.appendChild(checkIcon);
    } else {
      const numberCircle = document.createElement('div');
      numberCircle.style.cssText = `
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'Amazon Ember';
        font-weight: 500;
        font-size: 12px;
        background: ${step.state === 'active' ? '#470CED' : '#E9EBF2'};
        color: ${step.state === 'active' ? 'white' : '#606F95'};
      `;
      numberCircle.textContent = step.number;
      stepContainer.appendChild(numberCircle);
    }

    const stepText = document.createElement('span');
    stepText.style.cssText = `
      font-family: 'Amazon Ember';
      font-weight: 500;
      font-size: 14px;
      color: ${step.state === 'completed' ? '#0D0B26' : 
              step.state === 'active' ? '#470CED' : 
              '#606F95'};
    `;
    stepText.textContent = step.text;
    stepContainer.appendChild(stepText);

    if (index < confirmSteps.length - 1) {
      const separator = document.createElement('div');
      separator.style.cssText = `
        width: 24px;
        height: 1px;
        background: ${step.state === 'completed' ? '#470CED' : '#E9EBF2'};
        margin: 0 4px;
      `;
      stepContainer.appendChild(separator);
    }

    confirmStepper.appendChild(stepContainer);
  });

  const confirmCloseButton = document.createElement('button');
  confirmCloseButton.style.cssText = `
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  `;
  const confirmCloseIcon = document.createElement('img');
  confirmCloseIcon.src = chrome.runtime.getURL('assets/close-ham-popup-ic.svg');
  confirmCloseIcon.style.cssText = `
    width: 24px;
    height: 24px;
  `;
  confirmCloseButton.appendChild(confirmCloseIcon);
  confirmCloseButton.addEventListener('click', () => {
      confirmProductsSection.style.display = 'none';
      overlay.style.display = 'none';
      document.body.style.overflow = '';
  });
  rightDiv.appendChild(confirmStepper);
  rightDiv.appendChild(confirmCloseButton);

  topDiv.appendChild(leftDiv);
  topDiv.appendChild(rightDiv);

  confirmProductsSection.appendChild(topDiv);
  confirmProductsSection.appendChild(issueCardsContainer);
  confirmProductsSection.appendChild(primaryContactContainer);
  confirmProductsSection.appendChild(secondaryContactContainer);
  confirmProductsSection.appendChild(confirmButtonContainer);

  document.body.appendChild(confirmProductsSection);

  backButton.addEventListener('click', () => {
    confirmProductsSection.style.display = 'none';
    validationSection.style.display = 'flex'; 
  });

  function showPopup() {
    overlay.style.display = 'block';
    snapHammerPopup.style.display = 'flex';
    document.body.style.overflow = 'hidden';
  }

  function hidePopup() {
    overlay.style.display = 'none';
    snapHammerPopup.style.display = 'none';
    confirmProductsSection.style.display = 'none';
    validationSection.style.display = 'none';
    document.body.style.overflow = '';
    
    if (validationInput) {
      validationInput.value = '';
      validateButton.style.background = '#CFD4D4';
      validateButton.style.opacity = '1';
      validateButton.style.cursor = 'not-allowed';
      validateButton.style.pointerEvents = 'none';
      validateButton.style.color = 'rgba(0, 0, 0, 0.5)';
      validateButton.disabled = true;
      validateIcon.style.filter = 'brightness(0)';
      validateIcon.style.opacity = '0.5';
    }
  }

  function showConfirmProductsSection() {
    const confirmButton = confirmProductsSection.querySelector('.confirmBtn');
    if (confirmButton) {
      confirmButton.style.position = 'static';
      confirmButton.style.bottom = 'auto';
      confirmButton.style.left = 'auto';
      confirmButton.style.transform = 'none';
      confirmButton.style.margin = '20px 0 0 auto';
      confirmButton.style.width = 'auto';
    }
    
    snapHammerPopup.style.display = 'none';
    confirmProductsSection.style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    loadFormData();
    
    if (asinInput) {
      asinInput.value = '';
      formValidation.asin = false;
    }
    
    setTimeout(() => {
      updateConfirmButtonState();
      verifyButtonDisabledState(confirmButton);
    }, 100);
  }

  snapHammer.addEventListener('click', showPopup);
  closeButton.addEventListener('click', hidePopup);
  overlay.addEventListener('click', hidePopup);

  const reportedAsins = new Set();
  const productTitles = new Map();
  const asinMarketplaces = new Map();
  const productBSRs = new Map();
  const productPublishDates = new Map(); 
  const originalAsinData = new Map(); 

  chrome.storage.local.get(['reportedAsins', 'productTitles', 'asinMarketplaces', 'productBSRs', 'productPublishDates'], (result) => {
    if (result.reportedAsins) {
      const savedAsins = JSON.parse(result.reportedAsins);
      savedAsins.forEach(asin => reportedAsins.add(asin));
    }
    if (result.productTitles) {
      const savedTitles = JSON.parse(result.productTitles);
      Object.entries(savedTitles).forEach(([asin, title]) => {
        productTitles.set(asin, title);
      });
    }
    if (result.asinMarketplaces) {
      const savedMarketplaces = JSON.parse(result.asinMarketplaces);
      Object.entries(savedMarketplaces).forEach(([asin, marketplace]) => {
        asinMarketplaces.set(asin, marketplace);
      });
    }
    if (result.productBSRs) {
      const savedBSRs = JSON.parse(result.productBSRs);
      Object.entries(savedBSRs).forEach(([asin, bsr]) => {
        productBSRs.set(asin, bsr);
      });
    }
    if (result.productPublishDates) {
      const savedDates = JSON.parse(result.productPublishDates);
      Object.entries(savedDates).forEach(([asin, date]) => {
        productPublishDates.set(asin, date);
      });
    }
    
    const currentMarketplace = getCurrentMarketplace();
    const marketplaceCount = Array.from(reportedAsins).filter(asin => 
      asinMarketplaces.get(asin) === currentMarketplace
    ).length;
    counter.textContent = marketplaceCount;
    counter.style.backgroundColor = marketplaceCount === 0 ? '#FA583A' : '#01BB87';
    asinCounter.textContent = `${marketplaceCount}/50`;
    updateTableView();
  });

  function saveAsins() {
    chrome.storage.local.set({
      reportedAsins: JSON.stringify(Array.from(reportedAsins)),
      productTitles: JSON.stringify(Object.fromEntries(productTitles)),
      asinMarketplaces: JSON.stringify(Object.fromEntries(asinMarketplaces)),
      productBSRs: JSON.stringify(Object.fromEntries(productBSRs)),
      productPublishDates: JSON.stringify(Object.fromEntries(productPublishDates))
    });
  }

  const MARKETPLACE_DOMAINS = {
    'US': 'amazon.com',
    'UK': 'amazon.co.uk',
    'DE': 'amazon.de',
    'FR': 'amazon.fr',
    'IT': 'amazon.it',
    'ES': 'amazon.es',
    'JP': 'amazon.co.jp',
  };

  const MARKETPLACE_FLAGS = {
    'US': 'assets/us.svg',
    'UK': 'assets/uk.svg',
    'DE': 'assets/de.svg',
    'FR': 'assets/fr.svg',
    'IT': 'assets/it.svg',
    'ES': 'assets/es.svg',
    'JP': 'assets/jp.svg'
  };

  function getMarketplaceFromUrl(url) {
    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname.toLowerCase();
      
      return Object.entries(MARKETPLACE_DOMAINS).find(([code, marketplaceDomain]) => 
        domain.includes(marketplaceDomain.toLowerCase())
      )?.[0] || 'US';
    } catch (e) {
      console.error();
      return 'US';
    }
  }

  async function verifyAsinInMarketplaces(asin) {
    const results = await Promise.all(
      Object.keys(MARKETPLACE_DOMAINS).map(marketplace => 
        fetchProductTitle(asin, marketplace)
      )
    );
    
    const validResult = results.find(r => r.isValid) || results[0];
    return validResult;
  }

  const BSR_CONFIG = {
    'US': {
      rankText: ['Best Sellers Rank', 'Amazon Best Sellers Rank'],
      selectors: [
        '#detailBulletsWrapper_feature_div',
        '#detailBullets_feature_div',
        '#SalesRank',
        '#productDetails_detailBullets_sections1'
      ]
    },
    'UK': {
      rankText: ['Best Sellers Rank'],
      selectors: [
        '#detailBulletsWrapper_feature_div',
        '#detailBullets_feature_div'
      ]
    },
    'DE': {
      rankText: ['Best Sellers Rank', 'Bestseller-Rang'],
      selectors: [
        '#detailBulletsWrapper_feature_div',
        '#detailBullets_feature_div'
      ]
    },
    'FR': {
      rankText: ['Best Sellers Rank', 'Classement des meilleures ventes d\'Amazon'],
      selectors: [
        '#detailBulletsWrapper_feature_div',
        '#detailBullets_feature_div'
      ]
    },
    'IT': {
      rankText: ['Best Sellers Rank', 'Posizione nella classifica Bestseller di Amazon'],
      selectors: [
        '#detailBulletsWrapper_feature_div',
        '#detailBullets_feature_div'
      ]
    },
    'ES': {
      rankText: ['Best Sellers Rank', 'Clasificación en los más vendidos de Amazon'],
      selectors: [
        '#detailBulletsWrapper_feature_div',
        '#detailBullets_feature_div'
      ]
    },
    'JP': {
      rankText: ['Amazon Bestseller', 'ベストセラー順位'],
      selectors: [
        '#detailBulletsWrapper_feature_div',
        '#detailBullets_feature_div'
      ]
    }
  };

  function cleanBSRForStorage(bsr) {
    if (!bsr || bsr === 'N/A' || bsr === 'Loading...') return bsr;
    return bsr.replace(/[#,]/g, '');
  }

  function formatBSRForDisplay(bsr) {
    if (!bsr || bsr === 'N/A' || bsr === 'Loading...') return bsr;
    const cleanNumber = bsr.toString().replace(/[#,]/g, '');
    const number = parseInt(cleanNumber, 10);
    if (isNaN(number)) return 'N/A';
    return `#${number.toLocaleString()}`;
  }

  async function fetchProductTitle(asin, marketplace = 'US') {
    const asinMarketplace = asinMarketplaces.get(asin) || marketplace;
    const domain = MARKETPLACE_DOMAINS[asinMarketplace];

    if (productTitles.has(asin) && productBSRs.has(asin) && productPublishDates.has(asin)) {
      return {
        title: productTitles.get(asin),
        bsr: productBSRs.get(asin),
        published: productPublishDates.get(asin),
        isValid: true,
        marketplace: asinMarketplace,
        brand: 'N/A'
      };
    }

    if (!domain) {
      return {
        title: 'Listing not found in this marketplace',
        bsr: 'N/A',
        published: 'N/A',
        isValid: false,
        marketplace: asinMarketplace,
        brand: 'N/A'
      };
    }

    try {
      const response = await fetch(`https://www.${domain}/dp/${asin}`);
      
      if (!response.ok) {
        return {
          title: 'Listing not found in this marketplace',
          bsr: 'N/A',
          published: 'N/A',
          isValid: false,
          marketplace: asinMarketplace,
          brand: 'N/A'
        };
      }

      const text = await response.text();
      const parser = new DOMParser();
      const doc = parser.parseFromString(text, 'text/html');
      const titleElement = doc.querySelector('#productTitle');
      const title = titleElement ? titleElement.textContent.trim() : 'Listing title not found';
      
      let bsr = 'N/A';
      const marketplaceConfig = BSR_CONFIG[asinMarketplace] || BSR_CONFIG['US'];
      
      for (const selector of marketplaceConfig.selectors) {
        const element = doc.querySelector(selector);
        if (!element) continue;
        
        const content = element.textContent;
        for (const rankText of marketplaceConfig.rankText) {
          if (content.includes(rankText)) {
            const rankMatch = content.split(rankText)[1]?.match(/#?([0-9,]+)/);
            if (rankMatch) {
              const cleanNumber = cleanBSRForStorage(rankMatch[0]);
              if (cleanNumber) {
                bsr = cleanNumber;
                break;
              }
            }
          }
        }
        if (bsr !== 'N/A') break;
      }
      
      let published = 'N/A';
      const detailBullets = doc.querySelector('#detailBullets_feature_div');
      
      if (detailBullets) {
        const listItems = detailBullets.querySelectorAll('li');
        for (const item of listItems) {
          const itemText = item.textContent.trim();
          if (itemText.includes('Date First Available')) {
            const dateText = itemText.split(':')[1]?.trim();
            if (dateText) {
              try {
                let date;
                if (/[A-Za-z]+ \d{1,2},? \d{4}/.test(dateText)) {
                  date = new Date(dateText);
                } 
                else if (/\d{1,2} [A-Za-z]+ \d{4}/.test(dateText)) {
                  const [day, month, year] = dateText.match(/(\d{1,2}) ([A-Za-z]+) (\d{4})/).slice(1);
                  date = new Date(`${month} ${day}, ${year}`);
                }
                else if (/\d{1,2} [A-Za-z]+\. \d{4}/.test(dateText)) {
                  const [day, monthAbbr, year] = dateText.match(/(\d{1,2}) ([A-Za-z]+)\. (\d{4})/).slice(1);
                  const month = monthAbbr.replace('.', '');
                  date = new Date(`${month} ${day}, ${year}`);
                }
                else {
                  date = new Date(dateText);
                }

                if (!isNaN(date.getTime())) {
                  published = date.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                  });
                }
              } catch (e) {
                console.error();
                published = 'N/A';
              }
            }
            break;
          }
        }
      }

      if (titleElement) {
        productTitles.set(asin, title);
        if (bsr !== 'N/A') {
          productBSRs.set(asin, bsr);
        }
        if (published !== 'N/A') {
          productPublishDates.set(asin, published);
        }
        
        saveAsins();
      }
      
      return {
        title,
        bsr,
        published,
        isValid: !!titleElement,
        marketplace: asinMarketplace,
        brand: 'N/A'
      };
    } catch (error) {
      console.error();
      return {
        title: 'Error fetching listing',
        bsr: 'N/A',
        published: 'N/A',
        isValid: false,
        marketplace: asinMarketplace,
        brand: 'N/A'
      };
    }
  }

  function extractAsin(url) {
    const asinPattern = /\/dp\/([A-Z0-9]{10})/;
    const match = url.match(asinPattern);
    if (match) return match[1];

    const sponsoredPattern = /[?&]pd_rd_i=([A-Z0-9]{10})/;
    const sponsoredMatch = url.match(sponsoredPattern);
    if (sponsoredMatch) return sponsoredMatch[1];

    const encodedSponsoredPattern = /%2Fdp%2F([A-Z0-9]{10})/;
    const encodedMatch = url.match(encodedSponsoredPattern);
    if (encodedMatch) return encodedMatch[1];

    try {
      const urlObj = new URL(url);
      
      if (urlObj.pathname.startsWith('/sspa/click')) {
        const targetUrl = urlObj.searchParams.get('url');
        if (targetUrl) {
          const encodedMatch = targetUrl.match(/%2Fdp%2F([A-Z0-9]{10})/);
          if (encodedMatch) return encodedMatch[1];
          return extractAsin(targetUrl);
        }
      }

      const pathParts = urlObj.pathname.split('/');
      const dpIndex = pathParts.indexOf('dp');
      if (dpIndex !== -1 && dpIndex + 1 < pathParts.length) {
        const asin = pathParts[dpIndex + 1];
        if (asin.length === 10) return asin;
      }
    } catch (e) {
      console.error();
    }
    return null;
  }

  const MAX_ASINS = 50;

  function showChromeNotification(title, message) {
    chrome.notifications.create({
      type: 'basic',
      iconUrl: chrome.runtime.getURL('assets/hammer-ic.svg'),
      title: title,
      message: message
    });
  }

  snapHammer.addEventListener('dragover', (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
    snapHammer.style.transform = 'scale(1.1)';
    img.style.transform = 'scale(1.1)';
  });

  snapHammer.addEventListener('drop', (e) => {
    e.preventDefault();
    snapHammer.style.transform = 'scale(1)';
    img.style.transform = 'scale(1)';

    const currentMarketplace = getCurrentMarketplace();
    const marketplaceAsins = Array.from(reportedAsins).filter(asin => 
      asinMarketplaces.get(asin) === currentMarketplace
    );

    if (marketplaceAsins.length >= MAX_ASINS) {
      showChromeNotification(
        'Maximum ASINs Reached',
        `You can only report up to 50 ASINs at a time for ${currentMarketplace}. Please submit your current report first.`
      );
      return;
    }

    const types = Array.from(e.dataTransfer.types);
    let url;
    let isImage = false;

    if (types.includes('Files') && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      if (file.type.startsWith('image/')) {
        isImage = true;
        url = window.location.href;
      }
    } else {
      if (types.includes('text/uri-list')) {
        url = e.dataTransfer.getData('text/uri-list');
      } else if (types.includes('text/plain')) {
        url = e.dataTransfer.getData('text/plain');
      }

      if (url && url.includes('amazon.com/images/')) {
        isImage = true;
        url = window.location.href;
      } else if (!url) {
        const htmlContent = e.dataTransfer.getData('text/html');
        if (htmlContent) {
          const temp = document.createElement('div');
          temp.innerHTML = htmlContent;
          const link = temp.querySelector('a');
          const img = temp.querySelector('img');
          
          if (link) {
            url = link.href;
          } else if (img && img.src && img.src.includes('amazon.com/images/')) {
            isImage = true;
            url = window.location.href;
          }
        }
      }
    }

    if (!url) return;

    const asin = extractAsin(url);
    if (!asin) return;

    const marketplace = getMarketplaceFromUrl(url);
    asinMarketplaces.set(asin, marketplace);

    reportedAsins.add(asin);
    window.updateSnapCounter(reportedAsins.size);
    saveAsins();
  });

  document.addEventListener('drop', (e) => {
    e.preventDefault();
  });

  document.addEventListener('dragover', (e) => {
    e.preventDefault();
  });

  document.body.appendChild(snapHammer);

  window.updateSnapCounter = (count) => {
    const currentMarketplace = getCurrentMarketplace();
    const marketplaceCount = Array.from(reportedAsins).filter(asin => 
      asinMarketplaces.get(asin) === currentMarketplace
    ).length;
    
    counter.textContent = marketplaceCount;
    counter.style.backgroundColor = marketplaceCount === 0 ? '#FA583A' : '#01BB87';
    asinCounter.textContent = `${marketplaceCount}/50`;
    updateTableView();
  };

  let isListingsTabActive = true;

  function setActiveTab(activeTab, inactiveTab, activeContent, inactiveContent) {
    isListingsTabActive = activeTab === listingsTab;
    
    if (!isListingsTabActive && fetchQueue) {
      fetchQueue.hideFetchingLoader();
    }

    activeTab.style.background = 'white';
    activeTab.style.border = '0.3px solid #DCE0E5';
    activeTab.querySelector('div').style.color = '#470CED';
    activeTab.querySelector('div').style.fontWeight = '700';
    activeTab.querySelector('img').style.filter = 'none';
    
    inactiveTab.style.background = 'transparent';
    inactiveTab.style.border = 'none';
    inactiveTab.querySelector('div').style.color = '#606F95';
    inactiveTab.querySelector('div').style.fontWeight = '500';
    inactiveTab.querySelector('img').style.filter = 'brightness(0) saturate(100%) invert(46%) sepia(11%) saturate(1013%) hue-rotate(189deg) brightness(90%) contrast(87%)';
  
    const stepper = document.querySelector('.listings-right > div:first-child');
    if (stepper) {
        stepper.style.display = activeTab === listingsTab ? 'flex' : 'none';
    }
  
    actionsBar.style.display = activeTab === listingsTab ? 'flex' : 'none';
    
    const reportButton = document.getElementById('report-button');
    if (reportButton) {
      reportButton.style.display = activeTab === listingsTab ? 'flex' : 'none';
    }
    
    activeContent.style.display = 'flex';
    inactiveContent.style.display = 'none';
    
    if (activeTab === listingsTab) {
      updateTableView();
    } else {
      historyContent.innerHTML = '';
      historyContent.appendChild(historyEmptyState);
    }
  }

  setActiveTab(listingsTab, historyTab, listingsContent, historyContent);

  listingsTab.addEventListener('click', () => {
    setActiveTab(listingsTab, historyTab, listingsContent, historyContent);
  });

  historyTab.addEventListener('click', () => {
    setActiveTab(historyTab, listingsTab, historyContent, listingsContent);
  });

  const fetchQueue = {
    queue: [],
    isProcessing: false,
    batchSize: 10,
    isFetchingActive: false,
    
    add: function(asin, row) {
      if (!productTitles.has(asin) || !productBSRs.has(asin) || !productPublishDates.has(asin)) {
        this.queue.push({ asin, row });
        if (!this.isProcessing) {
          this.processNext();
          this.showFetchingLoader();
        }
      } else {
        this.updateRowWithStoredData(asin, row);
      }
    },

    showFetchingLoader: function() {
      if (document.getElementById('fetching-loader')) return;
      
      if (!isListingsTabActive) return;
      
      this.isFetchingActive = true;
      
      this.setUIElementsEnabled(false);
      
      const loaderContainer = document.createElement('div');
      loaderContainer.id = 'fetching-loader';
      loaderContainer.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        background-color: rgba(255, 255, 255, 0.95);
        z-index: 4000;
        pointer-events: none;
      `;
      
      const spinner = document.createElement('div');
      spinner.style.cssText = `
        width: 40px;
        height: 40px;
        border: 4px solid rgba(96, 111, 149, 0.1);
        border-radius: 50%;
        border-left-color: #470CED;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
      `;
      
      const styleSheet = document.createElement('style');
      styleSheet.textContent = `
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `;
      document.head.appendChild(styleSheet);
      
      const text = document.createElement('div');
      text.textContent = 'Fetching ASIN\'s data...';
      text.style.cssText = `
        font-family: 'Amazon Ember';
        font-weight: 500;
        font-size: 16px;
        color: #606F95;
      `;
      
      loaderContainer.appendChild(spinner);
      loaderContainer.appendChild(text);
      
      const listingsContent = document.querySelector('.listings-tab-content') || content.querySelector('div:first-child');
      if (listingsContent) {
        listingsContent.style.position = 'relative';
        listingsContent.appendChild(loaderContainer);
      }
    },
    
    hideFetchingLoader: function() {
      const loader = document.getElementById('fetching-loader');
      if (loader) {
        loader.style.opacity = '0';
        loader.style.transition = 'opacity 0.3s ease';
        setTimeout(() => {
          loader.remove();
        }, 300);
      }
      
      this.isFetchingActive = false;
      
      this.setUIElementsEnabled(true);
    },
    
    setUIElementsEnabled: function(isEnabled) {
      const allCheckboxes = document.querySelectorAll('input[type="checkbox"]');
      allCheckboxes.forEach(checkbox => {
        checkbox.disabled = !isEnabled;
        if (!isEnabled) {
          checkbox.style.opacity = '0.5';
          checkbox.style.cursor = 'not-allowed';
        } else {
          checkbox.style.opacity = '1';
          checkbox.style.cursor = 'pointer';
        }
      });
      
      if (isEnabled) {

        const selectedCheckboxes = document.querySelectorAll('#product-table tbody input[type="checkbox"]:checked');
        updateReportButtonState(selectedCheckboxes.length > 0);
      }

      const deleteButton = document.querySelector('button[data-tooltip="Delete selected"]');
      const downloadButton = document.querySelector('button[data-tooltip="Download selected"]');

      const setButtonDisabled = (button, iconElement) => {
        if (!button) return;
        if (!isEnabled) {
          button.disabled = true;
          button.style.background = '#CFD4D4';
          button.style.opacity = '1';
          button.style.cursor = 'not-allowed';
          button.style.pointerEvents = 'none';
          button.style.color = 'rgba(0, 0, 0, 0.5)';

          if (iconElement) {
            iconElement.style.filter = 'brightness(0)';
            iconElement.style.opacity = '0.5';
          }
        } else {

          updateButtonStates(selectedCheckboxes ? selectedCheckboxes.size : 0);
        }
      };

      if (deleteButton) {
        const clearIcon = deleteButton.querySelector('img');
        setButtonDisabled(deleteButton, clearIcon);
      }

      if (downloadButton) {
        const downloadIcon = downloadButton.querySelector('img');
        setButtonDisabled(downloadButton, downloadIcon);
      }

      const searchInput = document.querySelector('input[placeholder="Search for ASIN, listings..."]');
      if (searchInput) {
        searchInput.disabled = !isEnabled;
        if (!isEnabled) {
          searchInput.style.opacity = '0.5';
          searchInput.style.cursor = 'not-allowed';
        } else {
          searchInput.style.opacity = '1';
          searchInput.style.cursor = 'text';
        }
      }

      const clearSearchButton = document.querySelector('button:has(> img[src*="close-ham-popup-ic.svg"])');
      if (clearSearchButton) {
        if (!isEnabled) {
          clearSearchButton.disabled = true;
          clearSearchButton.style.pointerEvents = 'none';
        } else {
          clearSearchButton.disabled = false;
          clearSearchButton.style.pointerEvents = 'auto';
        }
      }

      if (!clearSearchButton) {
        const searchWrapper = document.querySelector('input[placeholder="Search for ASIN, listings..."]')?.parentElement;
        const alternativeClearButton = searchWrapper?.querySelector('button');
        if (alternativeClearButton) {
          if (!isEnabled) {
            alternativeClearButton.disabled = true;
            alternativeClearButton.style.pointerEvents = 'none';
          } else {
            alternativeClearButton.disabled = false;
            alternativeClearButton.style.pointerEvents = 'auto';
          }
        }
      }
    },

    updateRowWithStoredData: function(asin, row) {
      const title = productTitles.get(asin);
      const bsr = productBSRs.get(asin);
      const published = productPublishDates.get(asin);
      const marketplace = asinMarketplaces.get(asin);

      const titleLink = row.querySelector('div:nth-child(3) a');
      if (titleLink) {
        titleLink.textContent = title;
        if (title === 'Listing not found in this marketplace' || title === 'Error fetching listing') {
          titleLink.style.color = '#FF391F';
          titleLink.style.textDecoration = 'none';
          titleLink.style.cursor = 'default';
          titleLink.style.pointerEvents = 'none';
          titleLink.style.pointerEvents = 'none';
        }
      }

      const publishedCell = row.querySelector('div:nth-child(4)');
      if (publishedCell) {
        publishedCell.textContent = published || 'N/A';
      }

      const bsrCell = row.querySelector('div:nth-child(5)');
      if (bsrCell) {

        bsrCell.innerHTML = '';

        const bsrLabel = document.createElement('div');
        bsrLabel.style.cssText = `
          padding: 4px 8px;
          border-radius: 4px;
          font-family: 'Amazon Ember';
          font-size: 14px;
          font-weight: 700;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        `;

        const displayBSR = formatBSRForDisplay(bsr);
        bsrLabel.textContent = displayBSR;
        const bsrStyle = getBSRStyle(bsr);
        bsrLabel.style.background = bsrStyle.background;
        bsrLabel.style.color = bsrStyle.color;

        bsrCell.appendChild(bsrLabel);
      }
    },

    processBatch: async function(batch) {
      const promises = batch.map(({ asin, row }) => {
        return (async () => {
          try {
            const result = await fetchProductTitle(asin);

            const titleLink = row.querySelector('div:nth-child(3) a');
            if (titleLink) {
              titleLink.textContent = result.title;
              if (!result.isValid) {
                titleLink.style.color = '#FF391F';
                titleLink.style.textDecoration = 'none';
                titleLink.style.cursor = 'default';
                titleLink.style.pointerEvents = 'none';
              }
            }

            if (!result.isValid) {
              const marketplaceText = row.querySelector('div:nth-child(2) span:last-child');
              if (marketplaceText) {
                marketplaceText.style.color = '#FF391F';
                marketplaceText.title = result.title;
              }
            }

            const publishedCell = row.querySelector('div:nth-child(4)');
            if (publishedCell) {
              publishedCell.textContent = result.published;
            }

            const bsrCell = row.querySelector('div:nth-child(5)');
            if (bsrCell) {

              bsrCell.innerHTML = '';

              const bsrLabel = document.createElement('div');
              bsrLabel.style.cssText = `
                padding: 4px 8px;
                border-radius: 4px;
                font-family: 'Amazon Ember';
                font-size: 14px;
                font-weight: 700;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
              `;

              const displayBSR = formatBSRForDisplay(result.bsr);
              bsrLabel.textContent = displayBSR;
              const bsrStyle = getBSRStyle(result.bsr);
              bsrLabel.style.background = bsrStyle.background;
              bsrLabel.style.color = bsrStyle.color;

              bsrCell.appendChild(bsrLabel);
            }

            if (result.bsr !== 'N/A' && result.bsr !== 'Loading...') {
              productBSRs.set(asin, result.bsr);
              saveAsins();
            }
          } catch (error) {
            console.error();
            this.handleError(row);
          }
        })();
      });

      await Promise.all(promises);
    },

    handleError: function(row) {
      const titleLink = row.querySelector('div:nth-child(3) a');
      const marketplaceText = row.querySelector('div:nth-child(2) span:last-child');
      const publishedCell = row.querySelector('div:nth-child(4)');
      const bsrLabel = row.querySelector('div:nth-child(5) div');

      if (titleLink) {
        titleLink.textContent = 'Error fetching listing';
        titleLink.style.color = '#FF391F';
        titleLink.style.textDecoration = 'none';
        titleLink.style.cursor = 'default';
        titleLink.style.pointerEvents = 'none';
      }

      if (marketplaceText) {
        marketplaceText.style.color = '#FF391F';
      }

      if (publishedCell) {
        publishedCell.textContent = 'N/A';
      }

      if (bsrLabel) {
        bsrLabel.textContent = 'N/A';
        bsrLabel.style.background = 'rgba(250, 88, 58, 0.1)';
        bsrLabel.style.color = '#FA583A';
      }
    },

    processNext: function() {
      if (this.queue.length === 0) {
        this.isProcessing = false;
        this.hideFetchingLoader(); 
        return;
      }

      this.isProcessing = true;

      if (this.isFetchingActive) {
        const allCheckboxes = document.querySelectorAll('input[type="checkbox"]');
        allCheckboxes.forEach(checkbox => {
          checkbox.disabled = true;
          checkbox.style.opacity = '0.5';
          checkbox.style.cursor = 'not-allowed';
        });
      }

      const batch = this.queue.splice(0, this.batchSize);

      this.processBatch(batch)
        .catch(error => {
          console.error();

        })
        .finally(() => {

          setTimeout(() => this.processNext(), 100);
        });
    }
  };

  function updateTableView() {
    const currentMarketplace = getCurrentMarketplace();
    const marketplaceAsins = Array.from(reportedAsins).filter(asin => 
      asinMarketplaces.get(asin) === currentMarketplace
    );

    const hasItems = marketplaceAsins.length > 0;

    asinCounter.textContent = `${marketplaceAsins.length}/50`;

    const reportButton = document.getElementById('report-button');

    if (hasItems) {

      if (reportButton) {
        reportButton.style.display = 'flex';
      }

      listingsContent.style.justifyContent = 'flex-start';
      listingsContent.innerHTML = '';

      const contentWrapper = document.createElement('div');
      contentWrapper.style.cssText = `
        display: flex;
        flex-direction: column;
        width: 100%;

      `;
      
      contentWrapper.appendChild(table);

      listingsContent.appendChild(contentWrapper);
      actionsBar.style.display = 'flex';

      function updateButtonStates(selectedCount) {

        const reportButton = document.getElementById('report-button');
        if (!reportButton) return; 

        const applyIcon = reportButton.querySelector('img'); 

        if (selectedCount > 0) {

          deleteButton.style.background = '#FF391F';
          deleteButton.style.opacity = '1';
          deleteButton.style.cursor = 'pointer';
          deleteButton.style.pointerEvents = 'auto';
          deleteButton.style.color = 'white';
          deleteButton.disabled = false;
          deleteButton.style.filter = 'none';
          clearIcon.style.filter = 'brightness(0) invert(1)';
          clearIcon.style.opacity = '1';

          downloadButton.style.background = '#470CED';
          downloadButton.style.opacity = '1';
          downloadButton.style.cursor = 'pointer';
          downloadButton.style.pointerEvents = 'auto';
          downloadButton.style.color = 'white';
          downloadButton.disabled = false;
          downloadButton.style.filter = 'none';
          downloadIcon.style.filter = 'brightness(0) invert(1)';
          downloadIcon.style.opacity = '1';

          reportButton.style.background = '#470CED';
          reportButton.style.opacity = '1';
          reportButton.style.cursor = 'pointer';
          reportButton.style.pointerEvents = 'auto';
          reportButton.style.color = 'white';
          reportButton.disabled = false;
          reportButton.style.filter = 'none';
          if (applyIcon) { 
            applyIcon.style.filter = 'brightness(0) invert(1)';
            applyIcon.style.opacity = '1';
          }
        } else {

          deleteButton.style.background = '#CFD4D4';
          deleteButton.style.opacity = '1';
          deleteButton.style.cursor = 'not-allowed';
          deleteButton.style.pointerEvents = 'none';
          deleteButton.style.color = 'rgba(0, 0, 0, 0.5)';
          deleteButton.disabled = true;
          clearIcon.style.filter = 'brightness(0)';
          clearIcon.style.opacity = '0.5';

          downloadButton.style.background = '#CFD4D4';
          downloadButton.style.opacity = '1';
          downloadButton.style.cursor = 'not-allowed';
          downloadButton.style.pointerEvents = 'none';
          downloadButton.style.color = 'rgba(0, 0, 0, 0.5)';
          downloadButton.disabled = true;
          downloadIcon.style.filter = 'brightness(0)';
          downloadIcon.style.opacity = '0.5';

          reportButton.style.background = '#CFD4D4';
          reportButton.style.opacity = '1';
          reportButton.style.cursor = 'not-allowed';
          reportButton.style.pointerEvents = 'none';
          reportButton.style.color = 'rgba(0, 0, 0, 0.5)';
          reportButton.disabled = true;
          if (applyIcon) { 
            applyIcon.style.filter = 'brightness(0)';
            applyIcon.style.opacity = '0.5';
          }
        }
      }

      tableBody.innerHTML = '';
      headerCheckbox.checked = false;
      headerCheckbox.style.backgroundColor = 'white';
      headerCheckbox.style.borderColor = '#E4E4E7';
      headerCheckbox.style.backgroundImage = 'none';
      selectedCheckboxes.clear();
      updateButtonStates(0);

      headerCheckbox.addEventListener('change', function() {

        if (fetchQueue.isFetchingActive) {
          return;
        }

        const visibleCheckboxes = Array.from(tableBody.querySelectorAll('input[type="checkbox"]')).filter(checkbox => {
            const row = checkbox.closest('div[style*="grid"]');
            return row && row.style.display !== 'none';
        });
        const isChecked = this.checked;

        selectedCheckboxes.clear();

        if (this.checked) {
            this.style.backgroundColor = '#470CED';
            this.style.borderColor = '#470CED';
            this.style.backgroundImage = "url(\"data:image/svg+xml,%3Csvg width='10' height='8' viewBox='0 0 10 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 1L3.5 6.5L1 4' stroke='white' stroke-width='1.6666' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\")";
            this.style.backgroundRepeat = 'no-repeat';
            this.style.backgroundPosition = 'center';
        } else {
            this.style.backgroundColor = 'white';
            this.style.borderColor = '#E4E4E7';
            this.style.backgroundImage = 'none';
        }

        visibleCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
            if (isChecked) {
                checkbox.style.backgroundColor = '#470CED';
                checkbox.style.borderColor = '#470CED';
                checkbox.style.backgroundImage = "url(\"data:image/svg+xml,%3Csvg width='10' height='8' viewBox='0 0 10 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 1L3.5 6.5L1 4' stroke='white' stroke-width='1.6666' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\")";
                checkbox.style.backgroundRepeat = 'no-repeat';
                checkbox.style.backgroundPosition = 'center';
                selectedCheckboxes.add(checkbox);
            } else {
                checkbox.style.backgroundColor = 'white';
                checkbox.style.borderColor = '#E4E4E7';
                checkbox.style.backgroundImage = 'none';
            }
        });

        updateButtonStates(selectedCheckboxes.size);
      });

      let asinsArray = marketplaceAsins;

      if (productSortState === 'asc' || productSortState === 'desc') {
        asinsArray.sort();
      } else if (asinSortState === 'asc') {
        asinsArray.sort((a, b) => a.localeCompare(b));
      } else if (asinSortState === 'desc') {
        asinsArray.sort((a, b) => b.localeCompare(a));
      }

      fetchQueue.queue = [];
      fetchQueue.isProcessing = false;

      asinsArray.forEach(asin => {
        const row = document.createElement('div');
        row.style.cssText = `
          display: grid;
          grid-template-columns: 48px 100px 378px 130px 120px 120px;
          gap: 0;
          border-bottom: 1px solid #E4E4E7;
          width: 100%;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          transition: background-color 0.2s ease;
        `;

        row.addEventListener('mouseenter', () => {
          row.style.backgroundColor = '#F7F8FA';
        });

        row.addEventListener('mouseleave', () => {
          row.style.backgroundColor = 'transparent';
        });

        const checkboxCell = document.createElement('div');
        checkboxCell.style.cssText = `
          padding: 12px 12px 18px 12px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          -webkit-font-smoothing: auto;
          -moz-osx-font-smoothing: grayscale;
        `;
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.style.cssText = `
          min-width: 20px;
          min-height: 20px;
          width: 20px;
          height: 20px;
          border: 1.5px solid #E4E4E7;
          border-radius: 4px;
          appearance: none;
          cursor: pointer;
          position: relative;
          background: white;
          transition: all 0.2s ease;
          padding: 0;
          margin: 0;
          box-sizing: border-box;
        `;

        if (fetchQueue.isFetchingActive) {
          checkbox.disabled = true;
          checkbox.style.opacity = '0.5';
          checkbox.style.cursor = 'not-allowed';
        }

        checkbox.addEventListener('change', function() {

          if (fetchQueue.isFetchingActive) {
            return;
          }

          if (this.checked) {
            this.style.backgroundColor = '#470CED';
            this.style.borderColor = '#470CED';
            this.style.backgroundImage = "url(\"data:image/svg+xml,%3Csvg width='10' height='8' viewBox='0 0 10 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 1L3.5 6.5L1 4' stroke='white' stroke-width='1.6666' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\")";
            this.style.backgroundRepeat = 'no-repeat';
            this.style.backgroundPosition = 'center';
            selectedCheckboxes.add(this);
          } else {
            this.style.backgroundColor = 'white';
            this.style.borderColor = '#E4E4E7';
            this.style.backgroundImage = 'none';
            selectedCheckboxes.delete(this);
          }

          const checkboxes = tableBody.querySelectorAll('input[type="checkbox"]');
          const allChecked = Array.from(checkboxes).every(cb => cb.checked);

          headerCheckbox.checked = allChecked;
          if (allChecked) {
            headerCheckbox.style.backgroundColor = '#470CED';
            headerCheckbox.style.borderColor = '#470CED';
            headerCheckbox.style.backgroundImage = "url(\"data:image/svg+xml,%3Csvg width='10' height='8' viewBox='0 0 10 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 1L3.5 6.5L1 4' stroke='white' stroke-width='1.6666' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\")";
            headerCheckbox.style.backgroundRepeat = 'no-repeat';
            headerCheckbox.style.backgroundPosition = 'center';
          } else {
            headerCheckbox.style.backgroundColor = 'white';
            headerCheckbox.style.borderColor = '#E4E4E7';
            headerCheckbox.style.backgroundImage = 'none';
          }

          updateButtonStates(selectedCheckboxes.size);
        });

        checkboxCell.appendChild(checkbox);

        const marketplaceCell = document.createElement('div');
        marketplaceCell.style.cssText = `
          padding: 16px 16px 16px 0;
          display: flex;
          align-items: center;
          gap: 8px;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        `;
        
        const storedMarketplace = asinMarketplaces.get(asin) || 'US';
        const flagIcon = document.createElement('img');
        flagIcon.src = chrome.runtime.getURL(MARKETPLACE_FLAGS[storedMarketplace]);
        flagIcon.style.cssText = `
          width: 20px;
          height: 20px;
          display: block;
          image-rendering: -webkit-optimize-contrast;
        `;
        
        const marketplaceText = document.createElement('span');
        marketplaceText.textContent = storedMarketplace;
        marketplaceText.style.cssText = `
          font-family: 'Amazon Ember';
          font-size: 14px;
          line-height: 1.4;
          color: #09090B;
          font-weight: 500;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        `;
        
        marketplaceCell.appendChild(flagIcon);
        marketplaceCell.appendChild(marketplaceText);

        const productCell = document.createElement('div');
        productCell.style.cssText = `
          padding: 16px 16px 16px 0;
          font-family: 'Amazon Ember';
          font-size: 14px;
          line-height: 1.4;
          color: #09090B;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          display: flex;
          align-items: center;
          max-width: 100%;
          overflow: hidden;
        `;

        const productLink = document.createElement('a');
        productLink.href = `https://www.${MARKETPLACE_DOMAINS[storedMarketplace]}/dp/${asin}`;
        productLink.target = '_blank';
        productLink.style.cssText = `
          color: #09090B;
          text-decoration: none;
          cursor: pointer;
          font-weight: 500;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          line-height: 1.4;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
          display: block;
          &:hover {
            color: #470CED;
            text-decoration: underline;
          }
        `;
        productLink.textContent = 'Loading...';
        productCell.appendChild(productLink);

        const publishedCell = document.createElement('div');
        publishedCell.style.cssText = `
          padding: 16px 16px 16px 0;
          font-family: 'Amazon Ember';
          font-size: 14px;
          line-height: 1.4;
          color: #09090B;
          font-weight: 500;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          display: flex;
          align-items: center;
        `;
        publishedCell.textContent = 'Loading...';

        const bsrCell = document.createElement('div');
        bsrCell.style.cssText = `
          padding: 16px 16px 16px 0;
          display: flex;
          align-items: center;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        `;

        const bsrLabel = document.createElement('div');
        bsrLabel.style.cssText = `
          padding: 4px 8px;
          background: rgba(96, 111, 149, 0.1);
          border-radius: 4px;
          font-family: 'Amazon Ember';
          font-size: 14px;
          font-weight: 700;
          color: #606F95;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        `;
        bsrLabel.textContent = 'Loading...';
        bsrCell.appendChild(bsrLabel);

        const asinCell = document.createElement('div');
        asinCell.style.cssText = `
          padding: 16px 16px 16px 0;
          font-family: 'Amazon Ember';
          font-size: 14px;
          line-height: 1.4;
          color: #09090B;
          font-weight: 500;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          display: flex;
          align-items: center;
        `;
        asinCell.textContent = asin;

        row.appendChild(checkboxCell);
        row.appendChild(marketplaceCell);
        row.appendChild(productCell);
        row.appendChild(publishedCell);
        row.appendChild(bsrCell);
        row.appendChild(asinCell);

        tableBody.appendChild(row);
        
        fetchQueue.add(asin, row);
      });
    } else {
      if (reportButton) {
        reportButton.style.display = 'none';
      }
      
      listingsContent.style.justifyContent = 'center';
      listingsContent.innerHTML = '';
      
      const newEmptyState = document.createElement('div');
      newEmptyState.style.cssText = `
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 24px;
      `;

      const emptyStateIcon = document.createElement('img');
      emptyStateIcon.src = chrome.runtime.getURL('assets/no-profile-img.svg');
      emptyStateIcon.style.cssText = `
        width: 80px;
        height: 91px;
      `;

      const emptyStateWrapper = document.createElement('div');
      emptyStateWrapper.style.cssText = `
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0;
      `;

      const emptyStateTitle = document.createElement('h3');
      emptyStateTitle.textContent = 'No listings added yet';
      emptyStateTitle.style.cssText = `
        font-family: 'Amazon Ember';
        font-weight: 700;
        font-size: 16px;
        color: #606F95;
        margin: 0;
      `;

      const emptyStateText = document.createElement('p');
      emptyStateText.textContent = 'Drag listings from the Amazon search results into the\ncircle at the bottom-right to start reporting.';
      emptyStateText.style.cssText = `
        font-family: 'Amazon Ember';
        font-weight: 400;
        font-size: 14px;
        color: #606F95;
        margin: 0;
        text-align: center;
        white-space: pre-line;
      `;

      const newPasteButton = document.createElement('button');
      newPasteButton.id = 'paste-btn';
      newPasteButton.style.cssText = `
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background: #470CED;
        border: none;
        border-radius: 6px;
        font-family: 'Amazon Ember';
        font-weight: 500;
        font-size: 14px;
        color: white;
        cursor: pointer;
        margin-top: 24px;
        transition: background 0.2s ease;
      `;
      const newPasteIcon = document.createElement('img');
      newPasteIcon.src = chrome.runtime.getURL('assets/paste-asin-ic.svg');
      newPasteIcon.style.cssText = `
        width: 16px;
        filter: brightness(0) invert(1);
      `;
      newPasteButton.appendChild(newPasteIcon);
      newPasteButton.appendChild(document.createTextNode('Click to paste ASINs or (Ctrl/CMD+V)'));

      newPasteButton.addEventListener('mouseenter', () => {
        newPasteButton.style.background = '#3D0BCE';
      });

      newPasteButton.addEventListener('mouseleave', () => {
        newPasteButton.style.background = '#470CED';
      });

      newPasteButton.addEventListener('click', async () => {
        if (isListingsTabActive) {
          handlePaste();
        }
      });

      emptyStateWrapper.appendChild(emptyStateTitle);
      emptyStateWrapper.appendChild(emptyStateText);

      newEmptyState.appendChild(emptyStateIcon);
      newEmptyState.appendChild(emptyStateWrapper);
      newEmptyState.appendChild(newPasteButton);

      listingsContent.appendChild(newEmptyState);
      actionsBar.style.display = 'none';
    }
  }

  const originalUpdateSnapCounter = window.updateSnapCounter;
  window.updateSnapCounter = (count) => {
    const currentMarketplace = getCurrentMarketplace();
    const marketplaceCount = Array.from(reportedAsins).filter(asin => 
      asinMarketplaces.get(asin) === currentMarketplace
    ).length;

    counter.textContent = marketplaceCount;
    counter.style.backgroundColor = marketplaceCount === 0 ? '#FA583A' : '#01BB87';
    asinCounter.textContent = `${marketplaceCount}/50`;
    updateTableView();
  };

  content.appendChild(listingsContent);
  content.appendChild(historyContent);

  deleteButton.addEventListener('click', () => {
    if (selectedCheckboxes.size === 0) return;

    const checkedRows = Array.from(selectedCheckboxes).map(checkbox => checkbox.closest('div[style*="grid"]'));

    checkedRows.forEach(row => {

      const asinCell = row.querySelector('div:nth-child(6)');
      if (!asinCell) return;

      const asin = asinCell.textContent.trim();

      reportedAsins.delete(asin);
      productTitles.delete(asin);
      asinMarketplaces.delete(asin);
      productBSRs.delete(asin);

      row.remove();
    });

    const remainingRows = tableBody.querySelectorAll('div[style*="grid"]');
    if (remainingRows.length === 0) {

      const contentWrapper = listingsContent.querySelector('div');
      if (contentWrapper) {
        contentWrapper.remove();
      }

      const reportButton = document.getElementById('report-button');
      if (reportButton) {
        reportButton.style.display = 'none';
      }

      listingsContent.style.justifyContent = 'center';
      listingsContent.innerHTML = '';

      const newEmptyState = document.createElement('div');
      newEmptyState.style.cssText = `
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 24px;
      `;

      const emptyStateIcon = document.createElement('img');
      emptyStateIcon.src = chrome.runtime.getURL('assets/no-profile-img.svg');
      emptyStateIcon.style.cssText = `
        width: 80px;
        height: 91px;
      `;

      const emptyStateWrapper = document.createElement('div');
      emptyStateWrapper.style.cssText = `
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0;
      `;

      const emptyStateTitle = document.createElement('h3');
      emptyStateTitle.textContent = 'No listings added yet';
      emptyStateTitle.style.cssText = `
        font-family: 'Amazon Ember';
        font-weight: 700;
        font-size: 16px;
        color: #606F95;
        margin: 0;
      `;

      const emptyStateText = document.createElement('p');
      emptyStateText.textContent = 'Drag listings from the Amazon search results into the\ncircle at the bottom-right to start reporting.';
      emptyStateText.style.cssText = `
        font-family: 'Amazon Ember';
        font-weight: 400;
        font-size: 14px;
        color: #606F95;
        margin: 0;
        text-align: center;
        white-space: pre-line;
      `;

      const newPasteButton = document.createElement('button');
      newPasteButton.id = 'paste-btn';
      newPasteButton.style.cssText = `
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background: #470CED;
        border: none;
        border-radius: 6px;
        font-family: 'Amazon Ember';
        font-weight: 500;
        font-size: 14px;
        color: white;
        cursor: pointer;
        margin-top: 24px;
        transition: background 0.2s ease;
      `;
      const newPasteIcon = document.createElement('img');
      newPasteIcon.src = chrome.runtime.getURL('assets/paste-asin-ic.svg');
      newPasteIcon.style.cssText = `
        width: 16px;
        filter: brightness(0) invert(1);
      `;
      newPasteButton.appendChild(newPasteIcon);
      newPasteButton.appendChild(document.createTextNode('Click to paste ASINs or (Ctrl/CMD+V)'));

      newPasteButton.addEventListener('mouseenter', () => {
        newPasteButton.style.background = '#3D0BCE';
      });

      newPasteButton.addEventListener('mouseleave', () => {
        newPasteButton.style.background = '#470CED';
      });

      newPasteButton.addEventListener('click', async () => {
        if (isListingsTabActive) {
          handlePaste();
        }
      });

      emptyStateWrapper.appendChild(emptyStateTitle);
      emptyStateWrapper.appendChild(emptyStateText);

      newEmptyState.appendChild(emptyStateIcon);
      newEmptyState.appendChild(emptyStateWrapper);
      newEmptyState.appendChild(newPasteButton);

      listingsContent.appendChild(newEmptyState);
      actionsBar.style.display = 'none';
    }

    saveAsins();

    const currentMarketplace = getCurrentMarketplace();
    const marketplaceCount = Array.from(reportedAsins).filter(asin => 
      asinMarketplaces.get(asin) === currentMarketplace
    ).length;

    counter.textContent = marketplaceCount;
    counter.style.backgroundColor = marketplaceCount === 0 ? '#FA583A' : '#01BB87';
    asinCounter.textContent = `${marketplaceCount}/50`;

    headerCheckbox.checked = false;
    headerCheckbox.style.backgroundColor = 'white';
    headerCheckbox.style.borderColor = '#E4E4E7';
    headerCheckbox.style.backgroundImage = 'none';

    selectedCheckboxes.clear();

    deleteButton.style.background = '#CFD4D4';
    deleteButton.style.opacity = '1';
    deleteButton.style.cursor = 'not-allowed';
    deleteButton.style.pointerEvents = 'none';
    deleteButton.style.color = 'rgba(0, 0, 0, 0.5)';
    deleteButton.disabled = true;
    clearIcon.style.filter = 'brightness(0)';
    clearIcon.style.opacity = '0.5';

    downloadButton.style.background = '#CFD4D4';
    downloadButton.style.opacity = '1';
    downloadButton.style.cursor = 'not-allowed';
    downloadButton.style.pointerEvents = 'none';
    downloadButton.style.color = 'rgba(0, 0, 0, 0.5)';
    downloadButton.disabled = true;
    downloadIcon.style.filter = 'brightness(0)';
    downloadIcon.style.opacity = '0.5';

    const reportButton = document.getElementById('report-button');
    if (reportButton) {
        reportButton.style.background = '#CFD4D4';
        reportButton.style.opacity = '1';
        reportButton.style.cursor = 'not-allowed';
        reportButton.style.pointerEvents = 'none';
        reportButton.style.color = 'rgba(0, 0, 0, 0.5)';
        reportButton.disabled = true;
        const applyIcon = reportButton.querySelector('img');
        if (applyIcon) {
            applyIcon.style.filter = 'brightness(0)';
            applyIcon.style.opacity = '0.5';
        }
    }

    if (remainingRows.length > 0) {
        const reportButton = document.getElementById('report-button');
        if (reportButton) {
            reportButton.style.display = 'flex';
            reportButton.style.background = '#CFD4D4';
            reportButton.style.opacity = '1';
            reportButton.style.cursor = 'not-allowed';
            reportButton.style.pointerEvents = 'none';
            reportButton.style.color = 'rgba(0, 0, 0, 0.5)';
            reportButton.disabled = true;
            const applyIcon = reportButton.querySelector('img');
            if (applyIcon) {
                applyIcon.style.filter = 'brightness(0)';
                applyIcon.style.opacity = '0.5';
            }
        }
    }
  });

  async function handlePaste(event) {

    if (!isListingsTabActive) return;

    let text;
    try {
      if (event instanceof ClipboardEvent) {
        text = event.clipboardData.getData('text');
      } else {
        text = await navigator.clipboard.readText();
      }

      if (!text) return;

      const potentialAsins = text.split(/[\s,;\n]+/).filter(Boolean);

      const validAsins = potentialAsins.filter(asin => /^[A-Z0-9]{10}$/.test(asin));

      if (validAsins.length > 0) {
        const currentMarketplace = getCurrentMarketplace();
        const marketplaceAsins = Array.from(reportedAsins).filter(asin => 
          asinMarketplaces.get(asin) === currentMarketplace
        );
        const availableSlots = MAX_ASINS - marketplaceAsins.length;

        if (availableSlots <= 0) {
          showChromeNotification(
            'Maximum ASINs Reached',
            `You can only report up to 50 ASINs at a time for ${currentMarketplace}. Please submit your current report first.`
          );
          return;
        }

        const asinsToAdd = validAsins.slice(0, availableSlots);
        const skippedAsins = validAsins.slice(availableSlots);

        asinsToAdd.forEach(asin => {
          if (!reportedAsins.has(asin)) {
            reportedAsins.add(asin);
            asinMarketplaces.set(asin, currentMarketplace);
          }
        });

        saveAsins();
        window.updateSnapCounter(reportedAsins.size);

        if (skippedAsins.length > 0) {
          showChromeNotification(
            'Some ASINs Skipped',
            `Added ${asinsToAdd.length} ASINs for ${currentMarketplace}. Skipped ${skippedAsins.length} ASINs due to 50 ASIN limit.\n\nSkipped ASINs: ${skippedAsins.join(', ')}`
          );
        }

        deleteButton.style.background = '#CFD4D4';
        deleteButton.style.opacity = '1';
        deleteButton.style.cursor = 'not-allowed';
        deleteButton.style.pointerEvents = 'none';
        deleteButton.style.color = 'rgba(0, 0, 0, 0.5)';
        deleteButton.disabled = true;
        clearIcon.style.filter = 'brightness(0)';
        clearIcon.style.opacity = '0.5';
      }
    } catch (error) {
      console.error();
    }
  }

  document.addEventListener('keydown', (e) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 'v' && snapHammerPopup.style.display === 'flex' && isListingsTabActive) {
      e.preventDefault();
      handlePaste(e);
    }
  });

  document.addEventListener('paste', (e) => {
    if (snapHammerPopup.style.display === 'flex' && isListingsTabActive) {
      e.preventDefault();
      handlePaste(e);
    }
  });

  function getCurrentMarketplace() {
    return getMarketplaceFromUrl(window.location.href);
  }

  downloadButton.addEventListener('click', () => {
    if (selectedCheckboxes.size === 0) return;

    const checkedRows = Array.from(selectedCheckboxes).map(checkbox => checkbox.closest('div[style*="grid"]'));

    const headers = ['#', 'Marketplace', 'Listing', 'Published', 'BSR', 'ASIN'];

    const csvData = checkedRows
      .filter(row => {
        const marketplaceText = row.querySelector('div:nth-child(2) span:last-child');
        const listingText = row.querySelector('div:nth-child(3) a').textContent;
        return marketplaceText.style.color !== '#FF391F' && 
               listingText !== 'Listing not found in this marketplace' &&
               listingText !== 'Error fetching listing';
      })
      .map((row, index) => {
        const marketplace = row.querySelector('div:nth-child(2) span:last-child').textContent.trim();
        const listing = row.querySelector('div:nth-child(3) a').textContent.trim();
        const published = row.querySelector('div:nth-child(4)').textContent.trim();
        const asinCell = row.querySelector('div:nth-child(6)');
        const asin = asinCell ? asinCell.textContent.trim() : '';

        const storedBSR = productBSRs.get(asin);
        const bsr = storedBSR ? formatBSRForDisplay(storedBSR) : 'N/A';

        return [index + 1, marketplace, listing, published, bsr, asin];
      });

    if (csvData.length === 0) {
      showChromeNotification(
        'No Valid Listings',
        'All selected listings have errors or are invalid. Please select valid listings to download.'
      );
      return;
    }

    const csvContent = [
      headers.join(','),
      ...csvData.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const currentMarketplace = getCurrentMarketplace();
    link.href = URL.createObjectURL(blob);
    link.download = `Snap_ASIN_${currentMarketplace}.csv`;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  });

  function getBSRStyle(bsr) {
    if (bsr === 'N/A' || !bsr || bsr === 'Loading...') {
      return {
        background: 'rgba(96, 111, 149, 0.1)',
        color: '#606F95'
      };
    }

    if (bsr === 'Error') {
      return {
        background: 'rgba(250, 88, 58, 0.1)',
        color: '#FA583A'
      };
    }

    const value = parseInt(bsr, 10);
    if (isNaN(value)) return {
      background: 'rgba(96, 111, 149, 0.1)',
      color: '#606F95'
    };

    if (value <= 200000) {
      return {
        background: 'rgba(1, 187, 135, 0.1)',
        color: '#01BB87'
      };
    } else if (value <= 1000000) {
      return {
        background: 'rgba(71, 12, 237, 0.1)',
        color: '#470CED'
      };
    } else if (value <= 2000000) {
      return {
        background: 'rgba(255, 174, 0, 0.1)',
        color: '#FFAE00'
      };
    } else {
      return {
        background: 'rgba(250, 88, 58, 0.1)',
        color: '#FA583A'
      };
    }
  }

  let bsrSortState = null;

  bsrSortButton.addEventListener('click', () => {
    if (bsrSortState === 'asc') {
      bsrSortState = 'desc';
      bsrAscending.style.display = 'none';
      bsrDescending.style.display = 'block';
    } else {
      bsrSortState = 'asc';
      bsrAscending.style.display = 'block';
      bsrDescending.style.display = 'none';
    }

    productSortState = null;
    publishedSortState = null;
    asinSortState = null;

    productAscending.style.display = 'block';
    productDescending.style.display = 'none';
    publishedAscending.style.display = 'block';
    publishedDescending.style.display = 'none';
    asinAscending.style.display = 'block';
    asinDescending.style.display = 'none';

    sortRows();
  });

  backButton.addEventListener('mouseenter', () => {
    backButton.style.background = '#2A00A0';
  });

  backButton.addEventListener('mouseleave', () => {
    backButton.style.background = '#470CED';
  });

  const formValidation = {
    cardSelected: false, 
    firstName: false,
    lastName: false,
    addressLine1: false,
    country: false,
    city: false,
    zipCode: false,
    phoneNumber: false,
    contactName: false,
    contactEmail: false
  };

  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  function isValidPhoneNumber(phone) {
    const phoneRegex = /^\+?[\d\s-()]{10,}$/;
    return phoneRegex.test(phone);
  }

  function isFormValid() {
    const requiredFields = [
        'cardSelected',
        'firstName',
        'lastName',
        'addressLine1',
        'country',
        'city',
        'zipCode',
        'phoneNumber',
        'contactName',
        'contactEmail'
    ];

    return requiredFields.every(field => formValidation[field]);
  }

  function setButtonDisabledState(button, isDisabled) {
    button.disabled = isDisabled;
    if (isDisabled) {
      button.style.background = '#CFD4D4';
      button.style.opacity = '1';
      button.style.cursor = 'not-allowed';
      button.style.pointerEvents = 'none';
      button.style.color = 'rgba(0, 0, 0, 0.5)';
      const buttonIcon = button.querySelector('img');
      if (buttonIcon) {
        buttonIcon.style.filter = 'brightness(0)';
        buttonIcon.style.opacity = '0.5';
      }
    } else {
      button.style.background = '#470CED';
      button.style.opacity = '1';
      button.style.cursor = 'pointer';
      button.style.pointerEvents = 'auto';
      button.style.color = 'white';
      const buttonIcon = button.querySelector('img');
      if (buttonIcon) {
        buttonIcon.style.filter = 'brightness(0) invert(1)';
        buttonIcon.style.opacity = '1';
      }
    }

    setTimeout(() => verifyButtonDisabledState(button), 50);
  }

  function updateConfirmButtonState() {

    const cardSelected = formValidation.cardSelected;
    const countrySelected = formValidation.country;
    const fieldsValid = Object.keys(requiredInputs).every(field => formValidation[field]);

    const isValid = cardSelected && countrySelected && fieldsValid;

    setButtonDisabledState(confirmButton, !isValid);

    setTimeout(() => verifyButtonDisabledState(confirmButton), 10);
  }

  function updateReportButtonState(isEnabled) {

    if (fetchQueue && fetchQueue.isFetchingActive) {
      setButtonDisabledState(reportButton, true);
      return;
    }

    setButtonDisabledState(reportButton, !isEnabled);
  }

  function addButtonHoverEffects(button) {
    button.addEventListener('mouseenter', () => {
      if (!button.disabled) {
        button.style.background = '#2A00A0';
      }
    });

    button.addEventListener('mouseleave', () => {
      if (!button.disabled) {
        button.style.background = '#470CED';
      }
    });
  }

  addButtonHoverEffects(reportButton);
  addButtonHoverEffects(confirmButton);

  updateReportButtonState(true); 

  updateConfirmButtonState();

  function updateButtonStates(selectedCount) {
    updateReportButtonState(selectedCount > 0);

  }

  asinInput.addEventListener('input', (e) => {
    const value = e.target.value.trim();
    formValidation.asin = value.length === 10;
    updateConfirmButtonState();
  });

  document.querySelectorAll('.issue-card').forEach(card => {
    card.addEventListener('click', () => {
      formValidation.cardSelected = true;
      updateConfirmButtonState();

      saveFormData();
    });
  });

  const requiredInputs = {
    firstName: 'First Name',
    lastName: 'Last Name',
    addressLine1: 'Address Line 1',
    city: 'City',
    zipCode: 'ZIP Code',
    phoneNumber: 'Phone Number',
    contactName: 'Contact Name',
    contactEmail: 'Contact E-mail'
  };

  Object.entries(requiredInputs).forEach(([field, label]) => {
    const input = Array.from(document.querySelectorAll('input[type="text"]')).find(input => 
      input.previousElementSibling?.textContent.includes(label)
    );

    if (input) {
      input.addEventListener('input', (e) => {
        const value = e.target.value.trim();

        if (field === 'phoneNumber') {
          formValidation[field] = isValidPhoneNumber(value);
        } else if (field === 'contactEmail') {
          formValidation[field] = isValidEmail(value);
        } else {
          formValidation[field] = value.length > 0;
        }

        updateConfirmButtonState();
      });
    }
  });

  const dropdownHeader = document.querySelector('.dropdown-header');
  if (dropdownHeader) {
    const headerText = dropdownHeader.querySelector('span');
    const observer = new MutationObserver(() => {
      formValidation.country = headerText.textContent !== 'Country/Region';
      updateConfirmButtonState();
    });

    observer.observe(headerText, { childList: true, characterData: true, subtree: true });
  }

  confirmButton.disabled = true;
  confirmButton.style.opacity = '0.5';
  confirmButton.style.cursor = 'not-allowed';
  confirmButton.style.background = '#CFD4D4';
  confirmButton.style.pointerEvents = 'none';
  const initialConfirmIcon = confirmButton.querySelector('img');
  if (initialConfirmIcon) {
    initialConfirmIcon.style.filter = 'brightness(0)';
    initialConfirmIcon.style.opacity = '0.5';
  }

  function enhanceDropdownKeyboardNavigation() {
    const dropdownContainer = document.querySelector('.select-dropdown');
    const dropdownList = dropdownContainer?.querySelector('.dropdown-list');

    if (!dropdownContainer || !dropdownList) return;

    let selectedIndex = -1;
    let items = [];

    dropdownContainer.addEventListener('keydown', (e) => {
      if (!dropdownList.classList.contains('hidden')) {
        items = Array.from(dropdownList.querySelectorAll('.dropdown-item'));
        if (items.length === 0) return;

        if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
          e.preventDefault();

          if (e.key === 'ArrowDown') {
            selectedIndex = (selectedIndex + 1) % items.length;
          } else {
            selectedIndex = selectedIndex <= 0 ? items.length - 1 : selectedIndex - 1;
          }

          items.forEach((item, index) => {
            item.style.backgroundColor = index === selectedIndex ? '#F3F4F6' : 'transparent';
          });

          items[selectedIndex].scrollIntoView({ block: 'nearest' });
        } else if (e.key === 'Enter') {
          e.preventDefault();
          if (selectedIndex >= 0) {
            items[selectedIndex].click();
          }
        } else if (e.key.length === 1) {

          const key = e.key.toLowerCase();
          const matchingItems = items.filter(item => 
            item.textContent.toLowerCase().startsWith(key)
          );

          if (matchingItems.length > 0) {

            const currentMatchIndex = matchingItems.findIndex(item => 
              items.indexOf(item) === selectedIndex
            );

            const nextMatch = matchingItems[
              currentMatchIndex === matchingItems.length - 1 ? 0 : currentMatchIndex + 1
            ] || matchingItems[0];

            selectedIndex = items.indexOf(nextMatch);
            items[selectedIndex].scrollIntoView({ block: 'nearest' });

            items.forEach((item, index) => {
              item.style.backgroundColor = index === selectedIndex ? '#F3F4F6' : 'transparent';
            });
          }
        }
      }
    });

    document.addEventListener('click', (e) => {
      if (!dropdownContainer.contains(e.target)) {
        selectedIndex = -1;
        items.forEach(item => {
          item.style.backgroundColor = 'transparent';
        });
      }
    });
  }

  enhanceDropdownKeyboardNavigation();

  function saveFormData() {
    const formData = {};

    const fieldMapping = {
      'First Name': 'firstName',
      'Last Name': 'lastName',
      'Company': 'company',
      'Address Line 1': 'addressLine1',
      'Address Line 2': 'addressLine2',
      'Phone Number': 'phoneNumber',
      'Contact Name': 'contactName',
      'Contact E-mail': 'contactEmail',
      'City': 'city',
      'State': 'state',
      'ZIP Code': 'zipCode'
    };

    const allInputs = document.querySelectorAll('input[type="text"]');
    allInputs.forEach(input => {

      const labelContainer = input.previousElementSibling;
      const labelText = labelContainer?.querySelector('div')?.textContent || labelContainer?.textContent;

      if (labelText && !labelText.includes('ASIN')) {

        const cleanLabel = labelText.replace(/\s*Optional\s*$/, '').trim();
        const fieldName = fieldMapping[cleanLabel];

        if (fieldName) {
          formData[fieldName] = input.value || '';
        }
      }
    });

    const countryText = document.querySelector('.dropdown-header span')?.textContent;
    formData.country = countryText || '';

    const selectedCard = document.querySelector('.issue-card.selected');
    if (selectedCard) {

      if (selectedCard.classList.contains('stolen-artwork-card')) {
        formData.selectedIssueCard = 'Stolen Artwork';
      } else if (selectedCard.classList.contains('copied-design-card')) {
        formData.selectedIssueCard = 'Copied Design';
      } else if (selectedCard.classList.contains('copied-listing-card')) {
        formData.selectedIssueCard = 'Copied Listing';
      }
    }

    console.log();

    chrome.storage.local.set({ confirmUserSettings: formData }, () => {
      if (chrome.runtime.lastError) {
        console.error();
      } else {

        chrome.storage.local.get(['confirmUserSettings'], (result) => {
          console.log();
        });
      }
    });
  }

  document.querySelectorAll('.issue-card').forEach(card => {
    card.addEventListener('click', () => {

      document.querySelectorAll('.issue-card').forEach(otherCard => {
        otherCard.classList.remove('selected');
        otherCard.style.background = '#FFFFFF';
        otherCard.style.border = '1.5px solid #E2E8F0';

        const title = otherCard.querySelector('div > div:last-child > div:first-child');
        const description = otherCard.querySelector('div > div:last-child > div:last-child');
        if (title) title.style.color = '#606F95';
        if (description) description.style.color = '#606F95';

        const checkbox = otherCard.querySelector('div > div:first-child > img:last-child');
        if (checkbox) {
          checkbox.style.filter = 'brightness(0) saturate(100%) invert(93%) sepia(5%) saturate(851%) hue-rotate(178deg) brightness(83%) contrast(90%)';
        }

        const cardIcon = otherCard.querySelector('.card-icon');
        if (cardIcon && cardIcon.dataset.normalIcon) {
          cardIcon.src = chrome.runtime.getURL(cardIcon.dataset.normalIcon);
        }
      });

      card.classList.add('selected');
      card.style.background = '#FFFFFF';
      card.style.border = '1.5px solid #470CED';

      const title = card.querySelector('div > div:last-child > div:first-child');
      const description = card.querySelector('div > div:last-child > div:last-child');
      if (title) title.style.color = '#470CED';
      if (description) description.style.color = '#470CED';

      const checkbox = card.querySelector('div > div:first-child > img:last-child');
      if (checkbox) {
        checkbox.style.filter = 'none';
      }

      const cardIcon = card.querySelector('.card-icon');
      if (cardIcon && cardIcon.dataset.activeIcon) {
        cardIcon.src = chrome.runtime.getURL(cardIcon.dataset.activeIcon);
      }

      formValidation.cardSelected = true;
      updateConfirmButtonState();
      saveFormData();
    });
  });

  function loadFormData() {
    chrome.storage.local.get(['confirmUserSettings'], (result) => {
      console.log();

      if (result.confirmUserSettings) {
        const formData = result.confirmUserSettings;

        Object.keys(formValidation).forEach(key => {
          formValidation[key] = false;
        });

        const fieldMapping = {
          'firstName': 'First Name',
          'lastName': 'Last Name',
          'company': 'Company',
          'addressLine1': 'Address Line 1',
          'addressLine2': 'Address Line 2',
          'phoneNumber': 'Phone Number',
          'contactName': 'Contact Name',
          'contactEmail': 'Contact E-mail',
          'city': 'City',
          'state': 'State',
          'zipCode': 'ZIP Code'
        };

        Object.entries(formData).forEach(([key, value]) => {
          if (key !== 'country' && key !== 'selectedIssueCard') {
            const label = fieldMapping[key];
            if (label) {

              const input = Array.from(document.querySelectorAll('input[type="text"]')).find(input => {
                const labelContainer = input.previousElementSibling;
                const labelText = labelContainer?.querySelector('div')?.textContent || 
                                labelContainer?.textContent;
                return labelText?.replace(/\s*Optional\s*$/, '').trim() === label;
              });

              if (input) {
                input.value = value;

                if (requiredInputs[key]) {
                  if (key === 'phoneNumber') {
                    formValidation[key] = isValidPhoneNumber(value);
                  } else if (key === 'contactEmail') {
                    formValidation[key] = isValidEmail(value);
                  } else {
                    formValidation[key] = value.length > 0;
                  }
                }
              }
            }
          }
        });

        if (formData.country && formData.country !== 'Country/Region') {
          const headerText = document.querySelector('.dropdown-header span');
          if (headerText) {
            headerText.textContent = formData.country;
            formValidation.country = true;
          }
        }

        if (formData.selectedIssueCard) {
          const cardMapping = {
            'Stolen Artwork': '.stolen-artwork-card',
            'Copied Design': '.copied-design-card',
            'Copied Listing': '.copied-listing-card'
          };

          const cardSelector = cardMapping[formData.selectedIssueCard];
          if (cardSelector) {
            const card = document.querySelector(cardSelector);
            if (card) {

              card.click();
            }
          }
        } else {

          document.querySelectorAll('.issue-card').forEach(card => {
            card.classList.remove('selected');
            card.style.background = '#FFFFFF';
            card.style.border = '1.5px solid #E2E8F0';

            const title = card.querySelector('div > div:last-child > div:first-child');
            const description = card.querySelector('div > div:last-child > div:last-child');
            if (title) title.style.color = '#606F95';
            if (description) description.style.color = '#606F95';

            const checkbox = card.querySelector('div > div:first-child > img:last-child');
            if (checkbox) {
              checkbox.style.filter = 'brightness(0) saturate(100%) invert(93%) sepia(5%) saturate(851%) hue-rotate(178deg) brightness(83%) contrast(90%)';
            }

            const cardIcon = card.querySelector('.card-icon');
            if (cardIcon && cardIcon.dataset.normalIcon) {
              cardIcon.src = chrome.runtime.getURL(cardIcon.dataset.normalIcon);
            }
          });

          formValidation.cardSelected = false;
          updateConfirmButtonState();
        }

        updateValidationStates(formData);
      }
    });
  }

  function addFormFieldListeners() {

    document.querySelectorAll('input[type="text"]').forEach(input => {
      const labelContainer = input.previousElementSibling;
      const labelText = labelContainer?.querySelector('div')?.textContent || 
                       labelContainer?.textContent;

      if (labelText && !labelText.includes('ASIN')) {
        ['input', 'change', 'blur'].forEach(eventType => {
          input.addEventListener(eventType, () => {
            saveFormData();
          });
        });
      }
    });

    const dropdownHeader = document.querySelector('.dropdown-header');
    if (dropdownHeader) {
      const headerText = dropdownHeader.querySelector('span');
      const observer = new MutationObserver(() => {
        saveFormData();
      });
      observer.observe(headerText, { childList: true, characterData: true, subtree: true });
    }

    document.querySelectorAll('.issue-card').forEach(card => {
      card.addEventListener('click', () => {
        setTimeout(() => {
          saveFormData();
        }, 0);
      });
    });
  }

  addFormFieldListeners();

  loadFormData();

  function saveAsinsToSession() {
    const asinsData = {
      reportedAsins: Array.from(reportedAsins),
      asinMarketplaces: Object.fromEntries(asinMarketplaces),
      productTitles: Object.fromEntries(productTitles),
      productBSRs: Object.fromEntries(productBSRs),
      productPublishDates: Object.fromEntries(productPublishDates)
    };

    chrome.storage.local.set({ snapHammerAsins: asinsData }, () => {
      if (chrome.runtime.lastError) {
        console.error();
      } else {
        console.log();
      }
    });
  }

  function loadAsinsFromSession() {

    chrome.storage.local.get(['snapHammerAsins'], (result) => {
      if (result.snapHammerAsins) {
        const data = result.snapHammerAsins;

        reportedAsins.clear();
        asinMarketplaces.clear();
        productTitles.clear();
        productBSRs.clear();
        productPublishDates.clear();

        data.reportedAsins.forEach(asin => reportedAsins.add(asin));
        Object.entries(data.asinMarketplaces).forEach(([asin, marketplace]) => 
          asinMarketplaces.set(asin, marketplace)
        );
        Object.entries(data.productTitles).forEach(([asin, title]) => 
          productTitles.set(asin, title)
        );
        Object.entries(data.productBSRs).forEach(([asin, bsr]) => 
          productBSRs.set(asin, bsr)
        );
        Object.entries(data.productPublishDates).forEach(([asin, date]) => 
          productPublishDates.set(asin, date)
        );

        const currentMarketplace = getCurrentMarketplace();
        const marketplaceCount = Array.from(reportedAsins).filter(asin => 
          asinMarketplaces.get(asin) === currentMarketplace
        ).length;
        counter.textContent = marketplaceCount;
        counter.style.backgroundColor = marketplaceCount === 0 ? '#FA583A' : '#01BB87';
        asinCounter.textContent = `${marketplaceCount}/50`;
        updateTableView();

        console.log();
      }
    });
  }

  const originalSaveAsins = saveAsins;
  saveAsins = function() {
    originalSaveAsins();
    saveAsinsToSession();
  };

  loadAsinsFromSession();

  function updateConfirmButtonState() {
    const isValid = isFormValid();
    confirmButton.disabled = !isValid;

    if (!isValid) {
      confirmButton.style.background = '#CFD4D4';
      confirmButton.style.opacity = '1';
      confirmButton.style.cursor = 'not-allowed';
      confirmButton.style.pointerEvents = 'none';
      confirmButton.style.color = 'rgba(0, 0, 0, 0.5)';
      const buttonIcon = confirmButton.querySelector('img');
      if (buttonIcon) {
        buttonIcon.style.filter = 'brightness(0)';
        buttonIcon.style.opacity = '0.5';
      }
    } else {
      confirmButton.style.background = '#470CED';
      confirmButton.style.opacity = '1';
      confirmButton.style.cursor = 'pointer';
      confirmButton.style.pointerEvents = 'auto';
      confirmButton.style.color = 'white';
      const buttonIcon = confirmButton.querySelector('img');
      if (buttonIcon) {
        buttonIcon.style.filter = 'brightness(0) invert(1)';
        buttonIcon.style.opacity = '1';
      }
    }
  }

  Object.entries(requiredInputs).forEach(([field, label]) => {
    const input = Array.from(document.querySelectorAll('input[type="text"]')).find(input => 
      input.previousElementSibling?.textContent.includes(label)
    );

    if (input) {
      input.addEventListener('input', (e) => {
        const value = e.target.value.trim();

        if (field === 'phoneNumber') {
          formValidation[field] = isValidPhoneNumber(value);
        } else if (field === 'contactEmail') {
          formValidation[field] = isValidEmail(value);
        } else {
          formValidation[field] = value.length > 0;
        }

        updateConfirmButtonState();
      });

      input.addEventListener('blur', () => {
        const value = input.value.trim();

        if (field === 'phoneNumber') {
          formValidation[field] = isValidPhoneNumber(value);
        } else if (field === 'contactEmail') {
          formValidation[field] = isValidEmail(value);
        } else {
          formValidation[field] = value.length > 0;
        }

        updateConfirmButtonState();
      });
    }
  });

  function loadFormData() {
    chrome.storage.local.get(['confirmUserSettings'], (result) => {
      if (result.confirmUserSettings) {
        const formData = result.confirmUserSettings;
        
        Object.keys(formValidation).forEach(key => {
          if (key !== 'cardSelected') {
            formValidation[key] = false;
          }
        });
        
        Object.entries(requiredInputs).forEach(([field, label]) => {
          const input = Array.from(document.querySelectorAll('input[type="text"]')).find(input => 
            input.previousElementSibling?.textContent.includes(label)
          );
          
          if (input && formData[field]) {
            input.value = formData[field];
            const value = formData[field].trim();
            
            if (field === 'phoneNumber') {
              formValidation[field] = isValidPhoneNumber(value);
            } else if (field === 'contactEmail') {
              formValidation[field] = isValidEmail(value);
            } else {
              formValidation[field] = value.length > 0;
            }
          }
        });

        if (formData.country && formData.country !== 'Country/Region') {
          const headerText = document.querySelector('.dropdown-header span');
          if (headerText) {
            headerText.textContent = formData.country;
            formValidation.country = true;
          }
        }

        const optionalFields = ['company', 'addressLine2', 'state'];
        optionalFields.forEach(field => {
          const input = Array.from(document.querySelectorAll('input[type="text"]')).find(input => 
            input.previousElementSibling?.textContent.includes(field.replace(/([A-Z])/g, ' $1').trim())
          );
          if (input && formData[field]) {
            input.value = formData[field];
          }
        });

        setTimeout(() => {
          updateConfirmButtonState();
        }, 0);
      }
    });
  }

  function verifyButtonDisabledState(button) {
    if (button.disabled) {
      button.style.background = '#CFD4D4';
      button.style.opacity = '1';
      button.style.cursor = 'not-allowed';
      button.style.pointerEvents = 'none';
      button.style.color = 'rgba(0, 0, 0, 0.5)';
      const buttonIcon = button.querySelector('img');
      if (buttonIcon) {
        buttonIcon.style.filter = 'brightness(0)';
        buttonIcon.style.opacity = '0.5';
      }
    }
  }

  const fetchingLoader = document.createElement('div');
  fetchingLoader.id = 'asin-fetching-loader';
  fetchingLoader.style.cssText = `
    position: absolute;
    right: 12px;
    top: calc(50% + 6.5px);
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 1.5px solid rgba(96, 111, 149, 0.1);
    border-radius: 50%;
    border-left-color: #470CED;
    animation: spin 1s linear infinite;
    pointer-events: none;
    display: none;
    box-sizing: border-box;
  `;

  if (!document.querySelector('#spin-keyframes')) {
    const styleSheet = document.createElement('style');
    styleSheet.id = 'spin-keyframes';
    styleSheet.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(styleSheet);
  }

  const asinInputContainer = asinInput.parentElement;
  if (asinInputContainer) {
    asinInputContainer.style.position = 'relative';
    asinInputContainer.appendChild(fetchingLoader);
  }

  let currentSessionAsin = '';

  asinInput.addEventListener('input', (e) => {
    const value = e.target.value.trim();
    formValidation.asin = value.length === 10;
    
    const selectedCard = document.querySelector('.issue-card.selected');
    if (selectedCard) {
        const title = selectedCard.querySelector('div > div:last-child > div:first-child')?.textContent;
        if (title) {
            saveFormData();
        }
    }
    
    if (formValidation.asin) {
      const domain = MARKETPLACE_DOMAINS['US'];
      currentSessionAsin = value;
      
      asinInput.disabled = true;
      asinInput.style.backgroundColor = '#F7F8FA';
      asinInput.style.cursor = 'not-allowed';
      fetchingLoader.style.display = 'block';
      asinInput.style.paddingRight = '36px';
      
      fetch(`https://www.${domain}/dp/${value}`).then(async response => {
        if (!response.ok) throw new Error();
        
        const text = await response.text();
        const parser = new DOMParser();
        const doc = parser.parseFromString(text, 'text/html');
        
        let brand = 'N/A';
        console.log();
        
        const titleBlock = doc.querySelector('#titleBlockLeftSection');
        console.log();
        if (titleBlock) {
          const brandLink = titleBlock.querySelector('a#bylineInfo');
          console.log();
          if (brandLink) {
            if (brandLink.textContent.includes('Brand:')) {
              brand = brandLink.textContent.split('Brand:')[1].trim();
            } else {
              brand = brandLink.textContent.trim();
            }
            console.log();
          }
        }

        if (brand === 'N/A') {
          const bylineInfo = doc.querySelector('#bylineInfo');
          console.log();
          if (bylineInfo) {
            const brandLink = bylineInfo.querySelector('a');
            console.log();
            if (brandLink) {
              brand = brandLink.textContent.trim();
              console.log();
            } else if (bylineInfo.textContent) {
              brand = bylineInfo.textContent.trim();
              console.log();
            }
          }
        }

        if (brand === 'N/A') {
          const brandLink = doc.querySelector('a[href*="field-brandtextbin"]');
          console.log();
          if (brandLink && brandLink.href) {
            try {
              const url = new URL(brandLink.href);
              const brandParam = url.searchParams.get('field-brandtextbin');
              if (brandParam) {
                brand = decodeURIComponent(brandParam).replace(/\+/g, ' ');
                console.log();
              }
            } catch (e) {
              console.error();
            }
          }
        }

        if (brand === 'N/A') {
          console.log();
          const elements = Array.from(doc.querySelectorAll('*'));
          for (const element of elements) {
            if (element.textContent && element.textContent.includes('Brand:')) {
              brand = element.textContent.split('Brand:')[1].trim();
              console.log();
              console.log();
              break;
            }
          }
        }

        if (brand === 'N/A') {
          console.log();
          const detailsTable = doc.querySelector('#productDetails_detailBullets_sections1');
          if (detailsTable) {
            const rows = detailsTable.querySelectorAll('tr');
            rows.forEach(row => {
              const label = row.querySelector('th');
              if (label && label.textContent.includes('Brand')) {
                const value = row.querySelector('td');
                if (value) {
                  brand = value.textContent.trim();
                  console.log();
                }
              }
            });
          }
        }

        if (brand !== 'N/A') {
          const originalBrand = brand;
          brand = brand
            .replace(/^Visit the |\sStore$/g, '')  
            .replace(/Brand:\s*/i, '')            
            .replace(/\s+/g, ' ')                 
            .trim();
          console.log();
        }

        console.log();
        
        let date = 'N/A';
        const detailBullets = doc.querySelector('#detailBullets_feature_div');
        if (detailBullets) {
          const listItems = detailBullets.querySelectorAll('li');
          for (const item of listItems) {
            const itemText = item.textContent.trim();
            if (itemText.includes('Date First Available')) {
              const dateText = itemText.split(':')[1]?.trim();
              if (dateText) {
                try {
                  const parsedDate = new Date(dateText);
                  if (!isNaN(parsedDate.getTime())) {
                    date = parsedDate.toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric'
                    });
                  }
                } catch (e) {
                  console.error();
                }
              }
              break;
            }
          }
        }
        
        const dataToSave = {
          asin: value,
          brand: brand,
          date: date
        };
        console.log();

        if (brand === 'N/A' && doc.documentElement.innerHTML.includes('Brand')) {
          console.log();

          const allText = doc.documentElement.textContent;
          const brandMatch = allText.match(/Brand:?\s*([^"\n\r]+)/);
          if (brandMatch && brandMatch[1]) {
            brand = brandMatch[1].trim()
              .replace(/^Visit the |\sStore$/g, '')
              .replace(/Brand:\s*/i, '')
              .replace(/\s+/g, ' ')
              .trim();
            console.log();
            dataToSave.brand = brand;
          }
        }

        try {
          chrome.storage.local.set({
            originalAsinData: JSON.stringify(dataToSave)
          }, () => {
            if (chrome.runtime.lastError) {
              console.error();
            } else {

              saveFormData();
            }
          });
        } catch (e) {
          console.error();
          chrome.storage.local.set({
            originalAsinData: JSON.stringify({
              asin: value,
              brand: 'N/A',
              date: 'N/A'
            })
          });

          saveFormData();
        }

      }).catch(error => {
        console.error();
        chrome.storage.local.set({
          originalAsinData: JSON.stringify({
            asin: value,
            brand: 'N/A',
            date: 'N/A'
          })
        });

        saveFormData();
      }).finally(() => {

        asinInput.disabled = false;
        asinInput.style.backgroundColor = 'white';
        asinInput.style.cursor = 'text';
        fetchingLoader.style.display = 'none';
        asinInput.style.paddingRight = '12px';

        saveFormData();
      });
    }

    updateConfirmButtonState();
  });

  asinInput.addEventListener('change', () => {
    const value = asinInput.value.trim();
    formValidation.asin = value.length === 10;

    if (formValidation.asin && value !== currentSessionAsin) {
      fetchProductTitle(value).then(result => {
        if (result.isValid) {
          console.log();
        }
      }).catch(error => {
        console.error();
      });
    }

    currentSessionAsin = value;

    setTimeout(() => {
        updateConfirmButtonState();
        verifyButtonDisabledState(confirmButton);
    }, 0);
  });

  asinInput.addEventListener('blur', () => {
    const value = asinInput.value.trim();
    formValidation.asin = value.length === 10;

    if (formValidation.asin && value !== currentSessionAsin) {
      fetchProductTitle(value).then(result => {
        if (result.isValid) {
          console.log();
        }
      }).catch(error => {
        console.error();
      });
    }

    currentSessionAsin = value;
    updateConfirmButtonState();
  });

  function hidePopup() {
    overlay.style.display = 'none';
    snapHammerPopup.style.display = 'none';
    confirmProductsSection.style.display = 'none';
    validationSection.style.display = 'none';
    document.body.style.overflow = '';

    if (validationInput) {
      validationInput.value = '';
      validateButton.style.background = '#CFD4D4';
      validateButton.style.opacity = '1';
      validateButton.style.cursor = 'not-allowed';
      validateButton.style.pointerEvents = 'none';
      validateButton.style.color = 'rgba(0, 0, 0, 0.5)';
      validateButton.disabled = true;
      validateIcon.style.filter = 'brightness(0)';
      validateIcon.style.opacity = '0.5';
    }
  }

  function showConfirmProductsSection() {
    const confirmButton = confirmProductsSection.querySelector('.confirmBtn');
    if (confirmButton) {
      confirmButton.style.position = 'static';
      confirmButton.style.bottom = 'auto';
      confirmButton.style.left = 'auto';
      confirmButton.style.transform = 'none';
      confirmButton.style.margin = '20px 0 0 auto';
      confirmButton.style.width = 'auto';
    }
    
    snapHammerPopup.style.display = 'none';
    confirmProductsSection.style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    loadFormData();
    
    if (asinInput) {
      asinInput.value = '';
      formValidation.asin = false;
    }
    
    setTimeout(() => {
      updateConfirmButtonState();
      verifyButtonDisabledState(confirmButton);
    }, 100);
  }

  chrome.storage.local.get(['confirmUserSettings'], (result) => {
    if (result.confirmUserSettings) {
      const formData = result.confirmUserSettings;
      console.log();

      Object.entries(formData).forEach(([key, value]) => {
        if (key !== 'country' && key !== 'selectedIssueCard') {
          const label = fieldMapping[key];
          if (label) {
            const input = Array.from(document.querySelectorAll('input[type="text"]')).find(input => {
              const inputLabel = input.previousElementSibling?.querySelector('div')?.textContent || 
                               input.previousElementSibling?.textContent;
              return inputLabel?.includes(label);
            });

            if (input) {
              input.value = value;

              if (requiredInputs[key]) {
                if (key === 'phoneNumber') {
                  formValidation[key] = isValidPhoneNumber(value);
                } else if (key === 'contactEmail') {
                  formValidation[key] = isValidEmail(value);
                } else {
                  formValidation[key] = value.length > 0;
                }
              }
            }
          }
        }
      });

      if (formData.country && formData.country !== 'Country/Region') {
        const headerText = document.querySelector('.dropdown-header span');
        if (headerText) {
          headerText.textContent = formData.country;
          formValidation.country = true;
        }
      }

      if (formData.selectedIssueCard) {
        const cardMapping = {
          'Stolen Artwork': '.stolen-artwork-card',
          'Copied Design': '.copied-design-card',
          'Copied Listing': '.copied-listing-card'
        };

        const cardSelector = cardMapping[formData.selectedIssueCard];
        if (cardSelector) {
          const card = document.querySelector(cardSelector);
          if (card) {

            card.click();
          }
        }
      } else {

        document.querySelectorAll('.issue-card').forEach(card => {
          card.classList.remove('selected');
          card.style.background = '#FFFFFF';
          card.style.border = '1.5px solid #E2E8F0';

          const title = card.querySelector('div > div:last-child > div:first-child');
          const description = card.querySelector('div > div:last-child > div:last-child');
          if (title) title.style.color = '#606F95';
          if (description) description.style.color = '#606F95';

          const checkbox = card.querySelector('div > div:first-child > img:last-child');
          if (checkbox) {
            checkbox.style.filter = 'brightness(0) saturate(100%) invert(93%) sepia(5%) saturate(851%) hue-rotate(178deg) brightness(83%) contrast(90%)';
          }

          const cardIcon = card.querySelector('.card-icon');
          if (cardIcon && cardIcon.dataset.normalIcon) {
            cardIcon.src = chrome.runtime.getURL(cardIcon.dataset.normalIcon);
          }
        });

        formValidation.cardSelected = false;
        updateConfirmButtonState();
      }

      updateValidationStates(formData);
    }
  });

  function getCurrentFormData() {
    const formData = {};

    document.querySelectorAll('input[type="text"]').forEach(input => {
        const labelContainer = input.previousElementSibling;
        if (labelContainer) {
            const labelText = labelContainer.querySelector('div')?.textContent || labelContainer.textContent;

            if (labelText && !labelText.includes('ASIN')) {
                const key = labelText.replace(/\s*Optional\s*$/, '').trim();
                switch(key) {
                    case 'First Name': formData.firstName = input.value; break;
                    case 'Last Name': formData.lastName = input.value; break;
                    case 'Company': formData.company = input.value; break;
                    case 'Address Line 1': formData.addressLine1 = input.value; break;
                    case 'Address Line 2': formData.addressLine2 = input.value; break;
                    case 'Phone Number': formData.phoneNumber = input.value; break;
                    case 'Contact Name': formData.contactName = input.value; break;
                    case 'Contact E-mail': formData.contactEmail = input.value; break;
                    case 'City': formData.city = input.value; break;
                    case 'State': formData.state = input.value; break;
                    case 'ZIP Code': formData.zipCode = input.value; break;
                }
            }
        }
    });

    const countryText = document.querySelector('.dropdown-header span')?.textContent;
    formData.country = countryText || '';

    const selectedCard = document.querySelector('.issue-card.selected');
    if (selectedCard) {

        if (selectedCard.classList.contains('stolen-artwork-card')) {
            formData.selectedIssueCard = 'Stolen Artwork';
        } else if (selectedCard.classList.contains('copied-design-card')) {
            formData.selectedIssueCard = 'Copied Design';
        } else if (selectedCard.classList.contains('copied-listing-card')) {
            formData.selectedIssueCard = 'Copied Listing';
        }
    }

    return formData;
  }

  function saveFormDataImmediately() {
    const formData = getCurrentFormData();
    chrome.storage.local.set({ confirmUserSettings: formData }, () => {
      if (chrome.runtime.lastError) {
        console.error();
      }
    });
  }

  function addRealTimeListeners() {

    document.querySelectorAll('input[type="text"]').forEach(input => {
      input.addEventListener('input', saveFormDataImmediately);
      input.addEventListener('change', saveFormDataImmediately);
      input.addEventListener('blur', saveFormDataImmediately);
    });

    const dropdownHeader = document.querySelector('.dropdown-header');
    if (dropdownHeader) {
      const observer = new MutationObserver(saveFormDataImmediately);
      observer.observe(dropdownHeader.querySelector('span'), {
        childList: true,
        characterData: true,
        subtree: true
      });

      const dropdownItems = document.querySelectorAll('.dropdown-item');
      dropdownItems.forEach(item => {
        item.addEventListener('click', () => {
          setTimeout(saveFormDataImmediately, 0);
        });
      });
    }

    document.querySelectorAll('.issue-card').forEach(card => {
      card.addEventListener('click', () => {
        setTimeout(saveFormDataImmediately, 0);
      });
    });
  }

  function loadFormData() {
    chrome.storage.local.get(['confirmUserSettings'], (result) => {
      if (result.confirmUserSettings) {
        const formData = result.confirmUserSettings;

        document.querySelectorAll('input[type="text"]').forEach(input => {
          const labelContainer = input.previousElementSibling;
          if (labelContainer) {
            const labelText = labelContainer.querySelector('div')?.textContent || labelContainer.textContent;
            if (labelText && !labelText.includes('ASIN')) {
              const key = labelText.replace(/\s*Optional\s*$/, '').trim();
              let value = '';
              switch(key) {
                case 'First Name': value = formData.firstName; break;
                case 'Last Name': value = formData.lastName; break;
                case 'Company': value = formData.company; break;
                case 'Address Line 1': value = formData.addressLine1; break;
                case 'Address Line 2': value = formData.addressLine2; break;
                case 'Phone Number': value = formData.phoneNumber; break;
                case 'Contact Name': value = formData.contactName; break;
                case 'Contact E-mail': value = formData.contactEmail; break;
                case 'City': value = formData.city; break;
                case 'State': value = formData.state; break;
                case 'ZIP Code': value = formData.zipCode; break;
              }
              input.value = value || '';
            }
          }
        });

        if (formData.country) {
          const headerText = document.querySelector('.dropdown-header span');
          if (headerText) {
            headerText.textContent = formData.country;
          }
        }

        if (formData.selectedIssueCard) {
          const cardMapping = {
            'Stolen Artwork': '.stolen-artwork-card',
            'Copied Design': '.copied-design-card',
            'Copied Listing': '.copied-listing-card'
          };

          const cardSelector = cardMapping[formData.selectedIssueCard];
          if (cardSelector) {
            const card = document.querySelector(cardSelector);
            if (card) {

              card.click();
            }
          }
        } else {

          document.querySelectorAll('.issue-card').forEach(card => {
            card.classList.remove('selected');
            card.style.background = '#FFFFFF';
            card.style.border = '1.5px solid #E2E8F0';

            const title = card.querySelector('div > div:last-child > div:first-child');
            const description = card.querySelector('div > div:last-child > div:last-child');
            if (title) title.style.color = '#606F95';
            if (description) description.style.color = '#606F95';

            const checkbox = card.querySelector('div > div:first-child > img:last-child');
            if (checkbox) {
              checkbox.style.filter = 'brightness(0) saturate(100%) invert(93%) sepia(5%) saturate(851%) hue-rotate(178deg) brightness(83%) contrast(90%)';
            }

            const cardIcon = card.querySelector('.card-icon');
            if (cardIcon && cardIcon.dataset.normalIcon) {
              cardIcon.src = chrome.runtime.getURL(cardIcon.dataset.normalIcon);
            }
          });

          formValidation.cardSelected = false;
          updateConfirmButtonState();
        }

        updateValidationStates(formData);
      }
    });
  }

  function updateValidationStates(formData) {

    Object.keys(formValidation).forEach(key => {
      formValidation[key] = false;
    });

    if (formData.firstName) formValidation.firstName = true;
    if (formData.lastName) formValidation.lastName = true;
    if (formData.addressLine1) formValidation.addressLine1 = true;
    if (formData.city) formValidation.city = true;
    if (formData.zipCode) formValidation.zipCode = true;
    if (formData.phoneNumber) formValidation.phoneNumber = isValidPhoneNumber(formData.phoneNumber);
    if (formData.contactName) formValidation.contactName = true;
    if (formData.contactEmail) formValidation.contactEmail = isValidEmail(formData.contactEmail);
    if (formData.country && formData.country !== 'Country/Region') formValidation.country = true;
    if (formData.selectedIssueCard) formValidation.cardSelected = true;

    setTimeout(() => {
      updateConfirmButtonState();
      verifyButtonDisabledState(confirmButton);
    }, 0);
  }

  addRealTimeListeners();

  async function fetchOriginalAsinData(asin, marketplace = null) {
    try {

        const storageResult = await new Promise((resolve) => {
            chrome.storage.local.get(['originalAsinData'], (result) => {
                resolve(result);
            });
        });

        if (storageResult.originalAsinData) {
            const savedData = JSON.parse(storageResult.originalAsinData);
            if (savedData.asin === asin && savedData.brand !== 'N/A') {
                console.log();
                return {
                    brand: savedData.brand,
                    date: savedData.date,
                    isValid: true
                };
            }
        }

        if (!marketplace) {
            marketplace = getCurrentMarketplace();
        }

        const domain = MARKETPLACE_DOMAINS[marketplace];
        if (!domain) {
            return {
                brand: 'N/A',
                date: 'N/A',
                isValid: false
            };
        }
        console.log();
        
        const response = await fetch(`https://www.${domain}/dp/${asin}`);
        
        if (!response.ok) {
            console.log();
            return {
                brand: 'N/A',
                date: 'N/A',
                isValid: false
            };
        }

        const text = await response.text();
        const parser = new DOMParser();
        const doc = parser.parseFromString(text, 'text/html');

        let brand = 'N/A';
        
        const titleBlock = doc.querySelector('#titleBlockLeftSection');
        if (titleBlock) {
            const brandLink = titleBlock.querySelector('a#bylineInfo');
            if (brandLink) {
                if (brandLink.textContent.includes('Brand:')) {
                    brand = brandLink.textContent.split('Brand:')[1].trim();
                } else {
                    brand = brandLink.textContent.trim();
                }
            }
        }

        if (brand === 'N/A') {
            const bylineInfo = doc.querySelector('#bylineInfo');
            if (bylineInfo) {
                const brandLink = bylineInfo.querySelector('a');
                if (brandLink) {
                    brand = brandLink.textContent.trim();
                } else if (bylineInfo.textContent) {
                    brand = bylineInfo.textContent.trim();
                }
            }
        }

        if (brand === 'N/A') {
            const brandLink = doc.querySelector('a[href*="field-brandtextbin"]');
            if (brandLink && brandLink.href) {
                try {
                    const url = new URL(brandLink.href);
                    const brandParam = url.searchParams.get('field-brandtextbin');
                    if (brandParam) {
                        brand = decodeURIComponent(brandParam).replace(/\+/g, ' ');
                    }
                } catch (e) {
                    console.error();
                }
            }
        }

        if (brand !== 'N/A') {
            brand = brand
                .replace(/^Visit the |\sStore$/g, '')
                .replace(/Brand:\s*/i, '')
                .replace(/\s+/g, ' ')
                .trim();
        }

        let date = 'N/A';
        const detailBullets = doc.querySelector('#detailBullets_feature_div');
        if (detailBullets) {
            const listItems = detailBullets.querySelectorAll('li');
            for (const item of listItems) {
                const itemText = item.textContent.trim();
                if (itemText.includes('Date First Available')) {
                    const dateText = itemText.split(':')[1]?.trim();
                    if (dateText) {
                        try {
                            let parsedDate;
                            if (/[A-Za-z]+ \d{1,2},? \d{4}/.test(dateText)) {
                                parsedDate = new Date(dateText);
                            } 
                            else if (/\d{1,2} [A-Za-z]+ \d{4}/.test(dateText)) {
                                const [day, month, year] = dateText.match(/(\d{1,2}) ([A-Za-z]+) (\d{4})/).slice(1);
                                parsedDate = new Date(`${month} ${day}, ${year}`);
                            }
                            else if (/\d{1,2} [A-Za-z]+\. \d{4}/.test(dateText)) {
                                const [day, monthAbbr, year] = dateText.match(/(\d{1,2}) ([A-Za-z]+)\. (\d{4})/).slice(1);
                                const month = monthAbbr.replace('.', '');
                                parsedDate = new Date(`${month} ${day}, ${year}`);
                            }
                            else {
                                parsedDate = new Date(dateText);
                            }

                            if (!isNaN(parsedDate.getTime())) {
                                date = parsedDate.toLocaleDateString('en-US', {
                                    year: 'numeric',
                                    month: 'short',
                                    day: 'numeric'
                                });
                            }
                        } catch (e) {
                            console.error();
                        }
                    }
                    break;
                }
            }
        }

        return {
            brand,
            date,
            isValid: true,
            marketplace
        };

    } catch (error) {
        console.error();
        return {
            brand: 'N/A',
            date: 'N/A',
            isValid: false
        };
    }
}

  const validationSection = document.createElement('div');
  validationSection.id = 'validationSection';
  validationSection.className = 'validation-section';
  validationSection.style.cssText = `
    display: none;
    flex-direction: column;
    width: 994px;
    height: 96vh;
    background: #F7F8FA;
    border-radius: 28px;
    padding: 40px;
    z-index: 999999;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 4px 24px rgba(0,0,0,0.1);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #DCE0E5 transparent;
    scroll-behavior: smooth;
    overscroll-behavior: contain;
  `;

  const validationTop = document.createElement('div');
  validationTop.className = 'validation-top';
  validationTop.style.cssText = `
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 32px;
    height: 36px;
  `;

  const validationLeft = document.createElement('div');
  validationLeft.className = 'validation-left';
  const validationBackButton = document.createElement('button');
  validationBackButton.style.cssText = `
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    width: 122px;
    height: 36px;
    background: #470CED;
    border: none;
    border-radius: 6px;
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 14px;
    color: #F8FAFC;
    cursor: pointer;
`;

  const validationBackIcon = document.createElement('img');
  validationBackIcon.src = chrome.runtime.getURL('assets/back-ic.svg');
  validationBackIcon.style.cssText = `
    width: 20px;
    height: 20px;
    filter: brightness(0) invert(1);
`;

  const validationBackText = document.createElement('span');
  validationBackText.textContent = 'Back';
  validationBackText.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 14px;
    color: #F8FAFC;
    line-height: 24px;
    display: flex;
    align-items: center;
`;
  validationBackButton.appendChild(validationBackIcon);
  validationBackButton.appendChild(validationBackText);
  validationLeft.appendChild(validationBackButton);

  validationBackButton.addEventListener('mouseenter', () => {
    validationBackButton.style.background = '#3D0BCE';
  });

  validationBackButton.addEventListener('mouseleave', () => {
    validationBackButton.style.background = '#470CED';
  });

  const validationRight = document.createElement('div');
  validationRight.className = 'validation-right';
  validationRight.style.cssText = `
    display: flex;
    align-items: center;
    gap: 24px;
  `;

  const validationStepper = document.createElement('div');
  validationStepper.style.cssText = `
    display: flex;
    align-items: center;
    gap: 16px;
  `;

  const validationSteps = [
    { number: '01', text: 'Add Infringing ASINs', state: 'completed' },
    { number: '02', text: 'Validate your ASIN', state: 'active' },
    { number: '03', text: 'Confirm & Report', state: 'inactive' }
  ];

  validationSteps.forEach((step, index) => {
    const stepContainer = document.createElement('div');
    stepContainer.style.cssText = `
      display: flex;
      align-items: center;
      gap: 8px;
    `;

    if (step.state === 'completed') {
      const checkIcon = document.createElement('img');
      checkIcon.src = chrome.runtime.getURL('assets/loaded.svg');
      checkIcon.style.cssText = `
        width: 24px;
        height: 24px;
      `;
      stepContainer.appendChild(checkIcon);
    } else {
      const numberCircle = document.createElement('div');
      numberCircle.style.cssText = `
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'Amazon Ember';
        font-weight: 500;
        font-size: 12px;
        background: ${step.state === 'active' ? '#470CED' : '#E9EBF2'};
        color: ${step.state === 'active' ? 'white' : '#606F95'};
      `;
      numberCircle.textContent = step.number;
      stepContainer.appendChild(numberCircle);
    }

    const stepText = document.createElement('span');
    stepText.style.cssText = `
      font-family: 'Amazon Ember';
      font-weight: 500;
      font-size: 14px;
      color: ${step.state === 'completed' ? '#0D0B26' : 
              step.state === 'active' ? '#470CED' : 
              '#606F95'};
    `;
    stepText.textContent = step.text;
    stepContainer.appendChild(stepText);

    if (index < validationSteps.length - 1) {
      const separator = document.createElement('div');
      separator.style.cssText = `
        width: 24px;
        height: 1px;
        background: ${step.state === 'completed' ? '#470CED' : '#E9EBF2'};
        margin: 0 4px;
      `;
      stepContainer.appendChild(separator);
    }

    validationStepper.appendChild(stepContainer);
  });

  const validationCloseButton = document.createElement('button');
  validationCloseButton.style.cssText = `
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  `;

  const validationCloseIcon = document.createElement('img');
  validationCloseIcon.src = chrome.runtime.getURL('assets/close-ham-popup-ic.svg');
  validationCloseIcon.style.cssText = `
    width: 24px;
    height: 24px;
  `;

  validationCloseButton.appendChild(validationCloseIcon);
  validationRight.appendChild(validationStepper);
  validationRight.appendChild(validationCloseButton);

  validationTop.appendChild(validationLeft);
  validationTop.appendChild(validationRight);

  const validationContent = document.createElement('div');
  validationContent.style.cssText = `
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
  `;

  const validationBadge = document.createElement('div');
  validationBadge.textContent = 'ASIN VALIDATION';
  validationBadge.style.cssText = `
    padding: 4px 12px;
    background: rgba(71, 12, 237, 0.1);
    border-radius: 999px;
    font-family: 'Amazon Ember';
    font-size: 12px;
    font-weight: 500;
    color: #470CED;
    margin-bottom: 16px;
  `;

  const validationHeading = document.createElement('h2');
  validationHeading.textContent = 'What is your ASIN?';
  validationHeading.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 700;
    font-size: 24px;
    color: #1F2937;
    margin: 0 0 24px 0;
  `;

  const validationInputContainer = document.createElement('div');
  validationInputContainer.style.cssText = `
    width: 100%;
    max-width: 400px;
    margin-bottom: 24px;
  `;

  const validationInput = document.createElement('input');
  validationInput.type = 'text';
  validationInput.placeholder = 'Enter your ASIN';
  validationInput.id = 'originalasininput';  
  validationInput.style.cssText = `
    width: 100%;
    height: 40px;
    padding: 0 12px;
    border: 1.5px solid #DCE0E5;
    border-radius: 4px;
    font-family: 'Amazon Ember';
    font-size: 14px;
    color: #1F2937;
    transition: border-color 0.2s ease;
    outline: none;
    box-shadow: none;
  `;

  validationInput.addEventListener('focus', () => {
    validationInput.style.borderColor = '#470CED';
  });

  validationInput.addEventListener('blur', () => {
    validationInput.style.borderColor = '#DCE0E5';
  });

  validationInputContainer.appendChild(validationInput);

  const validateButton = document.createElement('button');
  validateButton.style.cssText = `
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    max-width: 400px;
    height: 48px;
    background: #CFD4D4;
    border: none;
    border-radius: 6px;
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.5);
    cursor: not-allowed;
    transition: all 0.2s ease;
    opacity: 1;
    pointer-events: none;
  `;

  const validateIcon = document.createElement('img');
  validateIcon.src = chrome.runtime.getURL('assets/apply.svg');
  validateIcon.style.cssText = `
    width: 20px;
    height: 20px;
    filter: brightness(0);
    opacity: 0.5;
  `;

  validateButton.appendChild(validateIcon);
  validateButton.appendChild(document.createTextNode('Validate your ASIN'));

  validationInput.addEventListener('input', (e) => {
    const value = e.target.value.trim();
    const isValid = value.length === 10;

    if (isValid) {
        validateButton.style.background = '#470CED';
        validateButton.style.opacity = '1';
        validateButton.style.cursor = 'pointer';
        validateButton.style.pointerEvents = 'auto';
        validateButton.style.color = 'white';
        validateButton.disabled = false;
        validateButton.style.filter = 'none';
        validateIcon.style.filter = 'brightness(0) invert(1)';
        validateIcon.style.opacity = '1';

        validateButton.addEventListener('mouseenter', () => {
            validateButton.style.background = '#3D0BCE';
        });
        validateButton.addEventListener('mouseleave', () => {
            validateButton.style.background = '#470CED';
        });
    } else {
        validateButton.style.background = '#CFD4D4';
        validateButton.style.opacity = '1';
        validateButton.style.cursor = 'not-allowed';
        validateButton.style.pointerEvents = 'none';
        validateButton.style.color = 'rgba(0, 0, 0, 0.5)';
        validateButton.disabled = true;
        validateIcon.style.filter = 'brightness(0)';
        validateIcon.style.opacity = '0.5';
    }
  });

  validationContent.appendChild(validationBadge);
  validationContent.appendChild(validationHeading);
  validationContent.appendChild(validationInputContainer);
  validationContent.appendChild(validateButton);

  validationSection.appendChild(validationTop);
  validationSection.appendChild(validationContent);

  document.body.appendChild(validationSection);

  reportButton.addEventListener('click', () => {
    if (!reportButton.disabled) {
      snapHammerPopup.style.display = 'none';
      validationSection.style.display = 'flex';
    }
  });

  validationBackButton.addEventListener('click', () => {
    validationSection.style.display = 'none';
    snapHammerPopup.style.display = 'flex';
  });

  validationCloseButton.addEventListener('click', hidePopup);  

  validationBackButton.replaceWith(validationBackButton.cloneNode(true));
  const newBackBtn = document.querySelector('.validation-section button:has(img[src*="back-ic.svg"])');
  newBackBtn.addEventListener('click', () => {
    validationSection.style.display = 'none';
    snapHammerPopup.style.display = 'flex';
  });

  validationCloseButton.replaceWith(validationCloseButton.cloneNode(true));
  const newCloseBtn = document.querySelector('.validation-section button:has(img[src*="close-ham-popup-ic.svg"])');
  newCloseBtn.addEventListener('click', hidePopup);  

  const validationUnsuccessful = document.createElement('div');
  validationUnsuccessful.style.cssText = `
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: #F7F8FA;
    z-index: 2;
    padding: 40px;
    border-radius: 28px;
  `;

  const unsuccessfulIcon = document.createElement('img');
  unsuccessfulIcon.src = chrome.runtime.getURL('assets/vali-fetch-error-img.svg');
  unsuccessfulIcon.style.cssText = `
    width: 80px;
    height: 91px;
    margin-bottom: 24px;
  `;

  const unsuccessfulHeading = document.createElement('h2');
  unsuccessfulHeading.textContent = 'Validation Unsuccessful';
  unsuccessfulHeading.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 700;
    font-size: 16px;
    line-height: 19.2px;
    color: #606F95;
    margin: 0 0 4px 0;
    text-align: center;
  `;

  const unsuccessfulMessage = document.createElement('p');
  unsuccessfulMessage.textContent = `One or more ASINs were listed before your product's publication date, You can only report listings that copied your original work.`;
  unsuccessfulMessage.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
    color: #606F95;
    margin: 0 0 24px 0;
    text-align: center;
    max-width: 450px;
  `;

  const doneButton = document.createElement('button');
  doneButton.style.cssText = `
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    height: 42px;
    background: #470CED;
    border: none;
    border-radius: 8px;
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: white;
    cursor: pointer;
    transition: background 0.2s ease;
    width: 403px;
  `;
  doneButton.textContent = 'Done';

  doneButton.addEventListener('mouseenter', () => {
    doneButton.style.background = '#2A00A0';
  });

  doneButton.addEventListener('mouseleave', () => {
    doneButton.style.background = '#470CED';
  });

  doneButton.addEventListener('click', () => {
    validationUnsuccessful.style.display = 'none';
    validationContent.style.display = 'flex';
    validationInput.value = '';
    validateButton.style.background = '#CFD4D4';
    validateButton.style.opacity = '1';
    validateButton.style.cursor = 'not-allowed';
    validateButton.style.pointerEvents = 'none';
    validateButton.style.color = 'rgba(0, 0, 0, 0.5)';
    validateButton.disabled = true;
    validateIcon.style.filter = 'brightness(0)';
    validateIcon.style.opacity = '0.5';
  });

  validationUnsuccessful.appendChild(unsuccessfulIcon);
  validationUnsuccessful.appendChild(unsuccessfulHeading);
  validationUnsuccessful.appendChild(unsuccessfulMessage);
  validationUnsuccessful.appendChild(doneButton);

  validationSection.appendChild(validationUnsuccessful);

  const validationLoader = document.createElement('div');
  validationLoader.style.cssText = `
    position: absolute;
    inset: 0;
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.95);
    z-index: 1;
    border-radius: 28px;
  `;

  const validationSpinner = document.createElement('div');
  validationSpinner.style.cssText = `
    width: 40px;
    height: 40px;
    border: 4px solid rgba(96, 111, 149, 0.1);
    border-radius: 50%;
    border-left-color: #470CED;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  `;

  const validationLoadingText = document.createElement('div');
  validationLoadingText.textContent = 'Fetching ASIN\'s data...';
  validationLoadingText.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 16px;
    color: #606F95;
  `;

  validationLoader.appendChild(validationSpinner);
  validationLoader.appendChild(validationLoadingText);
  validationSection.appendChild(validationLoader);

  validateButton.addEventListener('click', async () => {
    const asin = validationInput.value.trim();
    if (asin.length === 10) {

        validationLoadingText.textContent = `Fetching ASIN's data...`;
        validationLoader.style.display = 'flex';
        validationContent.style.opacity = '0.5';
        validationContent.style.pointerEvents = 'none';

        try {

            const currentMarketplace = getCurrentMarketplace();
            console.log();

            const isReportedInCurrentMarketplace = Array.from(reportedAsins).some(reportedAsin => 
                reportedAsin === asin && asinMarketplaces.get(reportedAsin) === currentMarketplace
            );

            if (isReportedInCurrentMarketplace) {
                validationContent.style.display = 'none';
                asinConflictState.style.display = 'flex';
                return;
            }

            const originalAsinData = await fetchOriginalAsinData(asin, currentMarketplace);
            console.log();

            validationLoadingText.textContent = `Validating ASIN's data...`;

            if (!originalAsinData.date || originalAsinData.date === 'N/A') {
                validationContent.style.display = 'none';
                fetchErrorState.style.display = 'flex';
                return;
            }

            const marketplaceAsins = Array.from(reportedAsins).filter(reportedAsin => 
                asinMarketplaces.get(reportedAsin) === currentMarketplace
            );
            console.log(`Found ${marketplaceAsins.length} ASINs in ${currentMarketplace} marketplace`);

            const reportedAsinsData = [];
            for (const reportedAsin of marketplaceAsins) {
                const data = await fetchProductTitle(reportedAsin, currentMarketplace);
                reportedAsinsData.push({
                    asin: reportedAsin,
                    date: data.published
                });

                await new Promise(resolve => setTimeout(resolve, 500));
            }

            function standardizeDate(dateStr) {
                console.log();
                if (!dateStr || dateStr === 'N/A') {
                    return new Date(0);
                }

                try {

                    return new Date(dateStr);
                } catch (e) {
                    console.error();
                    return new Date(0);
                }
            }

            const originalDate = standardizeDate(originalAsinData.date);
            console.log();

            let hasNewerAsin = false;
            for (const data of reportedAsinsData) {
                if (data.date === 'N/A') continue;
                const reportedDate = standardizeDate(data.date);
                console.log();

                if (reportedDate < originalDate) {
                    hasNewerAsin = true;
                    console.log();
                    break;
                }
            }

            if (hasNewerAsin) {
                validationContent.style.display = 'none';
                validationUnsuccessful.style.display = 'flex';
            } else {

                chrome.storage.local.set({
                    originalAsinData: JSON.stringify({
                        asin: asin,
                        brand: originalAsinData.brand,
                        date: originalAsinData.date,
                        marketplace: currentMarketplace 
                    })
                }, () => {
                    validationSection.style.display = 'none';
                    showConfirmProductsSection();
                });
            }
        } catch (error) {
            console.error();
            validationContent.style.display = 'none';
            fetchErrorState.style.display = 'flex';
        } finally {
            validationLoader.style.display = 'none';
            validationContent.style.opacity = '1';
            validationContent.style.pointerEvents = 'auto';
        }
    }
  });

  const fetchErrorState = document.createElement('div');
  fetchErrorState.style.cssText = `
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: #F7F8FA;
    z-index: 2;
    padding: 40px;
    border-radius: 28px;
  `;

  const fetchErrorIcon = document.createElement('img');
  fetchErrorIcon.src = chrome.runtime.getURL('assets/vali-fetch-error-img.svg');
  fetchErrorIcon.style.cssText = `
    width: 80px;
    height: 91px;
    margin-bottom: 24px;
  `;

  const fetchErrorHeading = document.createElement('h2');
  fetchErrorHeading.textContent = `Unable to fetch your ASIN's data`;
  fetchErrorHeading.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 700;
    font-size: 16px;
    line-height: 19.2px;
    color: #606F95;
    margin: 0 0 4px 0;
    text-align: center;
  `;

  const fetchErrorMessage = document.createElement('p');
  fetchErrorMessage.innerHTML = 'Product information unavailable. Ensure the site is in English<br>and the ASIN exists in the marketplace.';
  fetchErrorMessage.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
    color: #606F95;
    margin: 0 0 24px 0;
    text-align: center;
    max-width: 450px;
  `;

  const tryAgainButton = document.createElement('button');
  tryAgainButton.style.cssText = `
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    height: 42px;
    background: #470CED;
    border: none;
    border-radius: 8px;
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: white;
    cursor: pointer;
    transition: background 0.2s ease;
    width: 403px;
  `;
  tryAgainButton.textContent = 'Try again';

  tryAgainButton.addEventListener('mouseenter', () => {
    tryAgainButton.style.background = '#2A00A0';
  });

  tryAgainButton.addEventListener('mouseleave', () => {
    tryAgainButton.style.background = '#470CED';
  });

  tryAgainButton.addEventListener('click', () => {
    fetchErrorState.style.display = 'none';
    validationContent.style.display = 'flex';
    validationInput.value = '';
    validateButton.style.background = '#CFD4D4';
    validateButton.style.opacity = '1';
    validateButton.style.cursor = 'not-allowed';
    validateButton.style.pointerEvents = 'none';
    validateButton.style.color = 'rgba(0, 0, 0, 0.5)';
    validateButton.disabled = true;
    validateIcon.style.filter = 'brightness(0)';
    validateIcon.style.opacity = '0.5';
  });

  fetchErrorState.appendChild(fetchErrorIcon);
  fetchErrorState.appendChild(fetchErrorHeading);
  fetchErrorState.appendChild(fetchErrorMessage);
  fetchErrorState.appendChild(tryAgainButton);

  validationSection.appendChild(fetchErrorState);

  const asinConflictState = document.createElement('div');
  asinConflictState.style.cssText = `
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: #F7F8FA;
    z-index: 2;
    padding: 40px;
    border-radius: 28px;
  `;

  const conflictIcon = document.createElement('img');
  conflictIcon.src = chrome.runtime.getURL('assets/vali-fetch-error-img.svg');
  conflictIcon.style.cssText = `
    width: 80px;
    height: 91px;
    margin-bottom: 24px;
  `;

  const conflictHeading = document.createElement('h2');
  conflictHeading.textContent = 'Original ASIN Conflict';
  conflictHeading.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 700;
    font-size: 16px;
    line-height: 19.2px;
    color: #606F95;
    margin: 0 0 4px 0;
    text-align: center;
  `;

  const conflictMessage = document.createElement('p');
  conflictMessage.textContent = 'Your ASIN cannot be listed as both the original and reported listing.';
  conflictMessage.style.cssText = `
    font-family: 'Amazon Ember';
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
    color: #606F95;
    margin: 0 0 24px 0;
    text-align: center;
    max-width: 450px;
  `;

  const goBackButton = document.createElement('button');
  goBackButton.style.cssText = `
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    height: 42px;
    background: #470CED;
    border: none;
    border-radius: 8px;
    font-family: 'Amazon Ember';
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: white;
    cursor: pointer;
    transition: background 0.2s ease;
    width: 403px;
  `;
  goBackButton.textContent = 'Go back';

  goBackButton.addEventListener('mouseenter', () => {
    goBackButton.style.background = '#2A00A0';
  });

  goBackButton.addEventListener('mouseleave', () => {
    goBackButton.style.background = '#470CED';
  });

  goBackButton.addEventListener('click', () => {
    asinConflictState.style.display = 'none';
    validationContent.style.display = 'flex';
    validationInput.value = '';
    validateButton.style.background = '#CFD4D4';
    validateButton.style.opacity = '1';
    validateButton.style.cursor = 'not-allowed';
    validateButton.style.pointerEvents = 'none';
    validateButton.style.color = 'rgba(0, 0, 0, 0.5)';
    validateButton.disabled = true;
    validateIcon.style.filter = 'brightness(0)';
    validateIcon.style.opacity = '0.5';
  });

  asinConflictState.appendChild(conflictIcon);
  asinConflictState.appendChild(conflictHeading);
  asinConflictState.appendChild(conflictMessage);
  asinConflictState.appendChild(goBackButton);

  validationSection.appendChild(asinConflictState);

})();