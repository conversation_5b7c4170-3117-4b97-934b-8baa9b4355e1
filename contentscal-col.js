function initInjection() {
  try {
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key && key.startsWith('snap_selected_color_')) {
        sessionStorage.removeItem(key);
        i--;
      }
    }
  } catch (e) {}

  const products = [
    {
      name: "POP_SOCKET",
      injectPosition: "third",
      class: "product-card",
      id: "POP_SOCKET-card",
      editButtonClass: "POP_SOCKET-edit-btn",
      productDetails: "product-editor",
      scalablecolorClass: "pop-scalable-color",
      colorPickerToggle: ["https://m.media-amazon.com/images/G/01/gear/designerapp/icons/icon_palette.svg", "btn btn-secondary icon"],
      colorHex: "color-editable-input"
    },
    {
      name: "PHONE_CASE_APPLE_IPHONE",
      injectPosition: "third",
      class: "product-card",
      id: "PHONE_CASE_APPLE_IPHONE-card",
      editButtonClass: "PHONE_CASE_APPLE_IPHONE-edit-btn",
      productDetails: "product-editor",
      scalablecolorClass: "phone-scalable-color",
      colorPickerToggle: ["https://m.media-amazon.com/images/G/01/gear/designerapp/icons/icon_palette.svg", "btn btn-secondary icon"],
      colorHex: "color-editable-input"
    },
    {
      name: "TOTE_BAG",
      injectPosition: "second",
      class: "product-card",
      id: "TOTE_BAG-card",
      editButtonClass: "TOTE_BAG-edit-btn",
      productDetails: "product-editor",
      scalablecolorClass: "tote-scalable-color",
      colorPickerToggle: ["https://m.media-amazon.com/images/G/01/gear/designerapp/icons/icon_palette.svg", "btn btn-secondary icon"],
      colorHex: "color-editable-input"
    },
    {
      name: "THROW_PILLOW",
      injectPosition: "third",
      class: "product-card",
      id: "THROW_PILLOW-card",
      editButtonClass: "THROW_PILLOW-edit-btn",
      productDetails: "product-editor",
      scalablecolorClass: "throw-scalable-color",
      colorPickerToggle: ["https://m.media-amazon.com/images/G/01/gear/designerapp/icons/icon_palette.svg", "btn btn-secondary icon"],
      colorHex: "color-editable-input"
    }
  ];

  const allowedProducts = products.map(p => p.name);

  function removeUnwantedScalableContainers() {

    const allCards = document.querySelectorAll('[id$="-card"]');
    allCards.forEach(card => {

      const cardId = card.id;
      const productName = cardId.replace('-card', '').toUpperCase();

      if (!allowedProducts.includes(productName)) {

        const targetContainer = card.querySelector('.form-row > .col-6:nth-child(2)');
        if (targetContainer) {
          const scalableContainers = targetContainer.querySelectorAll('[class*="scalable-color"]');
          scalableContainers.forEach(container => container.remove());
        }
      }
    });
  }

  let lastSelectedColor = null;

  function handleColorSelect(color) {
      console.log();
      lastSelectedColor = color;
  }

  const swatchDisableStyle = document.createElement('style');
  swatchDisableStyle.textContent = `
      .color-swatch.disabled {
          pointer-events: none;
          opacity: 0.5;
          cursor: not-allowed !important;
          border-color: #d6d6d6 !important;
          transition: opacity 0.2s ease, border-color 0.2s ease;
      }
      .color-swatch.disabled[data-tooltip]:before,
      .color-swatch.disabled[data-tooltip]:after {
          display: none !important;
      }
      .color-swatch.deletable {
          cursor: grab;
          user-select: none;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
      }
      .color-swatch.deletable.disabled {
        cursor: not-allowed !important;
        opacity: 0.5;
      }
      .color-swatch.color-applying {
          pointer-events: none;
          opacity: 0.5;
          transition: opacity 0.2s ease;
      }
      .color-swatch.color-applying[data-tooltip]:before,
      .color-swatch.color-applying[data-tooltip]:after {
          display: none !important;
      }
  `;
  document.head.appendChild(swatchDisableStyle);

  const toggleSwatchesForColorApplication = (disable = true) => {
    const allSwatches = document.querySelectorAll('.color-swatch');
    allSwatches.forEach(swatch => {
      if (disable) {
        swatch.classList.add('color-applying');
      } else {
        swatch.classList.remove('color-applying');
      }
    });
  };

  const toggleSwatchesInteractivity = (disable = true) => {
    const allSwatches = document.querySelectorAll('.color-swatch');
    allSwatches.forEach(swatch => {
      if (disable) {
        swatch.classList.add('disabled');
        swatch.draggable = false;
      } else {
        swatch.classList.remove('disabled');
        if (swatch.classList.contains('deletable')) {
          swatch.draggable = true;
        } else {
          swatch.draggable = false;
        }
      }
    });
    if (disable) {
      lastSelectedColor = null;
    }
    const currentToAllBtns = document.querySelectorAll('.current-to-all-btn');
    currentToAllBtns.forEach(btn => {
      if (disable) {
        btn.style.opacity = '0.5';
        btn.style.cursor = 'not-allowed';
        btn.disabled = true;
      } else {
        btn.style.opacity = '';
        btn.style.cursor = 'pointer';
        btn.disabled = false;
      }
    });
  };

  const checkDeleteButtonVisibility = () => {
    const deleteButton = document.querySelector('.delete-button');
    const isDeleteButtonVisible = deleteButton &&
      window.getComputedStyle(deleteButton).display !== 'none' &&
      window.getComputedStyle(deleteButton).visibility !== 'hidden' &&
      window.getComputedStyle(deleteButton).opacity !== '0' &&
      window.getComputedStyle(deleteButton).pointerEvents !== 'none' &&
      deleteButton.offsetParent !== null &&
      !deleteButton.disabled;

    toggleSwatchesInteractivity(!isDeleteButtonVisible);
  };
  
  const swatchStateInterval = setInterval(checkDeleteButtonVisibility, 100);
  
  checkDeleteButtonVisibility();
  
  const cleanupSwatchStateInterval = () => {
    if (swatchStateInterval) {
      clearInterval(swatchStateInterval);
    }
  };

  async function applyColorToDesign(hexColor) {
      console.log();

      toggleSwatchesForColorApplication(true);

      const maxRetries = 3;
      let attempt = 0;

      try {
          while (attempt < maxRetries) {
              try {
                  attempt++;
                  console.log();

                  const waitForPageReady = async (timeout = 1000) => {
                      const startTime = Date.now();
                      while (Date.now() - startTime < timeout) {
                          if (document.readyState === 'complete') {
                              return true;
                          }
                          await new Promise(resolve => setTimeout(resolve, 50));
                      }
                      return false;
                  };

                  if (!await waitForPageReady()) {
                      throw new Error('Page not ready after timeout');
                  }

                  const waitForColorPicker = async (timeout = 2000) => {
                      const startTime = Date.now();
                      let attempts = 0;
                      const maxAttempts = 5;

                      const waitForElement = async (selector, timeout = 500) => {
                          const start = Date.now();
                          while (Date.now() - start < timeout) {
                              const element = document.querySelector(selector);
                              if (element) return element;
                              await new Promise(resolve => setTimeout(resolve, 50));
                          }
                          return null;
                      };

                      while (Date.now() - startTime < timeout) {
                          const colorButton = await waitForElement([
                              'button#color-btn.btn.btn-secondary.icon',
                              'button.btn.btn-secondary.icon[id*="color"]',
                              'button.btn-secondary.icon[id*="color"]'
                          ].join(','));

                          const sketchPicker = await waitForElement('.sketch-picker, div[class*="sketch-picker"]');

                          if (colorButton && sketchPicker) {
                              console.log();
                              return { colorButton, sketchPicker };
                          }

                          if (colorButton && !sketchPicker && attempts < maxAttempts) {
                              console.log();
                              colorButton.click();
                              attempts++;

                              const pickerAfterClick = await waitForElement('.sketch-picker', 300);
                              if (pickerAfterClick) {
                                  return { colorButton, sketchPicker: pickerAfterClick };
                              }
                          }

                          if (attempts >= maxAttempts && !sketchPicker && colorButton) {
                              console.log();
                              colorButton.dispatchEvent(new MouseEvent('click', {
                                  bubbles: true,
                                  cancelable: true,
                                  view: window
                              }));
                              attempts = 0;

                              const pickerAfterSynthetic = await waitForElement('.sketch-picker', 300);
                              if (pickerAfterSynthetic) {
                                  return { colorButton, sketchPicker: pickerAfterSynthetic };
                              }
                          }

                          await new Promise(resolve => setTimeout(resolve, 100));
                      }

                      throw new Error('Color picker elements not found');
                  };

                  let elements = await waitForColorPicker();
                  if (!elements) {
                      console.log();
                      elements = await waitForColorPicker();
                      if (!elements) {
                          throw new Error();
                      }
                  }

                  const waitForHexInput = async (sketchPicker, timeout = 1000) => {
                      const startTime = Date.now();
                      while (Date.now() - startTime < timeout) {
                          const hexInput = sketchPicker.querySelector('.sketch-fields input');
                          if (hexInput) return hexInput;
                          await new Promise(resolve => setTimeout(resolve, 50));
                      }
                      throw new Error('Could not find hex input');
                  };

                  const hexInput = await waitForHexInput(elements.sketchPicker);

                  if (!hexInput || !hexInput.offsetParent) {
                      throw new Error('Hex input not visible or accessible');
                  }

                  const cleanHex = hexColor.replace('#', '').toUpperCase();

                  hexInput.value = '';
                  hexInput.dispatchEvent(new Event('input', { bubbles: true }));
                  await new Promise(resolve => setTimeout(resolve, 50));

                  hexInput.value = cleanHex;
                  hexInput.dispatchEvent(new Event('input', { bubbles: true }));
                  hexInput.dispatchEvent(new Event('change', { bubbles: true }));

                  await new Promise(resolve => setTimeout(resolve, 100));

                  if (hexInput.value.toUpperCase() !== cleanHex) {
                      throw new Error('Hex value not set correctly');
                  }

                  hexInput.dispatchEvent(new KeyboardEvent('keydown', { 
                      key: 'Enter', 
                      code: 'Enter', 
                      keyCode: 13, 
                      bubbles: true,
                      cancelable: true 
                  }));

                  await new Promise(resolve => setTimeout(resolve, 50));

                  hexInput.dispatchEvent(new KeyboardEvent('keyup', { 
                      key: 'Enter', 
                      code: 'Enter', 
                      keyCode: 13, 
                      bubbles: true,
                      cancelable: true 
                  }));

                  await new Promise(resolve => setTimeout(resolve, 150));

                  const verifyColorApplied = async () => {
                      const colorPreview = document.querySelector('.color-preview, .color-swatch.selected');
                      if (colorPreview) {
                          const computedStyle = window.getComputedStyle(colorPreview);
                          const currentColor = computedStyle.backgroundColor;

                          const rgbToHex = (rgb) => {
                              const match = rgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
                              if (!match) return null;
                              const hex = '#' + match.slice(1).map(n => {
                                  const hex = parseInt(n).toString(16);
                                  return hex.length === 1 ? '0' + hex : hex;
                              }).join('').toUpperCase();
                              return hex;
                          };

                          const currentHex = rgbToHex(currentColor);
                          if (currentHex && currentHex.toUpperCase() === hexColor.toUpperCase()) {
                              return true;
                          }
                      }
                      return false;
                  };

                  let verified = false;
                  for (let i = 0; i < 5; i++) {
                      if (await verifyColorApplied()) {
                          verified = true;
                          break;
                      }
                      await new Promise(resolve => setTimeout(resolve, 100));
                  }

                  if (!verified) {
                      throw new Error('Color application could not be verified');
                  }

                  console.log();
                  return true;

              } catch (error) {
                  console.error();
                  if (attempt === maxRetries) {
                      throw error;
                  }

                  await new Promise(resolve => setTimeout(resolve, 500));
              }
          }
      } finally {
          await new Promise(resolve => setTimeout(resolve, 500));
          
          toggleSwatchesForColorApplication(false);

          await new Promise(resolve => setTimeout(resolve, 100));

          document.body.dispatchEvent(new MouseEvent('click', {
              bubbles: true,
              cancelable: true,
          }));

          await new Promise(resolve => setTimeout(resolve, 200));

          document.body.classList.remove('temp-hide-color-picker');
      }
  }

  document.addEventListener('click', async (e) => {
      if (e.target.matches('.btn.btn-outline-primary.btn-DESIGN') || 
          e.target.closest('.btn.btn-outline-primary.btn-DESIGN')) {

          console.log();

          if (lastSelectedColor) {
              console.log();
              try {
                  await applyColorToDesign(lastSelectedColor);
              } catch (error) {
                  console.error();

                  toggleSwatchesInteractivity(false);
              }
          }
      }
  });

  let lastColorPickerHideTime = null;

  function injectScalableColorContainer(product, targetContainer) {

    products.forEach(p => {
      if (p.name !== product.name) {
        targetContainer.querySelectorAll(`.${p.scalablecolorClass}`).forEach(el => el.remove());
      }
    });

    if (targetContainer.querySelector(`.${product.scalablecolorClass}`)) return;

    const container = document.createElement('div');
    container.className = product.scalablecolorClass;
    container.style.width = '492px';
    container.style.border = '1px solid #d6d6d6';
    container.style.marginTop = '10px';
    container.style.backgroundColor = '#fff';
    container.style.display = 'flex';
    container.style.flexDirection = 'column';

    const header = document.createElement('div');
    header.style.display = 'flex';
    header.style.alignItems = 'center';
    header.style.padding = '8px 8px 8px 15px';
    header.style.backgroundColor = '#fafafa';
    header.style.borderBottom = '1px solid #eee';

    const headerText = document.createElement('span');
    headerText.textContent = 'Select color: ';
    headerText.style.fontSize = '13px';
    headerText.style.fontWeight = '600';
    headerText.style.color = 'rgb(51, 51, 51)';
    headerText.style.marginLeft = '0';
    
    const subText = document.createElement('span');
    subText.textContent = '(Drag saved-swatch to bottom-center to delete)';
    subText.style.fontSize = '13px';
    subText.style.fontWeight = '400';
    subText.style.color = 'rgb(51, 51, 51)';
    subText.style.marginLeft = '4px';
    
    header.appendChild(headerText);
    header.appendChild(subText);

    const swatchesContainer = document.createElement('div');
    swatchesContainer.style.display = 'flex';
    swatchesContainer.style.flexWrap = 'wrap';
    swatchesContainer.style.gap = '10px';
    swatchesContainer.style.margin = '15px';
    swatchesContainer.style.padding = '0';

    const colors = [
      { name: 'Dark Red', color: '#840A08' },
      { name: 'Crimson', color: '#C70010' },
      { name: 'Vivid Orange', color: '#F36900' },
      { name: 'Bright Yellow', color: '#FEC600' },
      { name: 'Kelly Green', color: '#01B62F' },
      { name: 'Forest Green', color: '#1C8C46' },
      { name: 'Dark Olive', color: '#37602B' },
      { name: 'Sky Blue', color: '#1AB7EA' },
      { name: 'Royal Blue', color: '#002BB6' },
      { name: 'Purple', color: '#5C2D91' },
      { name: 'Hot Pink', color: '#E0218A' },
      { name: 'Pale Pink', color: '#E9CDDB' },
      { name: 'Brown', color: '#7B4A1B' },
      { name: 'Gray', color: '#979797' },
      { name: 'White', color: '#ffffff' },
      { name: 'Black', color: '#000000' }
    ];

    const selectionStyle = document.createElement('style');
    selectionStyle.textContent = `
      .color-swatch {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .color-swatch .checkmark {
        width: 16px;
        height: 16px;
        position: absolute;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.2s ease;
      }
      .color-swatch.selected .checkmark {
        opacity: 1;
      }
      .color-swatch.dragging {
        cursor: grabbing !important;
      }

      body.temp-hide-color-picker .sketch-picker,
      body.temp-hide-color-picker .popover.color-picker-popover,
      body.temp-hide-color-picker ngb-popover-window[class*="color-picker-popover"] {
        position: absolute !important;
        visibility: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
        height: 0 !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        width: 0 !important;
      }

      body.temp-hide-color-picker .popover.color-picker-popover .arrow,
      body.temp-hide-color-picker ngb-popover-window[class*="color-picker-popover"] .arrow {
        display: none !important;
      }

      .sketch-picker input {
        pointer-events: auto !important;
      }
    `;
    document.head.appendChild(selectionStyle);

    let selectedSwatch = null;

    let savedSessionColor = null;
    try {
      savedSessionColor = sessionStorage.getItem('snap_selected_color_' + product.name);
    } catch (e) {}

    colors.forEach(color => {
      const swatch = document.createElement('div');
      swatch.className = 'color-swatch';
      swatch.setAttribute('data-tooltip', color.name);
      swatch.style.width = '32px';
      swatch.style.height = '32px';
      swatch.style.backgroundColor = color.color;
      swatch.style.border = '1px solid #d6d6d6';
      swatch.style.borderRadius = '0';
      swatch.style.cursor = 'pointer';
      swatch.style.position = 'relative';
      swatch.draggable = false;

      const checkmark = document.createElement('img');
      checkmark.src = chrome.runtime.getURL("assets/check-color-ic.svg");
      checkmark.className = 'checkmark';
      checkmark.alt = '';
      swatch.appendChild(checkmark);

      if (savedSessionColor && color.color.toLowerCase() === savedSessionColor.toLowerCase()) {
        swatch.classList.add('selected');
        selectedSwatch = swatch;
      }

      swatch.addEventListener('click', async (e) => {
        if (swatch.classList.contains('disabled')) {
          return;
        }
        if (!swatch.classList.contains('dragging')) {
          try {
            document.body.dispatchEvent(new MouseEvent('click', {
              bubbles: true,
              cancelable: true,
            }));
            await new Promise(resolve => setTimeout(resolve, 100));
            if (selectedSwatch) {
              selectedSwatch.classList.remove('selected');
            }
            swatch.classList.add('selected');
            selectedSwatch = swatch;
            lastSelectedColor = color.color;
            try {
              sessionStorage.setItem('snap_selected_color_' + product.name, color.color);
            } catch (e) {}
            handleColorSelect(color.color);
            await new Promise(resolve => setTimeout(resolve, 150));
            document.body.classList.add('temp-hide-color-picker');
            const designButton = document.querySelector('.btn.btn-outline-primary.btn-DESIGN.ng-star-inserted');
            if (designButton) {
              console.log();
              designButton.click();
            }
          } catch (error) {
            console.error();
          }
        }
      });

      swatchesContainer.appendChild(swatch);
    });

    const swatchTooltipStyle = document.createElement('style');
    swatchTooltipStyle.textContent = `
      .color-swatch[data-tooltip] {
        position: relative;
      }

      .color-swatch[data-tooltip]:before {
        content: attr(data-tooltip);
        position: absolute;
        bottom: calc(100% + 8px);
        left: 50%;
        transform: translateX(-50%);
        padding: 4px 12px;
        background-color: #1F2937;
        color: white;
        font-size: 12px;
        border-radius: 999px;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: none;
        z-index: 1000;
        pointer-events: none;
      }

      .color-swatch[data-tooltip]:after {
        content: '';
        position: absolute;
        bottom: calc(100% + 4px);
        left: 50%;
        transform: translateX(-50%);
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #1F2937;
        opacity: 0;
        visibility: hidden;
        transition: none;
        z-index: 1000;
        pointer-events: none;
      }

      .color-swatch[data-tooltip]:hover:not(.dragging):before,
      .color-swatch[data-tooltip]:hover:not(.dragging):after {
        opacity: 1;
        visibility: visible;
      }

      .color-swatch.dragging[data-tooltip]:before,
      .color-swatch.dragging[data-tooltip]:after {
        opacity: 0 !important;
        visibility: hidden !important;
      }

      .current-to-all-btn {
        position: relative;
        font-family: "Amazon Ember", sans-serif;
        transition: background-color 0.2s ease;
      }

      .current-to-all-btn:hover {
        background-color: #3A0ABD;
      }

      .current-to-all-btn:active {
        background-color: #2D0894;
      }

      .current-to-all-btn img {
        width: 16px;
        height: 16px;
        filter: brightness(0) invert(1);
      }

      .current-to-all-btn span {
        color: #FFFFFF;
        font-size: 14px;
        font-weight: 400;
      }
    `;
    document.head.appendChild(swatchTooltipStyle);

    const customColorSection = document.createElement('div');
    customColorSection.className = 'color-to-swatch';
    customColorSection.style.display = 'flex';
    customColorSection.style.alignItems = 'center';
    customColorSection.style.gap = '10px';
    customColorSection.style.margin = '0 15px 15px';
    customColorSection.style.justifyContent = 'space-between';

    const colorControlsGroup = document.createElement('div');
    colorControlsGroup.style.display = 'flex';
    colorControlsGroup.style.alignItems = 'center';
    colorControlsGroup.style.gap = '10px';

    const customColorLabel = document.createElement('span');
    customColorLabel.textContent = 'Save a swatch:';
    customColorLabel.style.fontSize = '14px';
    customColorLabel.style.color = '#212529';

    const hexInputContainer = document.createElement('div');
    hexInputContainer.className = 'snap-hex-container';
    hexInputContainer.style.position = 'relative';
    hexInputContainer.style.width = '138px';
    hexInputContainer.style.height = '33px';
    hexInputContainer.style.border = '1px solid rgb(220, 224, 229) !important';
    hexInputContainer.style.borderRadius = '4px';
    hexInputContainer.style.display = 'flex';
    hexInputContainer.style.alignItems = 'center';

    const hashPrefix = document.createElement('div');
    hashPrefix.className = 'hex-#';
    hashPrefix.style.width = '31px';
    hashPrefix.style.height = '31px';
    hashPrefix.style.backgroundColor = '#DCE0E5';
    hashPrefix.style.display = 'flex';
    hashPrefix.style.alignItems = 'center';
    hashPrefix.style.justifyContent = 'center';

    const hashText = document.createElement('span');
    hashText.textContent = '#';
    hashText.style.color = '#FFFFFF';
    hashText.style.fontSize = '14px';
    hashText.style.fontWeight = '400';
    hashText.style.lineHeight = '19px';

    hashPrefix.appendChild(hashText);

    const colorInput = document.createElement('input');
    colorInput.type = 'text';
    colorInput.className = 'snap-hex-input';
    colorInput.placeholder = 'Enter a hex code';
    colorInput.style.width = '80px';
    colorInput.style.height = '100%';
    colorInput.style.padding = '0 8px';
    colorInput.style.border = 'none';
    colorInput.style.outline = 'none';
    colorInput.style.fontSize = '12px';
    colorInput.style.color = '#1F2937';
    colorInput.style.backgroundColor = 'transparent';
    colorInput.style.fontFamily = '"Amazon Ember", sans-serif';
    colorInput.style.fontWeight = '400';
    colorInput.style.lineHeight = 'normal';
    colorInput.style.webkitAppearance = 'none';
    colorInput.style.mozAppearance = 'none';
    colorInput.style.appearance = 'none';
    colorInput.setAttribute('tabindex', '-1');

    const inputStyle = document.createElement('style');
    inputStyle.textContent = `
      input::placeholder {
        color: #DCE0E5;
        font-size: 12px;
        font-family: "Amazon Ember", sans-serif;
        font-weight: 400;
        line-height: normal;
        opacity: 1;
      }

      .snap-hex-container {
        border: 1px solid rgb(220, 224, 229) !important;
      }

      .snap-hex-input {
        border: none !important;
        outline: none !important;
        background-color: transparent !important;
        -webkit-appearance: none !important;
        -moz-appearance: none !important;
        appearance: none !important;
        color: #1F2937 !important;
      }

      .snap-hex-input,
      .snap-hex-input:hover,
      .snap-hex-input:focus,
      .snap-hex-input:focus-visible,
      .snap-hex-input:focus-within,
      .snap-hex-input:active,
      .snap-hex-input:-webkit-autofill,
      .snap-hex-input:-webkit-autofill:hover,
      .snap-hex-input:-webkit-autofill:focus {
        border: none !important;
        outline: none !important;
        box-shadow: none !important;
        -webkit-box-shadow: none !important;
        -webkit-text-fill-color: #1F2937 !important;
        background: transparent !important;
        -webkit-background-clip: text !important;
      }

      .snap-hex-container:hover,
      .snap-hex-container:focus,
      .snap-hex-container:focus-within,
      .snap-hex-container:active {
        border: 1px solid rgb(220, 224, 229) !important;
        outline: none !important;
        box-shadow: none !important;
      }
    `;
    document.head.appendChild(inputStyle);

    const colorWheelButton = document.createElement('button');
    colorWheelButton.className = 'color-wheel-btn';
    colorWheelButton.setAttribute('data-tooltip', 'Color Picker');
    colorWheelButton.style.width = '32px';
    colorWheelButton.style.height = '31px';
    colorWheelButton.style.border = 'none';
    colorWheelButton.style.background = 'transparent';
    colorWheelButton.style.padding = '0';
    colorWheelButton.style.display = 'flex';
    colorWheelButton.style.alignItems = 'center';
    colorWheelButton.style.justifyContent = 'center';
    colorWheelButton.style.cursor = 'pointer';
    colorWheelButton.style.outline = 'none';
    colorWheelButton.style.webkitTapHighlightColor = 'transparent';
    colorWheelButton.setAttribute('type', 'button');
    colorWheelButton.style.position = 'relative'; 

    const buttonStyle = document.createElement('style');
    buttonStyle.textContent = `
      .color-wheel-btn:focus {
        outline: none;
        box-shadow: none;
      }

      .color-wheel-btn[data-tooltip]:before {
        content: attr(data-tooltip);
        position: absolute;
        bottom: calc(100% + 8px);
        left: 50%;
        transform: translateX(-50%);
        padding: 4px 12px;
        background-color: #1F2937;
        color: white;
        font-size: 12px;
        border-radius: 999px;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s ease, visibility 0.2s ease;
        z-index: 1000;
        pointer-events: none;
      }

      .color-wheel-btn[data-tooltip]:after {
        content: '';
        position: absolute;
        bottom: calc(100% + 4px);
        left: 50%;
        transform: translateX(-50%);
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #1F2937;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s ease, visibility 0.2s ease;
        z-index: 1000;
        pointer-events: none;
      }

      .color-wheel-btn[data-tooltip]:hover:before,
      .color-wheel-btn[data-tooltip]:hover:after {
        opacity: 1;
        visibility: visible;
      }
    `;
    document.head.appendChild(buttonStyle);

    const colorPickerContainer = document.createElement('div');
    colorPickerContainer.className = 'color-picker-container';
    colorPickerContainer.style.position = 'absolute';
    colorPickerContainer.style.top = 'calc(100% + 10px)';
    colorPickerContainer.style.left = '0';
    colorPickerContainer.style.zIndex = '1000';
    colorPickerContainer.style.backgroundColor = '#fff';
    colorPickerContainer.style.padding = '15px';
    colorPickerContainer.style.borderRadius = '8px';
    colorPickerContainer.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
    colorPickerContainer.style.display = 'none';

    const colorPickerElement = document.createElement('div');
    colorPickerElement.id = `color-picker-${Math.random().toString(36).substr(2, 9)}`;
    colorPickerContainer.appendChild(colorPickerElement);

    const colorPickerStyle = document.createElement('style');
    colorPickerStyle.textContent = `
      .color-picker-container {
        animation: fadeIn 0.2s ease;
      }
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
      }
    `;
    document.head.appendChild(colorPickerStyle);

    let colorPicker = null;
    let isPickerVisible = false;

    colorWheelButton.addEventListener('click', () => {
      if (!isPickerVisible) {

        colorPickerContainer.style.display = 'block';
        isPickerVisible = true;

        if (!colorPicker) {
          colorPicker = new iro.ColorPicker(colorPickerElement, {
            width: 200,
            color: colorInput.value ? '#' + colorInput.value : '#ffffff',
            layout: [
              {
                component: iro.ui.Wheel,
                options: {}
              },
              {
                component: iro.ui.Slider,
                options: {
                  sliderType: 'value'
                }
              }
            ]
          });

          colorPicker.on('color:change', (color) => {
            const hexValue = color.hexString.replace('#', '');
            colorInput.value = hexValue;
            colorInput.dispatchEvent(new Event('input', { bubbles: true }));
          });
        } else {

          const currentColor = colorInput.value ? '#' + colorInput.value : '#ffffff';
          colorPicker.color.set(currentColor);
        }
      } else {

        colorPickerContainer.style.display = 'none';
        isPickerVisible = false;
      }
    });

    document.addEventListener('click', (e) => {
      if (isPickerVisible && !colorPickerContainer.contains(e.target) && !colorWheelButton.contains(e.target)) {
        colorPickerContainer.style.display = 'none';
        isPickerVisible = false;
      }
    });

    const colorWheelIcon = document.createElement('img');
    colorWheelIcon.src = chrome.runtime.getURL("assets/colorwheel-ic.svg");
    colorWheelIcon.alt = 'Color Wheel';
    colorWheelIcon.style.width = '16px';
    colorWheelIcon.style.height = '16px';
    colorWheelIcon.style.opacity = '1';
    colorWheelIcon.style.transition = 'opacity 0.2s ease';

    colorWheelButton.addEventListener('mouseenter', () => {
      colorWheelIcon.style.opacity = '0.8';
    });

    colorWheelButton.addEventListener('mouseleave', () => {
      colorWheelIcon.style.opacity = '1';
    });

    colorWheelButton.appendChild(colorWheelIcon);

    hexInputContainer.appendChild(hashPrefix);
    hexInputContainer.appendChild(colorInput);
    hexInputContainer.appendChild(colorWheelButton);
    hexInputContainer.appendChild(colorPickerContainer);

    colorInput.addEventListener('input', (e) => {
      let value = e.target.value;
      value = value.replace('#', '');
      value = value.slice(0, 6).replace(/[^0-9A-Fa-f]/g, '');
      colorInput.value = value;

      if (/^[0-9A-F]{6}$/i.test(value)) {
        const hexColor = '#' + value;

        if (value.toLowerCase() !== 'ffffff') {
          hexInputContainer.style.cssText += `border-color: ${hexColor} !important;`;
          hashPrefix.style.backgroundColor = hexColor;
        }
        saveButton.classList.remove('disabled');
        saveButton.style.cursor = 'pointer';
      } else {

        hexInputContainer.style.cssText += `border-color: rgb(220, 224, 229) !important;`;
        hashPrefix.style.backgroundColor = '#DCE0E5';
        saveButton.classList.add('disabled');
        saveButton.style.cursor = 'not-allowed';
      }
    });

    const saveButton = document.createElement('div');
    saveButton.className = 'save-btn disabled';
    saveButton.setAttribute('data-tooltip', 'Save Color');
    saveButton.style.cursor = 'not-allowed';
    saveButton.style.position = 'relative';

    const tooltipStyle = document.createElement('style');
    tooltipStyle.textContent = `
      .save-btn[data-tooltip] {
        position: relative;
      }

      .save-btn[data-tooltip]:before {
        content: attr(data-tooltip);
        position: absolute;
        bottom: calc(100% + 8px);
        left: 50%;
        transform: translateX(-50%);
        padding: 4px 12px;
        background-color: #1F2937;
        color: white;
        font-size: 12px;
        border-radius: 999px;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s ease, visibility 0.2s ease;
        z-index: 1000;
      }

      .save-btn[data-tooltip]:after {
        content: '';
        position: absolute;
        bottom: calc(100% + 4px);
        left: 50%;
        transform: translateX(-50%);
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #1F2937;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s ease, visibility 0.2s ease;
        z-index: 1000;
      }

      .save-btn[data-tooltip]:hover:before,
      .save-btn[data-tooltip]:hover:after {
        opacity: 1;
        visibility: visible;
      }

      .save-btn {
        width: 32px;
        height: 32px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        background-color: transparent;
        border: none;
        background-image: url("data:image/svg+xml,%3Csvg width='34' height='34' viewBox='0 0 34 34' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='1' y='1' width='32' height='32' rx='16' stroke='%23470CED' stroke-linecap='round' stroke-linejoin='round' stroke-dasharray='1 3'/%3E%3C/svg%3E");
        background-position: center;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        transition: all 0.2s ease;
      }

      .save-btn.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background-image: url("data:image/svg+xml,%3Csvg width='34' height='34' viewBox='0 0 34 34' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='1' y='1' width='32' height='32' rx='16' stroke='%23DCE0E5' stroke-linecap='round' stroke-linejoin='round' stroke-dasharray='1 3'/%3E%3C/svg%3E");
      }

      .save-btn.disabled img {
        filter: brightness(0);
        opacity: 0.2 !important;
      }

      .save-btn:not(.disabled):hover {
        background-image: none;
        background-color: #470CED;
      }

      .save-btn:not(.disabled):hover img {
        filter: brightness(0) invert(1) !important;
      }

      .save-btn img {
        width: 16px !important;
        height: 16px !important;
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: none;
        filter: none;
      }
    `;
    document.head.appendChild(tooltipStyle);

    const saveIcon = document.createElement('img');
    saveIcon.src = chrome.runtime.getURL("assets/save-color-ic.svg");
    saveIcon.alt = 'Save Color';
    saveIcon.style.width = '16px';
    saveIcon.style.height = '16px';

    saveButton.appendChild(saveIcon);
    saveButton.addEventListener('click', async () => {
      const value = colorInput.value;
      if (/^[0-9A-F]{6}$/i.test(value)) {
        const hexColor = '#' + value;

        const exists = checkAndFlashColor(hexColor);

        if (!exists) {

          const savedColors = await loadGlobalSavedColors();
          if (!savedColors.includes(hexColor)) {
            savedColors.push(hexColor);
            await chrome.storage.sync.set({
              globalSavedColors: savedColors
            });

            document.querySelectorAll('.form-row > .col-6:nth-child(2)').forEach(container => {
              if (container) {
                const scalableContainer = container.querySelector(`[class*="scalable-color"]`);
                if (scalableContainer) {
                  const swatchesContainer = scalableContainer.querySelector('div:nth-child(2)');
                  if (swatchesContainer) {
                    addColorSwatch(hexColor);
                  }
                }
              }
            });
            handleColorSelect(hexColor);
          }
        }
      }
    });

    const currentToAllBtn = document.createElement('button');
    currentToAllBtn.className = 'current-to-all-btn';
    currentToAllBtn.style.cssText = `
      height: 33px;
      flex: none;
      border: 1.5px solid #E9EBEF;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
      padding: 0 10px;
      outline: none;
      border-radius: 4px;
    `;
    currentToAllBtn.setAttribute('data-tooltip', 'Apply the selected color to all scalable products');
    
    currentToAllBtn.addEventListener('mouseenter', () => {
      if (!currentToAllBtn.classList.contains('selected') && !currentToAllBtn.disabled) {
        currentToAllBtn.style.backgroundColor = '#F9FAFC';
      }
    });
    
    currentToAllBtn.addEventListener('mouseleave', () => {
      if (!currentToAllBtn.classList.contains('selected') && !currentToAllBtn.disabled) {
        currentToAllBtn.style.backgroundColor = 'white';
      }
    });

    const quickColorIcon = document.createElement('img');
    quickColorIcon.src = chrome.runtime.getURL("assets/quick-color-selection-ic.svg");
    quickColorIcon.alt = 'Apply to all';
    quickColorIcon.style.width = '16px';
    quickColorIcon.style.height = '16px';
    quickColorIcon.style.filter = 'brightness(0) saturate(100%) invert(45%) sepia(11%) saturate(1129%) hue-rotate(189deg) brightness(94%) contrast(87%)';

    const btnText = document.createElement('span');
    btnText.textContent = 'Current to All';
    btnText.style.cssText = `
      font-size: 12px;
      color: #606F95;
      font-weight: 400;
      transition: all 0.2s ease;
    `;

    currentToAllBtn.appendChild(quickColorIcon);
    currentToAllBtn.appendChild(btnText);
    currentToAllBtn.setAttribute('data-tooltip', 'Apply the selected color to all scalable products');

    const setButtonDisabledState = (button, isDisabled) => {
      if (button !== currentToAllBtn) {
        button.disabled = isDisabled;
        
        if (isDisabled) {
          button.style.background = '#CFD4D4';
          button.style.opacity = '1';
          button.style.cursor = 'not-allowed';
          button.style.pointerEvents = 'none';
          button.style.color = 'rgba(0, 0, 0, 0.5)';
          
          const buttonIcon = button.querySelector('img');
          if (buttonIcon) {
            buttonIcon.style.filter = 'brightness(0)';
            buttonIcon.style.opacity = '0.5';
          }
        } else {
          button.style.background = '#470CED';
          button.style.opacity = '1';
          button.style.cursor = 'pointer';
          button.style.pointerEvents = 'auto';
          button.style.color = 'white';
          
          const buttonIcon = button.querySelector('img');
          if (buttonIcon) {
            buttonIcon.style.filter = 'brightness(0) invert(1)';
            buttonIcon.style.opacity = '1';
          }
        }
        currentToAllBtn.setAttribute('data-tooltip', 'Apply the selected color to all scalable products');
      }
    };

    currentToAllBtn.addEventListener('click', async () => {
      if (currentToAllBtn.disabled) {
        return;
      }
      
      try {
        currentToAllBtn.classList.add('selected');
        currentToAllBtn.style.backgroundColor = '#EBF0FF';
        currentToAllBtn.style.borderColor = '#470CED';
        btnText.style.color = '#470CED';
        quickColorIcon.style.filter = 'brightness(0) saturate(100%) invert(24%) sepia(87%) saturate(7352%) hue-rotate(246deg) brightness(91%) contrast(99%)';
        btnText.textContent = 'Applying...';
        
        let currentProductCard = null;
        let currentProductName = null;
        
        currentProductCard = document.querySelector('.product-editor')?.closest('[id$="-card"]');
        
        if (!currentProductCard) {
          for (const product of products) {
            const scalableContainer = document.querySelector(`.${product.scalablecolorClass}`);
            if (scalableContainer && 
                window.getComputedStyle(scalableContainer).display !== 'none' && 
                scalableContainer.offsetParent !== null) {
              currentProductCard = document.getElementById(`${product.name}-card`);
              if (currentProductCard) {
                currentProductName = product.name;
                console.log(``);
                break;
              }
            }
          }
        }
        
        if (!currentProductCard) {
          const selectedSwatch = document.querySelector('.color-swatch.selected');
          if (selectedSwatch) {
            const scalableContainer = selectedSwatch.closest('[class*="scalable-color"]');
            if (scalableContainer) {
              const className = scalableContainer.className;
              for (const product of products) {
                if (className.includes(product.scalablecolorClass)) {
                  currentProductCard = document.getElementById(`${product.name}-card`);
                  currentProductName = product.name;
                  console.log(``);
                  break;
                }
              }
            }
          }
        }
        
        if (!currentProductCard) {
          const firstProduct = products.find(p => allowedProducts.includes(p.name));
          if (firstProduct) {
            currentProductCard = document.getElementById(`${firstProduct.name}-card`);
            currentProductName = firstProduct.name;
            console.log(``);
          }
        }
        
        if (!currentProductCard) {
          throw new Error('Cannot determine current product. Please select a product first.');
        }
        
        const currentProductId = currentProductCard.id;
        if (!currentProductName) {
          currentProductName = currentProductId.replace('-card', '');
        }
        
        console.log(``);
        
        const selectedSwatch = document.querySelector('.color-swatch.selected');
        if (!selectedSwatch) {
          throw new Error('');
        }
        
        const selectedColor = selectedSwatch.style.backgroundColor;
        console.log(``);
        
        const productCards = document.querySelectorAll('[id$="-card"]');
        console.log(``);
        
        const targetCards = Array.from(productCards).filter(card => {
          const cardId = card.id;
          const productName = cardId.replace('-card', '');
          
          if (cardId === currentProductId) {
            console.log(``);
            return false;
          }
          
          if (card.classList.contains('disabled') || 
              card.hasAttribute('disabled') ||
              window.getComputedStyle(card).pointerEvents === 'none' || 
              window.getComputedStyle(card).opacity < '1') {
            console.log(``);
            return false;
          }
          
          const editBtn = card.querySelector(`.${productName}-edit-btn`);
          if (!editBtn) {
            console.log(``);
            return false;
          }
          
          const isEnabled = !editBtn.disabled && 
                          !editBtn.classList.contains('disabled') &&
                          window.getComputedStyle(editBtn).display !== 'none' &&
                          window.getComputedStyle(editBtn).pointerEvents !== 'none';
          
          if (!isEnabled) {
            console.log(``);
            return false;
          }
          
          if (!allowedProducts.includes(productName)) {
            console.log(``);
            return false;
          }
          
          console.log(``);
          return true;
        });
        
        console.log(``);
        
        if (targetCards.length === 0) {
          throw new Error('');
        }
        
        const waitForSwatchesEnabled = async (timeout = 3000) => {
          const startTime = Date.now();
          while (Date.now() - startTime < timeout) {
            const swatches = document.querySelectorAll('.color-swatch');
            let hasEnabledSwatch = false;
            
            for (const swatch of swatches) {
              if (!swatch.classList.contains('disabled')) {
                hasEnabledSwatch = true;
                break;
              }
            }
            
            if (hasEnabledSwatch) {
              return true;
            }
            
            await new Promise(resolve => setTimeout(resolve, 100));
          }
          return false;
        };
        
        for (let i = 0; i < targetCards.length; i++) {
          try {
            const card = targetCards[i];
            const cardId = card.id;
            const productName = cardId.replace('-card', '');
            
            if (cardId === currentProductId) {
              console.log(``);
              continue;
            }
            
            btnText.textContent = `${i+1}/${targetCards.length}...`;
            
            const editBtn = card.querySelector(`.${productName}-edit-btn`);
            if (editBtn) {
              console.log(``);
              
              editBtn.click();
              
              await new Promise(resolve => setTimeout(resolve, 500));
              const swatchesReady = await waitForSwatchesEnabled();
              
              if (swatchesReady) {
                const colorSwatches = document.querySelectorAll('.color-swatch:not(.disabled)');
                let matchingSwatch = null;
                
                for (const swatch of colorSwatches) {
                  if (swatch.style.backgroundColor === selectedColor) {
                    matchingSwatch = swatch;
                    break;
                  }
                }
                
                if (matchingSwatch) {
                  matchingSwatch.click();
                  await new Promise(resolve => setTimeout(resolve, 500));
                } else {
                  console.log(``);
                  if (lastSelectedColor) {
                    await applyColorToDesign(lastSelectedColor);
                  }
                }
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                await waitForSwatchesEnabled();
              } else {
                console.warn(``);
              }
            }
          } catch (error) {
            console.error();
          }
        }
        currentToAllBtn.classList.remove('selected');
        btnText.textContent = 'Current to All';
        currentToAllBtn.style.backgroundColor = 'white';
        currentToAllBtn.style.borderColor = '#E9EBEF';
        btnText.style.color = '#606F95';
        quickColorIcon.style.filter = 'brightness(0) saturate(100%) invert(45%) sepia(11%) saturate(1129%) hue-rotate(189deg) brightness(94%) contrast(87%)';
        currentToAllBtn.setAttribute('data-tooltip', 'Apply the selected color to all scalable products');
      } catch (error) {
        console.error();
        currentToAllBtn.classList.remove('selected');
        btnText.textContent = 'Current to All';
        currentToAllBtn.style.backgroundColor = 'white';
        currentToAllBtn.style.borderColor = '#E9EBEF';
        btnText.style.color = '#606F95';
        quickColorIcon.style.filter = 'brightness(0) saturate(100%) invert(45%) sepia(11%) saturate(1129%) hue-rotate(189deg) brightness(94%) contrast(87%)';
        currentToAllBtn.setAttribute('data-tooltip', 'Apply the selected color to all scalable products');
      }
    });

    const eyeDropperButton = document.createElement('div');
    eyeDropperButton.className = 'eye-dropper-btn';
    eyeDropperButton.setAttribute('data-tooltip', 'Pick a color from anywhere on the page');
    eyeDropperButton.style.cursor = 'pointer';
    eyeDropperButton.style.position = 'relative';
    eyeDropperButton.style.marginRight = '2px';
    eyeDropperButton.style.display = 'flex';
    eyeDropperButton.style.alignItems = 'center';
    eyeDropperButton.style.justifyContent = 'center';
    eyeDropperButton.style.width = '32px';
    eyeDropperButton.style.height = '32px';
    eyeDropperButton.style.borderRadius = '16px';
    eyeDropperButton.style.backgroundColor = 'transparent';
    eyeDropperButton.style.border = 'none';
    eyeDropperButton.style.transition = 'all 0.2s ease';
    eyeDropperButton.tabIndex = 0;

    const eyeDropperIcon = document.createElement('img');
    eyeDropperIcon.src = chrome.runtime.getURL('assets/eye-picker-ic.svg');
    eyeDropperIcon.alt = 'Eye Dropper';
    eyeDropperIcon.style.width = '16px';
    eyeDropperIcon.style.height = '16px';
    eyeDropperButton.appendChild(eyeDropperIcon);

    const eyeDropperBtnStyle = document.createElement('style');
    eyeDropperBtnStyle.textContent = `
      .eye-dropper-btn[data-tooltip] { position: relative; }
      .eye-dropper-btn[data-tooltip]:before {
        content: attr(data-tooltip);
        position: absolute;
        bottom: calc(100% + 8px);
        left: 50%;
        transform: translateX(-50%);
        padding: 4px 12px;
        background-color: #1F2937;
        color: white;
        font-size: 12px;
        border-radius: 999px;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s, visibility 0.2s;
        z-index: 1000;
        pointer-events: none;
      }
      .eye-dropper-btn[data-tooltip]:after {
        content: '';
        position: absolute;
        bottom: calc(100% + 4px);
        left: 50%;
        transform: translateX(-50%);
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #1F2937;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s, visibility 0.2s;
        z-index: 1000;
        pointer-events: none;
      }
      .eye-dropper-btn[data-tooltip]:hover:before,
      .eye-dropper-btn[data-tooltip]:hover:after {
        opacity: 1;
        visibility: visible;
      }
      .eye-dropper-btn {
        width: 32px;
        height: 32px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        background-color: transparent;
        border: none;
        transition: all 0.2s ease;
        background-image: url("data:image/svg+xml,%3Csvg width='34' height='34' viewBox='0 0 34 34' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='1' y='1' width='32' height='32' rx='16' stroke='%23470CED' stroke-linecap='round' stroke-linejoin='round' stroke-dasharray='1 3'/%3E%3C/svg%3E");
        background-position: center;
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .eye-dropper-btn:not(.disabled):hover {
        background-image: none !important;
        background-color: #470CED !important;
      }
      .eye-dropper-btn:not(.disabled):hover img {
        filter: brightness(0) invert(1) !important;
      }
      .eye-dropper-btn.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background-image: url("data:image/svg+xml,%3Csvg width='34' height='34' viewBox='0 0 34 34' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='1' y='1' width='32' height='32' rx='16' stroke='%23DCE0E5' stroke-linecap='round' stroke-linejoin='round' stroke-dasharray='1 3'/%3E%3C/svg%3E");
      }
      .eye-dropper-btn img {
        width: 16px !important;
        height: 16px !important;
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: none;
        filter: none;
      }
      .hex-# {
        width: 31px !important;
      }
    `;
    document.head.appendChild(eyeDropperBtnStyle);

    if (!window.EyeDropper) {
      eyeDropperButton.classList.add('disabled');
      eyeDropperButton.setAttribute('data-tooltip', 'Eye Dropper not supported in this browser');
      eyeDropperButton.style.cursor = 'not-allowed';
    } else {
      eyeDropperButton.addEventListener('click', async (e) => {
        if (eyeDropperButton.classList.contains('disabled')) return;
        try {
          const eyeDropper = new window.EyeDropper();
          const result = await eyeDropper.open();
          if (result && result.sRGBHex) {
            const pickedHex = result.sRGBHex.replace('#', '');
            colorInput.value = pickedHex;
            colorInput.dispatchEvent(new Event('input', { bubbles: true }));
            if (colorPicker) {
              colorPicker.color.set('#' + pickedHex);
            }
          }
        } catch (err) {
        }
      });
    }

    colorControlsGroup.appendChild(customColorLabel);
    colorControlsGroup.appendChild(hexInputContainer);
    colorControlsGroup.appendChild(saveButton);
    colorControlsGroup.appendChild(eyeDropperButton);
    
    customColorSection.appendChild(colorControlsGroup);
    customColorSection.appendChild(currentToAllBtn);

    container.appendChild(header);
    container.appendChild(swatchesContainer);
    container.appendChild(customColorSection);

    let sibling;
    switch (product.injectPosition) {
      case "first":
        sibling = targetContainer.firstChild;
        targetContainer.insertBefore(container, sibling);
        break;
      case "second":
        sibling = targetContainer.children[1];
        if (sibling)
          targetContainer.insertBefore(container, sibling);
        else
          targetContainer.appendChild(container);
        break;
      case "third":
        sibling = targetContainer.children[2];
        if (sibling)
          targetContainer.insertBefore(container, sibling);
        else
          targetContainer.appendChild(container);
        break;
      default:
        targetContainer.appendChild(container);
    }

    const loadGlobalSavedColors = async () => {
      try {
        const result = await chrome.storage.sync.get('globalSavedColors');
        return result.globalSavedColors || [];
      } catch (error) {
        console.error();
        return [];
      }
    };

    const flashStyle = document.createElement('style');
    flashStyle.textContent = `
      @keyframes flash-swatch {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.3; }
      }
      .flash-swatch {
        animation: flash-swatch 0.15s 2;
      }
    `;
    document.head.appendChild(flashStyle);

    const checkAndFlashColor = (hexColor) => {

      for (const swatch of swatchesContainer.children) {
        const swatchColor = swatch.style.backgroundColor;

        let swatchHex = swatchColor;
        if (swatchColor.startsWith('rgb')) {
          const rgb = swatchColor.match(/\d+/g);
          swatchHex = '#' + rgb.map(x => {
            const hex = parseInt(x).toString(16);
            return hex.length === 1 ? '0' + hex : hex;
          }).join('');
        }

        if (swatchHex.toUpperCase() === hexColor.toUpperCase()) {
          swatch.classList.add('flash-swatch');
          setTimeout(() => {
            swatch.classList.remove('flash-swatch');
          }, 600);
          return true;
        }
      }
      return false;
    };

    const dragDeleteStyle = document.createElement('style');
    dragDeleteStyle.textContent = `
      .color-swatch.dragging {
        opacity: 0.6;
        cursor: no-drop !important;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
      }
      .color-swatch.dragging[data-tooltip]:before,
      .color-swatch.dragging[data-tooltip]:after {
        display: none !important;
      }
      .color-swatch.deletable {
        cursor: grab;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
      }
      .delete-zone {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%) scale(0);
        padding: 12px 24px;
        background-color: #FF391F;
        color: white;
        border-radius: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: transform 0.2s ease;
        z-index: 9999;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        cursor: no-drop;
      }
      .delete-zone.active {
        transform: translateX(-50%) scale(1);
      }
      .delete-zone.highlight {
        background-color: #D92E19;
      }
    `;
    document.head.appendChild(dragDeleteStyle);

    let deleteZone = document.querySelector('.delete-zone');
    if (!deleteZone) {
      deleteZone = document.createElement('div');
      deleteZone.className = 'delete-zone';
      const deleteIcon = document.createElement('img');
      deleteIcon.src = chrome.runtime.getURL("assets/clear.svg");
      deleteIcon.alt = 'Delete';
      deleteIcon.style.width = '16px';
      deleteIcon.style.height = '16px';
      deleteIcon.style.filter = 'brightness(0) invert(1)'; 
      const deleteText = document.createElement('span');
      deleteText.textContent = 'Drop to delete';
      deleteZone.appendChild(deleteIcon);
      deleteZone.appendChild(deleteText);
      document.body.appendChild(deleteZone);
    }

    const addColorSwatch = (hexColor) => {

      const existingSwatch = Array.from(swatchesContainer.children)
        .find(swatch => swatch.getAttribute('data-tooltip') === hexColor);
      if (existingSwatch) return;

      const swatch = document.createElement('div');
      swatch.className = 'color-swatch deletable';
      swatch.setAttribute('data-tooltip', hexColor);
      swatch.setAttribute('data-saved', 'true');
      swatch.style.width = '32px';
      swatch.style.height = '32px';
      swatch.style.backgroundColor = hexColor;
      swatch.style.border = '1px solid #d6d6d6';
      swatch.style.borderRadius = '0';
      swatch.style.position = 'relative';
      swatch.style.cursor = 'pointer';
      swatch.draggable = true; 

      const checkmark = document.createElement('img');
      checkmark.src = chrome.runtime.getURL("assets/check-color-ic.svg");
      checkmark.className = 'checkmark';
      checkmark.alt = '';
      swatch.appendChild(checkmark);

      swatch.addEventListener('dragstart', (e) => {
        if (swatch.classList.contains('disabled')) {
          e.preventDefault();
          return false;
        }
        
        if (swatch.getAttribute('data-saved') === 'true') {
          swatch.classList.add('dragging');
          deleteZone.classList.add('active');

          e.dataTransfer.setData('text/plain', swatch.getAttribute('data-tooltip'));
          e.dataTransfer.setDragImage(swatch, 16, 16);
          e.dataTransfer.effectAllowed = 'move';
        }
      });

      swatch.addEventListener('dragend', () => {
        swatch.classList.remove('dragging');
        deleteZone.classList.remove('active');
        deleteZone.classList.remove('highlight');
      });

      swatch.addEventListener('click', async (e) => {
        if (swatch.classList.contains('disabled')) {
          return;
        }
        
        if (!swatch.classList.contains('dragging')) {
          try {

            document.body.dispatchEvent(new MouseEvent('click', {
              bubbles: true,
              cancelable: true,
            }));

            await new Promise(resolve => setTimeout(resolve, 100));

            if (selectedSwatch) {
              selectedSwatch.classList.remove('selected');
            }
            swatch.classList.add('selected');
            selectedSwatch = swatch;

            lastSelectedColor = hexColor;

            await new Promise(resolve => setTimeout(resolve, 150));

            document.body.classList.add('temp-hide-color-picker');

            const designButton = document.querySelector('.btn.btn-outline-primary.btn-DESIGN.ng-star-inserted');
            if (designButton) {
              console.log();
              designButton.click();
            }
          } catch (error) {
            console.error();
          }
        }
      });

      swatchesContainer.appendChild(swatch);
    };

    deleteZone.addEventListener('dragenter', (e) => {
      e.preventDefault();
      e.dataTransfer.dropEffect = 'move';
      deleteZone.classList.add('highlight');
    });

    deleteZone.addEventListener('dragleave', () => {
      deleteZone.classList.remove('highlight');
    });

    deleteZone.addEventListener('dragover', (e) => {
      e.preventDefault();
      e.dataTransfer.dropEffect = 'move';
    });

    deleteZone.addEventListener('drop', async (e) => {
      e.preventDefault();
      const color = e.dataTransfer.getData('text/plain');
      console.log();

      if (color) {
        try {
          const swatchesToRemove = document.querySelectorAll('.color-swatch[data-saved="true"]');
          let wasSelected = false;
          
          swatchesToRemove.forEach(swatch => {
            if (swatch.getAttribute('data-tooltip') === color) {
              wasSelected = swatch.classList.contains('selected');
              swatch.remove();
            }
          });
          
          if (wasSelected) {
            const whiteSwatches = document.querySelectorAll('.color-swatch[style*="background-color: rgb(255, 255, 255)"]');
            whiteSwatches.forEach(whiteSwatch => {
              whiteSwatch.classList.add('selected');
              selectedSwatch = whiteSwatch;
              handleColorSelect('#ffffff');
            });

            const designButton = document.querySelector('.btn.btn-outline-primary.btn-DESIGN.ng-star-inserted');
            if (designButton) {
              console.log();
              designButton.click();
            }
          }
          
          chrome.storage.sync.get('globalSavedColors').then(result => {
            const savedColors = result.globalSavedColors || [];
            const updatedColors = savedColors.filter(c => c.toLowerCase() !== color.toLowerCase());
            chrome.storage.sync.set({ globalSavedColors: updatedColors });
          }).catch(error => {
            console.error('Error updating saved colors:', error);
          });
        } catch (error) {
          console.error();
        }
      }
      
      deleteZone.classList.remove('active');
      deleteZone.classList.remove('highlight');
    });

    loadGlobalSavedColors().then(savedColors => {
      savedColors.forEach(color => addColorSwatch(color));
    });

    if (!document.getElementById('global-tooltip-style')) {
      const globalTooltipStyle = document.createElement('style');
      globalTooltipStyle.id = 'global-tooltip-style';
      globalTooltipStyle.textContent = `
        [data-tooltip] {
          position: relative;
        }
        [data-tooltip]:before {
          content: attr(data-tooltip);
          position: absolute;
          bottom: calc(100% + 8px);
          left: 50%;
          transform: translateX(-50%);
          padding: 4px 12px;
          background-color: #1F2937;
          color: white;
          font-size: 12px;
          font-family: 'Amazon Ember', sans-serif;
          font-weight: 500;
          white-space: nowrap;
          border-radius: 999px;
          pointer-events: none;
          opacity: 0;
          visibility: hidden;
          transition: opacity 0.2s, visibility 0.2s;
          z-index: 9999;
        }
        [data-tooltip]:after {
          content: '';
          position: absolute;
          bottom: calc(100% + 4px);
          left: 50%;
          transform: translateX(-50%);
          border-left: 5px solid transparent;
          border-right: 5px solid transparent;
          border-top: 5px solid #1F2937;
          pointer-events: none;
          opacity: 0;
          visibility: hidden;
          transition: opacity 0.2s, visibility 0.2s;
          z-index: 9999;
        }
        [data-tooltip]:hover:before,
        [data-tooltip]:hover:after {
          opacity: 1;
          visibility: visible;
        }
        [data-tooltip][disabled]:before,
        [data-tooltip][disabled]:after {
          display: none !important;
        }
      `;
      document.head.appendChild(globalTooltipStyle);
    }
  }

  const observer = new MutationObserver(() => {

    removeUnwantedScalableContainers();

    products.forEach(product => {
      const card = document.getElementById(`${product.name}-card`);
      if (card) {
        const targetContainer = card.querySelector('.form-row > .col-6:nth-child(2)');
        if (targetContainer && !targetContainer.querySelector(`.${product.scalablecolorClass}`)) {
          injectScalableColorContainer(product, targetContainer);
        }
      }
    });
  });
  observer.observe(document.body, { childList: true, subtree: true });

  document.addEventListener("click", function(e) {
    if (e.target.matches(".btn.btn-secondary.btn-edit") || e.target.matches(".btn-secondary.btn-edit")) {
      const btn = e.target;
      const classes = Array.from(btn.classList);
      let prodName = classes.find(c => c.endsWith("-edit-btn"));
      if (prodName) {
        prodName = prodName.replace("-edit-btn", "").toUpperCase();

        if (allowedProducts.includes(prodName)) {
          const product = products.find(p => p.name === prodName);
          if (product) {
            const card = btn.closest("div.mb-base");
            if (!card) return;
            const targetContainer = card.querySelector(".form-row > .col-6:nth-child(2)");
            if (!targetContainer) return;

            products.forEach(p => {
              targetContainer.querySelectorAll(`.${p.scalablecolorClass}`).forEach(el => el.remove());
            });

            injectScalableColorContainer(product, targetContainer);
          }
        } else {

          const card = btn.closest("div.mb-base");
          if (card) {
            const targetContainer = card.querySelector(".form-row > .col-6:nth-child(2)");
            if (targetContainer) {
              targetContainer.querySelectorAll('[class*="scalable-color"]').forEach(el => el.remove());
            }
          }
        }
      }
    }
  });

  removeUnwantedScalableContainers();
  products.forEach(product => {
    const card = document.getElementById(`${product.name}-card`);
    if (!card) return;
    const targetContainer = card.querySelector('.form-row > .col-6:nth-child(2)');
    if (!targetContainer) return;
    injectScalableColorContainer(product, targetContainer);
  });

  const colorPickerHideStyle = document.createElement('style');
  colorPickerHideStyle.textContent = `

    body.temp-hide-color-picker .sketch-picker,
    body.temp-hide-color-picker .popover.color-picker-popover,
    body.temp-hide-color-picker ngb-popover-window[class*="color-picker-popover"] {
      position: absolute !important;
      visibility: hidden !important;
      opacity: 0 !important;
      pointer-events: none !important;
      height: 0 !important;
      overflow: hidden !important;
      margin: 0 !important;
      padding: 0 !important;
      border: none !important;
      width: 0 !important;
    }

    body.temp-hide-color-picker .popover.color-picker-popover .arrow,
    body.temp-hide-color-picker ngb-popover-window[class*="color-picker-popover"] .arrow {
      display: none !important;
    }

    .sketch-picker input {
      pointer-events: auto !important;
    }
  `;
  document.head.appendChild(colorPickerHideStyle);
}

if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", initInjection);
} else {
  initInjection();
}