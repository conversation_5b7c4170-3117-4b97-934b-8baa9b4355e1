!function(){"use strict";const e="https://merch.amazon.com",t="/designs/new";function n(){return window.location.origin===e&&window.location.pathname===t}function l(){var e,t;!document.querySelector(".snap-upload-container")&&((e=new MutationObserver((e,t)=>{var n=document.querySelector(".row.mb-4");n&&(o(n),t.disconnect())})).observe(document.body,{childList:!0,subtree:!0}),t=document.querySelector(".row.mb-4"))&&(o(t),e.disconnect())}n()&&("loading"===document.readyState?document.addEventListener("DOMContentLoaded",()=>{n()&&l()}):n()&&l());{let e=window.location.href;setInterval(()=>{window.location.href!==e&&(e=window.location.href,n())&&l()},1e3);const a=history.pushState,r=(history.pushState=function(...e){a.apply(this,e),window.dispatchEvent(new Event("locationchange"))},history.replaceState);history.replaceState=function(...e){r.apply(this,e),window.dispatchEvent(new Event("locationchange"))},window.addEventListener("locationchange",()=>{n()&&l()})}async function L(r,i){return new Promise(o=>{var e=new Image;const a=URL.createObjectURL(r);e.crossOrigin="Anonymous",e.src=a,e.onload=async()=>{URL.revokeObjectURL(a);var e=await r.arrayBuffer(),t=new Uint8Array(e),n=[137,80,78,71,13,10,26,10];for(let e=0;e<n.length;e++)if(t[e]!==n[e])return void o(r);var e=Math.round(39.3701*i),e=new Uint8Array([e>>>24&255,e>>>16&255,e>>>8&255,255&e,e>>>24&255,e>>>16&255,e>>>8&255,255&e,1]),e=new Uint8Array([112,72,89,115,...e]),l=function(t){let n=-1;for(let e=0;e<t.length;e++)n=n>>>8^s[255&(n^t[e])];return(-1^n)>>>0}(e),e=new Uint8Array([0,0,0,9,...e,l>>>24&255,l>>>16&255,l>>>8&255,255&l]),l=new Uint8Array(t.length+e.length),e=(l.set(t.slice(0,33),0),l.set(e,33),l.set(t.slice(33),33+e.length),new Blob([l],{type:"image/png"}));o(e)},e.onerror=()=>{URL.revokeObjectURL(a),o(r)}})}const s=(()=>{let t;var n=[];for(let e=0;e<256;e++){t=e;for(let e=0;e<8;e++)t=1&t?3988292384^t>>>1:t>>>1;n[e]=t}return n})();function o(t){if(!t.querySelector(".snap-upload-container")){const o=document.createElement("div");o.classList.add("snap-upload-container"),o.style.border="2px dashed #470CED",o.style.borderRadius="8px",o.style.width="100%",o.style.height="75%",o.style.display="flex",o.style.flexDirection="column",o.style.alignItems="center",o.style.justifyContent="center",o.style.boxSizing="border-box",o.style.cursor="pointer",o.style.marginLeft="16px",o.style.backgroundColor="white",o.style.position="relative";var n=i(),n=(o.appendChild(n),o.addEventListener("dragover",e=>{e.preventDefault(),o.style.borderColor="#01BB87"}),o.addEventListener("dragleave",()=>{o.style.borderColor="#470CED"}),o.addEventListener("drop",e=>{e.preventDefault(),o.style.borderColor="#470CED",p(e.dataTransfer.files[0],o)}),o.addEventListener("click",()=>{const e=document.createElement("input");e.type="file",e.accept="image/png",e.style.display="none",e.onchange=()=>{p(e.files[0],o)},document.body.appendChild(e),e.click(),document.body.removeChild(e)}),t.appendChild(o),o);if(!n.nextElementSibling||"download-toggle-container"!==n.nextElementSibling.id){var l=document.createElement("div");l.id="download-toggle-container",l.style.cssText=`
      display: flex;
      align-items: center;
      justify-content: flex-start;
      cursor: pointer;
      margin-top: 10px;
      width: 100%;
    `;const a=document.createElement("div"),r=(a.style.cssText=`
      width: 36px;
      height: 18px;
      border-radius: 18px;
      background-color: rgb(207, 212, 212);
      position: relative;
      transition: background-color 0.3s;
      margin-right: 10px;
    `,document.createElement("span"));r.style.cssText=`
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background-color: white;
      position: absolute;
      top: 3px;
      left: 3px;
      transition: transform 0.3s;
    `,a.appendChild(r);var e=document.createElement("span");e.textContent="Download after processing",e.style.cssText=`
      font-size: 14px;
      color: rgb(100, 100, 100);
      user-select: none;
    `,!function(e,t){let n=localStorage.getItem("downloadAfterProcessing");null===n&&(n="false",localStorage.setItem("downloadAfterProcessing",n));"true"===n?(e.style.backgroundColor="#470CED",t.style.transform="translateX(18px)"):(e.style.backgroundColor="rgb(207, 212, 212)",t.style.transform="translateX(0)")}(a,r),l.addEventListener("click",()=>{var e,t,n;e=a,t=r,n=!(n="true"===localStorage.getItem("downloadAfterProcessing")),localStorage.setItem("downloadAfterProcessing",n.toString()),n?(e.style.backgroundColor="#470CED",t.style.transform="translateX(18px)"):(e.style.backgroundColor="rgb(207, 212, 212)",t.style.transform="translateX(0)")}),l.appendChild(a),l.appendChild(e),n.parentNode.insertBefore(l,n.nextSibling)}e=o;if(!e.nextElementSibling||"scaling-options-container"!==e.nextElementSibling.id){const s=document.createElement("div");s.id="scaling-options-container",s.style.cssText=`
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-top: 20px;
      width: 100%;
      padding-left: 0;
    `;[{id:"scaleCustom",label:"",value:"custom"},{id:"scale100",label:"100%",value:"100"},{id:"scale85",label:"85%",value:"85"},{id:"scale75",label:"75%",value:"75"}].forEach(t=>{const n=document.createElement("label"),l=(n.style.cssText=`
        display: flex;
        align-items: center;
        margin-right: 15px;
        font-size: 14px;
        cursor: pointer;
        position: relative;
      `,document.createElement("input")),o=(l.type="radio",l.name="scaleOption",l.id=t.id,l.value=t.value,l.style.display="none",document.createElement("span"));function a(){var e;o.innerHTML="",l.checked?(o.style.display="inline-block",o.style.width="1rem",o.style.height="1rem",o.style.border="0.0625rem solid #470CED",o.style.borderRadius="50%",o.style.backgroundColor="#470CED",o.style.marginRight="5px",o.style.position="relative",(e=document.createElement("span")).style.height="0.5rem",e.style.width="0.5rem",e.style.backgroundColor="rgb(255, 255, 255)",e.style.borderRadius="50%",e.style.position="absolute",e.style.top="50%",e.style.left="50%",e.style.transform="translate(-50%, -50%)",o.appendChild(e)):(o.style.display="inline-block",o.style.width="1rem",o.style.height="1rem",o.style.border="0.0625rem solid #470CED",o.style.borderRadius="50%",o.style.backgroundColor="#fff",o.style.marginRight="5px",o.style.position="relative")}if(o.style.cursor="pointer",o.addEventListener("click",()=>{var e;l.checked=!0,u(t.id,s),c(s),"scaleCustom"===t.id&&(e=n.querySelector('input[type="number"]'))&&e.focus()}),n.addEventListener("click",()=>{"scaleCustom"!==t.id&&(l.checked=!0,u(t.id,s),c(s))}),n.appendChild(l),n.appendChild(o),t.label&&n.appendChild(document.createTextNode(t.label)),"scaleCustom"===t.id){const i=document.createElement("input");i.type="number",i.min="50",i.max="100",i.step="0.1",i.style.width="64px",i.style.height="calc(1.5rem * 1.1)",i.style.marginLeft="5px",i.style.fontSize="13px",i.style.padding="2px",i.style.border="none",i.style.borderBottom="2px solid #ccc",i.style.borderRadius="0",i.style.transition="border-bottom 0.3s ease",i.disabled="scaleCustom"!==window.selectedScaleOption;var e=document.createElement("div"),r=(e.style.display="flex",e.style.alignItems="center",e.appendChild(i),document.createElement("span"));r.textContent="%",r.style.marginLeft="4px",r.style.fontSize="13px",e.appendChild(r),i.addEventListener("click",e=>{e.stopPropagation(),l.checked=!0,u("scaleCustom",s),c(s),i.focus()}),i.addEventListener("input",()=>{var e=parseFloat(i.value);!isNaN(e)&&50<=e&&e<=100?(localStorage.setItem("customScaleValue",i.value),i.style.borderColor="#ccc"):i.style.borderColor="red",m()}),l.customScaleInput=i,n.appendChild(e)}l.addEventListener("change",()=>{var e;a(),l.checked&&"scaleCustom"===l.id&&(e=n.querySelector('input[type="number"]'))&&e.focus()}),a(),s.appendChild(n)}),e.parentNode.insertBefore(s,e.nextSibling)}{l=t;let e=l.querySelector(".snap-right-container");e||((e=document.createElement("div")).classList.add("snap-right-container","col"),l.appendChild(e));var n=l.querySelector(".snap-upload-container"),t=l.querySelector("#download-toggle-container"),l=l.querySelector("#scaling-options-container");n&&t&&l&&(e.appendChild(n),e.appendChild(l),e.appendChild(t),d(e,{display:"flex",flexDirection:"column",alignItems:"stretch"}),d(n,{alignSelf:"flex-end"}),d(l,{alignSelf:"flex-start",marginTop:"20px"}),d(t,{alignSelf:"flex-start",marginTop:"10px"}))}n=localStorage.getItem("selectedScaleOption");window.selectedScaleOption=n||"scale100",(n=document.getElementById(window.selectedScaleOption))&&(n.checked=!0,n.dispatchEvent(new Event("change"))),"scaleCustom"===window.selectedScaleOption&&(n=document.querySelector('#scaling-options-container input[type="number"]'))&&(n.style.cursor="text",n.disabled=!1,(t=localStorage.getItem("customScaleValue"))&&(n.value=t),n.focus()),m()}}function i(){var e=document.createElement("div");e.style.display="flex",e.style.flexDirection="column",e.style.alignItems="center";const t=document.createElement("img");var n=chrome.runtime.getURL("assets/draganddrop.svg"),n=(t.src=n,t.alt="Drag and Drop",t.style.width="40px",t.style.height="40px",t.style.marginBottom="8px",t.onerror=()=>{t.style.display="none"},document.createElement("p")),l=(n.textContent="Upload and convert to 4500x5400px",n.style.fontSize="14px",n.style.fontWeight="700",n.style.color="#470CED",n.style.margin="0",document.createElement("p"));return l.textContent="Drag and drop PNG format",l.style.fontSize="14px",l.style.color="#9AA5B1",l.style.margin="0",e.appendChild(t),e.appendChild(n),e.appendChild(l),e}function c(e){e.querySelectorAll('input[type="radio"]').forEach(e=>{e.dispatchEvent(new Event("change"))})}function d(t,n){Object.keys(n).forEach(e=>{t.style[e]=n[e]})}async function p(t,n){var l,o,a,r,i,s,c,d;if(t&&"image/png"===t.type)try{s="Processing image...",(i=n).innerHTML="",i.style.borderColor="#FFA500",(c=document.createElement("div")).style.display="flex",c.style.flexDirection="column",c.style.alignItems="center",(d=document.createElement("p")).textContent=s,d.style.fontSize="14px",d.style.fontWeight="700",d.style.color="#FFA500",d.style.margin="0",c.appendChild(d),i.appendChild(c);var p,u=function(e){let t="";var n=new Uint8Array(e);for(let e=0;e<n.byteLength;e++)t+=String.fromCharCode(n[e]);e="data:image/png;base64,"+window.btoa(t);return e}(await t.arrayBuffer()),m=document.querySelector(".mb-4");m&&(p=m.querySelector("i.sci-delete-forever"))&&(p.click(),r=300,await new Promise(e=>setTimeout(e,r)));let e=1;var y=window.selectedScaleOption||"scale100";if("scale100"===y)e=1;else if("scale85"===y)e=.85;else if("scale75"===y)e=.75;else if("scaleCustom"===y){var g=document.querySelector('#scaling-options-container input[type="number"]'),h=parseFloat(g.value);if(!(!isNaN(h)&&50<=h&&h<=100))throw new Error("Invalid custom scale value.");e=h/100}var f=await async function(e,h){const f=new Image;return f.crossOrigin="Anonymous",f.src=e,new Promise((y,g)=>{f.onload=async()=>{try{var t=function(e){var n=document.createElement("canvas"),t=(n.width=e.width,n.height=e.height,n.getContext("2d")),t=(t.drawImage(e,0,0),t.getImageData(0,0,n.width,n.height)),l=t.data;let o=null,a=null,r=null,i=null;for(let t=0;t<n.height;t++)for(let e=0;e<n.width;e++){var s=4*(t*n.width+e);0!==l[3+s]&&(null===o&&(o=t),a=t,(null===r||e<r)&&(r=e),null===i||e>i)&&(i=e)}if(null===o)return{canvas:null,width:0,height:0};var t=i-r+1,c=a-o+1,d=document.createElement("canvas"),p=(d.width=t,d.height=c,d.getContext("2d"));return p.drawImage(e,r,o,t,c,0,0,t,c),{canvas:d,width:t,height:c}}(f);if(!t.canvas)throw new Error("The uploaded image is fully transparent.");

              const isAlready4500x5400 = 
                Math.abs(t.width - 4500) <= 10 && 
                Math.abs(t.height - 5400) <= 10;
              
              let finalCanvas;
              
              if (isAlready4500x5400 && h === 1) {
                finalCanvas = t.canvas;
              } else {
                var n=document.createElement("canvas"),
                    l=(n.width=4500,n.height=5400,n.getContext("2d")),
                    o=(l.clearRect(0,0,n.width,n.height),4500*h),
                    a=5370*h,
                    r=Math.min(o/t.width,a/t.height),
                    i=t.width*r,
                    s=t.height*r,
                    c=(4500-i)/2;
                l.drawImage(t.canvas,c,30,i,s);
                finalCanvas = n;
              }

              let e = await new Promise((t,n)=>{
                finalCanvas.toBlob(e=>{
                  e?t(e):n(new Error("Canvas toBlob failed."))
                },"image/png",1)
              });
              
              if (e.size > 10485760) {
                let compressionLevel = 1024;
                let compressedBlob = null;
                let attempts = 0;
                const MAX_ATTEMPTS = 5;
                
                while (attempts < MAX_ATTEMPTS) {
                  attempts++;
                  
                  d = e;
                  p = compressionLevel;
                  d = await d.arrayBuffer();
                  d = UPNG.decode(d);
                  u = UPNG.toRGBA8(d)[0];
                  u = UPNG.encode([u], d.width, d.height, p);
                  compressedBlob = await new Blob([u], {type: "image/png"});
                  
                  if (compressedBlob.size <= 20971520 || compressionLevel <= 128) {
                    break;
                  }
                  
                  compressionLevel = Math.floor(compressionLevel / 2);
                }
                
                e = compressedBlob;
              }
              
              e = await L(e, 300);
              y(e);
            } catch(e){g(e)}var d,p,u,m},f.onerror=()=>{g()}})}(u,e),w=t.name.replace(/\.[^/.]+$/,"")+"_Snap.png",v=("true"===localStorage.getItem("downloadAfterProcessing")&&(l=f,o=w,l=URL.createObjectURL(l),(a=document.createElement("a")).href=l,a.download=o,document.body.appendChild(a),a.click(),document.body.removeChild(a),await!URL.revokeObjectURL(l)),f),b=t.name;if(v){var x=document.querySelector("div.dropzone-container");if(x){x=x.querySelector('input[type="file"]');if(x)try{var C=new DataTransfer,S=new File([v],b,{type:"image/png"}),E=(C.items.add(S),x.files=C.files,new Event("change",{bubbles:!0}));x.dispatchEvent(E)}catch{}}}await 0,k(n)}catch{I(n),setTimeout(()=>k(n),2e3)}else I(n),setTimeout(()=>k(n),2e3)}function u(e,t){window.selectedScaleOption=e,localStorage.setItem("selectedScaleOption",e);t=t.querySelector('input[type="number"]');t&&("scaleCustom"===e?(t.disabled=!1,t.style.cursor="text",(e=localStorage.getItem("customScaleValue"))&&(t.value=e),t.focus()):(t.disabled=!0,t.value="",t.style.cursor="not-allowed",t.style.borderColor="#ccc"),m())}function m(){var e,t=document.querySelector(".snap-upload-container .resize-btn");t&&("scaleCustom"===window.selectedScaleOption?(e=document.querySelector('#scaling-options-container input[type="number"]'))&&(e=parseFloat(e.value),isNaN(e)||e<50||100<e?(t.disabled=!0,t.style.cursor="not-allowed"):(t.disabled=!1,t.style.cursor="pointer")):["scale100","scale85","scale75"].includes(window.selectedScaleOption)?(t.disabled=!1,t.style.cursor="pointer"):(t.disabled=!0,t.style.cursor="not-allowed"))}function k(e){e.innerHTML="",e.style.borderColor="#470CED";var t=i();e.appendChild(t)}function I(e){e.innerHTML="",e.style.borderColor="#FF391F";var t=document.createElement("div");t.style.display="flex",t.style.alignItems="center",t.style.justifyContent="center",t.style.gap="16px";const n=document.createElement("img");var l=chrome.runtime.getURL("assets/error.svg"),l=(n.src=l,n.alt="Error",n.style.width="40px",n.style.height="40px",n.onerror=()=>{n.style.display="none"},document.createElement("div")),o=document.createElement("p"),a=(o.textContent="Invalid File",o.style.fontSize="16px",o.style.color="#FF391F",o.style.fontWeight="bold",o.style.margin="0",document.createElement("p"));a.textContent="Upload a PNG file.",a.style.fontSize="14px",a.style.color="#9AA5B1",a.style.margin="0",l.appendChild(o),l.appendChild(a),t.appendChild(n),t.appendChild(l),e.appendChild(t)}}();