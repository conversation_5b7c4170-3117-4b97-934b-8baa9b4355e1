!function(){function e(){let e="true"===localStorage.getItem("guidesOn");document.querySelectorAll("toggle-switch label.toggle-switch").forEach((t=>{const n=t.querySelector("input#guides-toggle-switch"),o=t.querySelector("span.rounded-slider");if(n&&o){const o=n.checked;(e&&!o||!e&&o)&&t.click()}}))}!function t(){if(!document.body.contains(document.querySelector("div.container-fluid")))return void setTimeout(t,500);const n=document.querySelector("div.container-fluid").querySelector(".row.mb-4 > .col-3:not(.p-base)");if(n){if(!n.querySelector("#guides-toggle-button")){const t=document.createElement("div");t.id="guides-toggle-button",t.style.cssText="\n                display: inline-flex;\n                align-items: center;\n                cursor: pointer;\n                margin-top: 20px;\n            ";const o=document.createElement("div");o.style.cssText="\n                width: 36px;\n                height: 18px;\n                border-radius: 18px;\n                background-color: rgb(207, 212, 212);\n                position: relative;\n                transition: background-color 0.3s;\n                margin-right: 10px;\n            ";const r=document.createElement("span");r.style.cssText="\n                width: 12px;\n                height: 12px;\n                border-radius: 50%;\n                background-color: white;\n                position: absolute;\n                top: 3px;\n                left: 3px;\n                right: 3px;\n                transition: transform 0.3s;\n            ",o.appendChild(r);const l=document.createElement("span");l.style.cssText="\n                font-size: 14px;\n                color: rgb(100, 100, 100);\n            ",function(e,t,n){let o=localStorage.getItem("guidesOn");null===o&&(o="true",localStorage.setItem("guidesOn",o));"true"===o?(e.style.backgroundColor="#470CED",t.style.transform="translateX(18px)",n.textContent="Guides On",n.style.color="rgb(0, 0, 0)"):(e.style.backgroundColor="rgb(207, 212, 212)",t.style.transform="translateX(0)",n.textContent="Guides Off",n.style.color="rgb(100, 100, 100)")}(o,r,l),t.addEventListener("click",(function(){!function(t,n,o){let r="true"===localStorage.getItem("guidesOn");r=!r,localStorage.setItem("guidesOn",r),r?(t.style.backgroundColor="#470CED",n.style.transform="translateX(18px)",o.textContent="Guides On",o.style.color="rgb(0, 0, 0)"):(t.style.backgroundColor="rgb(207, 212, 212)",n.style.transform="translateX(0)",o.textContent="Guides Off",o.style.color="rgb(100, 100, 100)");e()}(o,r,l),e()})),t.appendChild(o),t.appendChild(l),n.appendChild(t)}document.body.addEventListener("click",(function(t){t.target.closest("div.product-card")&&e()}))}else setTimeout(t,500)}()}();