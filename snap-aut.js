class SnapAutomation {
    constructor() {
        this.isProcessing = false;
        this.currentFile = null;
        this.settings = null;
        this.retryCount = 0;
        this.maxRetries = 5;
        this.currentOperation = null;
        this.uploadComplete = false;
        this.generatingImageCount = 0;
        this.requiredGeneratingImageCount = 1;
        this.lastClickedProduct = null; 
        this.productSelectors = {
            uploadContainer: '.snap-upload-container',
            deleteIcon: '.sci-icon.sci-delete-forever',
            generatingMessages: [
                "Generating image....",
                "Bild lädt....",
                "Chargement de l'image....",
                "Creando immagine....",
                "Generando imagen....",
                "画像を読み込み中...."
            ],
            textFields: [
                '#designCreator-productEditor-title',
                '#designCreator-productEditor-brandName',
                '#designCreator-productEditor-featureBullet1',
                '#designCreator-productEditor-featureBullet2',
                '#designCreator-productEditor-description'
            ],
            copyEnToAll: {
                checkmark: '.col-auto.pl-medium-large.pr-0.pt-1.ml-3.mb-0',
                container: '.ng-star-inserted',
                findContainer: 'snap-copy-find-container',
                copyBtn: '#copyBtn',
                pasteAllBtn: '#pasteAllBtn'
            },
            saveToDrafts: 'button#draft-button'
        };

        this.initialize();
    }

    async initialize() {
        try {
            const urlParams = new URLSearchParams(window.location.search);
            const snapParam = urlParams.get('Snap');

            if (!snapParam) {
                return;
            }

            this.hideSnapBulkUploadButton();

            const [index, ...filenameParts] = snapParam.split('-');
            const filename = filenameParts.join('-');

            if (!filename) {
                return;
            }

            const state = await this.getAutomationState();
            if (!state || !state.isRunning) {
                console.log();
                return;
            }

            this.currentFile = state.currentFile;
            this.settings = state.settings;

            console.log('', JSON.stringify({
                useNativeUploader: this.settings?.useNativeUploader,
                hasSettings: !!this.settings,
                settingsType: typeof this.settings
            }));

            console.log('', {
                filename: this.currentFile?.filename,
                hasBlobUrl: !!this.currentFile?.blobUrl,
                hasBase64: !!this.currentFile?.base64,
                blobUrlType: typeof this.currentFile?.blobUrl,
                fullState: state
            });

            console.log();
            await new Promise((resolve) => {
                chrome.runtime.sendMessage({
                    action: 'setActiveProgressTab'
                }, (response) => {
                    if (response && response.success) {
                        console.log();
                    } else {
                        console.warn();
                    }
                    resolve();
                });
            });

            if (this.settings) {
                console.log();

                if (this.settings.clothingProducts) {
                    const isActive = this.settings.clothingProducts.isActive === true;
                    console.log('', 
                        isActive ? 'ACTIVE' : 'INACTIVE',
                        'Type:', typeof this.settings.clothingProducts.isActive);

                    this.settings.clothingProducts.isActive = isActive;
                } else {
                    console.warn();
                    this.settings.clothingProducts = { isActive: false };
                }

                if (this.settings.scalableProducts) {
                    const isActive = this.settings.scalableProducts.isActive === true;
                    console.log('', 
                        isActive ? 'ACTIVE' : 'INACTIVE',
                        'Type:', typeof this.settings.scalableProducts.isActive);

                    this.settings.scalableProducts.isActive = isActive;
                } else {
                    console.warn();
                    this.settings.scalableProducts = { isActive: false };
                }

                if (this.settings.tumblerProduct) {
                    const isActive = this.settings.tumblerProduct.isActive === true;
                    console.log('', 
                        isActive ? 'ACTIVE' : 'INACTIVE',
                        'Type:', typeof this.settings.tumblerProduct.isActive);

                    this.settings.tumblerProduct.isActive = isActive;
                } else {
                    console.warn();
                    this.settings.tumblerProduct = { isActive: false };
                }

                const activeProducts = [];
                if (this.settings.clothingProducts.isActive === true) activeProducts.push('Clothing');
                if (this.settings.scalableProducts.isActive === true) activeProducts.push('Scalable');
                if (this.settings.tumblerProduct.isActive === true) activeProducts.push('Tumbler');

                console.log();
            } else {
                console.error();
                this.settings = {
                    clothingProducts: { isActive: false },
                    scalableProducts: { isActive: false },
                    tumblerProduct: { isActive: false }
                };
            }

            if (!this.currentFile || (!this.currentFile.blobUrl && !this.currentFile.base64)) {
                console.error();
                return;
            }

            this.updateProgressUI('Uploading', false);

            this.startAutomation();

        } catch (error) {
            console.error();
            this.handleError(error);
        }
    }

    async startAutomation() {
        try {
            if (this.isProcessing) {
                console.log();
                return;
            }

            this.isProcessing = true;
            console.log();

            this.updateProgressUI('Uploading', false);

            if (!this.settings) {
                console.error();
                throw new Error();
            }

            const activeProducts = [];
            if (this.settings.clothingProducts?.isActive === true) activeProducts.push('Clothing');
            if (this.settings.scalableProducts?.isActive === true) activeProducts.push('Scalable');
            if (this.settings.tumblerProduct?.isActive === true) activeProducts.push('Tumbler');

            console.log();

            console.log();
            await this.waitForElement(this.productSelectors.uploadContainer);
            console.log();

            await this.uploadFile();

            console.log();
            await this.waitForElement(this.productSelectors.deleteIcon);
            this.uploadComplete = true;
            console.log();

            console.log();
            await this.sleep(500);

            this.updateProgressUI('Processing Products', false);

            await this.processProducts();

            console.log();
            await this.sleep(500);

            await this.processActions();

            console.log();
            await this.sleep(500);

            console.log();
            this.updateProgressUI('Completed', true);

            this.isProcessing = false;

            await this.processNextFile();

        } catch (error) {
            console.error();
            this.handleError(error);
        }
    }

    async uploadFile() {
        try {
            console.log();
            this.currentOperation = 'Uploading';

            this.updateProgressUI('Uploading', false);

            const useNativeUploader = this.settings && this.settings.useNativeUploader === true;
            
            console.log('', useNativeUploader ? '' : '', 
                'Settings value:', this.settings?.useNativeUploader,
                'Settings type:', typeof this.settings?.useNativeUploader,
                'Full settings object available:', !!this.settings);
            
            if (useNativeUploader) {
                console.log('');
                const dropzoneContainer = document.querySelector('div.dropzone-container');
                if (!dropzoneContainer) {
                    throw new Error('');
                }
                const fileInput = dropzoneContainer.querySelector('input[type="file"]');
                if (!fileInput) {
                    throw new Error('');
                }
                let file;
                if (this.currentFile.blobUrl) {
                    file = await this.blobUrlToFile(this.currentFile.blobUrl, this.currentFile.filename);
                } else if (this.currentFile.base64) {
                    file = this.base64ToFile(this.currentFile.base64, this.currentFile.filename);
                } else {
                    throw new Error('');
                }
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                fileInput.files = dataTransfer.files;
                const changeEvent = new Event('change', { bubbles: true });
                fileInput.dispatchEvent(changeEvent);
            } else {
                console.log('');
                const uploadContainer = document.querySelector(this.productSelectors.uploadContainer);
                if (!uploadContainer) {
                    throw new Error('');
                }
                let file;
                if (this.currentFile.blobUrl) {
                    file = await this.blobUrlToFile(this.currentFile.blobUrl, this.currentFile.filename);
                } else if (this.currentFile.base64) {
                    file = this.base64ToFile(this.currentFile.base64, this.currentFile.filename);
                } else {
                    throw new Error('');
                }
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                const dropEvent = new DragEvent('drop', {
                    bubbles: true,
                    cancelable: true,
                    dataTransfer
                });
                uploadContainer.dispatchEvent(dropEvent);
            }

            console.log();
            await this.sleep(500);

        } catch (error) {
            console.error();
            throw error;
        }
    }

    async processProducts() {
        try {
            console.log();

            if (!this.settings) {
                console.error();
                throw new Error();
            }

            console.log();
            console.log();

            if (this.settings.clothingProducts && this.settings.clothingProducts.isActive === true) {
                console.log();

                this.updateProgressUI('Processing Products', false);
                await this.processClothingProducts();

                console.log();
                await this.sleep(1000);
            } else {
                console.log();
            }

            if (this.settings.scalableProducts && this.settings.scalableProducts.isActive === true) {
                console.log();

                this.updateProgressUI('Processing Scalable Products', false);
                await this.processScalableProducts();

                console.log();
                await this.sleep(1000);
            } else {
                console.log();
            }

            if (this.settings.tumblerProduct && this.settings.tumblerProduct.isActive === true) {
                console.log();

                this.updateProgressUI('Processing TUMBLER', false);
                await this.processTumblerProduct();

                console.log();
                await this.sleep(1000);
            } else {
                console.log();
            }

        } catch (error) {
            console.error();
            throw error;
        }
    }

    async processActions() {
        try {
            console.log();

            const actions = this.settings?.actions;
            if (!actions) {
                console.log();
                return;
            }

            if (actions.textSwap) {
                console.log();

                this.updateProgressUI('Text Swap', false);
                await this.processTextSwap();

                console.log();
                await this.sleep(1000);
            } else {
                console.log();
            }

            if (actions.copyEnToAll) {
                console.log();

                this.updateProgressUI('Copy EN to All', false);
                await this.processCopyEnToAll();

                console.log();
                await this.sleep(1000);
            } else {
                console.log();
            }

            if (actions.saveToDrafts) {
                console.log();

                this.updateProgressUI('Save to Drafts', false);
                await this.processSaveToDrafts();

                console.log();
                await this.sleep(1000);
            } else {
                console.log();
            }

        } catch (error) {
            console.error();
            throw error;
        }
    }

    async processTextSwap() {
        try {
            console.log();
            this.updateProgressUI('Text Swap', false);

            const cleanedFilename = this.cleanFilenameForTextSwap(this.currentFile.filename);
            console.log();

            for (const selector of this.productSelectors.textFields) {
                const field = document.querySelector(selector);
                if (field) {
                    console.log();
                    const originalText = field.value;
                    const newText = originalText.replace(/#snap/g, cleanedFilename);

                    if (originalText !== newText) {
                        console.log();
                        field.value = newText;
                        field.dispatchEvent(new Event('input', { bubbles: true }));

                        console.log();
                        await this.sleep(250);
                    } else {
                        console.log();
                    }
                } else {
                    console.log();
                }
            }

            console.log();
            await this.sleep(400);

            console.log();

        } catch (error) {
            console.error();
            throw error;
        }
    }

    async processCopyEnToAll() {
        try {
            console.log();
            this.updateProgressUI('Copy EN to All', false);

            console.log();
            const checkmark = document.querySelector(this.productSelectors.copyEnToAll.checkmark);
            if (checkmark) {
                console.log();
                checkmark.click();

                console.log();
                await this.sleep(300);

                console.log();
                const shadowHostElement = document.querySelector('snap-copy-find-container');

                if (shadowHostElement) {
                    console.log();

                    const shadowRoot = shadowHostElement.shadowRoot;

                    if (shadowRoot) {
                        console.log();

                        console.log();
                        const copyBtn = shadowRoot.querySelector('#copyBtn');
                        if (copyBtn) {
                            console.log();
                            copyBtn.click();

                            console.log();
                            await this.sleep(300);

                            console.log();
                            const pasteAllBtn = shadowRoot.querySelector('#pasteAllBtn');
                            if (pasteAllBtn) {
                                console.log();
                                pasteAllBtn.click();

                                console.log();
                                await this.sleep(1500);
                            } else {
                                console.log();
                            }
                        } else {
                            console.log();
                        }
                    } else {
                        console.log();

                        this.tryFallbackCopyPasteMethod();
                    }
                } else {
                    console.log();

                    this.tryFallbackCopyPasteMethod();
                }
            } else {
                console.log();
            }

            console.log();

        } catch (error) {
            console.error();
            throw error;
        }
    }

    async tryFallbackCopyPasteMethod() {
        console.log();

        const allElements = document.querySelectorAll('*');
        let foundShadowRoot = false;

        for (const element of allElements) {
            if (element.shadowRoot) {
                console.log();
                const shadowRoot = element.shadowRoot;

                const copyBtn = shadowRoot.querySelector('#copyBtn');
                if (copyBtn) {
                    console.log();
                    copyBtn.click();
                    await this.sleep(300);

                    const pasteAllBtn = shadowRoot.querySelector('#pasteAllBtn');
                    if (pasteAllBtn) {
                        console.log();
                        pasteAllBtn.click();
                        await this.sleep(1500);
                        foundShadowRoot = true;
                        break;
                    }
                }
            }
        }

        if (!foundShadowRoot) {
            console.log();

            const container = document.querySelector(this.productSelectors.copyEnToAll.container);
            const copyContainer = container?.querySelector(this.productSelectors.copyEnToAll.findContainer);

            if (copyContainer) {

                console.log();
                const copyBtn = copyContainer.querySelector(this.productSelectors.copyEnToAll.copyBtn);
                if (copyBtn) {
                    console.log();
                    copyBtn.click();

                    console.log();
                    await this.sleep(300);

                    console.log();
                    const pasteAllBtn = copyContainer.querySelector(this.productSelectors.copyEnToAll.pasteAllBtn);
                    if (pasteAllBtn) {
                        console.log();
                        pasteAllBtn.click();

                        console.log();
                        await this.sleep(1500);
                    } else {
                        console.log();
                    }
                } else {
                    console.log();
                }
            } else {
                console.log();
            }
        }
    }

    async processSaveToDrafts() {
        try {
            console.log();
            this.updateProgressUI('Save to Drafts', false);

            console.log();
            const draftButton = document.querySelector(this.productSelectors.saveToDrafts);
            if (draftButton) {
                console.log();
                draftButton.click();

                console.log();
                await this.sleep(1000);

                console.log();
            } else {
                console.log();
            }

        } catch (error) {
            console.error();
            throw error;
        }
    }

    async processClothingProducts() {
        try {
            console.log();

            const settings = this.settings.clothingProducts;

            if (!settings || settings.isActive !== true) {
                console.log();
                return;
            }

            const shirtProductTypes = [
                'STANDARD_TSHIRT',
                'PREMIUM_TSHIRT',
                'VNECK',
                'TANK_TOP',
                'STANDARD_LONG_SLEEVE',
                'RAGLAN',
                'STANDARD_SWEATSHIRT'
            ];

            const allClothingProductTypes = [
                ...shirtProductTypes,
                'STANDARD_PULLOVER_HOODIE',
                'ZIP_HOODIE'
            ];

            const availableProducts = {};
            const disabledProducts = new Set();

            for (const productType of allClothingProductTypes) {
                const editButtonSelector = `.${productType}-edit-btn`;
                const editButton = document.querySelector(editButtonSelector);

                if (editButton) {

                    const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                    const productCard = editButton.closest('.product-card');
                    const isCardDisabled = productCard && productCard.classList.contains('disabled');

                    if (isButtonDisabled || isCardDisabled) {
                        console.log();
                        disabledProducts.add(productType);
                    } else {
                        availableProducts[productType] = editButton;
                        console.log();
                    }
                } else {
                    console.log();
                }
            }

            console.log();

            const hasGlobalColorSettings = settings.allProducts && settings.allProducts.colors !== 'Skip';
            const hasGlobalPriceSettings = settings.allProducts && settings.allProducts.prices !== 'Skip';

            const processedProducts = new Set();

            const hasShirtNonDefaultSettings = settings.shirts && (
                settings.shirts.sidesOptions !== 'Default (Front)' ||
                (settings.shirts.colors && settings.shirts.colors !== 'Skip') ||
                (settings.shirts.prices && settings.shirts.prices !== 'Skip')
            );

            if (hasShirtNonDefaultSettings) {
                console.log();

                let processedCount = 0;
                const foundShirts = Object.keys(availableProducts).filter(type => shirtProductTypes.includes(type));

                console.log();

                for (const shirtType of foundShirts) {
                    try {
                        console.log();

                        const editButton = availableProducts[shirtType];

                        const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                        const productCard = editButton.closest('.product-card');
                        const isCardDisabled = productCard && productCard.classList.contains('disabled');

                        if (isButtonDisabled || isCardDisabled) {
                            console.log();
                            continue;
                        }

                        console.log();

                        this.updateProgressUI(`Processing ${shirtType}`, false);

                        editButton.click();

                        console.log();
                        await this.sleep(1200);

                        if (settings.shirts.colors && settings.shirts.colors !== 'Skip') {
                            console.log();
                            await this.processProductColors(settings.shirts.colors);

                            console.log();
                            await this.sleep(250);
                        } else if (hasGlobalColorSettings) {

                            console.log();
                            await this.processProductColors(settings.allProducts.colors);

                            console.log();
                            await this.sleep(250);
                        }

                        if (settings.shirts.prices && settings.shirts.prices !== 'Skip') {
                            console.log();
                            await this.processProductPrices(settings.shirts.prices);

                            console.log();
                            await this.sleep(250);
                        } else if (hasGlobalPriceSettings) {

                            console.log();
                            await this.processProductPrices(settings.allProducts.prices);

                            console.log();
                            await this.sleep(250);
                        }

                        if (settings.shirts.sidesOptions !== 'Default (Front)') {
                            console.log();
                            await this.processProductSides(shirtType, settings.shirts.sidesOptions);

                            console.log();
                            await this.sleep(500);
                        }

                        processedProducts.add(shirtType);
                        processedCount++;

                        if (processedCount < foundShirts.length) {
                            console.log();
                            await this.sleep(1000);
                        }
                    } catch (error) {
                        console.error();
                        console.log();
                    }
                }

                console.log();
            } else if (hasGlobalColorSettings || hasGlobalPriceSettings) {

                console.log();

                const foundShirts = Object.keys(availableProducts).filter(type => shirtProductTypes.includes(type));

                for (let i = 0; i < foundShirts.length; i++) {
                    const shirtType = foundShirts[i];
                    try {
                        console.log();

                        const editButton = availableProducts[shirtType];

                        const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                        const productCard = editButton.closest('.product-card');
                        const isCardDisabled = productCard && productCard.classList.contains('disabled');

                        if (isButtonDisabled || isCardDisabled) {
                            console.log();
                            continue;
                        }

                        console.log();

                        this.updateProgressUI(`Processing ${shirtType}`, false);

                        editButton.click();

                        console.log();
                        await this.sleep(1200);

                        if (hasGlobalColorSettings) {
                            console.log();
                            await this.processProductColors(settings.allProducts.colors);

                            console.log();
                            await this.sleep(250);
                        }

                        if (hasGlobalPriceSettings) {
                            console.log();
                            await this.processProductPrices(settings.allProducts.prices);

                            console.log();
                            await this.sleep(250);
                        }

                        processedProducts.add(shirtType);

                        if (i < foundShirts.length - 1) {
                            console.log();
                            await this.sleep(1000);
                        }
                    } catch (error) {
                        console.error();
                        console.log();
                    }
                }
            } else {
                console.log();
            }

            const hasPulloverHoodieNonDefaultSettings = settings.pulloverHoodie && (
                settings.pulloverHoodie.sidesOptions !== 'Default (Front)' ||
                (settings.pulloverHoodie.colors && settings.pulloverHoodie.colors !== 'Skip') ||
                (settings.pulloverHoodie.prices && settings.pulloverHoodie.prices !== 'Skip')
            );

            if (hasPulloverHoodieNonDefaultSettings && availableProducts['STANDARD_PULLOVER_HOODIE'] && !processedProducts.has('STANDARD_PULLOVER_HOODIE')) {
                try {
                    console.log();

                    const editButton = availableProducts['STANDARD_PULLOVER_HOODIE'];

                    const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                    const productCard = editButton.closest('.product-card');
                    const isCardDisabled = productCard && productCard.classList.contains('disabled');

                    if (isButtonDisabled || isCardDisabled) {
                        console.log();
                    } else {

                        console.log();

                        this.updateProgressUI('Processing STANDARD_PULLOVER_HOODIE', false);

                        editButton.click();

                        console.log();
                        await this.sleep(1200);

                        if (settings.pulloverHoodie.colors && settings.pulloverHoodie.colors !== 'Skip') {
                            console.log();
                            await this.processProductColors(settings.pulloverHoodie.colors);

                            console.log();
                            await this.sleep(250);
                        } else if (hasGlobalColorSettings) {

                            console.log();
                            await this.processProductColors(settings.allProducts.colors);

                            console.log();
                            await this.sleep(250);
                        }

                        if (settings.pulloverHoodie.prices && settings.pulloverHoodie.prices !== 'Skip') {
                            console.log();
                            await this.processProductPrices(settings.pulloverHoodie.prices);

                            console.log();
                            await this.sleep(250);
                        } else if (hasGlobalPriceSettings) {

                            console.log();
                            await this.processProductPrices(settings.allProducts.prices);

                            console.log();
                            await this.sleep(250);
                        }

                        if (settings.pulloverHoodie.sidesOptions !== 'Default (Front)') {
                            console.log();
                            await this.processProductSides('STANDARD_PULLOVER_HOODIE', settings.pulloverHoodie.sidesOptions);

                            console.log();
                            await this.sleep(500);
                        }

                        processedProducts.add('STANDARD_PULLOVER_HOODIE');
                    }
                } catch (error) {
                    console.error();
                }
            } else if (availableProducts['STANDARD_PULLOVER_HOODIE'] && !processedProducts.has('STANDARD_PULLOVER_HOODIE') && (hasGlobalColorSettings || hasGlobalPriceSettings)) {

                try {
                    console.log();

                    const editButton = availableProducts['STANDARD_PULLOVER_HOODIE'];

                    const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                    const productCard = editButton.closest('.product-card');
                    const isCardDisabled = productCard && productCard.classList.contains('disabled');

                    if (isButtonDisabled || isCardDisabled) {
                        console.log();
                    } else {

                        console.log();

                        this.updateProgressUI('Processing STANDARD_PULLOVER_HOODIE', false);

                        editButton.click();

                        console.log();
                        await this.sleep(1200);

                        if (hasGlobalColorSettings) {
                            console.log();
                            await this.processProductColors(settings.allProducts.colors);

                            console.log();
                            await this.sleep(250);
                        }

                        if (hasGlobalPriceSettings) {
                            console.log();
                            await this.processProductPrices(settings.allProducts.prices);

                            console.log();
                            await this.sleep(250);
                        }

                        processedProducts.add('STANDARD_PULLOVER_HOODIE');
                    }
                } catch (error) {
                    console.error();
                }
            } else if (disabledProducts.has('STANDARD_PULLOVER_HOODIE')) {
                console.log();
            } else if (availableProducts['STANDARD_PULLOVER_HOODIE'] && !processedProducts.has('STANDARD_PULLOVER_HOODIE')) {
                console.log();
            } else if (processedProducts.has('STANDARD_PULLOVER_HOODIE')) {
                console.log();
            } else {
                console.log();
            }

            const hasZipHoodieNonDefaultSettings = settings.zipHoodie && (
                settings.zipHoodie.sidesOptions !== 'Default (Front)' ||
                (settings.zipHoodie.colors && settings.zipHoodie.colors !== 'Skip') ||
                (settings.zipHoodie.prices && settings.zipHoodie.prices !== 'Skip')
            );

            if (hasZipHoodieNonDefaultSettings && availableProducts['ZIP_HOODIE'] && !processedProducts.has('ZIP_HOODIE')) {
                try {
                    console.log();

                    const editButton = availableProducts['ZIP_HOODIE'];

                    const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                    const productCard = editButton.closest('.product-card');
                    const isCardDisabled = productCard && productCard.classList.contains('disabled');

                    if (isButtonDisabled || isCardDisabled) {
                        console.log();
                    } else {

                        console.log();

                        this.updateProgressUI('Processing ZIP_HOODIE', false);

                        editButton.click();

                        console.log();
                        await this.sleep(1200);

                        if (settings.zipHoodie.colors && settings.zipHoodie.colors !== 'Skip') {
                            console.log();
                            await this.processProductColors(settings.zipHoodie.colors);

                            console.log();
                            await this.sleep(250);
                        } else if (hasGlobalColorSettings) {

                            console.log();
                            await this.processProductColors(settings.allProducts.colors);

                            console.log();
                            await this.sleep(250);
                        }

                        if (settings.zipHoodie.prices && settings.zipHoodie.prices !== 'Skip') {
                            console.log();
                            await this.processProductPrices(settings.zipHoodie.prices);

                            console.log();
                            await this.sleep(250);
                        } else if (hasGlobalPriceSettings) {

                            console.log();
                            await this.processProductPrices(settings.allProducts.prices);

                            console.log();
                            await this.sleep(250);
                        }

                        if (settings.zipHoodie.sidesOptions !== 'Default (Front)') {
                            console.log();
                            await this.processProductSides('ZIP_HOODIE', settings.zipHoodie.sidesOptions);

                            console.log();
                            await this.sleep(500);
                        }

                        processedProducts.add('ZIP_HOODIE');
                    }
                } catch (error) {
                    console.error();
                }
            } else if (availableProducts['ZIP_HOODIE'] && !processedProducts.has('ZIP_HOODIE') && (hasGlobalColorSettings || hasGlobalPriceSettings)) {

                try {
                    console.log();

                    const editButton = availableProducts['ZIP_HOODIE'];

                    const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                    const productCard = editButton.closest('.product-card');
                    const isCardDisabled = productCard && productCard.classList.contains('disabled');

                    if (isButtonDisabled || isCardDisabled) {
                        console.log();
                    } else {

                        console.log();

                        this.updateProgressUI('Processing ZIP_HOODIE', false);

                        editButton.click();

                        console.log();
                        await this.sleep(1200);

                        if (hasGlobalColorSettings) {
                            console.log();
                            await this.processProductColors(settings.allProducts.colors);

                            console.log();
                            await this.sleep(250);
                        }

                        if (hasGlobalPriceSettings) {
                            console.log();
                            await this.processProductPrices(settings.allProducts.prices);

                            console.log();
                            await this.sleep(250);
                        }

                        processedProducts.add('ZIP_HOODIE');
                    }
                } catch (error) {
                    console.error();
                }
            } else if (disabledProducts.has('ZIP_HOODIE')) {
                console.log();
            } else if (availableProducts['ZIP_HOODIE'] && !processedProducts.has('ZIP_HOODIE')) {
                console.log();
            } else if (processedProducts.has('ZIP_HOODIE')) {
                console.log();
            } else {
                console.log();
            }

            console.log();
            console.log();

        } catch (error) {
            console.error();

        }
    }

    async processProductSides(productName, sidesOption) {
        try {
            console.log();
            this.updateProgressUI(`Processing ${productName}`, false);

            let tabButton;
            let isBackAndPocket = false;

            if (sidesOption === 'Back') {
                tabButton = Array.from(document.querySelectorAll('.tab-button')).find(
                    btn => btn.textContent.trim() === 'Back'
                );
                console.log();
            } else if (sidesOption === 'Frontside (Pocket)') {
                tabButton = Array.from(document.querySelectorAll('.tab-button')).find(
                    btn => btn.textContent.trim().includes('Pocket')
                );
                console.log();
            } else if (sidesOption === 'Back & Pocket') {
                tabButton = Array.from(document.querySelectorAll('.tab-button')).find(
                    btn => btn.textContent.trim().includes('Back & Pocket')
                );
                console.log();
                isBackAndPocket = true;
            }

            if (!tabButton) {
                console.log();
                return;
            }

            if (tabButton.classList.contains('active')) {
                console.log();
            } else {

                console.log();
                tabButton.click();

                console.log();
                await this.sleep(500);
            }

            console.log();
            const resizeBtn = await this.waitForElement('.resize-btn:not([disabled])');

            console.log();
            resizeBtn.click();

            if (isBackAndPocket) {

                console.log();
                await this.waitForBackAndPocketMessages();
            } else {

                console.log();
                await this.sleep(1500);

                console.log();
                await this.waitForAnyGeneratingMessage();

                console.log();
                await this.sleep(150);
            }

            console.log();

        } catch (error) {
            console.error();

        }
    }

    async processProductColors(colorOption) {
        try {
            console.log();

            this.currentOperation = 'processProductColors';

            const isEditorOpen = document.querySelector('.product-editor') !== null;
            console.log();

            if (!isEditorOpen) {
                console.log();

                const productTypes = [
                    'STANDARD_TSHIRT',  
                    'PREMIUM_TSHIRT',
                    'VNECK',
                    'TANK_TOP',
                    'STANDARD_LONG_SLEEVE',
                    'RAGLAN',
                    'STANDARD_SWEATSHIRT',
                    'STANDARD_PULLOVER_HOODIE',
                    'ZIP_HOODIE'
                ];

                let editButton = null;
                let productToEdit = null;

                for (const productType of productTypes) {
                    const button = document.querySelector(`.${productType}-edit-btn`);
                    if (button && !button.disabled && !button.classList.contains('disabled')) {

                        const productCard = button.closest('.product-card');
                        const isCardDisabled = productCard && productCard.classList.contains('disabled');

                        if (!isCardDisabled) {
                            editButton = button;
                            productToEdit = productType;
                            break;
                        }
                    }
                }

                if (editButton) {
                    console.log();

                    if (productToEdit === 'STANDARD_TSHIRT') {
                        this.lastClickedProduct = 'Standard T-shirt';
                    } else if (productToEdit === 'PREMIUM_TSHIRT') {
                        this.lastClickedProduct = 'Premium T-shirt';
                    } else if (productToEdit === 'VNECK') {
                        this.lastClickedProduct = 'V-neck';
                    } else if (productToEdit === 'TANK_TOP') {
                        this.lastClickedProduct = 'Tank Top';
                    } else if (productToEdit === 'STANDARD_LONG_SLEEVE') {
                        this.lastClickedProduct = 'Long Sleeve';
                    } else if (productToEdit === 'RAGLAN') {
                        this.lastClickedProduct = 'Raglan';
                    } else if (productToEdit === 'STANDARD_SWEATSHIRT') {
                        this.lastClickedProduct = 'Sweatshirt';
                    } else if (productToEdit === 'STANDARD_PULLOVER_HOODIE') {
                        this.lastClickedProduct = 'Pullover Hoodie';
                    } else if (productToEdit === 'ZIP_HOODIE') {
                        this.lastClickedProduct = 'Zip Hoodie';
                    }

                    this.updateProgressUI(`Processing ${productToEdit}`, false);

                    console.log();
                    editButton.click();

                    console.log();
                    await this.sleep(1200);
                } else {
                    console.error();
                    return;
                }
            } else {
                console.log();
            }

            console.log();
            const colorContainer = await this.waitForElement('.color-groups-container');

            if (!colorContainer) {
                console.error();
                return;
            }

            console.log();

            const colorActionsContainer = document.querySelector('.product-color-actions');
            if (!colorActionsContainer) {
                console.error();
                return;
            }

            let colorButton;

            if (colorOption === 'Dark Colors') {
                colorButton = document.querySelector('#product-dark-colors-btn');
                console.log();
            } else if (colorOption === 'Light Colors') {
                colorButton = document.querySelector('#product-light-colors-btn');
                console.log();
            } else if (colorOption === 'All Colors') {
                colorButton = document.querySelector('#product-all-colors-btn');
                console.log();
            }

            if (!colorButton) {
                console.log();

                if (colorOption === 'Dark Colors') {
                    colorButton = document.querySelector('button[id="product-dark-colors-btn"]');
                    console.log();
                } else if (colorOption === 'Light Colors') {
                    colorButton = document.querySelector('button[id="product-light-colors-btn"]');
                    console.log();
                } else if (colorOption === 'All Colors') {
                    colorButton = document.querySelector('button[id="product-all-colors-btn"]');
                    console.log();
                }

                if (!colorButton) {
                    console.log();
                    return;
                }
            }

            console.log();
            colorButton.click();

            console.log();
            await this.sleep(300);

            console.log();

        } catch (error) {
            console.error();

        }
    }

    async processProductPrices(priceOption) {
        try {
            console.log();

            this.currentOperation = 'processProductPrices';

            const isEditorOpen = document.querySelector('.product-editor') !== null;
            console.log();

            if (!isEditorOpen) {
                console.log();

                const productTypes = [
                    'STANDARD_TSHIRT',  
                    'PREMIUM_TSHIRT',
                    'VNECK',
                    'TANK_TOP',
                    'STANDARD_LONG_SLEEVE',
                    'RAGLAN',
                    'STANDARD_SWEATSHIRT',
                    'STANDARD_PULLOVER_HOODIE',
                    'ZIP_HOODIE'
                ];

                let editButton = null;
                let productToEdit = null;

                for (const productType of productTypes) {
                    const button = document.querySelector(`.${productType}-edit-btn`);
                    if (button && !button.disabled && !button.classList.contains('disabled')) {

                        const productCard = button.closest('.product-card');
                        const isCardDisabled = productCard && productCard.classList.contains('disabled');

                        if (!isCardDisabled) {
                            editButton = button;
                            productToEdit = productType;
                            break;
                        }
                    }
                }

                if (editButton) {
                    console.log();

                    if (productToEdit === 'STANDARD_TSHIRT') {
                        this.lastClickedProduct = 'Standard T-shirt';
                    } else if (productToEdit === 'PREMIUM_TSHIRT') {
                        this.lastClickedProduct = 'Premium T-shirt';
                    } else if (productToEdit === 'VNECK') {
                        this.lastClickedProduct = 'V-neck';
                    } else if (productToEdit === 'TANK_TOP') {
                        this.lastClickedProduct = 'Tank Top';
                    } else if (productToEdit === 'STANDARD_LONG_SLEEVE') {
                        this.lastClickedProduct = 'Long Sleeve';
                    } else if (productToEdit === 'RAGLAN') {
                        this.lastClickedProduct = 'Raglan';
                    } else if (productToEdit === 'STANDARD_SWEATSHIRT') {
                        this.lastClickedProduct = 'Sweatshirt';
                    } else if (productToEdit === 'STANDARD_PULLOVER_HOODIE') {
                        this.lastClickedProduct = 'Pullover Hoodie';
                    } else if (productToEdit === 'ZIP_HOODIE') {
                        this.lastClickedProduct = 'Zip Hoodie';
                    }

                    this.updateProgressUI(`Processing ${productToEdit}`, false);

                    console.log();
                    editButton.click();

                    console.log();
                    await this.sleep(750);
                } else {
                    console.error();
                    return;
                }
            } else {
                console.log();
            }

            console.log();
            const priceContainer = await this.waitForElement('.product-price-actions');

            if (!priceContainer) {
                console.error();
                return;
            }

            console.log();

            let priceButton;

            if (priceOption === 'Default Prices') {
                priceButton = document.querySelector('#product-default-prices-btn');
                console.log();
            } else if (priceOption === 'Suggested Prices') {
                priceButton = document.querySelector('#product-suggested-prices-btn');
                console.log();
            } else if (priceOption === 'Market Average') {
                priceButton = document.querySelector('#product-market-average-btn');
                console.log();
            } else if (priceOption === 'Max Prices') {
                priceButton = document.querySelector('#product-max-prices-btn');
                console.log();
            }

            if (!priceButton) {
                console.log();

                if (priceOption === 'Default Prices') {
                    priceButton = document.querySelector('button[id="product-default-prices-btn"]');
                    console.log();
                } else if (priceOption === 'Suggested Prices') {
                    priceButton = document.querySelector('button[id="product-suggested-prices-btn"]');
                    console.log();
                } else if (priceOption === 'Market Average') {
                    priceButton = document.querySelector('button[id="product-market-average-btn"]');
                    console.log();
                } else if (priceOption === 'Max Prices') {
                    priceButton = document.querySelector('button[id="product-max-prices-btn"]');
                    console.log();
                }

                if (!priceButton) {
                    console.log();
                    return;
                }
            }

            const priceButtonId = priceButton.id;
            console.log();

            console.log();
            priceButton.click();

            console.log();
            let buttonDisabled = false;
            let checkCount = 0;
            const maxChecks = 10;

            while (!buttonDisabled && checkCount < maxChecks) {
                await this.sleep(200);
                const currentButton = document.querySelector(`#${priceButtonId}`);
                if (currentButton) {
                    buttonDisabled = currentButton.disabled || 
                                    currentButton.classList.contains('disabled') || 
                                    currentButton.getAttribute('disabled') !== null;

                    if (buttonDisabled) {
                        console.log();
                    }
                }
                checkCount++;
            }

            console.log();
            let buttonEnabled = false;
            checkCount = 0;
            const maxWaitChecks = 30; 

            while (!buttonEnabled && checkCount < maxWaitChecks) {
                await this.sleep(300);
                const currentButton = document.querySelector(`#${priceButtonId}`);
                if (currentButton) {
                    buttonEnabled = !currentButton.disabled && 
                                   !currentButton.classList.contains('disabled') && 
                                   currentButton.getAttribute('disabled') === null;

                    if (buttonEnabled) {
                        console.log();
                    }
                }
                checkCount++;
            }

            if (!buttonEnabled) {
                console.log();
            }

            console.log();
            await this.sleep(250);

            console.log();

        } catch (error) {
            console.error();

        }
    }

    async processScalableProducts() {
        try {
            console.log();

            const settings = this.settings.scalableProducts;

            if (!settings || settings.isActive !== true) {
                console.log();
                return;
            }

            const scalableProductTypes = [
                'POP_SOCKET',
                'PHONE_CASE_APPLE_IPHONE',
                'TOTE_BAG',
                'THROW_PILLOW'
            ];

            for (const productType of scalableProductTypes) {
                console.log();

                const editButton = document.querySelector(`.${productType}-edit-btn`);
                if (editButton) {
                    console.log();

                    const hasNonDefaultSettings = 
                        settings.scale !== 'Skip' || 
                        settings.color !== 'Default (White)' || 
                        settings.prices !== 'Skip';

                    if (hasNonDefaultSettings) {
                        console.log();

                        const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                        const productCard = editButton.closest('.product-card');
                        const isCardDisabled = productCard && productCard.classList.contains('disabled');

                        if (isButtonDisabled || isCardDisabled) {
                            console.log();
                            continue;
                        }

                        if (productType === 'POP_SOCKET') {
                            this.lastClickedProduct = 'POP_SOCKET';
                        } else if (productType === 'PHONE_CASE_APPLE_IPHONE') {
                            this.lastClickedProduct = 'PHONE_CASE';
                        } else if (productType === 'TOTE_BAG') {
                            this.lastClickedProduct = 'TOTE_BAG';
                        } else if (productType === 'THROW_PILLOW') {
                            this.lastClickedProduct = 'THROW_PILLOW';
                        } else {
                            this.lastClickedProduct = productType;
                        }

                        this.updateProgressUI(`Processing ${this.lastClickedProduct}`, false);

                        console.log();
                        editButton.click();

                        console.log();
                        await this.sleep(750);

                        if (settings.scale !== 'Skip') {
                            console.log();
                            await this.processScalableProductScaleWithoutEdit(productType, settings.scale, settings.customScale);

                            console.log();
                            await this.sleep(250);
                        }

                        if (settings.color !== 'Default (White)') {
                            console.log();
                            await this.processScalableProductColorWithoutEdit(productType, settings.color, settings.customColor);

                            console.log();
                            await this.sleep(250);
                        }

                        if (settings.prices !== 'Skip') {
                            console.log();
                            await this.processScalableProductPricesWithoutEdit(productType, settings.prices);

                            console.log();
                            await this.sleep(250);
                        }
                    } else {
                        console.log();
                    }
                } else {
                    console.log();
                }
            }

            console.log();
        } catch (error) {
            console.error();

        }
    }

    async processScalableProductScaleWithoutEdit(productType, scaleOption, customScale) {
        try {
            console.log();

            this.currentOperation = 'processScalableProductScale';

            console.log();
            let scaleInput;

            if (scaleOption === 'Keep current') {
                console.log();

            } else if (scaleOption === 'Custom scale') {
                console.log();

                const customRadio = document.querySelector('input#custom[type="radio"]');
                if (!customRadio) {
                    console.log();
                    scaleInput = document.querySelector('input[type="radio"][id*="custom"], input[type="radio"][value*="custom"]');
                } else {
                    scaleInput = customRadio;
                }

                if (scaleInput) {
                    console.log();
                    scaleInput.click();

                    console.log();
                    await this.sleep(1000);

                    if (customScale) {
                        console.log();

                        let customScaleInputField = document.querySelector('input[type="number"]:not([disabled])');

                        if (!customScaleInputField) {
                            console.log();
                            customScaleInputField = document.querySelector('input[type="number"]');

                            if (customScaleInputField && customScaleInputField.disabled) {
                                console.log();

                                customScaleInputField.removeAttribute('disabled');

                                await this.sleep(500);
                            }
                        }

                        if (!customScaleInputField) {
                            console.log();

                            const alternativeInputs = [
                                document.querySelector('input.custom-scale-input'),
                                document.querySelector('input.scale-input'),
                                document.querySelector('input.scale-value'),
                                document.querySelector('input[placeholder*="scale"], input[placeholder*="Scale"]'),
                                document.querySelector('input:not([type="radio"])[id*="scale"], input:not([type="radio"])[id*="Scale"]'),
                                document.querySelector('input:not([type="radio"]):not([type="checkbox"]).ng-untouched'),
                                document.querySelector('input[min="50"][max="100"]'),
                                document.querySelector('input[style*="border-bottom: 2px solid"]')
                            ];

                            customScaleInputField = alternativeInputs.find(input => input !== null);
                        }

                        if (customScaleInputField) {
                            console.log();

                            if (customScaleInputField.disabled) {
                                console.log();
                                customScaleInputField.disabled = false;
                                customScaleInputField.removeAttribute('disabled');
                            }

                            console.log();
                            customScaleInputField.focus();
                            await this.sleep(300);

                            console.log();
                            customScaleInputField.value = '';
                            customScaleInputField.dispatchEvent(new Event('input', { bubbles: true }));
                            await this.sleep(300);

                            console.log();
                            customScaleInputField.value = customScale;
                            customScaleInputField.dispatchEvent(new Event('input', { bubbles: true }));
                            customScaleInputField.dispatchEvent(new Event('change', { bubbles: true }));

                            console.log();
                            customScaleInputField.blur();
                            await this.sleep(300);

                            console.log();
                        } else {
                            console.log();
                        }
                    }
                }
            } else if (scaleOption === '100%') {
                console.log();
                scaleInput = document.querySelector('input#percent100');
                if (scaleInput) scaleInput.click();
            } else if (scaleOption === '85%') {
                console.log();
                scaleInput = document.querySelector('input#percent85');
                if (scaleInput) scaleInput.click();
            } else if (scaleOption === '75%') {
                console.log();
                scaleInput = document.querySelector('input#percent75');
                if (scaleInput) scaleInput.click();
            } else if (scaleOption === 'Pattern') {
                console.log();
                scaleInput = document.querySelector('input#pattern');
                if (scaleInput) scaleInput.click();
            }

            if (!scaleInput && scaleOption !== 'Keep current') {
                console.log();
                return;
            }

            if (scaleOption !== 'Keep current') {
                console.log();
                await this.sleep(1000);
            }

            console.log();
            const resizeBtn = await this.waitForElement('.resize-btn:not([disabled])');

            if (!resizeBtn) {
                console.log();
                const alternativeResizeButtons = [
                    document.querySelector('button.resize-btn'),
                    document.querySelector('button[class*="resize"]'),
                    document.querySelector('button:contains("Resize")'),
                    document.querySelector('button:contains("Apply")')
                ];

                const resizeButton = alternativeResizeButtons.find(btn => btn !== null && !btn.disabled);

                if (resizeButton) {
                    console.log();
                    resizeButton.click();
                } else {
                    console.log();
                    return;
                }
            } else {

                console.log();
                resizeBtn.click();
            }

            console.log();
            await this.sleep(3000);

            console.log();
            await this.waitForElement('.delete-button');

            console.log();
            await this.sleep(150);

            console.log();

        } catch (error) {
            console.error();

        }
    }

    async processScalableProductColorWithoutEdit(productType, colorOption, customColor) {
        try {
            console.log();

            this.currentOperation = 'processScalableProductColor';

            if (colorOption === 'Default (White)') {
                console.log();
                return;
            }

            console.log();
            await this.waitForElement('.delete-button');
            console.log();

            console.log();

            if (colorOption === 'Custom Color' && customColor) {
                console.log();

                await this.applyCustomColorToProduct(customColor);
            } else {
                console.log();

                const colorSwatches = document.querySelectorAll('.color-swatch');
                let targetSwatch;

                const colorMap = {
                    'Black': '#000000',
                    'Dark Red': '#840A08',
                    'Crimson': '#C70010',
                    'Vivid Orange': '#F36900',
                    'Bright Yellow': '#FEC600',
                    'Kelly Green': '#01B62F',
                    'Forest Green': '#1C8C46',
                    'Dark Olive': '#37602B',
                    'Sky Blue': '#1AB7EA',
                    'Royal Blue': '#002BB6',
                    'Purple': '#5C2D91',
                    'Hot Pink': '#E0218A',
                    'Pale Pink': '#E9CDDB',
                    'Brown': '#7B4A1B',
                    'Gray': '#979797'
                };

                const targetColor = colorMap[colorOption];

                if (targetColor) {
                    console.log();

                    for (const swatch of colorSwatches) {
                        const style = window.getComputedStyle(swatch);
                        const backgroundColor = style.backgroundColor;
                        const hexColor = this.rgbToHex(backgroundColor);

                        console.log();

                        if (hexColor.toLowerCase() === targetColor.toLowerCase()) {
                            targetSwatch = swatch;
                            console.log();
                            break;
                        }
                    }

                    if (!targetSwatch) {
                        console.log();
                        let closestSwatch = null;
                        let closestDistance = Number.MAX_VALUE;

                        for (const swatch of colorSwatches) {
                            const style = window.getComputedStyle(swatch);
                            const backgroundColor = style.backgroundColor;
                            const hexColor = this.rgbToHex(backgroundColor);

                            const distance = this.calculateColorDistance(hexColor, targetColor);
                            console.log();

                            if (distance < closestDistance) {
                                closestDistance = distance;
                                closestSwatch = swatch;
                            }
                        }

                        if (closestSwatch) {
                            targetSwatch = closestSwatch;
                            const style = window.getComputedStyle(closestSwatch);
                            const closestColor = this.rgbToHex(style.backgroundColor);
                            console.log();
                        }
                    }

                    if (targetSwatch) {
                        console.log();
                        targetSwatch.click();
                    } else {
                        console.log();
                    }
                }
            }

            console.log();
            await this.sleep(1500);

            console.log();

        } catch (error) {
            console.error();

        }
    }

    async processScalableProductPricesWithoutEdit(productType, priceOption) {
        try {
            console.log();

            this.currentOperation = 'processScalableProductPrices';

            console.log();
            const priceContainer = await this.waitForElement('.product-price-actions');

            if (!priceContainer) {
                console.log();
                return;
            }

            console.log();

            let priceButton;

            if (priceOption === 'Default Prices') {
                priceButton = document.querySelector('#product-default-prices-btn');
                console.log();
            } else if (priceOption === 'Suggested Prices') {
                priceButton = document.querySelector('#product-suggested-prices-btn');
                console.log();
            } else if (priceOption === 'Market Average') {
                priceButton = document.querySelector('#product-market-average-btn');
                console.log();
            } else if (priceOption === 'Max Prices') {
                priceButton = document.querySelector('#product-max-prices-btn');
                console.log();
            }

            if (!priceButton) {
                console.log();

                if (priceOption === 'Default Prices') {
                    priceButton = document.querySelector('button[id="product-default-prices-btn"]');
                    console.log();
                } else if (priceOption === 'Suggested Prices') {
                    priceButton = document.querySelector('button[id="product-suggested-prices-btn"]');
                    console.log();
                } else if (priceOption === 'Market Average') {
                    priceButton = document.querySelector('button[id="product-market-average-btn"]');
                    console.log();
                } else if (priceOption === 'Max Prices') {
                    priceButton = document.querySelector('button[id="product-max-prices-btn"]');
                    console.log();
                }

                if (!priceButton) {
                    console.log();
                    return;
                }
            }

            const priceButtonId = priceButton.id;
            console.log();

            console.log();
            priceButton.click();

            console.log();
            let buttonDisabled = false;
            let checkCount = 0;
            const maxChecks = 10;

            while (!buttonDisabled && checkCount < maxChecks) {
                await this.sleep(200);
                const currentButton = document.querySelector(`#${priceButtonId}`);
                if (currentButton) {
                    buttonDisabled = currentButton.disabled || 
                                    currentButton.classList.contains('disabled') || 
                                    currentButton.getAttribute('disabled') !== null;

                    if (buttonDisabled) {
                        console.log();
                    }
                }
                checkCount++;
            }

            console.log();
            let buttonEnabled = false;
            checkCount = 0;
            const maxWaitChecks = 30; 

            while (!buttonEnabled && checkCount < maxWaitChecks) {
                await this.sleep(300);
                const currentButton = document.querySelector(`#${priceButtonId}`);
                if (currentButton) {
                    buttonEnabled = !currentButton.disabled && 
                                   !currentButton.classList.contains('disabled') && 
                                   currentButton.getAttribute('disabled') === null;

                    if (buttonEnabled) {
                        console.log();
                    }
                }
                checkCount++;
            }

            if (!buttonEnabled) {
                console.log();
            }

            console.log();
            await this.sleep(250);

            console.log();

        } catch (error) {
            console.error();

        }
    }

    async applyCustomColorToProduct(hexColor) {
        console.log();

        const cleanHex = hexColor.replace('#', '').toUpperCase();
        console.log();

        try {

            console.log();
            const colorButton = document.querySelector([
                'button#color-btn.btn.btn-secondary.icon',
                'button.btn.btn-secondary.icon[id*="color"]',
                'button.btn-secondary.icon[id*="color"]'
            ].join(','));

            if (!colorButton) {
                throw new Error();
            }

            this.addColorPickerHidingCSS();
            console.log();

            console.log();
            colorButton.click();

            console.log();
            await this.sleep(1000);

            console.log();
            let sketchPicker = document.querySelector('.sketch-picker, div[class*="sketch-picker"]');

            if (!sketchPicker) {

                console.log();
                await this.sleep(1000);

                sketchPicker = document.querySelector('.sketch-picker, div[class*="sketch-picker"]');

                if (!sketchPicker) {
                    throw new Error();
                }

                console.log();
            }

            console.log();
            const hexInput = sketchPicker.querySelector('.sketch-fields input');

            if (!hexInput) {
                throw new Error();
            }

            console.log();

            console.log();
            hexInput.value = '';
            hexInput.dispatchEvent(new Event('input', { bubbles: true }));
            await this.sleep(100);

            console.log();
            hexInput.value = cleanHex;
            hexInput.dispatchEvent(new Event('input', { bubbles: true }));
            hexInput.dispatchEvent(new Event('change', { bubbles: true }));

            await this.sleep(200);

            console.log();
            hexInput.dispatchEvent(new KeyboardEvent('keydown', { 
                key: 'Enter', 
                code: 'Enter', 
                keyCode: 13, 
                bubbles: true,
                cancelable: true 
            }));

            await this.sleep(100);

            hexInput.dispatchEvent(new KeyboardEvent('keyup', { 
                key: 'Enter', 
                code: 'Enter', 
                keyCode: 13, 
                bubbles: true,
                cancelable: true 
            }));

            await this.sleep(500);

            await this.cleanupAfterColorApplication();

            console.log();
            return true;

        } catch (error) {

            this.removeColorPickerHidingCSS();
            console.error();
            throw error;
        }
    }

    addColorPickerHidingCSS() {

        if (!document.getElementById('temp-hide-color-picker-style')) {
            const styleEl = document.createElement('style');
            styleEl.id = 'temp-hide-color-picker-style';
            styleEl.textContent = `

                body.temp-hide-color-picker .sketch-picker,
                body.temp-hide-color-picker .popover.color-picker-popover,
                body.temp-hide-color-picker ngb-popover-window[class*="color-picker-popover"] {
                    position: absolute !important;
                    visibility: hidden !important;
                    opacity: 0 !important;
                    pointer-events: none !important;
                    height: 0 !important;
                    overflow: hidden !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    border: none !important;
                    width: 0 !important;
                }

                body.temp-hide-color-picker .popover.color-picker-popover .arrow,
                body.temp-hide-color-picker ngb-popover-window[class*="color-picker-popover"] .arrow {
                    display: none !important;
                }

                .sketch-picker input {
                    pointer-events: auto !important;
                }
            `;
            document.head.appendChild(styleEl);
        }

        document.body.classList.add('temp-hide-color-picker');
    }

    removeColorPickerHidingCSS() {

        document.body.classList.remove('temp-hide-color-picker');
        console.log();
    }

    async cleanupAfterColorApplication() {
        console.log();

        document.body.dispatchEvent(new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
        }));

        await this.sleep(100);

        this.removeColorPickerHidingCSS();

        console.log();
    }

    async processTumblerProduct() {
        try {
            console.log();

            const settings = this.settings.tumblerProduct;

            if (!settings || settings.isActive !== true) {
                console.log();
                return;
            }

            const hasNonDefaultSettings = 
                settings.sides !== 'Default (One Side)' || 
                settings.scale !== '100%' || 
                settings.colors !== 'Skip' || 
                settings.prices !== 'Skip';

            if (hasNonDefaultSettings) {
                console.log();

                const editButton = document.querySelector('.TUMBLER-edit-btn');
                if (!editButton) {
                    console.log();
                    return;
                }

                const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                const productCard = editButton.closest('.product-card');
                const isCardDisabled = productCard && productCard.classList.contains('disabled');

                if (isButtonDisabled || isCardDisabled) {
                    console.log();
                    return;
                }

                console.log();

                this.lastClickedProduct = 'TUMBLER';

                this.updateProgressUI('Processing TUMBLER', false);

                editButton.click();

                console.log();
                await this.sleep(750);

                if (settings.colors !== 'Skip') {
                    console.log();
                    await this.processTumblerColors(settings.colors);

                    console.log();
                    await this.sleep(250);
                } else {
                    console.log();
                }

                if (settings.prices !== 'Skip') {
                    console.log();
                    await this.processTumblerPrices(settings.prices);

                    console.log();
                    await this.sleep(250);
                } else {
                    console.log();
                }

                console.log();
                await this.processTumblerScale(settings.scale, settings.customScale);

                console.log();
                await this.sleep(250);

                if (settings.sides !== 'Default (One Side)') {
                    console.log();
                    await this.processTumblerSides(settings.sides);

                    console.log();
                    await this.sleep(500);
                } else {
                    console.log();
                }

                console.log();
            } else {
                console.log();
            }
        } catch (error) {
            console.error();

        }
    }

    async getAutomationState() {
        return new Promise((resolve) => {
            chrome.runtime.sendMessage({ action: 'getAutomationState' }, (response) => {
                if (response && response.success && response.state) {
                    resolve(response.state);
                } else {
                    resolve(null);
                }
            });
        });
    }

    async processNextFile() {
        try {
            console.log();

            console.log();
            let isCompleted = false;

            await new Promise((resolve) => {
                chrome.runtime.sendMessage({
                    action: 'getAutomationState'
                }, (response) => {
                    if (response && response.success && response.state) {
                        const state = response.state;
                        if (state.currentFile && 
                            state.currentFile.filename === this.currentFile?.filename &&
                            state.currentFile.status === 'completed') {
                            console.log();
                            isCompleted = true;
                        } else {
                            console.log();
                        }
                    } else {
                        console.warn();
                    }
                    resolve();
                });
            });

            if (!isCompleted) {
                console.log();
                await new Promise((resolve) => {
                    chrome.runtime.sendMessage({
                        action: 'markTabCompleted',
                        filename: this.currentFile?.filename
                    }, (response) => {
                        if (response && response.success) {
                            console.log();
                            isCompleted = true;
                        } else {
                            console.warn();
                        }
                        resolve();
                    });
                });
            }

            const state = await this.getAutomationState();
            const isFinalFile = state && 
                               state.totalFiles && 
                               state.processedFiles && 
                               (state.processedFiles.length + 1 >= state.totalFiles);

            console.log();

            const currentFileIndex = this.currentFile?.index + 1 || 1;
            const totalFiles = state?.totalFiles || this.settings?.totalFiles || 0;

            const percentPerFile = 100 / totalFiles;
            const exactFilePercentage = currentFileIndex * percentPerFile;

            console.log();

            const statusInfo = {
                status: 'Completed',
                statusIndex: 21, 
                totalStatuses: 22, 
                isCompleted: true 
            };

            console.log();
            await new Promise((resolve) => {
                chrome.runtime.sendMessage({
                    action: 'updateAutomationProgress',
                    status: 'Completed',
                    filename: this.currentFile?.filename,
                    currentFile: currentFileIndex,
                    totalFiles: totalFiles,
                    isTabCompleted: false, 
                    updateProgressOnly: true,
                    updateProgressBar: true, 
                    forceProgressUpdate: true, 
                    completed: isFinalFile, 
                    statusInfo: statusInfo 
                }, (response) => {
                    console.log();
                    resolve();
                });
            });

            console.log();
            await this.sleep(250);

            if (isFinalFile) {
                console.log();

                await this.sleep(1000);

                console.log();
                await new Promise((resolve) => {
                    chrome.runtime.sendMessage({
                        action: 'hideAllProgressUI'
                    }, (response) => {
                        console.log();
                        resolve();
                    });
                });

                return true;
            }

            console.log();
            const result = await new Promise((resolve) => {
                chrome.runtime.sendMessage({ 
                    action: 'processNextFile',
                    waitForCompletion: true 
                }, (response) => {
                    if (response && response.success) {
                        console.log();
                    } else {
                        console.warn();
                    }
                    resolve(response && response.success);
                });
            });

            return result;
        } catch (error) {
            console.error();
            throw error;
        }
    }

    updateProgressUI(status, isTabCompleted = false) {
        console.log();

        const orderedStatuses = [
            'Uploading...',
            'Processing Products',
            'Processing Design',
            'Processing STANDARD_TSHIRT',
            'Processing PREMIUM_TSHIRT',
            'Processing VNECK',
            'Processing TANK_TOP',
            'Processing STANDARD_LONG_SLEEVE',
            'Processing RAGLAN',
            'Processing STANDARD_PULLOVER_HOODIE',
            'Processing ZIP_HOODIE',
            'Processing STANDARD_SWEATSHIRT',
            'Processing POP_SOCKET',
            'Processing PHONE_CASE',
            'Processing TOTE_BAG',
            'Processing THROW_PILLOW',
            'Processing TUMBLER',
            'Processing Scalable Products',
            'Actions: Text Swap',
            'Actions: Copy EN to All',
            'Actions: Save to Drafts',
            'Completed'
        ];

        let formattedStatus = status;

        if (status === 'Uploading') {
            formattedStatus = 'Uploading...';
        } else if (status === 'Completed') {
            formattedStatus = 'Completed';
        } else if (status === 'Text Swap') {
            formattedStatus = 'Actions: Text Swap';
        } else if (status === 'Copy EN to All') {
            formattedStatus = 'Actions: Copy EN to All';
        } else if (status === 'Save to Drafts') {
            formattedStatus = 'Actions: Save to Drafts';
        } 

        else if (status.startsWith('Processing ')) {

            if (status.includes('STANDARD_TSHIRT')) {
                formattedStatus = 'Processing STANDARD_TSHIRT';
            } else if (status.includes('PREMIUM_TSHIRT')) {
                formattedStatus = 'Processing PREMIUM_TSHIRT';
            } else if (status.includes('VNECK')) {
                formattedStatus = 'Processing VNECK';
            } else if (status.includes('TANK_TOP')) {
                formattedStatus = 'Processing TANK_TOP';
            } else if (status.includes('STANDARD_LONG_SLEEVE')) {
                formattedStatus = 'Processing STANDARD_LONG_SLEEVE';
            } else if (status.includes('RAGLAN')) {
                formattedStatus = 'Processing RAGLAN';
            } else if (status.includes('STANDARD_PULLOVER_HOODIE')) {
                formattedStatus = 'Processing STANDARD_PULLOVER_HOODIE';
            } else if (status.includes('ZIP_HOODIE')) {
                formattedStatus = 'Processing ZIP_HOODIE';
            } else if (status.includes('STANDARD_SWEATSHIRT')) {
                formattedStatus = 'Processing STANDARD_SWEATSHIRT';
            } else if (status.includes('POP_SOCKET')) {
                formattedStatus = 'Processing POP_SOCKET';
            } else if (status.includes('PHONE_CASE')) {
                formattedStatus = 'Processing PHONE_CASE';
            } else if (status.includes('TOTE_BAG')) {
                formattedStatus = 'Processing TOTE_BAG';
            } else if (status.includes('THROW_PILLOW')) {
                formattedStatus = 'Processing THROW_PILLOW';
            } else if (status.includes('TUMBLER')) {
                formattedStatus = 'Processing TUMBLER';
            }

            else if (this.currentOperation) {
                if (this.lastClickedProduct) {
                    formattedStatus = `Processing ${this.lastClickedProduct}`;
                } else {

                    if (this.currentOperation.includes('Tumbler')) {
                        formattedStatus = 'Processing TUMBLER';
                    } else if (this.currentOperation.includes('Scalable')) {
                        formattedStatus = 'Processing POP_SOCKET';
                    } else {

                        formattedStatus = 'Processing Design';
                    }
                }
            } else {

                formattedStatus = 'Processing Design';
            }
        } else if (status === 'Processing Products') {

            formattedStatus = 'Processing Products';
        }

        console.log();

        const statusIndex = orderedStatuses.indexOf(formattedStatus);
        const totalStatuses = orderedStatuses.length;

        const statusInfo = {
            status: formattedStatus,
            statusIndex: statusIndex !== -1 ? statusIndex : 0,
            totalStatuses: totalStatuses,
            isCompleted: formattedStatus === 'Completed'
        };

        console.log();

        const updateProgressBar = formattedStatus === 'Completed' ? true : false;

        chrome.runtime.sendMessage({ action: 'getAutomationState' }, (response) => {
            if (response && response.success && response.state) {
                const state = response.state;
                const totalFiles = state.totalFiles || this.settings?.totalFiles || 0;
                const processedFiles = state.processedFiles?.length || 0;
                const currentFileIndex = this.currentFile?.index !== undefined ? this.currentFile.index + 1 : 1;

                const queueProgress = {
                    current: currentFileIndex,
                    total: totalFiles,
                    processed: processedFiles,
                    remaining: totalFiles - processedFiles - 1 
                };

                console.log();

                chrome.runtime.sendMessage({
                    action: 'updateAutomationProgress',
                    status: formattedStatus,
                    filename: this.currentFile?.filename,
                    currentFile: currentFileIndex,
                    totalFiles: totalFiles,
                    isTabCompleted: isTabCompleted,
                    updateProgressOnly: true, 
                    updateProgressBar, 
                    forceProgressUpdate: formattedStatus === 'Completed', 
                    queueProgress: queueProgress, 
                    statusInfo: statusInfo 
                }, (response) => {
                    console.log();

                    if (formattedStatus === 'Completed' && currentFileIndex >= totalFiles) {
                        this.playCompletionSound();
                    }
                });
            } else {

                chrome.runtime.sendMessage({
                    action: 'updateAutomationProgress',
                    status: formattedStatus,
                    filename: this.currentFile?.filename,
                    currentFile: this.currentFile?.index + 1,
                    totalFiles: this.settings?.totalFiles || 0,
                    isTabCompleted: isTabCompleted,
                    updateProgressOnly: true, 
                    updateProgressBar,
                    forceProgressUpdate: formattedStatus === 'Completed',
                    statusInfo: statusInfo 
                }, (response) => {
                    console.log();

                    if (formattedStatus === 'Completed' && (this.currentFile?.index + 1) >= (this.settings?.totalFiles || 0)) {
                        this.playCompletionSound();
                    }
                });
            }
        });
    }

    getCurrentProductName() {
        try {

            const productEditor = document.querySelector('.product-editor');
            if (productEditor) {

                const productTitle = productEditor.querySelector('.product-title');
                if (productTitle && productTitle.textContent) {

                    const productName = productTitle.textContent.trim().replace(/\s+by\s+Amazon$/i, '');

                    if (productName.includes('T-shirt')) return 'STANDARD_TSHIRT';
                    if (productName.includes('Premium') && productName.includes('T-shirt')) return 'PREMIUM_TSHIRT';
                    if (productName.includes('V-neck')) return 'VNECK';
                    if (productName.includes('Tank Top')) return 'TANK_TOP';
                    if (productName.includes('Long Sleeve')) return 'STANDARD_LONG_SLEEVE';
                    if (productName.includes('Raglan')) return 'RAGLAN';
                    if (productName.includes('Pullover Hoodie')) return 'STANDARD_PULLOVER_HOODIE';
                    if (productName.includes('Zip Hoodie')) return 'ZIP_HOODIE';
                    if (productName.includes('Sweatshirt')) return 'STANDARD_SWEATSHIRT';

                    return productName.toUpperCase().replace(/\s+/g, '_');
                }

                const editorClasses = productEditor.className.split(' ');
                for (const cls of editorClasses) {
                    if (cls.includes('STANDARD_TSHIRT')) return 'STANDARD_TSHIRT';
                    if (cls.includes('PREMIUM_TSHIRT')) return 'PREMIUM_TSHIRT';
                    if (cls.includes('VNECK')) return 'VNECK';
                    if (cls.includes('TANK_TOP')) return 'TANK_TOP';
                    if (cls.includes('LONG_SLEEVE')) return 'STANDARD_LONG_SLEEVE';
                    if (cls.includes('RAGLAN')) return 'RAGLAN';
                    if (cls.includes('PULLOVER_HOODIE')) return 'STANDARD_PULLOVER_HOODIE';
                    if (cls.includes('ZIP_HOODIE')) return 'ZIP_HOODIE';
                    if (cls.includes('SWEATSHIRT')) return 'STANDARD_SWEATSHIRT';
                }
            }

            if (this.lastClickedProduct) {
                return this.lastClickedProduct;
            }

            return null;
        } catch (error) {
            console.error();
            return null;
        }
    }

    getScalableProductName() {
        try {

            const productEditor = document.querySelector('.product-editor');
            if (productEditor) {

                const productTitle = productEditor.querySelector('.product-title');
                if (productTitle && productTitle.textContent) {

                    const productName = productTitle.textContent.trim().replace(/\s+by\s+Amazon$/i, '');

                    if (productName.includes('PopSockets')) return 'POP_SOCKET';
                    if (productName.includes('Phone Case')) return 'PHONE_CASE';
                    if (productName.includes('Mouse Pad')) return 'MOUSEPAD';
                    if (productName.includes('Tote Bag')) return 'TOTE_BAG';
                    if (productName.includes('Throw Pillow')) return 'THROW_PILLOW';

                    return productName.toUpperCase().replace(/\s+/g, '_');
                }

                const editorClasses = productEditor.className.split(' ');
                for (const cls of editorClasses) {
                    if (cls.includes('POPSOCKET')) return 'POP_SOCKET';
                    if (cls.includes('PHONE_CASE')) return 'PHONE_CASE';
                    if (cls.includes('MOUSEPAD')) return 'MOUSEPAD';
                    if (cls.includes('TOTE_BAG')) return 'TOTE_BAG';
                    if (cls.includes('THROW_PILLOW')) return 'THROW_PILLOW';
                }
            }

            return null;
        } catch (error) {
            console.error();
            return null;
        }
    }

    playCompletionSound() {
        console.log();

        chrome.runtime.sendMessage({ action: 'getAutomationState' }, (response) => {
            if (response && response.success && response.state) {
                const state = response.state;
                const currentFileIndex = this.currentFile?.index + 1 || 1;
                const totalFiles = state.totalFiles || this.settings?.totalFiles || 0;

                const isLastFile = currentFileIndex >= totalFiles;
                console.log();

                chrome.runtime.sendMessage({
                    action: 'playCompletionSound',
                    isLastFile: isLastFile
                });
            } else {

                chrome.runtime.sendMessage({
                    action: 'playCompletionSound'
                });
            }
        });
    }

    handleError(error) {
        console.error();

        this.retryCount++;

        if (this.retryCount <= this.maxRetries) {
            console.log();

            setTimeout(() => this.startAutomation(), 5000);
        } else {
            console.error();

            this.updateProgressUI('Error: ' + error.message, true);

            chrome.runtime.sendMessage({
                action: 'showErrorNotification',
                filename: this.currentFile?.filename,
                error: error.message
            });

            console.log();
            chrome.runtime.sendMessage({
                action: 'markTabCompleted',
                filename: this.currentFile?.filename,
                status: 'error',
                error: error.message
            }, (response) => {
                console.log();

                console.log();
                setTimeout(() => {

                    this.processNextFile();
                }, 1000);
            });
        }
    }

    async waitForElement(selector, timeout = 300000) {
        console.log();
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const existingElement = document.querySelector(selector);
            if (existingElement) {
                console.log();
                resolve(existingElement);
                return;
            }

            const checkElement = () => {
                const element = document.querySelector(selector);

                if (element) {
                    console.log();
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    console.warn();
                    reject(new Error());
                } else {
                    setTimeout(checkElement, 500);
                }
            };

            checkElement();
        });
    }

    async blobUrlToFile(blobUrl, filename) {
        try {
            const response = await fetch(blobUrl);
            const blob = await response.blob();
            return new File([blob], filename, { type: blob.type });
        } catch (error) {
            console.error();
            throw error;
        }
    }

    base64ToFile(base64, filename) {
        const arr = base64.split(',');
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);

        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }

        return new File([u8arr], filename, { type: mime });
    }

    cleanFilenameForTextSwap(filename) {

        let cleanName = filename.replace(/\.[^/.]+$/, '');

        cleanName = cleanName.replace(/[-_]+$/, '');

        if (cleanName.length > 1 || /[-_\s(]/.test(cleanName)) {

            cleanName = cleanName.replace(/(?:[-_\s]*\d+|[-_\s]*\(\d+\))$/, '');
        }

        return cleanName.trim();
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async waitForGeneratingImageMessages() {
        try {
            console.log();

            this.generatingImageCount = 0;

            return new Promise((resolve, reject) => {
                const startTime = Date.now();
                const timeout = 300000; 

                const checkForMessages = () => {

                    let messageVisible = false;
                    let detectedMessage = '';

                    for (const message of this.productSelectors.generatingMessages) {
                        if (document.body.textContent.includes(message)) {
                            messageVisible = true;
                            detectedMessage = message;
                            break;
                        }
                    }

                    if (messageVisible) {
                        console.log();

                        this.generatingImageCount++;
                        console.log();

                        const waitForDisappear = () => {

                            let stillVisible = false;

                            for (const message of this.productSelectors.generatingMessages) {
                                if (document.body.textContent.includes(message)) {
                                    stillVisible = true;
                                    break;
                                }
                            }

                            if (!stillVisible) {
                                console.log();

                                if (this.generatingImageCount >= this.requiredGeneratingImageCount) {

                                    console.log();
                                    setTimeout(() => {
                                        resolve();
                                    }, 300);
                                } else {

                                    setTimeout(checkForMessages, 500);
                                }
                            } else if (Date.now() - startTime > timeout) {
                                reject(new Error());
                            } else {

                                setTimeout(waitForDisappear, 500);
                            }
                        };

                        setTimeout(waitForDisappear, 500);
                    } else if (Date.now() - startTime > timeout) {
                        reject(new Error());
                    } else {

                        setTimeout(checkForMessages, 500);
                    }
                };

                checkForMessages();
            });
        } catch (error) {
            console.error();
            throw error;
        }
    }

    async processTumblerColors(colorOption) {
        try {
            console.log();

            this.currentOperation = 'processTumblerColors';

            const isEditorOpen = document.querySelector('.product-editor') !== null;
            console.log();

            if (!isEditorOpen) {
                console.log();

                const editButton = document.querySelector('.TUMBLER-edit-btn');

                if (!editButton) {
                    console.error();
                    return;
                }

                const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                const productCard = editButton.closest('.product-card');
                const isCardDisabled = productCard && productCard.classList.contains('disabled');

                if (isButtonDisabled || isCardDisabled) {
                    console.error();
                    return;
                }

                console.log();
                editButton.click();

                console.log();
                await this.sleep(750);
            }

            console.log();
            const colorContainer = await this.waitForElement('.color-groups-container');

            if (!colorContainer) {
                console.error();
                return;
            }

            console.log();

            const colorActionsContainer = document.querySelector('.product-color-actions');
            if (!colorActionsContainer) {
                console.error();
                return;
            }

            let colorButton;

            if (colorOption === 'Dark Colors') {
                colorButton = document.querySelector('#product-dark-colors-btn');
                console.log();
            } else if (colorOption === 'Light Colors') {
                colorButton = document.querySelector('#product-light-colors-btn');
                console.log();
            } else if (colorOption === 'All Colors') {
                colorButton = document.querySelector('#product-all-colors-btn');
                console.log();
            }

            if (!colorButton) {
                console.log();

                if (colorOption === 'Dark Colors') {
                    colorButton = document.querySelector('button[id="product-dark-colors-btn"]');
                    console.log();
                } else if (colorOption === 'Light Colors') {
                    colorButton = document.querySelector('button[id="product-light-colors-btn"]');
                    console.log();
                } else if (colorOption === 'All Colors') {
                    colorButton = document.querySelector('button[id="product-all-colors-btn"]');
                    console.log();
                }

                if (!colorButton) {
                    console.log();
                    return;
                }
            }

            console.log();
            colorButton.click();

            console.log();
            await this.sleep(300);

            console.log();

        } catch (error) {
            console.error();

        }
    }

    async processTumblerPrices(priceOption) {
        try {
            console.log();

            this.currentOperation = 'processTumblerPrices';

            const isEditorOpen = document.querySelector('.product-editor') !== null;
            console.log();

            if (!isEditorOpen) {
                console.log();

                const editButton = document.querySelector('.TUMBLER-edit-btn');

                if (!editButton) {
                    console.error();
                    return;
                }

                const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                const productCard = editButton.closest('.product-card');
                const isCardDisabled = productCard && productCard.classList.contains('disabled');

                if (isButtonDisabled || isCardDisabled) {
                    console.error();
                    return;
                }

                console.log();
                editButton.click();

                console.log();
                await this.sleep(750);
            }

            console.log();
            const priceContainer = await this.waitForElement('.product-price-actions');

            if (!priceContainer) {
                console.error();
                return;
            }

            console.log();

            let priceButton;

            if (priceOption === 'Default Prices') {
                priceButton = document.querySelector('#product-default-prices-btn');
                console.log();
            } else if (priceOption === 'Suggested Prices') {
                priceButton = document.querySelector('#product-suggested-prices-btn');
                console.log();
            } else if (priceOption === 'Market Average') {
                priceButton = document.querySelector('#product-market-average-btn');
                console.log();
            } else if (priceOption === 'Max Prices') {
                priceButton = document.querySelector('#product-max-prices-btn');
                console.log();
            }

            if (!priceButton) {
                console.log();

                if (priceOption === 'Default Prices') {
                    priceButton = document.querySelector('button[id="product-default-prices-btn"]');
                    console.log();
                } else if (priceOption === 'Suggested Prices') {
                    priceButton = document.querySelector('button[id="product-suggested-prices-btn"]');
                    console.log();
                } else if (priceOption === 'Market Average') {
                    priceButton = document.querySelector('button[id="product-market-average-btn"]');
                    console.log();
                } else if (priceOption === 'Max Prices') {
                    priceButton = document.querySelector('button[id="product-max-prices-btn"]');
                    console.log();
                }

                if (!priceButton) {
                    console.log();
                    return;
                }
            }

            const priceButtonId = priceButton.id;
            console.log();

            console.log();
            priceButton.click();

            console.log();
            let buttonDisabled = false;
            let checkCount = 0;
            const maxChecks = 10;

            while (!buttonDisabled && checkCount < maxChecks) {
                await this.sleep(200);
                const currentButton = document.querySelector(`#${priceButtonId}`);
                if (currentButton) {
                    buttonDisabled = currentButton.disabled || 
                                    currentButton.classList.contains('disabled') || 
                                    currentButton.getAttribute('disabled') !== null;

                    if (buttonDisabled) {
                        console.log();
                    }
                }
                checkCount++;
            }

            console.log();
            let buttonEnabled = false;
            checkCount = 0;
            const maxWaitChecks = 30; 

            while (!buttonEnabled && checkCount < maxWaitChecks) {
                await this.sleep(300);
                const currentButton = document.querySelector(`#${priceButtonId}`);
                if (currentButton) {
                    buttonEnabled = !currentButton.disabled && 
                                   !currentButton.classList.contains('disabled') && 
                                   currentButton.getAttribute('disabled') === null;

                    if (buttonEnabled) {
                        console.log();
                    }
                }
                checkCount++;
            }

            if (!buttonEnabled) {
                console.log();
            }

            console.log();
            await this.sleep(250);

            console.log();

        } catch (error) {
            console.error();

        }
    }

    rgbToHex(rgb) {
        const match = rgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
        if (match) {
            const r = parseInt(match[1], 10).toString(16).padStart(2, '0');
            const g = parseInt(match[2], 10).toString(16).padStart(2, '0');
            const b = parseInt(match[3], 10).toString(16).padStart(2, '0');
            return `#${r}${g}${b}`;
        }
        return rgb;
    }

    calculateColorDistance(color1, color2) {
        const r1 = parseInt(color1.slice(1, 3), 16);
        const g1 = parseInt(color1.slice(3, 5), 16);
        const b1 = parseInt(color1.slice(5, 7), 16);
        const r2 = parseInt(color2.slice(1, 3), 16);
        const g2 = parseInt(color2.slice(3, 5), 16);
        const b2 = parseInt(color2.slice(5, 7), 16);
        return Math.sqrt((r2 - r1) ** 2 + (g2 - g1) ** 2 + (b2 - b1) ** 2);
    }

    async waitForBackAndPocketMessages() {
        try {
            console.log();

            console.log();
            await this.waitForAnyGeneratingMessage();
            console.log();

            console.log();
            await this.sleep(3000);

            console.log();
            await this.waitForAnyGeneratingMessage();
            console.log();

            console.log();
            await this.sleep(300);

            console.log();
        } catch (error) {
            console.error();
            throw error;
        }
    }

    async waitForAnyGeneratingMessage(timeout = 300000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const checkForMessage = () => {

                let messageVisible = false;
                let detectedMessage = '';

                for (const message of this.productSelectors.generatingMessages) {
                    if (document.body.textContent.includes(message)) {
                        messageVisible = true;
                        detectedMessage = message;
                        break;
                    }
                }

                if (messageVisible) {
                    console.log();
                    resolve();
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`Timeout waiting for generating image message after ${timeout/1000} seconds`));
                } else {

                    setTimeout(checkForMessage, 500);
                }
            };

            checkForMessage();
        });
    }

    async processTumblerSides(sidesOption) {
        try {
            console.log();
            this.currentOperation = 'processTumblerSides';

            let tabButton;

            if (sidesOption === 'Both Sides') {
                tabButton = Array.from(document.querySelectorAll('.tab-button')).find(
                    btn => btn.textContent.trim().includes('Both Sides') || btn.textContent.trim().includes('Two Sides')
                );
                console.log();
            } else if (sidesOption === 'Default (One Side)') {
                tabButton = Array.from(document.querySelectorAll('.tab-button')).find(
                    btn => btn.textContent.trim().includes('One Side') || btn.textContent.trim() === 'Front'
                );
                console.log();
            }

            if (!tabButton) {
                console.log();
                return;
            }

            if (tabButton.classList.contains('active')) {
                console.log();
            } else {

                console.log();
                tabButton.click();

                console.log();
                await this.sleep(500);
            }

            console.log();
            const resizeBtn = await this.waitForElement('.resize-btn:not([disabled])');

            console.log();
            resizeBtn.click();

            console.log();
            await this.sleep(1500);

            console.log();
            try {

                let messageVisible = false;
                let detectedMessage = '';

                for (const message of this.productSelectors.generatingMessages) {
                    if (document.body.textContent.includes(message)) {
                        messageVisible = true;
                        detectedMessage = message;
                        break;
                    }
                }

                if (messageVisible) {
                    console.log();
                } else {
                    console.log();

                    await this.waitForAnyGeneratingMessage(60000); 
                }

                console.log();
                await this.sleep(150);
            } catch (waitError) {
                console.warn();
            }

            console.log();

        } catch (error) {
            console.error();
            throw error;
        }
    }

    async processTumblerScale(scaleOption, customScale) {
        try {
            console.log();

            this.currentOperation = 'processTumblerScale';

            const isEditorOpen = document.querySelector('.product-editor') !== null;
            console.log();

            if (!isEditorOpen) {
                console.log();

                const editButton = document.querySelector('.TUMBLER-edit-btn');

                if (!editButton) {
                    console.error();
                    return;
                }

                const isButtonDisabled = editButton.disabled || editButton.classList.contains('disabled');

                const productCard = editButton.closest('.product-card');
                const isCardDisabled = productCard && productCard.classList.contains('disabled');

                if (isButtonDisabled || isCardDisabled) {
                    console.error();
                    return;
                }

                console.log();
                editButton.click();

                console.log();
                await this.sleep(1200);
            }

            console.log();

            const tumblerSettings = this.settings.tumblerProduct || {};
            const desiredSide = tumblerSettings.sides || 'Default (One Side)';
            console.log();

            const sideTabButtons = Array.from(document.querySelectorAll('.tab-button'));
            console.log();

            if (sideTabButtons.length > 0) {
                console.log();
                sideTabButtons.forEach(btn => {
                    console.log();
                });
            }

            const activeSideTab = sideTabButtons.find(btn => btn.classList.contains('active'));

            let exactMatchTab = null;

            if (desiredSide === 'Two Sides') {

                exactMatchTab = sideTabButtons.find(btn => btn.textContent.trim() === 'Two Sides');
                console.log();
            } else if (desiredSide === 'Both Sides') {

                exactMatchTab = sideTabButtons.find(btn => btn.textContent.trim() === 'Both Sides');
                console.log();
            } else if (desiredSide === 'Default (One Side)') {

                exactMatchTab = sideTabButtons.find(btn => 
                    btn.textContent.trim() === 'One Side' || 
                    btn.textContent.trim() === 'Default (One Side)' ||
                    btn.textContent.trim() === 'Front'
                );
                console.log();
            }

            if (exactMatchTab && (!activeSideTab || activeSideTab.textContent.trim() !== exactMatchTab.textContent.trim())) {
                console.log();
                exactMatchTab.click();

                console.log();
                await this.sleep(1200);
            } 

            else if (!exactMatchTab && !activeSideTab && sideTabButtons.length > 0) {
                console.log();

                const firstTab = sideTabButtons[0];
                console.log();
                firstTab.click();

                console.log();
                await this.sleep(1200);
            }

            else if (activeSideTab) {
                if (exactMatchTab && activeSideTab.textContent.trim() === exactMatchTab.textContent.trim()) {
                    console.log();
                } else {
                    console.log();
                }
            } else {
                console.log();
            }

            console.log();
            let scaleInput;

            if (scaleOption === 'Keep current') {
                console.log();

            } else if (scaleOption === 'Custom scale') {
                console.log();

                const customRadio = document.querySelector('input#custom[type="radio"]');
                if (!customRadio) {
                    console.log();
                    scaleInput = document.querySelector('input[type="radio"][id*="custom"], input[type="radio"][value*="custom"]');
                } else {
                    scaleInput = customRadio;
                }

                if (scaleInput) {
                    console.log();
                    scaleInput.click();

                    console.log();
                    await this.sleep(500);

                    if (customScale) {
                        console.log();

                        let customScaleInputField = document.querySelector('input[type="number"]:not([disabled])');

                        if (!customScaleInputField) {
                            console.log();
                            customScaleInputField = document.querySelector('input[type="number"]');

                            if (customScaleInputField && customScaleInputField.disabled) {
                                console.log();

                                customScaleInputField.removeAttribute('disabled');

                                await this.sleep(500);
                            }
                        }

                        if (!customScaleInputField) {
                            console.log();

                            const alternativeInputs = [
                                document.querySelector('input.custom-scale-input'),
                                document.querySelector('input.scale-input'),
                                document.querySelector('input.scale-value'),
                                document.querySelector('input[placeholder*="scale"], input[placeholder*="Scale"]'),
                                document.querySelector('input:not([type="radio"])[id*="scale"], input:not([type="radio"])[id*="Scale"]'),
                                document.querySelector('input:not([type="radio"]):not([type="checkbox"]).ng-untouched'),
                                document.querySelector('input[min="50"][max="100"]'),
                                document.querySelector('input[style*="border-bottom: 2px solid"]')
                            ];

                            customScaleInputField = alternativeInputs.find(input => input !== null);
                        }

                        if (customScaleInputField) {
                            console.log();

                            if (customScaleInputField.disabled) {
                                console.log();
                                customScaleInputField.disabled = false;
                                customScaleInputField.removeAttribute('disabled');
                            }

                            console.log();
                            customScaleInputField.focus();
                            await this.sleep(300);

                            console.log();
                            customScaleInputField.value = '';
                            customScaleInputField.dispatchEvent(new Event('input', { bubbles: true }));
                            await this.sleep(300);

                            console.log();
                            customScaleInputField.value = customScale;
                            customScaleInputField.dispatchEvent(new Event('input', { bubbles: true }));
                            customScaleInputField.dispatchEvent(new Event('change', { bubbles: true }));

                            console.log();
                            customScaleInputField.blur();
                            await this.sleep(300);

                            console.log();
                        } else {
                            console.log();
                        }
                    }
                }
            } else if (scaleOption === '100%') {
                console.log();
                scaleInput = document.querySelector('input#percent100');
                if (scaleInput) scaleInput.click();
            } else if (scaleOption === '85%') {
                console.log();
                scaleInput = document.querySelector('input#percent85');
                if (scaleInput) scaleInput.click();
            } else if (scaleOption === '75%') {
                console.log();
                scaleInput = document.querySelector('input#percent75');
                if (scaleInput) scaleInput.click();
            } else if (scaleOption === 'Pattern') {
                console.log();
                scaleInput = document.querySelector('input#pattern');
                if (scaleInput) scaleInput.click();
            }

            if (!scaleInput && scaleOption !== 'Keep current') {
                console.log();
                return;
            }

            if (scaleOption !== 'Keep current') {
                console.log();
                await this.sleep(1000);
            }

            console.log();
            const resizeBtn = await this.waitForElement('.resize-btn:not([disabled])');

            if (!resizeBtn) {
                console.log();
                const alternativeResizeButtons = [
                    document.querySelector('button.resize-btn'),
                    document.querySelector('button[class*="resize"]'),
                    document.querySelector('button:contains("Resize")'),
                    document.querySelector('button:contains("Apply")')
                ];

                const resizeButton = alternativeResizeButtons.find(btn => btn !== null && !btn.disabled);

                if (resizeButton) {
                    console.log();
                    resizeButton.click();
                } else {
                    console.log();
                    return;
                }
            } else {

                console.log();
                resizeBtn.click();
            }

            console.log();
            try {

                await this.waitForAnyGeneratingMessage(60000); 
                console.log();

                await this.waitForGeneratingImageMessages();
                console.log();
            } catch (waitError) {
                console.warn();
                console.log();
            }

            console.log();

        } catch (error) {
            console.error();

        }
    }

    hideSnapBulkUploadButton() {
        try {
            console.log();

            const style = document.createElement('style');
            style.id = 'snap-automation-hide-button-style';
            style.textContent = `
                #snap-bulk-upload-btn {
                    display: none !important;
                    visibility: hidden !important;
                    opacity: 0 !important;
                    pointer-events: none !important;
                }
            `;
            document.head.appendChild(style);

            const existingButton = document.querySelector('#snap-bulk-upload-btn');
            if (existingButton) {
                existingButton.style.display = 'none';
                existingButton.style.visibility = 'hidden';
                existingButton.style.opacity = '0';
                existingButton.style.pointerEvents = 'none';
                console.log();
            }

            const observer = new MutationObserver((mutations) => {
                for (const mutation of mutations) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        for (const node of mutation.addedNodes) {
                            if (node.nodeType === Node.ELEMENT_NODE) {

                                if (node.id === 'snap-bulk-upload-btn') {
                                    node.style.display = 'none';
                                    node.style.visibility = 'hidden';
                                    node.style.opacity = '0';
                                    node.style.pointerEvents = 'none';
                                    console.log();
                                }

                                const button = node.querySelector('#snap-bulk-upload-btn');
                                if (button) {
                                    button.style.display = 'none';
                                    button.style.visibility = 'hidden';
                                    button.style.opacity = '0';
                                    button.style.pointerEvents = 'none';
                                    console.log();
                                }
                            }
                        }
                    }
                }
            });

            observer.observe(document.body, { 
                childList: true, 
                subtree: true 
            });

            console.log();
        } catch (error) {
            console.error();

        }
    }
}

window.snapAutomation = new SnapAutomation();