<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }

      .cls-2 {
        fill: #606f95;
      }

      .cls-3 {
        clip-path: url(#clippath);
      }
    </style>
    <clipPath id="clippath">
      <rect class="cls-1" width="16" height="16"/>
    </clipPath>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g class="cls-3">
      <path class="cls-2" d="M8,0C3.57,0,0,3.57,0,8s3.57,8,8,8,8-3.57,8-8S12.43,0,8,0ZM10.44,11.49c-.43.37-.96.57-1.64.68v.19c0,.31-.25.57-.57.57h-.47c-.31,0-.57-.25-.57-.57v-.21c-.64-.09-1.16-.31-1.59-.65-.6-.48-.89-1.12-.89-1.97h1.6c.05.37.16.64.29.81.27.33.73.51,1.39.51s.72-.04.96-.13c.47-.17.69-.48.69-.93s-.12-.47-.35-.61c-.23-.13-.6-.27-1.08-.37l-.85-.19c-.83-.19-1.41-.39-1.72-.61-.55-.37-.81-.95-.81-1.73s.25-1.32.77-1.77c.39-.35.91-.57,1.56-.65v-.27c0-.31.25-.57.57-.57h.47c.31,0,.57.25.57.57v.31c.48.09.91.29,1.28.57.6.44.91,1.11.93,1.95h-1.6c-.04-.48-.23-.83-.64-1.03-.27-.13-.57-.19-.96-.19s-.77.08-1.03.25c-.25.17-.39.43-.39.73s.13.51.39.64c.16.09.51.21,1.04.33l1.37.33c.6.16,1.07.35,1.37.57.48.39.72.93.72,1.64s-.29,1.33-.83,1.81Z"/>
    </g>
  </g>
</svg>