<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 52.28 52.28">
  <defs>
    <style>
      .cls-1 {
        fill: #ff391f;
      }

      .cls-2 {
        fill-rule: evenodd;
      }

      .cls-2, .cls-3 {
        fill: #fff;
      }

      .cls-4 {
        fill: #e1ecee;
      }

      .cls-5 {
        fill: #bdcedb;
      }

      .cls-6 {
        fill: url(#linear-gradient);
      }

      .cls-7 {
        fill: #bfd6dc;
      }
    </style>
    <linearGradient id="linear-gradient" x1="26.14" y1="-.37" x2="26.14" y2="50.84" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#bdcedb"/>
      <stop offset="1" stop-color="#bdcedb" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <g>
        <path class="cls-6" d="M26.14,52.28c14.44,0,26.14-11.7,26.14-26.14S40.57,0,26.14,0,0,11.7,0,26.14s11.7,26.14,26.14,26.14Z"/>
        <g>
          <g>
            <g>
              <path class="cls-3" d="M42.76,40.55H9.57c-1.21,0-2.14-.93-2.14-2.07V14.93c0-1.14.93-2.07,2.14-2.07h33.19c1.21,0,2.29.93,2.29,2.07v23.55c-.05,1.14-1.13,2.07-2.29,2.07Z"/>
              <path class="cls-5" d="M42.76,41.01H9.57c-1.45,0-2.59-1.11-2.59-2.53V14.93c0-1.42,1.14-2.53,2.59-2.53h33.19c1.48,0,2.74,1.16,2.74,2.53v23.55c-.07,1.38-1.32,2.53-2.74,2.53ZM9.57,13.31c-.94,0-1.68.71-1.68,1.62v23.55c0,.91.74,1.62,1.68,1.62h33.19c.93,0,1.79-.77,1.83-1.64V14.93c0-.86-.86-1.62-1.83-1.62H9.57Z"/>
            </g>
            <g>
              <path class="cls-3" d="M26.42,40.55l.15-27.7"/>
              <rect class="cls-5" x="12.65" y="26.25" width="27.7" height=".91" transform="translate(-.33 53.07) rotate(-89.73)"/>
            </g>
            <rect class="cls-5" x="7.43" y="18.23" width="37.62" height=".91"/>
          </g>
          <path class="cls-7" d="M10.78,16.73c.52,0,.93-.41.93-.93s-.42-.93-.93-.93-.93.41-.93.93.42.93.93.93Z"/>
          <path class="cls-7" d="M13.52,16.73c.52,0,.93-.41.93-.93s-.42-.93-.93-.93-.93.41-.93.93.42.93.93.93Z"/>
          <path class="cls-7" d="M16.32,16.73c.52,0,.93-.41.93-.93s-.42-.93-.93-.93-.93.41-.93.93.42.93.93.93Z"/>
          <path class="cls-7" d="M15.99,21.85h-5.27c-.33,0-.6-.27-.6-.49,0-.27.27-.49.6-.49h5.27c.33,0,.6.27.6.49,0,.27-.27.49-.6.49Z"/>
          <path class="cls-7" d="M14.73,30.3h-3.95c-.33,0-.6-.27-.6-.49,0-.27.27-.49.6-.49h3.95c.33,0,.6.27.6.49-.05.22-.33.49-.6.49Z"/>
          <path class="cls-4" d="M19.55,33.08h-6.69c-.33,0-.6-.27-.6-.49,0-.27.27-.49.6-.49h6.69c.33,0,.6.27.6.49-.05.27-.33.49-.6.49Z"/>
          <path class="cls-4" d="M26.14,32.05v1.04h-4.01c-.33,0-.6-.27-.6-.49,0-.16.05-.27.22-.38.11-.05.22-.16.44-.16h3.95Z"/>
          <path class="cls-7" d="M18.46,35.81h-7.74c-.33,0-.6-.27-.6-.49,0-.27.27-.49.6-.49h7.74c.33,0,.6.27.6.49,0,.27-.22.49-.6.49Z"/>
          <path class="cls-4" d="M14.18,38.64h-3.29c-.33,0-.6-.27-.6-.49,0-.27.27-.49.6-.49h3.29c.33,0,.6.27.6.49,0,.27-.22.49-.6.49Z"/>
          <path class="cls-4" d="M16.98,38.64h-.71c-.33,0-.6-.27-.6-.49,0-.27.27-.49.6-.49h.71c.33,0,.6.27.6.49,0,.27-.27.49-.6.49Z"/>
          <path class="cls-4" d="M12.31,27.58h-1.59c-.33,0-.6-.27-.6-.49,0-.27.27-.49.6-.49h1.59c.33,0,.6.27.6.49,0,.27-.27.49-.6.49Z"/>
          <path class="cls-4" d="M18.46,24.69h-7.74c-.33,0-.6-.27-.6-.49,0-.27.27-.49.6-.49h7.74c.33,0,.6.27.6.49,0,.27-.22.49-.6.49Z"/>
          <path class="cls-7" d="M26.15,23.65v1.04h-5.11c-.33,0-.6-.27-.6-.49,0-.16.05-.27.22-.38.05-.11.22-.16.44-.16h5.06Z"/>
        </g>
      </g>
      <g>
        <path class="cls-2" d="M38.88,35.15l-4.43-4.43c.54-.82.86-1.8.86-2.85,0-2.86-2.32-5.17-5.17-5.17-2.86,0-5.17,2.32-5.17,5.17s2.32,5.17,5.17,5.17c1.05,0,2.04-.32,2.85-.86l4.43,4.43c.4.4,1.06.4,1.46,0,.4-.4.4-1.06,0-1.46ZM30.14,30.98c-1.71,0-3.1-1.39-3.1-3.1s1.39-3.1,3.1-3.1c1.71,0,3.1,1.39,3.1,3.1s-1.39,3.1-3.1,3.1Z"/>
        <path class="cls-7" d="M38.15,37.25c-.35,0-.7-.13-.96-.4l-4.24-4.24c-.85.5-1.81.77-2.81.77-3.03,0-5.5-2.47-5.5-5.5s2.47-5.5,5.5-5.5,5.5,2.47,5.5,5.5c0,.99-.26,1.96-.77,2.81l4.24,4.24c.26.26.4.6.4.96s-.14.71-.4.96c-.27.26-.61.4-.96.4ZM32.99,31.86c.08,0,.17.03.23.1l4.43,4.43c.27.28.72.28,1,0,.28-.28.28-.72,0-1l-4.43-4.43c-.11-.11-.13-.28-.04-.41.53-.79.8-1.72.8-2.67,0-2.67-2.17-4.85-4.85-4.85s-4.85,2.17-4.85,4.85,2.17,4.85,4.85,4.85c.95,0,1.88-.28,2.67-.8.06-.04.12-.05.18-.05ZM30.14,31.31c-1.89,0-3.43-1.54-3.43-3.43s1.54-3.43,3.43-3.43,3.43,1.54,3.43,3.43-1.54,3.43-3.43,3.43ZM30.14,25.1c-1.53,0-2.78,1.25-2.78,2.78s1.25,2.78,2.78,2.78,2.78-1.25,2.78-2.78-1.25-2.78-2.78-2.78Z"/>
      </g>
      <g>
        <circle class="cls-3" cx="44.11" cy="44.11" r="8.17"/>
        <circle class="cls-1" cx="44.11" cy="44.11" r="6.3"/>
        <g>
          <path class="cls-3" d="M45.34,42.44l-1.29,1.28-1.28-1.29c-.06-.06-.14-.1-.23-.1-.09,0-.17.03-.23.09s-.1.14-.1.23c0,.09.03.17.09.23l1.28,1.29-1.29,1.28c-.06.06-.1.14-.1.23,0,.09.03.17.09.23s.14.1.23.1c.09,0,.17-.03.23-.09l1.29-1.28,1.28,1.29c.06.06.14.1.23.1.09,0,.17-.03.23-.09s.1-.14.1-.23c0-.09-.03-.17-.09-.23l-1.28-1.29,1.29-1.28c.06-.06.1-.14.1-.23,0-.09-.03-.17-.09-.23s-.14-.1-.23-.1c-.09,0-.17.03-.23.09Z"/>
          <path class="cls-3" d="M45.56,46.15c-.13,0-.24-.05-.33-.14l-1.19-1.2-1.2,1.19c-.09.09-.21.13-.32.13-.12,0-.23-.05-.32-.14-.08-.08-.13-.2-.13-.32s.05-.23.13-.32l1.2-1.19-1.19-1.2c-.08-.08-.13-.2-.13-.32s.05-.23.13-.32c.17-.17.47-.17.64,0l1.19,1.2,1.2-1.19c.17-.17.47-.17.64,0,.08.08.13.2.13.32,0,.12-.05.23-.14.32l-1.2,1.19,1.19,1.2c.09.09.13.2.13.32,0,.12-.05.24-.14.32-.08.08-.2.13-.32.13ZM44.04,44.45l1.37,1.38s.09.06.14.06h0c.05,0,.1-.02.14-.06.04-.04.06-.09.06-.14,0-.05-.02-.1-.06-.14l-1.37-1.38,1.38-1.37s.06-.09.06-.14c0-.05-.02-.1-.06-.14-.07-.08-.2-.07-.28,0l-1.38,1.37-1.37-1.38c-.08-.08-.21-.07-.28,0-.04.04-.06.09-.06.14s.02.1.06.14l1.37,1.38-1.38,1.37s-.06.09-.06.14.02.1.06.14.09.06.14.06h0c.05,0,.1-.02.14-.06l1.38-1.37Z"/>
        </g>
      </g>
    </g>
  </g>
</svg>