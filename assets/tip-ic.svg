<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }

      .cls-2 {
        fill: #470ced;
      }

      .cls-3 {
        clip-path: url(#clippath);
      }
    </style>
    <clipPath id="clippath">
      <rect class="cls-1" width="16" height="16"/>
    </clipPath>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g class="cls-3">
      <path class="cls-2" d="M8,0C3.57,0,0,3.57,0,8S3.57,16,8,16s8-3.57,8-8S12.43,0,8,0ZM8.89,12.33c-.36.17-.79.28-1.1.28-.47,0-.88-.22-1.14-.45-.26-.23-.39-.53-.39-.88,0-.14,0-.28.03-.43.02-.14.05-.31.09-.49l.49-1.73c.04-.17.08-.32.11-.47.03-.15.05-.29.05-.41,0-.22-.05-.38-.14-.46-.09-.09-.26-.13-.52-.13-.13,0-.25-.5.32-.7.29-.1.57-.14.83-.14.47,0,.83.11,1.09.34.25.23.38.52.38.89,0,.07,0,.21-.03.4-.02.19-.05.36-.1.52l-.49,1.73c-.04.14-.08.3-.11.48-.03.18-.05.31-.05.4,0,.23.05.38.15.47.1.08.28.12.53.12.12,0,.28-.16.4-.06.21.17,0,.53-.41.72ZM9.41,5.25c-.23.21-.5.32-.82.32s-.59-.11-.82-.32c-.23-.21-.34-.47-.34-.77s.11-.56.34-.77c.23-.21.5-.32.82-.32s.59.11.82.32.34.47.34.77-.11.56-.34.77Z"/>
    </g>
  </g>
</svg>