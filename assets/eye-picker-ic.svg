<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }

      .cls-2 {
        fill: #470ced;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <rect class="cls-1" width="20" height="20"/>
      <g>
        <path class="cls-2" d="M10.6,11.66c-.45.12-1.69.39-2.8-.05-.68-.27-1.15-.28-1.46-.21l3.59-3.6c-.26-.31-.48-.6-.66-.88l-4.54,4.54c-.43.43-.71.98-.82,1.58-.09.51-.11,1.19-.45,1.57l-1.08,1.08c-.52.53-.52,1.39.01,1.91.53.53,1.38.53,1.92,0l1.07-1.07c.39-.35,1.07-.38,1.58-.46.6-.1,1.15-.38,1.58-.81l4.54-4.54c-.28-.18-.57-.41-.88-.66l-1.6,1.6Z"/>
        <path class="cls-2" d="M17.27,2.73c-.99-.99-2.61-.96-3.56.06l-1.61,1.71c-.18.22-.5.28-.74.14-.5-.29-.87-.4-1.02-.26-.11.11-.48.48-.59.59-.16.16-.02.58.34,1.15.17.27.39.57.65.89.31.38.67.78,1.08,1.19.41.41.81.77,1.18,1.07.32.26.62.48.9.65.57.35.99.49,1.15.34.11-.11.48-.48.58-.59.15-.15.04-.52-.26-1.02-.14-.25-.09-.57.14-.74h0l1.71-1.61c1.02-.96,1.05-2.57.06-3.56ZM16.66,4.81c-.42-.39-.99-.63-1.61-.63-.43,0-.83.11-1.17.31l.77-.82c.23-.25.54-.38.87-.38s.62.12.84.35c.23.23.35.54.35.86,0,.11-.02.21-.05.31Z"/>
      </g>
    </g>
  </g>
</svg>