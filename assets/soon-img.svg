<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 52.28 52.28">
  <defs>
    <style>
      .cls-1 {
        mask: url(#mask);
      }

      .cls-2 {
        fill: url(#linear-gradient);
      }

      .cls-3 {
        fill: #fff;
      }

      .cls-4 {
        fill: #6c92af;
      }
    </style>
    <mask id="mask" x="0" y="-2.79" width="52.28" height="57.85" maskUnits="userSpaceOnUse">
      <g id="mask0_295_10136" data-name="mask0 295 10136">
        <path class="cls-3" d="M52.28-2.79H0v57.85h52.28V-2.79Z"/>
      </g>
    </mask>
    <linearGradient id="linear-gradient" x1="-179.86" y1="-5614.53" x2="-179.86" y2="-5666.81" gradientTransform="translate(206 -5614.53) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#bdcedb"/>
      <stop offset="1" stop-color="#bdcedb" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <g class="cls-1">
        <path class="cls-2" d="M26.14,52.28c5.17,0,10.22-1.53,14.52-4.41,4.3-2.87,7.65-6.95,9.63-11.73,1.98-4.78,2.5-10.03,1.49-15.1-1.01-5.07-3.5-9.73-7.15-13.38-3.66-3.66-8.31-6.14-13.38-7.15C26.17-.51,20.91.01,16.14,1.99c-4.78,1.98-8.86,5.33-11.73,9.63C1.53,15.91,0,20.97,0,26.14c0,6.93,2.75,13.58,7.66,18.48,4.9,4.9,11.55,7.66,18.48,7.66Z"/>
      </g>
      <g>
        <path class="cls-4" d="M38.81,26.14c0,1.64-.27,3.13-.89,4.57-1.36,3.54-4.29,6.27-7.91,7.5-1.23.41-2.52.61-3.88.61-7.02,0-12.68-5.66-12.68-12.68s5.66-12.68,12.68-12.68,12.68,5.72,12.68,12.68Z"/>
        <path class="cls-3" d="M28.76,24.32v-2.43c0-1.33-1.13-2.43-2.49-2.43-.68,0-1.29.29-1.81.74-.45.44-.76,1.03-.76,1.77v2.43c-.68.44-1.21.96-1.66,1.7-.38.66-.6,1.47-.6,2.28,0,2.58,2.12,4.64,4.76,4.64s4.76-2.06,4.76-4.64c.08-1.77-.83-3.24-2.19-4.05ZM26.8,28.82v1.25c0,.15-.15.29-.3.29h-.53c-.15,0-.3-.15-.3-.29v-1.25c-.45-.22-.68-.74-.6-1.25.08-.44.45-.81.91-.88.76-.15,1.44.44,1.44,1.11,0,.44-.23.81-.6,1.03ZM27.55,23.8c-.45-.15-.83-.22-1.36-.22-.45,0-.91.07-1.36.22v-1.84c0-.37.15-.74.38-.96.23-.22.6-.37.98-.37.76,0,1.36.59,1.36,1.33v1.84Z"/>
      </g>
    </g>
  </g>
</svg>