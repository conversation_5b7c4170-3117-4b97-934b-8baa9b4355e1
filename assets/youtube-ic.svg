<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 34 34">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }

      .cls-2 {
        fill: #758ca3;
        fill-rule: evenodd;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <g id="Page-1">
        <g id="Dribbble-Light-Preview">
          <g id="icons">
            <path id="youtube-_168_" data-name="youtube-[#168]" class="cls-2" d="M13.58,21.4v-9.54c3.39,1.59,6.01,3.13,9.11,4.79-2.56,1.42-5.73,3.01-9.11,4.75M32.45,7.11c-.58-.77-1.58-1.37-2.64-1.57-3.12-.59-22.55-.59-25.67,0-.85.16-1.61.54-2.26,1.14C-.85,9.23,0,22.87.67,25.08c.28.96.64,1.65,1.09,2.1.58.6,1.38,1.01,2.3,1.2,2.57.53,15.78.83,25.71.08.91-.16,1.72-.58,2.36-1.21,2.53-2.53,2.36-16.94.33-20.13"/>
          </g>
        </g>
      </g>
      <rect class="cls-1" width="34" height="34"/>
    </g>
  </g>
</svg>