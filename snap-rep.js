const reportIssueDescriptions = {
  "Stolen Artwork": [
    "Hello Amazon Support,\nI'm contacting you regarding a serious infringement involving my original artwork (ASIN: [MY ASIN]), which I first published on [My ASIN Date]. Someone has directly downloaded my design file from Amazon and re-uploaded it as their own listing. A review of timestamps clearly proves unauthorized extraction.\n\nPlease investigate immediately, remove the offending listing, and take necessary actions against this unauthorized seller. Your prompt resolution is highly appreciated.\n\nThanks in advance for addressing this matter promptly.",
    
    "Hello Amazon Team,\nI've noticed that another seller extracted my original design (ASIN: [MY ASIN]) from Amazon, originally published by me on [My ASIN Date], and re-uploaded it as their own. The timestamps clearly confirm my design was posted first, demonstrating unauthorized extraction.\n\nI'd appreciate your urgent help to remove this infringement and deal appropriately with the seller responsible.\n\nThank you for your prompt attention to this issue.",
    
    "Hello Amazon,\nUnfortunately, someone downloaded my original artwork from Amazon (ASIN: [MY ASIN]), initially listed on [My ASIN Date], and re-uploaded it without my permission. It's not just copied visually; my actual original file was extracted.\n\nCould you please act quickly to remove this infringement and ensure the seller responsible is addressed?\n\nI appreciate your immediate assistance.",
    
    "Dear Amazon Support,\nI'm reaching out because another seller has directly extracted my original design (ASIN: [MY ASIN]) published on Amazon since [My ASIN Date], and re-uploaded it onto Amazon without authorization.\n\nAs timestamps clearly demonstrate, my listing is the original source. I kindly ask for immediate removal of the infringing content and necessary action against the seller.\n\nThank you in advance for your help.",
    
    "Hello Amazon Team,\nI recently discovered someone directly downloaded and re-uploaded my original design file (ASIN: [MY ASIN]), which has been available on Amazon since [My ASIN Date]. The infringement is evident from the timestamps, clearly showing unauthorized extraction.\n\nPlease urgently remove this listing and take immediate action against the responsible seller.\n\nThank you for reviewing this matter quickly.",
    
    "Dear Amazon Support Team,\nI recently became aware of an unauthorized seller who directly extracted and re-uploaded my original artwork (ASIN: [MY ASIN]), first listed on [My ASIN Date]. Checking timestamps confirms that my design was directly taken from Amazon and re-uploaded.\n\nYour assistance in promptly removing this infringement and addressing the unauthorized seller would be greatly appreciated.\n\nI appreciate your timely cooperation.",
    
    "Hello Amazon,\nI found out today that my original artwork (ASIN: [MY ASIN], listed first on [My ASIN Date]) was directly downloaded from Amazon by someone else, who then re-uploaded it as their own listing.\n\nPlease remove this immediately and address the infringing seller quickly.\n\nThank you for handling this issue swiftly.",
    
    "Hello Amazon Support,\nI've noticed an alarming copyright violation: another seller directly downloaded and re-uploaded my original design (ASIN: [MY ASIN]), initially published on [My ASIN Date]. The extraction can easily be proven by comparing publication timestamps.\n\nI kindly request your immediate intervention to remove the unauthorized listing and take action against the seller involved.\n\nThank you for addressing this concern.",
    
    "Hello Amazon Team,\nUnfortunately, it appears another seller has directly downloaded and re-uploaded my original design (ASIN: [MY ASIN]), which I published on Amazon on [My ASIN Date]. This infringement clearly shows unauthorized extraction.\n\nI appreciate your swift action to remove this unauthorized copy and handle the responsible seller accordingly.\n\nThanks for your prompt review.",
    
    "Dear Amazon Team,\nThis message is to inform you about a clear violation: my original design (ASIN: [MY ASIN]) published on [My ASIN Date] has been directly extracted from Amazon by another seller and re-uploaded without permission.\nI urge immediate removal of this infringement and appropriate action against the infringer.\nThank you for your prompt attention to this matter.",
    
    "Hello Amazon Support,\nThere's a severe infringement occurring with my original design (ASIN: [MY ASIN]), listed since [My ASIN Date]. Someone directly extracted my artwork from Amazon without permission and re-uploaded it as it is their own. The timestamps clearly verify this infringement.\nPlease urgently remove this listing and take suitable actions.\nI appreciate your urgent assistance.",
    
    "Hello Amazon Team,\nI'm disappointed to see someone directly extracted my original artwork (ASIN: [MY ASIN]) listed on Amazon since [My ASIN Date], and re-uploaded it as their own. This unauthorized extraction is clearly proven by timestamps.\nPlease act swiftly to remove this infringement and address this unauthorized seller firmly.\nThanks for your immediate attention.",
    
    "Dear Amazon Support,\nI've just discovered another seller directly downloaded my original artwork (ASIN: [MY ASIN]), initially listed on [My ASIN Date], from Amazon and re-uploaded it unauthorized. Checking the publication dates will easily confirm this.\nI kindly ask you to urgently remove this infringing listing and handle the responsible seller accordingly.\nThank you for your prompt action.",
    
    "Hello Amazon Support Team,\nI'm writing because another seller directly downloaded my original file (ASIN: [MY ASIN]) from Amazon, where it has been listed since [My ASIN Date], and re-uploaded it without permission. Timestamps clearly confirm unauthorized extraction.\nCould you please promptly remove this infringement and address the infringing seller?\nThanks in advance for your support.",
    
    "Dear Amazon Team,\nI urgently need your help: someone directly extracted my original design (ASIN: [MY ASIN]), initially published on Amazon on [My ASIN Date], and re-uploaded it unauthorized. The timestamps clearly prove my listing was the original.\nI ask for immediate removal of the infringing listing and action against the seller involved.\nThank you for handling this matter promptly."
  ],
  "Copied Design": [
    "Hello Amazon Team,\nI'm reaching out because I noticed another seller has directly copied my exact design layout (ASIN: [MY ASIN]) that I initially uploaded to Amazon on [My ASIN Date]. This isn't just similar, it's an exact duplicate of my original work.\n\nSince the timestamps clearly confirm that my design appeared first, please help me by removing the unauthorized listing promptly and addressing this seller's violation.\n\nThanks in advance for addressing this matter.",
    
    "Dear Amazon Team,\nI've recently discovered that my exact layout, originally published by me on Amazon on [My ASIN Date] (ASIN: [MY ASIN]), has been duplicated precisely by another seller. This unauthorized duplication is clearly evident when comparing the publishing dates.\n\nI kindly ask that you swiftly remove this infringing listing and take necessary action against the seller involved.\n\nThank you for your timely attention.",
    
    "Hello Amazon Support,\nI urgently need your help, someone has precisely duplicated my original design layout (ASIN: [MY ASIN]), first uploaded on [My ASIN Date]. It's clearly an exact copy.\n\nCan you please immediately remove this unauthorized listing and deal appropriately with this seller?\n\nI appreciate your swift assistance.",
    
    "Hello Amazon Team,\nI'm writing to report that my original design layout (ASIN: [MY ASIN]), published first on [My ASIN Date], has been exactly duplicated by another seller. The evidence clearly confirms my original listing predates this infringement.\n\nI would appreciate your immediate assistance in removing the unauthorized copy and taking suitable action against the responsible seller.\n\nThank you for your swift support.",
    
    "Dear Amazon Team,\nSomeone on Amazon has copied my exact design layout (ASIN: [MY ASIN]), originally listed by me on [My ASIN Date]. This is a precise duplication, not merely a similar design.\n\nConsidering the timestamps clearly show my original listing appeared first, please remove this infringing content immediately and handle the seller accordingly.\n\nThanks for your prompt response.",
    
    "Hello Amazon Support,\nI've just spotted that another seller exactly duplicated my original layout (ASIN: [MY ASIN]) published on [My ASIN Date]. This is clearly a direct, unauthorized copy.\n\nCould you please quickly remove this infringing listing and take action against the seller?\n\nI appreciate your immediate help.",
    
    "Hey Amazon Team,\nThere's a clear issue here, another seller exactly duplicated my design layout (ASIN: [MY ASIN]), first uploaded on [My ASIN Date]. This isn't just inspiration; it's an exact copy of my original work.\n\nPlease handle this promptly by removing the infringing listing and addressing the responsible seller.\n\nThank you for addressing this promptly.",
    
    "Dear Amazon Team,\nI'm contacting you regarding a clear case of duplication. My original layout (ASIN: [MY ASIN]), published on [My ASIN Date], has been exactly copied by another seller on Amazon. The timestamps confirm without doubt my original listing was uploaded first.\n\nI request immediate removal of this unauthorized copy and appropriate action against this infringing seller.\n\nThank you for swiftly resolving this issue.",
    
    "Hello Amazon Support Team,\nI recently discovered that another seller has uploaded an exact copy of my original design layout (ASIN: [MY ASIN]), initially listed by me on [My ASIN Date]. This duplication is exact and unauthorized.\n\nGiven that my original listing clearly predates theirs, please urgently remove this infringing content and address the seller responsible.\n\nI appreciate your urgent action.",
    
    "Dear Amazon Team,\nI am writing to formally report an infringement of my original design layout (ASIN: [MY ASIN]), first published on Amazon on [My ASIN Date]. Another seller has precisely duplicated my design (P4P) and re-uploaded it to Amazon.\n\nAs timestamps clearly demonstrate, my listing predates the infringing copy. Kindly act quickly to remove this unauthorized listing and address the responsible seller.\n\nThank you in advance for your cooperation.",
    
    "Hello Amazon Team,\nIt's concerning to see my exact design layout (ASIN: [MY ASIN]), originally uploaded on [My ASIN Date], duplicated exactly by another seller on Amazon. This isn't merely similar, it's clearly an identical copy.\n\nPlease immediately address this infringement by removing the unauthorized listing and taking appropriate action against the seller.\n\nThank you for handling this issue promptly.",
    
    "Hello Amazon,\nI discovered that another seller copied my original design layout exactly (ASIN: [MY ASIN]), which I initially listed on [My ASIN Date]. This is clearly unauthorized duplication.\n\nPlease remove this infringing listing right away and deal appropriately with this seller.\n\nThanks for your prompt response.",
    
    "Hello Amazon Team,\nI'm contacting you because my original design layout (ASIN: [MY ASIN]), published first on [My ASIN Date], has been exactly duplicated by someone else on Amazon. Timestamps clearly prove my original design predates the infringing copy.\n\nPlease take immediate action to remove this listing and hold the seller accountable.\n\nThank you for your assistance.",
    
    "Dear Amazon Support,\nI need your immediate assistance regarding my original layout (ASIN: [MY ASIN]), which has been exactly duplicated by another seller. The timestamps clearly indicate that my original listing appeared first on [My ASIN Date].\n\nPlease urgently remove this infringing listing and address the responsible seller accordingly.\n\nThanks in advance for your prompt assistance.",
    
    "Hello Amazon Team,\nAn exact duplication of my original design layout (ASIN: [MY ASIN]), published on [My ASIN Date], was uploaded by another seller. The timestamps clearly confirm my original listing predates this infringement.\n\nI kindly request immediate removal of this unauthorized listing and appropriate action against the infringing seller.\n\nThank you in advance for quickly addressing this matter."
  ],
  "Copied Listing": [
    "Hello Amazon Team,\nI just noticed another seller has copied my exact product details, originally published on Amazon on [My ASIN Date] (ASIN: [MY ASIN]). It's clearly a direct duplication of all the specific details of my listing.\n\nCan you please assist by promptly removing this copied listing and handling this seller appropriately?\n\nThank you for addressing this quickly.",
    
    "Dear Amazon Team,\nI'm contacting you regarding an issue with my original product details (ASIN: [MY ASIN]), listed since [My ASIN Date]. Another seller has duplicated these exact details in their listing, which clearly violates my original content.\n\nPlease take immediate action to remove the infringing listing and address the responsible seller.\n\nI appreciate your swift attention.",
    
    "Hello Amazon Support,\nAnother seller has duplicated my exact product details (ASIN: [MY ASIN]) first published on [My ASIN Date]. This isn't a similarity, it's a precise copy of all my original listing content.\n\nI appreciate your quick help in removing this unauthorized listing and dealing with the seller involved.\n\nThank you!",
    
    "Hello Amazon Team,\nI recently realized someone duplicated my exact product listing details (ASIN: [MY ASIN]) that I originally posted on [My ASIN Date]. This duplication isn't accidental, it's clearly intentional and exact.\n\nCould you urgently remove this listing and take necessary actions against this seller?\n\nThanks for helping sort this out quickly!",
    
    "Dear Amazon Support Team,\nIt has come to my attention that my exact product details, originally listed by me on [My ASIN Date] (ASIN: [MY ASIN]), have been precisely duplicated by another seller on Amazon.\n\nI kindly request immediate removal of the infringing content and appropriate action against this unauthorized duplication.\n\nThank you for promptly addressing this matter.",
    
    "Hello Amazon Team,\nI'm writing to urgently report another seller who has duplicated my exact product details (ASIN: [MY ASIN]), initially published on Amazon on [My ASIN Date]. It's clearly identical content, not just similar.\n\nPlease act swiftly to remove the copied listing and take action against the responsible seller.\n\nThank you for your timely cooperation.",
    
    "Hello Amazon Support,\nI discovered that another seller has exactly duplicated my original product details (ASIN: [MY ASIN]), published first by me on [My ASIN Date].\n\nCould you please quickly address this by removing their unauthorized copy and dealing with the seller?\n\nI appreciate your prompt support.",
    
    "Hello Amazon Team,\nI'm contacting you because my exact product details (ASIN: [MY ASIN]), published originally on [My ASIN Date], have been precisely duplicated by another seller on Amazon. The duplication includes all of my product description and information.\n\nPlease immediately remove this unauthorized listing and address the seller involved.\n\nThanks for handling this promptly.",
    
    "Hello Amazon Team,\nI've noticed someone copied the exact details from my original Amazon product listing (ASIN: [MY ASIN]), first published on [My ASIN Date]. It's a clear copy of all my specific listing content.\n\nI kindly ask you to quickly remove this copied listing and take suitable action against this unauthorized seller.\n\nThank you for your assistance.",
    
    "Hello Amazon Team,\nI recently became aware that another seller duplicated my exact product details, originally published on Amazon (ASIN: [MY ASIN]) on [My ASIN Date]. It's clearly intentional and exactly identical.\n\nI'd appreciate your urgent assistance in removing this infringing content and addressing the responsible seller.\n\nThanks in advance for your urgent attention.",
    
    "Dear Amazon Team,\nMy exact product listing details (ASIN: [MY ASIN]), originally published on [My ASIN Date], have been duplicated precisely by another seller. The duplication covers my full description and detailed information.\n\nPlease urgently remove this unauthorized copy and take appropriate action against the seller responsible.\n\nThank you for your quick attention.",
    
    "Hello Amazon Support,\nSomeone duplicated my exact Amazon product details (ASIN: [MY ASIN]) originally published by me on [My ASIN Date]. The details are identical to mine, no differences at all.\n\nPlease handle this swiftly by removing the copied listing and addressing the responsible seller.\nThank you for addressing this matter quickly.",
    
    "Hello Amazon Team,\nAnother seller has exactly duplicated my original product listing details (ASIN: [MY ASIN]) first published on [My ASIN Date]. This isn't just close, it's word-for-word identical.\n\nI need your immediate help to remove this unauthorized listing and to take appropriate action against the seller involved.\n\nThank you for your prompt action.",
    
    "Dear Amazon Team,\nSomeone has copied my exact product listing details (ASIN: [MY ASIN]), originally published on Amazon on [My ASIN Date]. This duplication covers every single detail.\n\nPlease immediately remove this copied listing and deal appropriately with this seller.\n\nThanks for addressing this quickly.",
    
    "Hello Amazon Team,\nI'm disappointed to discover someone has duplicated my exact product details, originally listed by me on Amazon (ASIN: [MY ASIN]) on [My ASIN Date]. This is clearly unauthorized and an exact match to my original content.\n\nPlease promptly remove this infringing listing and take necessary actions against the responsible seller.\nThank you for your quick action on this issue."
  ]
};

function getRandomDescription(issueType) {
  console.log();
  const descriptions = reportIssueDescriptions[issueType];
  if (!descriptions || !Array.isArray(descriptions)) {
    console.error();
    switch (issueType) {
      case 'Copied Design':
        console.log();
        return reportIssueDescriptions["Copied Design"][0];
      case 'Copied Listing':
        console.log();
        return reportIssueDescriptions["Copied Listing"][0];
      default:
        console.log();
        return reportIssueDescriptions["Stolen Artwork"][0];
    }
  }
  const randomIndex = Math.floor(Math.random() * descriptions.length);
  console.log();
  return descriptions[randomIndex];
}

async function handleReport(formData) {
  try {
    await cleanupAutomationSession();
    
    console.log();
    console.log();
    console.log();
    
    const domain = window.location.hostname;
    let marketplace = 'US';
    
    if (domain.includes('amazon.co.uk')) marketplace = 'UK';
    else if (domain.includes('amazon.de')) marketplace = 'DE';
    else if (domain.includes('amazon.fr')) marketplace = 'FR';
    else if (domain.includes('amazon.it')) marketplace = 'IT';
    else if (domain.includes('amazon.es')) marketplace = 'ES';
    else if (domain.includes('amazon.co.jp')) marketplace = 'JP';
    
    await chrome.storage.local.set({ 
      reportAutomationActive: true,
      reportAutomationMarketplace: marketplace
    });
    
    const sessionTimestamp = Date.now();
    await chrome.storage.local.set({ reportSessionTimestamp: sessionTimestamp });
    
    await chrome.storage.local.set({ [`reportFormData_${marketplace}`]: formData });
    
    if (window.location.href.includes('/report/infringement')) {
      console.log();
      setTimeout(runReportAutomation, 1000);
      return;
    }
    
    const marketplaceDomains = {
      'US': 'amazon.com',
      'UK': 'amazon.co.uk',
      'DE': 'amazon.de',
      'FR': 'amazon.fr',
      'IT': 'amazon.it',
      'ES': 'amazon.es',
      'JP': 'amazon.co.jp'
    };
    
    const urlOpened = sessionStorage.getItem('reportUrlOpened');
    if (urlOpened) {
      console.log();
      return;
    }
    
    sessionStorage.setItem('reportUrlOpened', 'true');
    
    setTimeout(() => {
      sessionStorage.removeItem('reportUrlOpened');
    }, 2000);
    
    const reportUrl = `https://www.${marketplaceDomains[marketplace]}/report/infringement?Snap&marketplace=${marketplace}`;
    console.log();
    window.open(reportUrl, '_blank');
  } catch (error) {
    console.error();
    await chrome.storage.local.remove(['reportAutomationActive', 'reportAutomationMarketplace']);
    throw error;
  }
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function waitForElement(selector, maxWaitTime = 2000, checkInterval = 50) {
  console.log();
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxWaitTime) {
    const element = document.querySelector(selector);
    if (element) {
      console.log();
      return element;
    }
    await delay(checkInterval);
  }
  
  console.log();
  return null;
}

async function clickElement(selector, delayAfter = 75, maxAttempts = 3) {
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    console.log();
    
    const element = await waitForElement(selector);
    if (element) {
      try {
        console.log();
        element.click();
        await delay(delayAfter);
        return true;
      } catch (error) {
        console.error();
        if (attempt === maxAttempts) {
          console.log();
          break;
        }
        await delay(500);
      }
    } else if (attempt === maxAttempts) {
      console.log();
      break;
    }
  }
  
  console.log();
  return false;
}

async function setInputValue(selector, value, delayAfter = 75, maxAttempts = 3) {
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    console.log();
    
    const input = await waitForElement(selector);
    if (input) {
      try {
        console.log();
        input.value = value;
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        await delay(delayAfter);
        return true;
      } catch (error) {
        console.error();
        if (attempt === maxAttempts) {
          console.log();
          break;
        }
        await delay(500);
      }
    } else if (attempt === maxAttempts) {
      console.log();
      break;
    }
  }
  
  console.log();
  return false;
}

async function runReportAutomation() {
  try {
    const data = await chrome.storage.local.get([
      'reportAutomationActive', 
      'reportSessionTimestamp', 
      'reportAutomationMarketplace',
      'originalAsinData'
    ]);
    
    if (!data.reportAutomationActive) {
      console.log();
      return;
    }

    let marketplace = data.reportAutomationMarketplace || 'US';
    const urlParams = new URLSearchParams(window.location.search);
    const urlMarketplace = urlParams.get('marketplace');
    
    if (urlMarketplace) {
      marketplace = urlMarketplace;
      console.log();
    } else {
      console.log();
    }

    let originalAsinData = null;
    try {
      const { [`originalAsinData_${marketplace}`]: marketplaceData } = 
        await chrome.storage.local.get(`originalAsinData_${marketplace}`);
      
      if (marketplaceData) {
        originalAsinData = JSON.parse(marketplaceData);
        console.log();
      } 
      else if (data.originalAsinData) {
        originalAsinData = JSON.parse(data.originalAsinData);
        
        if (originalAsinData.marketplace && originalAsinData.marketplace !== marketplace) {
          console.log();
          return;
        }
      }
    } catch (e) {
      console.error();
    }
    
    const currentTime = Date.now();
    if (data.reportSessionTimestamp && (currentTime - data.reportSessionTimestamp > 300000)) {
      console.log();
      await cleanupAutomationSession(marketplace);
      return;
    }

    const { [`reportFormData_${marketplace}`]: reportFormData } = 
      await chrome.storage.local.get(`reportFormData_${marketplace}`);
    
    if (!reportFormData || !originalAsinData) {
      console.log();
      return;
    }

    console.log();
    console.log();

    await delay(750);

    console.log();
    let startBtnClicked = await clickElement('kat-button#pnf-website-start-report-button', 125, 3);
    
    if (!startBtnClicked) {
      startBtnClicked = await clickElement('button.button[type="button"]', 125, 3);
    }
    
    await delay(125);
    
    console.log();
    const formattedAsins = reportFormData.ReportedASINs.split(',').map(asin => asin.trim()).join(', ');
    await setInputValue('textarea#text-search-input', formattedAsins, 75, 3);
    
    await delay(75);
    
    console.log();
    const searchClicked = await clickElement('button#text-search-button.btn.btn-primary', 75, 3);
    
    console.log();
    await delay(750);
    
    console.log();
    
    let checkboxClicked = await clickElement('input#selectAllAsinsCheckbox.custom-control-input', 75, 2);
    
    if (!checkboxClicked) {
      checkboxClicked = await clickElement('label.custom-control-label[for="selectAllAsinsCheckbox"]', 75, 2);
    }
    
    if (!checkboxClicked) {
      checkboxClicked = await clickElement('div.custom-control.custom-checkbox.flo-checkbox.pt-2.pb-3.ml-3', 75, 2);
    }
    
    if (!checkboxClicked) {
      console.log();
      try {
        const checkbox = await waitForElement('input#selectAllAsinsCheckbox');
        if (checkbox) {
          console.log();
          const script = document.createElement('script');
          script.textContent = `
            (function() {
              const checkbox = document.querySelector('input#selectAllAsinsCheckbox');
              if (checkbox) {
                checkbox.checked = true;
                checkbox.dispatchEvent(new Event('change', {bubbles: true}));
                return true;
              }
              return false;
            })();
          `;
          document.head.appendChild(script);
          await delay(75);
        }
      } catch (error) {
        console.error('Error during direct JavaScript click:', error);
      }
    }
    
    await delay(125);
    
    console.log();
    await clickElement('button.btn.dropdown-toggle[data-toggle="dropdown"]', 75, 3);
    
    await delay(75);
    
    console.log();
    await clickElement('button#COPYRIGHT.dropdown-item', 75, 3);
    
    await delay(125);
    
    console.log();
    console.log();
    console.log();
    
    const radioContainers = document.querySelectorAll('.custom-control.custom-radio.col');
    console.log();
    
    radioContainers.forEach((container, index) => {
      console.log();
    });

    if (reportFormData.IssueSelected === 'Stolen Artwork' || reportFormData.IssueSelected === 'Copied Design') {
      console.log();
      try {
        let targetRadio = null;
        radioContainers.forEach((container, index) => {
          const radio = container.querySelector('#infringementTypeCOPYRIGHT_PRODUCT_PACKAGING');
          if (radio) {
            console.log();
            targetRadio = radio;
          }
        });
        
        if (targetRadio) {
          console.log('', {
            checked: targetRadio.checked,
            disabled: targetRadio.disabled,
            visible: targetRadio.offsetParent !== null
          });
          targetRadio.checked = true;
          targetRadio.dispatchEvent(new MouseEvent('click', { bubbles: true }));
          targetRadio.dispatchEvent(new Event('change', { bubbles: true }));
          console.log();
        } else {
          console.log();
          const allRadios = document.querySelectorAll('input[type="radio"]');
          console.log('', Array.from(allRadios).map(r => ({
            id: r.id,
            name: r.name,
            value: r.value
          })));
        }
      } catch (error) {
        console.log();
      }
    } else if (reportFormData.IssueSelected === 'Copied Listing') {
      console.log();
      try {
        let targetRadio = null;
        radioContainers.forEach((container, index) => {
          const radio = container.querySelector('#infringementTypeCOPYRIGHT_TEXT');
          if (radio) {
            console.log();
            targetRadio = radio;
          }
        });
        
        if (targetRadio) {
          console.log('', {
            checked: targetRadio.checked,
            disabled: targetRadio.disabled,
            visible: targetRadio.offsetParent !== null
          });
          targetRadio.checked = true;
          targetRadio.dispatchEvent(new MouseEvent('click', { bubbles: true }));
          targetRadio.dispatchEvent(new Event('change', { bubbles: true }));
          console.log();
        } else {
          console.log();
          const allRadios = document.querySelectorAll('input[type="radio"]');
          console.log('', Array.from(allRadios).map(r => ({
            id: r.id,
            name: r.name,
            value: r.value
          })));
        }
      } catch (error) {
        console.log();
      }
    } else {
      console.log();
    }
    
    await delay(250);
    
    console.log();
    await setInputValue('input#selectedBrand', reportFormData.BrandName, 75, 3);
    
    console.log();
    await delay(75);
    
    console.log();
    try {
      const radio = document.querySelector('input[id="copyrightTypeCOPYRIGHT_LINK"][value="COPYRIGHT_LINK"]');
      if (radio) {
        console.log();
        radio.checked = true;
        
        radio.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
        radio.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
        radio.dispatchEvent(new MouseEvent('click', { bubbles: true }));
        radio.dispatchEvent(new Event('change', { bubbles: true }));
        radio.dispatchEvent(new Event('input', { bubbles: true }));
        
        const label = document.querySelector('label[for="copyrightTypeCOPYRIGHT_LINK"]');
        if (label) {
          label.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
          label.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
          label.dispatchEvent(new MouseEvent('click', { bubbles: true }));
        }
        
        console.log();
        
        await delay(250);
      } else {
        console.error();
      }
    } catch (error) {
      console.error();
    }
    
    await delay(75);
    
    console.log();
    const marketplaceDomains = {
      'US': 'amazon.com',
      'UK': 'amazon.co.uk',
      'DE': 'amazon.de',
      'FR': 'amazon.fr',
      'IT': 'amazon.it',
      'ES': 'amazon.es',
      'JP': 'amazon.co.jp'
    };
    
    const asinUrl = `https://www.${marketplaceDomains[marketplace]}/dp/${originalAsinData.asin}`;
    await setInputValue('input#copyrightLink', asinUrl, 75, 3);
    
    console.log();
    await delay(75);
    
    console.log();
    let dateText = originalAsinData.date;
    if (dateText === 'N/A' && originalAsinData.date_raw) {
      dateText = originalAsinData.date_raw;
    }
    
    console.log();

    const description = getRandomDescription(reportFormData.IssueSelected)
      .replace('[MY ASIN]', originalAsinData.asin)
      .replace('[My ASIN Date]', dateText);
    
    await setInputValue('textarea#rav-form-text-area', description, 75, 3);
    
    console.log();
    await delay(125);
    
    console.log();
    const {firstName, lastName, company, addressLine1, addressLine2, city, state, zipCode, country, phoneNumber} = reportFormData.PrimaryInfo;
    
    console.log();
    await setInputValue('input#firstName.agreement-input.form-control', firstName, 75, 3);
    await delay(50);
    
    await setInputValue('input#lastName.agreement-input.form-control', lastName, 75, 3);
    await delay(50);
    
    await setInputValue('input#company.agreement-input.form-control', company, 75, 3);
    await delay(50);
    
    await setInputValue('input#addressLine1.agreement-input.form-control', addressLine1, 75, 3);
    await delay(50);
    
    await setInputValue('input#addressLine2.agreement-input.form-control', addressLine2 || '', 75, 3);
    await delay(50);
    
    await setInputValue('input#city.agreement-input.form-control', city, 75, 3);
    await delay(50);
    
    await setInputValue('input#stateOrRegion.agreement-input.form-control', state, 75, 3);
    await delay(50);
    
    await setInputValue('input#postalCode.agreement-input.form-control', zipCode, 75, 3);
    await delay(75);
    
    console.log();
    const countryInput = await waitForElement('input.rbt-input-main.form-control');
    if (countryInput) {
      try {
        console.log();
        
        countryInput.focus();
        countryInput.value = '';
        countryInput.dispatchEvent(new Event('input', { bubbles: true }));
        await delay(75);
        
        countryInput.value = country;
        countryInput.dispatchEvent(new Event('input', { bubbles: true }));
        countryInput.dispatchEvent(new Event('change', { bubbles: true }));
        
        await delay(250);
        
        let optionFound = false;
        for (let attempt = 1; attempt <= 3; attempt++) {
          console.log();
          
          const dropdownItems = document.querySelectorAll('.dropdown-item:not(.rbt-menu-pagination-option)');
          console.log();
          
          for (const item of dropdownItems) {
            const itemText = item.textContent.trim();
            console.log();
            
            if (itemText === country) {
              console.log();
              item.click();
              optionFound = true;
              break;
            }
          }
          
          if (optionFound) {
            console.log();
            break;
          }
          
          await delay(500);
        }
        
        if (!optionFound) {
          console.error();
        }
      } catch (error) {
        console.error();
      }
      
      await delay(125);
    }
    
    await delay(125);

    try {
      const phoneInput = document.querySelector('input#phoneNumber.agreement-input.form-control');
      if (phoneInput) {
        phoneInput.focus();
        phoneInput.click();
        console.log();
        await delay(50);
      } else {
        console.warn();
      }
    } catch (e) {
      console.error();
    }

    console.log();
    await setInputValue('input#phoneNumber.agreement-input.form-control', phoneNumber, 75, 3);
    
    console.log();
    await delay(75);
    
    console.log();
    await clickElement('label.custom-control-label[for="raOrRoRIGHTS_OWNER"]', 75, 3);
    
    console.log();
    await delay(75);
    
    console.log();
    await clickElement('label.custom-control-label[for="sellerNO"]', 75, 3);
    
    console.log();
    await delay(75);
    
    console.log();
    const { contactName, contactEmail } = reportFormData.SecInfo;
    
    await setInputValue('input#contactName.agreement-input.form-control', contactName, 75, 3);
    await delay(75);
    
    await setInputValue('input#contactEmailAddress.agreement-input.form-control', contactEmail, 75, 3);
      
    console.log();
    await delay(125);
    
    try {
      document.body.click();
      console.log();
    } catch (error) {
      console.error();
    }

    console.log();
    
    await cleanupAutomationSession(marketplace);
  } catch (error) {
    console.error();
    try {
      console.log();
    } catch (e) {
      console.log();
    }
    
    try {
      const urlSearchParams = new URLSearchParams(window.location.search);
      const errorMarketplace = urlSearchParams.get('marketplace');
      
      if (errorMarketplace) {
        await cleanupAutomationSession(errorMarketplace);
      } else {
        const marketplaceData = await chrome.storage.local.get('reportAutomationMarketplace');
        const fallbackMarketplace = marketplaceData.reportAutomationMarketplace || 'US';
        await cleanupAutomationSession(fallbackMarketplace);
      }
    } catch (e) {
      console.error();
      await cleanupAutomationSession();
    }
  }
}

async function cleanupAutomationSession(marketplace) {
  try {
    if (!marketplace) {
      const data = await chrome.storage.local.get('reportAutomationMarketplace');
      marketplace = data.reportAutomationMarketplace || 'US';
    }
    
    console.log();
    await chrome.storage.local.remove([
      `reportFormData_${marketplace}`,
      `originalAsinData_${marketplace}`,
      'reportAutomationActive',
      'reportSessionTimestamp',
      'reportAutomationMarketplace'
    ]);
    console.log();
  } catch (error) {
    console.error();
  }
}

window.clearAutomationState = async function(specificMarketplace) {
  if (specificMarketplace) {
    await cleanupAutomationSession(specificMarketplace);
  } else {
    try {
      const data = await chrome.storage.local.get('reportAutomationMarketplace');
      if (data.reportAutomationMarketplace) {
        await cleanupAutomationSession(data.reportAutomationMarketplace);
      } else {
        console.log();
        const marketplaces = ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP'];
        for (const mkt of marketplaces) {
          await chrome.storage.local.remove(`reportFormData_${mkt}`);
          await chrome.storage.local.remove(`originalAsinData_${mkt}`);
        }
        await chrome.storage.local.remove([
          'reportAutomationActive', 
          'reportSessionTimestamp', 
          'reportAutomationMarketplace',
          'originalAsinData'
        ]);
      }
    } catch (error) {
      console.error();
      await cleanupAutomationSession();
    }
  }
};

if (window.location.href.includes('/report/infringement')) {
  console.log();
  
  const hasSnapParam = window.location.href.includes('?Snap');
  if (!hasSnapParam) {
    console.log();
  } else {
    document.addEventListener('visibilitychange', async () => {
      if (document.visibilityState === 'hidden') {
        console.log();
        await cleanupAutomationSession();
      }
    });

    window.addEventListener('beforeunload', async () => {
      console.log();
      await cleanupAutomationSession();
    });

    setTimeout(async () => {
      const isActive = await chrome.storage.local.get('reportAutomationActive');
      
      let marketplace = 'US';
      const urlParams = new URLSearchParams(window.location.search);
      const urlMarketplace = urlParams.get('marketplace');
      
      if (urlMarketplace) {
        marketplace = urlMarketplace;
        console.log();
      } else {
        const { reportAutomationMarketplace } = await chrome.storage.local.get('reportAutomationMarketplace');
        if (reportAutomationMarketplace) {
          marketplace = reportAutomationMarketplace;
          console.log();
        } else {
          console.log();
        }
      }
      
      const { [`reportFormData_${marketplace}`]: reportFormData } = 
        await chrome.storage.local.get(`reportFormData_${marketplace}`);
      
      console.log();
      
      if (isActive.reportAutomationActive && hasSnapParam && reportFormData) {
        console.log();
        runReportAutomation();
      } else {
        console.log();
        if (isActive.reportAutomationActive || reportFormData) {
          await cleanupAutomationSession(marketplace);
        }
      }
    }, 1000);
  }
}

window.handleReport = handleReport;
window.runReportAutomation = runReportAutomation;
window.clearAutomationState = window.clearAutomationState;
