import{j as e,M as ne,J as H,k as R,K as Y,F as G,C as v,T as I,bL as re,bM as oe,o as M,e as o,am as le,bN as ie,a_ as Z,W as E,bO as ce,bP as de,ae as me,bQ as K,O as B,Q as U,bR as ue,_ as he,y as xe}from"./chunk-d1fd3088.js";import{r as S,R as ee}from"./chunk-58ddc683.js";import{c3 as se,v as te,cH as A,aj as fe,ak as $,bT as pe,S as z,cI as q,cJ as W,cK as J,cL as ge,cM as je,ah as ae,bS as be,T as we,c5 as X}from"./chunk-4fe4df4f.js";import{M as ye}from"./chunk-28eac69c.js";(function(){const f=document.createElement("link").relList;if(f&&f.supports&&f.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))g(n);new MutationObserver(n=>{for(const d of n)if(d.type==="childList")for(const m of d.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&g(m)}).observe(document,{childList:!0,subtree:!0});function t(n){const d={};return n.integrity&&(d.integrity=n.integrity),n.referrerPolicy&&(d.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?d.credentials="include":n.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function g(n){if(n.ep)return;n.ep=!0;const d=t(n);fetch(n.href,d)}})();const Ne=({open:f,handleClose:t})=>e.jsx(ne,{open:f,handleClose:t,uniqueId:"update-guidance",modalTitle:"Update Required",extraClass:"border-1 border-white !flex",bodySection:e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("p",{className:"text-gray-300 text-[15px]",children:"To continue using this extension, please update it to the latest version. Follow the steps below:"}),e.jsx("div",{className:"flex flex-col gap-2 p-3 bg-white bg-opacity-20 rounded",children:e.jsxs("ol",{className:"list-decimal list-inside text-gray-200 mb-0",children:[e.jsxs("li",{className:"mb-1",children:["Open Google Chrome and go to"," ",e.jsx("span",{className:"font-bold text-white",children:"chrome://extensions"}),"."]}),e.jsxs("li",{className:"mb-1",children:["Enable"," ",e.jsx("span",{className:"font-bold text-white",children:"Developer mode"})," ","using the toggle in the top-right corner of the page."]}),e.jsxs("li",{className:"mb-1",children:["Click the ",e.jsx("span",{className:"font-bold text-white",children:"Update"})," ","button to update your extensions."]})]})}),e.jsx("p",{className:"text-gray-300 text-[14px] mt-2",children:"This process will update the extension, and a notification will appear at the bottom of the page once it is complete."})]})}),ve={email:"",password:""},Se=H.object({email:H.string().email(),password:H.string()}),Ce=({setScreenType:f})=>{const[t,g]=S.useState(!1),[n,d]=S.useState({processing:!1,error:"",success:""}),[m,h]=ee.useState(!1),b=R({defaultValues:{...ve},mode:"onChange",resolver:Y(Se)}),{control:j,handleSubmit:L,formState:{errors:c}}=b,C=async w=>{d(a=>({...a,processing:!0,error:"",success:""}));const x=await o.login(w);if(x!=null&&x.is_version_outdated){d(a=>({...a,processing:!1})),h(!0);return}if(!(x!=null&&x.token)){d(a=>({...a,processing:!1,error:(x==null?void 0:x.message)||"Something went wrong!"}));return}await o.setStoreValue({token:x.token}),f(2),d(a=>({...a,processing:!1,success:"otp sent on your email"}))},T=()=>{h(!1)};return e.jsx(G,{...b,children:e.jsx("form",{onSubmit:L(C),className:"w-full auth-form text-sm p-3 px-6 ",children:e.jsxs("div",{className:"flex flex-col gap-y-3",children:[e.jsx("h3",{className:"text-lg",style:{color:"var(--color-gray-200)"},children:"Welcome to Merch Dominator!"}),e.jsx("p",{style:{color:"var(--color-gray-400)"},className:"mb-1",children:"Please sign in to your account and start the adventure!"}),n.error&&e.jsx("p",{className:"text-center text-red-500",children:n.error}),n.success&&e.jsx("p",{className:"text-center text-sm  text-green-500",children:n.success}),m&&e.jsx(Ne,{open:m,handleClose:T}),e.jsx(v,{name:"email",control:j,render:({field:{onChange:w,value:x}})=>{var a;return e.jsx(I,{label:"Email",onChange:w,value:x,uniqueId:"login-email",placeHolder:"Enter Email",inputClassName:"merch-input",helperText:(a=c==null?void 0:c.email)==null?void 0:a.message,autoComplete:"on",labelClass:"flex !font-medium mb-1"})}}),e.jsx(v,{name:"password",control:j,render:({field:{onChange:w,value:x}})=>{var a;return e.jsx(I,{label:"Password",type:t?"text":"password",onChange:w,value:x,uniqueId:"login-password",placeHolder:"Enter Password",inputClassName:"merch-input !flex h-[38px] flex-1",helperText:(a=c==null?void 0:c.password)==null?void 0:a.message,labelClass:"flex !font-medium mb-1",addonAfter:e.jsx("button",{type:"button",className:"flex bg-auth justify-center items-center h-full px-[12.3px] primary-text",onClick:()=>g(!t),children:t?e.jsx(re,{}):e.jsx(oe,{})})})}}),e.jsx("div",{children:e.jsx(M,{buttonContent:"Sign in",isLoading:n.processing,isDisabled:n.processing||!!(c!=null&&c.email),onclickHandler:()=>{},extraClass:"bg-merch-dominator color-white gap-x-1 !border-none !flex items-center w-full mt-1 !text-sm",buttonProps:{type:"submit"}})}),e.jsx("span",{className:"mt-1",children:"Don't have an account yet?"}),e.jsx(M,{buttonContent:"Sign up",isDisabled:!1,onclickHandler:()=>{se("pricing")},extraClass:"bg-auth color-white gap-x-1 gradient-border !flex items-center w-full !text-sm"})]})})})},Pe=H.object({two_factor_code:H.string().min(6,{message:"Otp should have at least 6 characters"}).max(6,{message:"Otp should have at most 6 characters"})}),Ve={two_factor_code:""},Ee=({setScreenType:f,setDetails:t,setDisplayConfig:g,setTheme:n})=>{const d=R({defaultValues:{...Ve},mode:"onChange",resolver:Y(Pe)}),[m,h]=S.useState({processing:!1,error:"",success:""}),[b,j]=S.useState(!1),{control:L,handleSubmit:c,formState:{errors:C}}=d,T=async a=>{if(m.processing)return;h(r=>({...r,processing:!0,error:"",success:""}));const i=await o.verifyOtp(a);if(i!=null&&i.success){h(p=>({...p,processing:!1,success:"Otp verified successfully"}));const r=(i==null?void 0:i.user_info)??{};await new Promise(p=>setTimeout(p,300));const O=Z(r==null?void 0:r.user_type,(r==null?void 0:r.stripe_status)||""),_=r==null?void 0:r.plan_name,k=te(r==null?void 0:r.subscription_modules);if(t(p=>({...p,isAuthenticated:O,userInfo:{...r,plan_name:_},moduleAccessConfig:k,error:!O})),O){const[p,y]=await Promise.all([o.fetchDisplayConfigs(),o.getStoreValue(["merchDominator.theme","merchDominator.hasListViewedBefore","merchDominator.isManualDefaultListView"])]);if(n((y==null?void 0:y["merchDominator.theme"])||"light"),k.listView){let P=!!(y!=null&&y["merchDominator.isManualDefaultListView"]);p.shouldListDefaultView=P;const V={shouldListDefaultView:P},s={"merchDominator.hasListViewedBefore":!0};y!=null&&y["merchDominator.hasListViewedBefore"]||(p.shouldListDefaultView=!0,p.analyzeListView=!0,p.analyzeGridView=!1,V.analyzeListView=!0,V.analyzeGridView=!1,V.shouldListDefaultView=!0,s["merchDominator.isManualDefaultListView"]=!0);const l=A(V,z);await Promise.all([o.setStoreValue(l),o.setStoreValue(s)])}else{p.analyzeGridView=!0,p.shouldListDefaultView=!1;const P=A({analyzeGridView:!0,shouldListDefaultView:!1},z);await Promise.all([o.setStoreValue(P),o.setStoreValue({"merchDominator.hasListViewedBefore":!1})]),await o.setStoreValue(P)}g(()=>p),f(3),await o.sendSuccessLoginEvent(),o.fetchConfigs(fe,$.MERCH_AMAZON),o.fetchConfigs(pe,$.AMAZON)}return}h(r=>({...r,processing:!1,error:(i==null?void 0:i.message)||"Something went wrong!"}))},w=async a=>{if(a.preventDefault(),m.processing||b)return;h(r=>({...r,error:"",success:""})),j(!0);const i=await o.resendOtp();if(i!=null&&i.success){h(r=>({...r,error:"",success:(i==null?void 0:i.message)||"Otp sent successfully"})),j(!1);return}h(r=>({...r,error:(i==null?void 0:i.message)||"Something went wrong!"})),j(!1)},x=a=>{a.preventDefault(),!m.processing&&f(1)};return e.jsx(G,{...d,children:e.jsx("form",{className:"w-full flex items-center text-sm min-h-[380px] p-3 px-6 bg-[#1d1d37]",onSubmit:c(T),children:e.jsxs("main",{className:"flex flex-col w-full h-full gap-y-3",children:[e.jsx("p",{className:"m-auto",children:"Enter the code sent to your email here to login"}),m.error&&e.jsx(le,{message:m.error,extraClass:"text-red-500"}),m.success&&e.jsx("p",{className:"text-center text-green-500",children:m.success}),e.jsxs("div",{className:"flex flex-col gap-y-3",children:[e.jsx(v,{name:"two_factor_code",control:L,render:({field:{onChange:a,value:i}})=>{var r;return e.jsx(I,{uniqueId:"two_factor_code",type:"text",placeholder:"123456",value:i,onChange:a,extraClass:"w-full",helperText:(r=C==null?void 0:C.two_factor_code)==null?void 0:r.message,autoFocus:!0})}}),e.jsx(M,{buttonContent:"Verify",isLoading:m.processing,isDisabled:m.processing,onclickHandler:()=>{},extraClass:"bg-merch-dominator color-white gap-2 !border-none !flex items-center !text-sm",buttonProps:{type:"submit"}})]}),e.jsxs("div",{className:"flex justify-center",children:["If you haven't received the code,",e.jsxs("div",{className:`text-purple-500 flex items-center  hover:underline ${b?"opacity-80 cursor-not-allowed":"cursor-pointer"}`,onClick:w,children:[b?e.jsx(ie,{className:"fa-spin"}):null," Click Here "]}),"to resend it"]}),e.jsx("button",{className:"text-purple-500 flex m-auto mt-1 border-2 py-2 px-4 border-purple-800 rounded-full",onClick:x,children:"Back to login"})]})})})},Le=({tabs:f,activeTab:t,setActiveTab:g,buttonExtraClass:n="",extraClass:d=""})=>{const m=h=>{g(h)};return e.jsx("div",{className:`tab-group w-full ${d}`,children:f.map(h=>{const b=h.value===t;return e.jsx("button",{onClick:j=>{j.stopPropagation(),j.preventDefault(),m(h.value)},className:`tab p-2 px-8${n?` ${n}`:""}`,style:{border:"none",borderBottom:`2px solid ${b?"var(--text-main-color)":"transparent"}`,background:"none",fontWeight:b?"bold":"normal"},children:h.label},h.value)})})},Q=11,D=Object.keys(je),Te=D.length,Oe=({displayConfigs:f,userDetails:t,moduleAccessConfig:g,theme:n,toggleTheme:d,isLoggingOut:m,loggedOut:h})=>{var P,V;const b=R({defaultValues:{...f},mode:"onChange"}),[j,L]=ee.useState(q[0].value),{control:c,handleSubmit:C,watch:T,getValues:w}=b,{listView:x,productSearch:a}=g,i=T("analyzeResearchEnabled"),r=T("shouldListDefaultView");S.useEffect(()=>(document.addEventListener("visibilitychange",()=>{const s=w(),l=A(s,z);o.setStoreValue(l)},!1),()=>{document.removeEventListener("visibilitychange",()=>{})}),[]);const O=(((P=t==null?void 0:t.first_name)==null?void 0:P.slice(0,1))||"")+(((V=t==null?void 0:t.last_name)==null?void 0:V.slice(0,1))||"")||"PV",_=()=>{se("profile")},k=async()=>{const s=w(),l=s.analyzeResearchEnabled,u=A(s,z);await o.setStoreValue(u),await o.sendEventToAmazonPages(ae,!l,null,"www.amazon")},p=async()=>{const s=w(),l=s.analyzeCreatePageEnabled,u=A(s,z);await o.setStoreValue(u),await o.sendEventToAmazonPages(be,!l,null,"merch.amazon")},y=async s=>{const l=w(),u=A(l,z);await o.setStoreValue(u),await o.sendEventToAmazonPages(we,!0,s)};return e.jsx(G,{...b,children:e.jsx("form",{onSubmit:C(s=>{}),className:"w-full visibility-settings-form text-primary mb-auto min-h-[380px]",children:e.jsxs("main",{className:"flex flex-col w-full",children:[e.jsxs("header",{className:"flex pt-2 justify-between items-center border-b border-[var(--divider-color)]",children:[e.jsx(Le,{tabs:q,activeTab:j,setActiveTab:L,extraClass:"text-base"}),e.jsx(E,{name:"theme-switcher",checked:n==="light",onChangeHandler:d,extraClass:"theme-switcher mr-4",children:e.jsxs(e.Fragment,{children:[e.jsx(ce,{className:"sun-icon"}),e.jsx(de,{className:"moon-icon"})]})})]}),j==="home"?e.jsxs("div",{className:"flex flex-col gap-y-2 p-3 px-6",children:[e.jsx("div",{className:"text-sm mb-1 montserrat-semibold",children:"Account Settings"}),e.jsxs("div",{className:"w-full flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center gap-x-2",children:[e.jsx("div",{className:"w-10 h-10 flex justify-center items-center text-base rounded-full overflow-hidden bg-merch-dominator color-white",children:t!=null&&t.photo?e.jsx("img",{src:t.photo,alt:"avatar",className:"object-cover w-full h-full"}):O}),(t==null?void 0:t.first_name)+" "+(t==null?void 0:t.last_name)]}),e.jsxs("div",{className:"auth-actions flex gap-x-4",children:[e.jsx(M,{isLoading:!1,isDisabled:!1,buttonIcon:e.jsx(me,{}),onclickHandler:_,extraClass:"bg-merch-dominator color-white gap-2 !border-none !flex items-center !text-sm !p-2"}),e.jsx(M,{isLoading:m,isDisabled:m,buttonIcon:e.jsx(K,{}),onclickHandler:h,extraClass:"bg-merch-dominator color-white gap-2 !border-none !flex items-center !text-sm !p-2"})]})]}),e.jsxs("div",{className:"flex flex-col gap-y-2",children:[e.jsxs("span",{className:"",children:["Email ID:",e.jsxs("span",{className:"montserrat-semibold",children:[" ",t==null?void 0:t.email]})]}),e.jsxs("span",{className:"",children:["Current Subscription:",e.jsxs("span",{className:"montserrat-semibold",children:[" ",t==null?void 0:t.plan_name]})]})]}),e.jsx("div",{className:"divider"}),e.jsxs("div",{className:"flex items-center mb-1 text-sm montserrat-semibold",children:["Extension Settings",e.jsx(B,{tooltipTitle:"Please refresh the page for the changes to take effect. Some options might be visible but unavailable if you don't have access to the related features.",extraClass:"!p-0 d-flex-important ml-1 !text-[var(--text-primary-color)] align-items-center",isButtonType:!1,children:e.jsx(U,{size:14}),tooltipId:"extension-settings-tooltip"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("div",{className:"flex flex-col gap-y-[0.20rem]",children:[e.jsx("span",{className:"text-sm",children:"Researching Amazon Product Pages"}),e.jsx("span",{className:"text-[11px] opacity-75",children:"(Turn extension On/Off)"})]}),e.jsx(v,{name:"analyzeResearchEnabled",control:c,render:({field:{onChange:s,value:l}})=>e.jsx(E,{name:"analyzeResearchEnabled",checked:l,onChangeHandler:u=>{s(u.target.checked),k()}})})]}),i?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex gap-2 justify-between",children:[e.jsxs("span",{className:"text-sm flex h-fit gap-1",children:["Turn Grid View"," ",e.jsx("span",{className:"text-[11px] opacity-75",children:"(On/Off)"})]}),e.jsx(v,{name:"analyzeGridView",control:c,render:({field:{onChange:s,value:l}})=>e.jsx(E,{name:"analyzeGridView",checked:l,onChangeHandler:s})})]}),e.jsxs("div",{className:"flex gap-2 justify-between",children:[e.jsxs("span",{className:"text-sm flex h-fit gap-1",children:["Turn List View"," ",e.jsx("span",{className:"text-[11px] opacity-75",children:"(On/Off)"})]}),e.jsx(v,{name:"analyzeListView",control:c,render:({field:{onChange:s,value:l}})=>e.jsx(E,{name:"analyzeListView",checked:l,onChangeHandler:s})})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm",children:r?"List View Default":"Grid View Default"}),e.jsx(v,{name:"shouldListDefaultView",control:c,render:({field:{onChange:s,value:l}})=>{const u=N=>{s(N.target.checked),o.setStoreValue({"merchDominator.isManualDefaultListView":N.target.checked})};return e.jsx(E,{name:"shouldListDefaultView",checked:l,onChangeHandler:u})}})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm",children:"Show Sponsored Products (For List View)"}),e.jsx(v,{name:"analyzeSponsoredProducts",control:c,render:({field:{onChange:s,value:l}})=>e.jsx(E,{name:"analyzeSponsoredProducts",checked:l,onChangeHandler:u=>{s(u.target.checked)},hasPermission:x})})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("span",{className:"text-sm",children:["Product Search Popup Action",e.jsx(B,{tooltipTitle:"The product search popup will close automatically after clicking on search when toggled on. The product search popup will remain open after clicking on search when toggled off",extraClass:"!p-0 d-flex-important ml-1 !text-[var(--text-primary-color)] align-items-center",isButtonType:!1,children:e.jsx(U,{size:14}),tooltipId:"productSearchPopupAction"})]}),e.jsx(v,{name:"shouldCloseProductSearchModal",control:c,render:({field:{onChange:s,value:l}})=>e.jsx(E,{name:"shouldCloseProductSearchModal",checked:l,onChangeHandler:u=>{const N=u.target.checked;s(N),y(N)},hasPermission:a})})]})]}):null,e.jsxs("div",{className:"flex gap-2 justify-between",children:[e.jsxs("span",{className:"text-sm flex h-fit gap-1",children:["Create Product Page",e.jsx("span",{className:"text-[11px] opacity-75",children:"(On/Off)"})]}),e.jsx(v,{name:"analyzeCreatePageEnabled",control:c,render:({field:{onChange:s,value:l}})=>e.jsx(E,{name:"analyzeCreatePageEnabled",checked:l,onChangeHandler:u=>{s(u.target.checked),p()}})})]}),e.jsx("div",{className:"divider my-1"}),i?e.jsxs("section",{className:"visibility-settings-container flex gap-4 max-h-[50px] overflow-y-auto scrollbar-style",children:[e.jsx("div",{className:"flex flex-col gap-y-2",children:D.slice(0,Q).map(s=>{const l=g[W[s]]??!0;return e.jsx(v,{name:s,control:c,render:({field:{onChange:u,value:N}})=>e.jsx(E,{name:s,checked:N,onChangeHandler:u,switchTitle:J[s],extraClass:"flex gap-x-2 items-center",hasPermission:l})},s)})}),e.jsx("div",{className:"flex flex-col gap-y-2",children:D.slice(Q,Te).map(s=>{const l=g[W[s]]??!0;return e.jsx(v,{name:s,control:c,render:({field:{onChange:u,value:N}})=>e.jsx(E,{name:s,checked:N,onChangeHandler:u,switchTitle:J[s],extraClass:"flex gap-x-2 items-center",hasPermission:l})},s)})})]}):null]}):e.jsxs("div",{className:"flex flex-col gap-y-2 p-3 px-6 text-sm min-h-[489px]",children:[e.jsx("div",{className:"montserrat-semibold",children:"Need help?"}),e.jsx("div",{className:"flex flex-col gap-y-2 ml-4",children:e.jsxs("a",{href:ge("support"),target:"_blank",rel:"noreferrer",className:"text-merch-dominator flex gap-x-1 items-center",children:["Contact Support ",e.jsx(ue,{})]})})]})]})})})},_e=Oe,ke=()=>{const[f,t]=S.useState(1),[g,n]=S.useState("dark"),[d,m]=S.useState({}),[h,b]=S.useState(!0),[j,L]=S.useState(!1),[c,C]=S.useState({isAuthenticated:!1,userInfo:{},moduleAccessConfig:{},error:!1}),T=async()=>{L(!0);const a=await o.logout();if(a!=null&&a.success){n("dark"),t(1),C(i=>({...i,isAuthenticated:!1,error:!1})),await Promise.all([o.sendEventToAmazonPages(ae,!0),o.removeStoreValue([X])]),L(!1);return}he.error("Something went wrong, Please try again!"),L(!1)},w=async()=>{const a=await o.checkValidToken(),{token_status:i,otp_status:r,stripe_status:O="",user_type:_=0,plan_name:k="",data:p={},subscription_modules:y={}}=(a==null?void 0:a.result)||{};if(i){const P=r?3:2;let V={};r&&(V={...p,plan_name:k,user_type:_});const s=Z(_,O),l=r&&!s;let u={};if(r&&s){u=te(y);const[N,F]=await Promise.all([o.fetchDisplayConfigs(),o.getStoreValue(["merchDominator.theme"])]);n((F==null?void 0:F["merchDominator.theme"])||"light"),m(()=>N)}else await o.removeStoreValue([X]);C(N=>({...N,isAuthenticated:!l,userInfo:V,moduleAccessConfig:u,error:l})),t(P),b(!1);return}b(!1)};S.useEffect(()=>{w()},[]);const x=async()=>{const a=g==="light"?"dark":"light";await o.setStoreValue({"merchDominator.theme":a}),await o.changeTheme(a),n(a)};return e.jsx("section",{className:"popup-container h-full merch-dominator-style-container montserrat-regular","data-theme":g,children:e.jsxs("div",{className:"flex flex-col h-full",children:[e.jsx("div",{className:"flex justify-center px-8 py-3 popup-header",children:e.jsx(ye,{extraClass:"!h-[36px]"})}),e.jsx("section",{className:"flex h-full justify-center items-center popup-body",style:{color:"var(--color-gray-200)"},children:h?e.jsx("div",{className:"flex flex-1 justify-center items-center min-h-[380px]",children:"Loading..."}):c.error?e.jsxs("div",{className:"flex flex-col justify-between items-center min-h-[380px]",children:[e.jsx(M,{isLoading:j,isDisabled:j,buttonIcon:e.jsx(K,{}),onclickHandler:T,extraClass:"bg-merch-dominator color-white gap-2 ml-auto mt-4 mr-4 !border-none !flex items-center !text-sm !p-2"}),e.jsxs("div",{className:"banner-section bg-merch-dominator color-white p-2 text-base text-center",children:["To access extension features, Premium Plan is Required Please Upgrade your plan by",e.jsx("a",{href:"https://merchdominator.com/pricing",target:"_blank",referrerPolicy:"no-referrer",className:"hover:underline",children:" Clicking here."})]}),e.jsx("span",{})]}):e.jsxs(e.Fragment,{children:[c.isAuthenticated&&f===3&&e.jsx(_e,{displayConfigs:d,userDetails:c.userInfo,moduleAccessConfig:c.moduleAccessConfig,theme:g,toggleTheme:x,loggedOut:T,isLoggingOut:j}),f===1&&e.jsx(Ce,{setScreenType:t}),f===2&&e.jsx(Ee,{setScreenType:t,setDetails:C,setDisplayConfig:m,setTheme:n})]})})]})})},Ae=ke,ze=async()=>{xe(document.getElementById("app")).render(e.jsx(Ae,{}))};ze();
