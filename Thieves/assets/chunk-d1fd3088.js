var Zo=Object.defineProperty;var Go=(t,e,a)=>e in t?Zo(t,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[e]=a;var Je=(t,e,a)=>(Go(t,typeof e!="symbol"?e+"":e,a),a),Fs=(t,e,a)=>{if(!e.has(t))throw TypeError("Cannot "+a)};var ge=(t,e,a)=>(Fs(t,e,"read from private field"),a?a.call(t):e.get(t)),Se=(t,e,a)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,a)},pa=(t,e,a,n)=>(Fs(t,e,"write to private field"),n?n.call(t,a):e.set(t,a),a);import{r as b,a as Yo,R as W,j as et,b as Jo,i as dt,_ as Oi,l as ns,E as Qo,F as Ri,G as Xo}from"./chunk-58ddc683.js";import{bo as el,F as wa,f as Ya,cM as tl,cN as al,u as ne,cG as nl,cO as Li,x as rl,c5 as rs,cE as sl,aH as ze,K as ss,cP as Ps,cc as il,c8 as Ni,cb as ol,cd as ll,cw as cl,ct as ul,cs as dl,cr as ml,cq as hl,cp as fl,co as pl,cn as gl,cm as vl,ci as yl,ch as kl,cg as bl,cj as wl,cl as xl,ck as _l,cz as jl,cy as Sl,cv as zl,cu as El,cD as Cl,cC as Tl,bm as is,cB as Al,cA as Ol,ca as Rl,c9 as Ll,S as Nl,cx as Ml,cf as Il,ce as Fl,cF as Pl,U as $l,cQ as qr,s as Ka,cR as Va,cS as An,b3 as Vl,cT as Dl,cU as ql,cV as Bl,cW as Ul,aD as Hl,aF as On,M as Wl,N as Kl,cX as Zl,E as Mi,y as Br,cY as $s,cZ as Un,c_ as Gl,C as Er,bv as Hn,B as Dt,c$ as Yl,d0 as Jl,az as Ql,c3 as Xl,q as ec,by as tc,bz as ac,d1 as nc,ae as rc,d2 as sc,am as ic,an as oc,ao as lc,d3 as cc,d4 as Ur,a5 as Hr,d5 as Da,L as ys,d6 as Rn,d7 as Wr}from"./chunk-4fe4df4f.js";function uc(t,e){for(var a=0;a<e.length;a++){const n=e[a];if(typeof n!="string"&&!Array.isArray(n)){for(const r in n)if(r!=="default"&&!(r in t)){const s=Object.getOwnPropertyDescriptor(n,r);s&&Object.defineProperty(t,r,s.get?s:{enumerable:!0,get:()=>n[r]})}}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}var Ii={exports:{}},Cr={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dc=b,mc=Symbol.for("react.element"),hc=Symbol.for("react.fragment"),fc=Object.prototype.hasOwnProperty,pc=dc.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,gc={key:!0,ref:!0,__self:!0,__source:!0};function Fi(t,e,a){var n,r={},s=null,i=null;a!==void 0&&(s=""+a),e.key!==void 0&&(s=""+e.key),e.ref!==void 0&&(i=e.ref);for(n in e)fc.call(e,n)&&!gc.hasOwnProperty(n)&&(r[n]=e[n]);if(t&&t.defaultProps)for(n in e=t.defaultProps,e)r[n]===void 0&&(r[n]=e[n]);return{$$typeof:mc,type:t,key:s,ref:i,props:r,_owner:pc.current}}Cr.Fragment=hc;Cr.jsx=Fi;Cr.jsxs=Fi;Ii.exports=Cr;var h=Ii.exports,Ja,Vs=Yo;Ja=Vs.createRoot,Vs.hydrateRoot;var Pi={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},Ds=W.createContext&&W.createContext(Pi),vc=["attr","size","title"];function yc(t,e){if(t==null)return{};var a=kc(t,e),n,r;if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);for(r=0;r<s.length;r++)n=s[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(a[n]=t[n])}return a}function kc(t,e){if(t==null)return{};var a={},n=Object.keys(t),r,s;for(s=0;s<n.length;s++)r=n[s],!(e.indexOf(r)>=0)&&(a[r]=t[r]);return a}function Wn(){return Wn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(t[n]=a[n])}return t},Wn.apply(this,arguments)}function qs(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),a.push.apply(a,n)}return a}function Kn(t){for(var e=1;e<arguments.length;e++){var a=arguments[e]!=null?arguments[e]:{};e%2?qs(Object(a),!0).forEach(function(n){bc(t,n,a[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):qs(Object(a)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(a,n))})}return t}function bc(t,e,a){return e=wc(e),e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function wc(t){var e=xc(t,"string");return typeof e=="symbol"?e:String(e)}function xc(t,e){if(typeof t!="object"||t===null)return t;var a=t[Symbol.toPrimitive];if(a!==void 0){var n=a.call(t,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function $i(t){return t&&t.map((e,a)=>W.createElement(e.tag,Kn({key:a},e.attr),$i(e.child)))}function q(t){return e=>W.createElement(_c,Wn({attr:Kn({},t.attr)},e),$i(t.child))}function _c(t){var e=a=>{var{attr:n,size:r,title:s}=t,i=yc(t,vc),o=r||a.size||"1em",l;return a.className&&(l=a.className),t.className&&(l=(l?l+" ":"")+t.className),W.createElement("svg",Wn({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},a.attr,n,i,{className:l,style:Kn(Kn({color:t.color||a.color},a.style),t.style),height:o,width:o,xmlns:"http://www.w3.org/2000/svg"}),s&&W.createElement("title",null,s),t.children)};return Ds!==void 0?W.createElement(Ds.Consumer,null,a=>e(a)):e(Pi)}function ng(t){return q({tag:"svg",attr:{fill:"currentColor",viewBox:"0 0 16 16"},child:[{tag:"path",attr:{d:"M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"},child:[]},{tag:"path",attr:{d:"M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"},child:[]}]})(t)}function rg(t){return q({tag:"svg",attr:{fill:"currentColor",viewBox:"0 0 16 16"},child:[{tag:"path",attr:{d:"M13.359 11.238C15.06 9.72 16 8 16 8s-3-5.5-8-5.5a7.028 7.028 0 0 0-2.79.588l.77.771A5.944 5.944 0 0 1 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.134 13.134 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755-.165.165-.337.328-.517.486l.708.709z"},child:[]},{tag:"path",attr:{d:"M11.297 9.176a3.5 3.5 0 0 0-4.474-4.474l.823.823a2.5 2.5 0 0 1 2.829 2.829l.822.822zm-2.943 1.299.822.822a3.5 3.5 0 0 1-4.474-4.474l.823.823a2.5 2.5 0 0 0 2.829 2.829z"},child:[]},{tag:"path",attr:{d:"M3.35 5.47c-.18.16-.353.322-.518.487A13.134 13.134 0 0 0 1.172 8l.195.288c.335.48.83 1.12 1.465 1.755C4.121 11.332 5.881 12.5 8 12.5c.716 0 1.39-.133 2.02-.36l.77.772A7.029 7.029 0 0 1 8 13.5C3 13.5 0 8 0 8s.939-1.721 2.641-3.238l.708.709zm10.296 8.884-12-12 .708-.708 12 12-.708.708z"},child:[]}]})(t)}function sg(t){return q({tag:"svg",attr:{fill:"currentColor",viewBox:"0 0 16 16"},child:[{tag:"path",attr:{d:"M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.133 13.133 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.133 13.133 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5c-2.12 0-3.879-1.168-5.168-2.457A13.134 13.134 0 0 1 1.172 8z"},child:[]},{tag:"path",attr:{d:"M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z"},child:[]}]})(t)}function jc(t){return q({tag:"svg",attr:{fill:"currentColor",viewBox:"0 0 16 16"},child:[{tag:"path",attr:{d:"m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"},child:[]}]})(t)}let Sc={data:""},zc=t=>typeof window=="object"?((t?t.querySelector("#_goober"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:t||Sc,Ec=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,Cc=/\/\*[^]*?\*\/|  +/g,Bs=/\n+/g,qt=(t,e)=>{let a="",n="",r="";for(let s in t){let i=t[s];s[0]=="@"?s[1]=="i"?a=s+" "+i+";":n+=s[1]=="f"?qt(i,s):s+"{"+qt(i,s[1]=="k"?"":e)+"}":typeof i=="object"?n+=qt(i,e?e.replace(/([^,])+/g,o=>s.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,l=>/&/.test(l)?l.replace(/&/g,o):o?o+" "+l:l)):s):i!=null&&(s=/^--/.test(s)?s:s.replace(/[A-Z]/g,"-$&").toLowerCase(),r+=qt.p?qt.p(s,i):s+":"+i+";")}return a+(e&&r?e+"{"+r+"}":r)+n},Ot={},Vi=t=>{if(typeof t=="object"){let e="";for(let a in t)e+=a+Vi(t[a]);return e}return t},Tc=(t,e,a,n,r)=>{let s=Vi(t),i=Ot[s]||(Ot[s]=(l=>{let c=0,d=11;for(;c<l.length;)d=101*d+l.charCodeAt(c++)>>>0;return"go"+d})(s));if(!Ot[i]){let l=s!==t?t:(c=>{let d,m,u=[{}];for(;d=Ec.exec(c.replace(Cc,""));)d[4]?u.shift():d[3]?(m=d[3].replace(Bs," ").trim(),u.unshift(u[0][m]=u[0][m]||{})):u[0][d[1]]=d[2].replace(Bs," ").trim();return u[0]})(t);Ot[i]=qt(r?{["@keyframes "+i]:l}:l,a?"":"."+i)}let o=a&&Ot.g?Ot.g:null;return a&&(Ot.g=Ot[i]),((l,c,d,m)=>{m?c.data=c.data.replace(m,l):c.data.indexOf(l)===-1&&(c.data=d?l+c.data:c.data+l)})(Ot[i],e,n,o),i},Ac=(t,e,a)=>t.reduce((n,r,s)=>{let i=e[s];if(i&&i.call){let o=i(a),l=o&&o.props&&o.props.className||/^go/.test(o)&&o;i=l?"."+l:o&&typeof o=="object"?o.props?"":qt(o,""):o===!1?"":o}return n+r+(i??"")},"");function Tr(t){let e=this||{},a=t.call?t(e.p):t;return Tc(a.unshift?a.raw?Ac(a,[].slice.call(arguments,1),e.p):a.reduce((n,r)=>Object.assign(n,r&&r.call?r(e.p):r),{}):a,zc(e.target),e.g,e.o,e.k)}let Di,os,ls;Tr.bind({g:1});let It=Tr.bind({k:1});function Oc(t,e,a,n){qt.p=e,Di=t,os=a,ls=n}function Qt(t,e){let a=this||{};return function(){let n=arguments;function r(s,i){let o=Object.assign({},s),l=o.className||r.className;a.p=Object.assign({theme:os&&os()},o),a.o=/ *go\d+/.test(l),o.className=Tr.apply(a,n)+(l?" "+l:""),e&&(o.ref=i);let c=t;return t[0]&&(c=o.as||t,delete o.as),ls&&c[0]&&ls(o),Di(c,o)}return e?e(r):r}}var Rc=t=>typeof t=="function",Zn=(t,e)=>Rc(t)?t(e):t,Lc=(()=>{let t=0;return()=>(++t).toString()})(),qi=(()=>{let t;return()=>{if(t===void 0&&typeof window<"u"){let e=matchMedia("(prefers-reduced-motion: reduce)");t=!e||e.matches}return t}})(),Nc=20,$n=new Map,Mc=1e3,Us=t=>{if($n.has(t))return;let e=setTimeout(()=>{$n.delete(t),ca({type:4,toastId:t})},Mc);$n.set(t,e)},Ic=t=>{let e=$n.get(t);e&&clearTimeout(e)},cs=(t,e)=>{switch(e.type){case 0:return{...t,toasts:[e.toast,...t.toasts].slice(0,Nc)};case 1:return e.toast.id&&Ic(e.toast.id),{...t,toasts:t.toasts.map(s=>s.id===e.toast.id?{...s,...e.toast}:s)};case 2:let{toast:a}=e;return t.toasts.find(s=>s.id===a.id)?cs(t,{type:1,toast:a}):cs(t,{type:0,toast:a});case 3:let{toastId:n}=e;return n?Us(n):t.toasts.forEach(s=>{Us(s.id)}),{...t,toasts:t.toasts.map(s=>s.id===n||n===void 0?{...s,visible:!1}:s)};case 4:return e.toastId===void 0?{...t,toasts:[]}:{...t,toasts:t.toasts.filter(s=>s.id!==e.toastId)};case 5:return{...t,pausedAt:e.time};case 6:let r=e.time-(t.pausedAt||0);return{...t,pausedAt:void 0,toasts:t.toasts.map(s=>({...s,pauseDuration:s.pauseDuration+r}))}}},Vn=[],Dn={toasts:[],pausedAt:void 0},ca=t=>{Dn=cs(Dn,t),Vn.forEach(e=>{e(Dn)})},Fc={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},Pc=(t={})=>{let[e,a]=b.useState(Dn);b.useEffect(()=>(Vn.push(a),()=>{let r=Vn.indexOf(a);r>-1&&Vn.splice(r,1)}),[e]);let n=e.toasts.map(r=>{var s,i;return{...t,...t[r.type],...r,duration:r.duration||((s=t[r.type])==null?void 0:s.duration)||(t==null?void 0:t.duration)||Fc[r.type],style:{...t.style,...(i=t[r.type])==null?void 0:i.style,...r.style}}});return{...e,toasts:n}},$c=(t,e="blank",a)=>({createdAt:Date.now(),visible:!0,type:e,ariaProps:{role:"status","aria-live":"polite"},message:t,pauseDuration:0,...a,id:(a==null?void 0:a.id)||Lc()}),xn=t=>(e,a)=>{let n=$c(e,t,a);return ca({type:2,toast:n}),n.id},nt=(t,e)=>xn("blank")(t,e);nt.error=xn("error");nt.success=xn("success");nt.loading=xn("loading");nt.custom=xn("custom");nt.dismiss=t=>{ca({type:3,toastId:t})};nt.remove=t=>ca({type:4,toastId:t});nt.promise=(t,e,a)=>{let n=nt.loading(e.loading,{...a,...a==null?void 0:a.loading});return t.then(r=>(nt.success(Zn(e.success,r),{id:n,...a,...a==null?void 0:a.success}),r)).catch(r=>{nt.error(Zn(e.error,r),{id:n,...a,...a==null?void 0:a.error})}),t};var Vc=(t,e)=>{ca({type:1,toast:{id:t,height:e}})},Dc=()=>{ca({type:5,time:Date.now()})},qc=t=>{let{toasts:e,pausedAt:a}=Pc(t);b.useEffect(()=>{if(a)return;let s=Date.now(),i=e.map(o=>{if(o.duration===1/0)return;let l=(o.duration||0)+o.pauseDuration-(s-o.createdAt);if(l<0){o.visible&&nt.dismiss(o.id);return}return setTimeout(()=>nt.dismiss(o.id),l)});return()=>{i.forEach(o=>o&&clearTimeout(o))}},[e,a]);let n=b.useCallback(()=>{a&&ca({type:6,time:Date.now()})},[a]),r=b.useCallback((s,i)=>{let{reverseOrder:o=!1,gutter:l=8,defaultPosition:c}=i||{},d=e.filter(p=>(p.position||c)===(s.position||c)&&p.height),m=d.findIndex(p=>p.id===s.id),u=d.filter((p,g)=>g<m&&p.visible).length;return d.filter(p=>p.visible).slice(...o?[u+1]:[0,u]).reduce((p,g)=>p+(g.height||0)+l,0)},[e]);return{toasts:e,handlers:{updateHeight:Vc,startPause:Dc,endPause:n,calculateOffset:r}}},Bc=It`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Uc=It`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Hc=It`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Wc=Qt("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Bc} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Uc} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${t=>t.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Hc} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Kc=It`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Zc=Qt("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${t=>t.secondary||"#e0e0e0"};
  border-right-color: ${t=>t.primary||"#616161"};
  animation: ${Kc} 1s linear infinite;
`,Gc=It`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Yc=It`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Jc=Qt("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Gc} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Yc} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${t=>t.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Qc=Qt("div")`
  position: absolute;
`,Xc=Qt("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,eu=It`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,tu=Qt("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${eu} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,au=({toast:t})=>{let{icon:e,type:a,iconTheme:n}=t;return e!==void 0?typeof e=="string"?b.createElement(tu,null,e):e:a==="blank"?null:b.createElement(Xc,null,b.createElement(Zc,{...n}),a!=="loading"&&b.createElement(Qc,null,a==="error"?b.createElement(Wc,{...n}):b.createElement(Jc,{...n})))},nu=t=>`
0% {transform: translate3d(0,${t*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,ru=t=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${t*-150}%,-1px) scale(.6); opacity:0;}
`,su="0%{opacity:0;} 100%{opacity:1;}",iu="0%{opacity:1;} 100%{opacity:0;}",ou=Qt("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,lu=Qt("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,cu=(t,e)=>{let a=t.includes("top")?1:-1,[n,r]=qi()?[su,iu]:[nu(a),ru(a)];return{animation:e?`${It(n)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${It(r)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},uu=b.memo(({toast:t,position:e,style:a,children:n})=>{let r=t.height?cu(t.position||e||"top-center",t.visible):{opacity:0},s=b.createElement(au,{toast:t}),i=b.createElement(lu,{...t.ariaProps},Zn(t.message,t));return b.createElement(ou,{className:t.className,style:{...r,...a,...t.style}},typeof n=="function"?n({icon:s,message:i}):b.createElement(b.Fragment,null,s,i))});Oc(b.createElement);var du=({id:t,className:e,style:a,onHeightUpdate:n,children:r})=>{let s=b.useCallback(i=>{if(i){let o=()=>{let l=i.getBoundingClientRect().height;n(t,l)};o(),new MutationObserver(o).observe(i,{subtree:!0,childList:!0,characterData:!0})}},[t,n]);return b.createElement("div",{ref:s,className:e,style:a},r)},mu=(t,e)=>{let a=t.includes("top"),n=a?{top:0}:{bottom:0},r=t.includes("center")?{justifyContent:"center"}:t.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:qi()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${e*(a?1:-1)}px)`,...n,...r}},hu=Tr`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,Ln=16,ig=({reverseOrder:t,position:e="top-center",toastOptions:a,gutter:n,children:r,containerStyle:s,containerClassName:i})=>{let{toasts:o,handlers:l}=qc(a);return b.createElement("div",{style:{position:"fixed",zIndex:9999,top:Ln,left:Ln,right:Ln,bottom:Ln,pointerEvents:"none",...s},className:i,onMouseEnter:l.startPause,onMouseLeave:l.endPause},o.map(c=>{let d=c.position||e,m=l.calculateOffset(c,{reverseOrder:t,gutter:n,defaultPosition:e}),u=mu(d,m);return b.createElement(du,{id:c.id,key:c.id,onHeightUpdate:l.updateHeight,className:c.visible?hu:"",style:u},c.type==="custom"?Zn(c.message,c):r?r(c):b.createElement(uu,{toast:c,position:d}))}))},Ze=nt;function og(t){return q({tag:"svg",attr:{viewBox:"0 0 320 512"},child:[{tag:"path",attr:{d:"M143 352.3L7 216.3c-9.4-9.4-9.4-24.6 0-33.9l22.6-22.6c9.4-9.4 24.6-9.4 33.9 0l96.4 96.4 96.4-96.4c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9l-136 136c-9.2 9.4-24.4 9.4-33.8 0z"},child:[]}]})(t)}function fu(t){return q({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M240.971 130.524l194.343 194.343c9.373 9.373 9.373 24.569 0 33.941l-22.667 22.667c-9.357 9.357-24.522 9.375-33.901.04L224 227.495 69.255 381.516c-9.379 9.335-24.544 9.317-33.901-.04l-22.667-22.667c-9.373-9.373-9.373-24.569 0-33.941L207.03 130.525c9.372-9.373 24.568-9.373 33.941-.001z"},child:[]}]})(t)}function Hs(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 8C119.033 8 8 119.033 8 256s111.033 248 248 248 248-111.033 248-248S392.967 8 256 8zm117.134 346.753c-1.592 1.867-39.776 45.731-109.851 45.731-84.692 0-144.484-63.26-144.484-145.567 0-81.303 62.004-143.401 143.762-143.401 66.957 0 101.965 37.315 103.422 38.904a12 12 0 0 1 1.238 14.623l-22.38 34.655c-4.049 6.267-12.774 7.351-18.234 2.295-.233-.214-26.529-23.88-61.88-23.88-46.116 0-73.916 33.575-73.916 76.082 0 39.602 25.514 79.692 74.277 79.692 38.697 0 65.28-28.338 65.544-28.625 5.132-5.565 14.059-5.033 18.508 1.053l24.547 33.572a12.001 12.001 0 0 1-.553 14.866z"},child:[]}]})(t)}function pu(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256.12 245.96c-13.25 0-24 10.74-24 24 1.14 72.25-8.14 141.9-27.7 211.55-2.73 9.72 2.15 30.49 23.12 30.49 10.48 0 20.11-6.92 23.09-17.52 13.53-47.91 31.04-125.41 29.48-224.52.01-13.25-10.73-24-23.99-24zm-.86-81.73C194 164.16 151.25 211.3 152.1 265.32c.75 47.94-3.75 95.91-13.37 142.55-2.69 12.98 5.67 25.69 18.64 28.36 13.05 2.67 25.67-5.66 28.36-18.64 10.34-50.09 15.17-101.58 14.37-153.02-.41-25.95 19.92-52.49 54.45-52.34 31.31.47 57.15 25.34 57.62 55.47.77 48.05-2.81 96.33-10.61 143.55-2.17 13.06 6.69 25.42 19.76 27.58 19.97 3.33 26.81-15.1 27.58-19.77 8.28-50.03 12.06-101.21 11.27-152.11-.88-55.8-47.94-101.88-104.91-102.72zm-110.69-19.78c-10.3-8.34-25.37-6.8-33.76 3.48-25.62 31.5-39.39 71.28-38.75 112 .59 37.58-2.47 75.27-9.11 112.05-2.34 13.05 6.31 25.53 19.36 27.89 20.11 3.5 27.07-14.81 27.89-19.36 7.19-39.84 10.5-80.66 9.86-121.33-.47-29.88 9.2-57.88 28-80.97 8.35-10.28 6.79-25.39-3.49-33.76zm109.47-62.33c-15.41-.41-30.87 1.44-45.78 4.97-12.89 3.06-20.87 15.98-17.83 28.89 3.06 12.89 16 20.83 28.89 17.83 11.05-2.61 22.47-3.77 34-3.69 75.43 1.13 137.73 61.5 138.88 134.58.59 37.88-1.28 76.11-5.58 113.63-1.5 13.17 7.95 25.08 21.11 26.58 16.72 1.95 25.51-11.88 26.58-21.11a929.06 929.06 0 0 0 5.89-119.85c-1.56-98.75-85.07-180.33-186.16-181.83zm252.07 121.45c-2.86-12.92-15.51-21.2-28.61-18.27-12.94 2.86-21.12 15.66-18.26 28.61 4.71 21.41 4.91 37.41 4.7 61.6-.11 13.27 10.55 24.09 23.8 24.2h.2c13.17 0 23.89-10.61 24-23.8.18-22.18.4-44.11-5.83-72.34zm-40.12-90.72C417.29 43.46 337.6 1.29 252.81.02 183.02-.82 118.47 24.91 70.46 72.94 24.09 119.37-.9 181.04.14 246.65l-.12 21.47c-.39 13.25 10.03 24.31 23.28 24.69.23.02.48.02.72.02 12.92 0 23.59-10.3 23.97-23.3l.16-23.64c-.83-52.5 19.16-101.86 56.28-139 38.76-38.8 91.34-59.67 147.68-58.86 69.45 1.03 134.73 35.56 174.62 92.39 7.61 10.86 22.56 13.45 33.42 5.86 10.84-7.62 13.46-22.59 5.84-33.43z"},child:[]}]})(t)}function lg(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M283.211 512c78.962 0 151.079-35.925 198.857-94.792 7.068-8.708-.639-21.43-11.562-19.35-124.203 23.654-238.262-71.576-238.262-196.954 0-72.222 38.662-138.635 101.498-174.394 9.686-5.512 7.25-20.197-3.756-22.23A258.156 258.156 0 0 0 283.211 0c-141.309 0-256 114.511-256 256 0 141.309 114.511 256 256 256z"},child:[]}]})(t)}function cg(t){return q({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M416 208H272V64c0-17.67-14.33-32-32-32h-32c-17.67 0-32 14.33-32 32v144H32c-17.67 0-32 14.33-32 32v32c0 17.67 14.33 32 32 32h144v144c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32V304h144c17.67 0 32-14.33 32-32v-32c0-17.67-14.33-32-32-32z"},child:[]}]})(t)}function ug(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M505.12019,19.09375c-1.18945-5.53125-6.65819-11-12.207-12.1875C460.716,0,435.507,0,410.40747,0,307.17523,0,245.26909,55.20312,199.05238,128H94.83772c-16.34763.01562-35.55658,11.875-42.88664,26.48438L2.51562,253.29688A28.4,28.4,0,0,0,0,264a24.00867,24.00867,0,0,0,24.00582,24H127.81618l-22.47457,22.46875c-11.36521,11.36133-12.99607,32.25781,0,45.25L156.24582,406.625c11.15623,11.1875,32.15619,13.15625,45.27726,0l22.47457-22.46875V488a24.00867,24.00867,0,0,0,24.00581,24,28.55934,28.55934,0,0,0,10.707-2.51562l98.72834-49.39063c14.62888-7.29687,26.50776-26.5,26.50776-42.85937V312.79688c72.59753-46.3125,128.03493-108.40626,128.03493-211.09376C512.07526,76.5,512.07526,51.29688,505.12019,19.09375ZM384.04033,168A40,40,0,1,1,424.05,128,40.02322,40.02322,0,0,1,384.04033,168Z"},child:[]}]})(t)}function dg(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z"},child:[]}]})(t)}function mg(t){return q({tag:"svg",attr:{viewBox:"0 0 320 512"},child:[{tag:"path",attr:{d:"M41 288h238c21.4 0 32.1 25.9 17 41L177 448c-9.4 9.4-24.6 9.4-33.9 0L24 329c-15.1-15.1-4.4-41 17-41z"},child:[]}]})(t)}function hg(t){return q({tag:"svg",attr:{viewBox:"0 0 320 512"},child:[{tag:"path",attr:{d:"M279 224H41c-21.4 0-32.1-25.9-17-41L143 64c9.4-9.4 24.6-9.4 33.9 0l119 119c15.2 15.1 4.5 41-16.9 41z"},child:[]}]})(t)}function fg(t){return q({tag:"svg",attr:{viewBox:"0 0 320 512"},child:[{tag:"path",attr:{d:"M41 288h238c21.4 0 32.1 25.9 17 41L177 448c-9.4 9.4-24.6 9.4-33.9 0L24 329c-15.1-15.1-4.4-41 17-41zm255-105L177 64c-9.4-9.4-24.6-9.4-33.9 0L24 183c-15.1 15.1-4.4 41 17 41h238c21.4 0 32.1-25.9 17-41z"},child:[]}]})(t)}function Qa(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M304 48c0 26.51-21.49 48-48 48s-48-21.49-48-48 21.49-48 48-48 48 21.49 48 48zm-48 368c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zm208-208c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zM96 256c0-26.51-21.49-48-48-48S0 229.49 0 256s21.49 48 48 48 48-21.49 48-48zm12.922 99.078c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48c0-26.509-21.491-48-48-48zm294.156 0c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48c0-26.509-21.49-48-48-48zM108.922 60.922c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.491-48-48-48z"},child:[]}]})(t)}function pg(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 160c-52.9 0-96 43.1-96 96s43.1 96 96 96 96-43.1 96-96-43.1-96-96-96zm246.4 80.5l-94.7-47.3 33.5-100.4c4.5-13.6-8.4-26.5-21.9-21.9l-100.4 33.5-47.4-94.8c-6.4-12.8-24.6-12.8-31 0l-47.3 94.7L92.7 70.8c-13.6-4.5-26.5 8.4-21.9 21.9l33.5 100.4-94.7 47.4c-12.8 6.4-12.8 24.6 0 31l94.7 47.3-33.5 100.5c-4.5 13.6 8.4 26.5 21.9 21.9l100.4-33.5 47.3 94.7c6.4 12.8 24.6 12.8 31 0l47.3-94.7 100.4 33.5c13.6 4.5 26.5-8.4 21.9-21.9l-33.5-100.4 94.7-47.3c13-6.5 13-24.7.2-31.1zm-155.9 106c-49.9 49.9-131.1 49.9-181 0-49.9-49.9-49.9-131.1 0-181 49.9-49.9 131.1-49.9 181 0 49.9 49.9 49.9 131.1 0 181z"},child:[]}]})(t)}function gg(t){return q({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M32 464a48 48 0 0 0 48 48h288a48 48 0 0 0 48-48V128H32zm272-256a16 16 0 0 1 32 0v224a16 16 0 0 1-32 0zm-96 0a16 16 0 0 1 32 0v224a16 16 0 0 1-32 0zm-96 0a16 16 0 0 1 32 0v224a16 16 0 0 1-32 0zM432 32H312l-9.4-18.7A24 24 0 0 0 281.1 0H166.8a23.72 23.72 0 0 0-21.4 13.3L136 32H16A16 16 0 0 0 0 48v32a16 16 0 0 0 16 16h416a16 16 0 0 0 16-16V48a16 16 0 0 0-16-16z"},child:[]}]})(t)}function vg(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M212.333 224.333H12c-6.627 0-12-5.373-12-12V12C0 5.373 5.373 0 12 0h48c6.627 0 12 5.373 12 12v78.112C117.773 39.279 184.26 7.47 258.175 8.007c136.906.994 246.448 111.623 246.157 248.532C504.041 393.258 393.12 504 256.333 504c-64.089 0-122.496-24.313-166.51-64.215-5.099-4.622-5.334-12.554-.467-17.42l33.967-33.967c4.474-4.474 11.662-4.717 16.401-.525C170.76 415.336 211.58 432 256.333 432c97.268 0 176-78.716 176-176 0-97.267-78.716-176-176-176-58.496 0-110.28 28.476-142.274 72.333h98.274c6.627 0 12 5.373 12 12v48c0 6.627-5.373 12-12 12z"},child:[]}]})(t)}function gu(t){return q({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M400 256H152V152.9c0-39.6 31.7-72.5 71.3-72.9 40-.4 72.7 32.1 72.7 72v16c0 13.3 10.7 24 24 24h32c13.3 0 24-10.7 24-24v-16C376 68 307.5-.3 223.5 0 139.5.3 72 69.5 72 153.5V256H48c-26.5 0-48 21.5-48 48v160c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V304c0-26.5-21.5-48-48-48zM264 408c0 22.1-17.9 40-40 40s-40-17.9-40-40v-48c0-22.1 17.9-40 40-40s40 17.9 40 40v48z"},child:[]}]})(t)}function yg(t){return q({tag:"svg",attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M528.1 171.5L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6zM388.6 312.3l23.7 138.4L288 385.4l-124.3 65.3 23.7-138.4-100.6-98 139-20.2 62.2-126 62.2 126 139 20.2-100.6 98z"},child:[]}]})(t)}let qa=null;function vu(){const[t,e]=b.useState(!1),a=()=>{window.scrollY>300?e(!0):e(!1)},n=()=>{qa||(a(),qa=setTimeout(()=>{qa=null},200))};b.useEffect(()=>(window.addEventListener("scroll",n),()=>{window.removeEventListener("scroll",n),qa&&clearTimeout(qa)}),[]);const r=()=>{window.scrollTo({top:0,behavior:"smooth"})};return h.jsx("button",{className:`btn d-flex-important i-p-0 align-items-center justify-center bg-merch-dominator back-to-top ${t?"show":"hide"}`,"data-title":"Back to Top",onClick:r,children:h.jsx(fu,{size:12})})}function yu(t){return q({tag:"svg",attr:{viewBox:"0 0 256 256",fill:"currentColor"},child:[{tag:"path",attr:{d:"M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm-24,88H96v40a8,8,0,0,1-16,0V112H72a8,8,0,0,1,0-16h32a8,8,0,0,1,0,16Zm88,40a8,8,0,0,1-16,0V125.29l-14,16a8,8,0,0,1-12,0l-14-16V152a8,8,0,0,1-16,0V104a8,8,0,0,1,14-5.27l22,25.12,22-25.12A8,8,0,0,1,192,104Z"},child:[]}]})(t)}function kg(t){return q({tag:"svg",attr:{viewBox:"0 0 256 256",fill:"currentColor"},child:[{tag:"path",attr:{d:"M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216ZM112,104a8,8,0,0,1-8,8H96v40a8,8,0,0,1-16,0V112H72a8,8,0,0,1,0-16h32A8,8,0,0,1,112,104Zm80,0v48a8,8,0,0,1-16,0V125.29l-14,16a8,8,0,0,1-12,0l-14-16V152a8,8,0,0,1-16,0V104a8,8,0,0,1,14-5.27l22,25.12,22-25.12A8,8,0,0,1,192,104Z"},child:[]}]})(t)}function bg(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeMiterlimit:"10",strokeWidth:"32",d:"M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192 192-86 192-192z"},child:[]},{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M256 176v160m80-80H176"},child:[]}]})(t)}function wg(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinejoin:"round",strokeWidth:"32",d:"M336 64h32a48 48 0 0 1 48 48v320a48 48 0 0 1-48 48H144a48 48 0 0 1-48-48V112a48 48 0 0 1 48-48h32"},child:[]},{tag:"rect",attr:{width:"160",height:"64",x:"176",y:"32",fill:"none",strokeLinejoin:"round",strokeWidth:"32",rx:"26.13",ry:"26.13"},child:[]}]})(t)}function xg(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeMiterlimit:"10",strokeWidth:"32",d:"M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192 192-86 192-192z"},child:[]},{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M320 320 192 192m0 128 128-128"},child:[]}]})(t)}function ku(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"m289.94 256 95-95A24 24 0 0 0 351 127l-95 95-95-95a24 24 0 0 0-34 34l95 95-95 95a24 24 0 1 0 34 34l95-95 95 95a24 24 0 0 0 34-34z"},child:[]}]})(t)}function _g(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeMiterlimit:"10",strokeWidth:"32",d:"M430.11 347.9c-6.6-6.1-16.3-7.6-24.6-9-11.5-1.9-15.9-4-22.6-10-14.3-12.7-14.3-31.1 0-43.8l30.3-26.9c46.4-41 46.4-108.2 0-149.2-34.2-30.1-80.1-45-127.8-45-55.7 0-113.9 20.3-158.8 60.1-83.5 73.8-83.5 194.7 0 268.5 41.5 36.7 97.5 55 152.9 55.4h1.7c55.4 0 110-17.9 148.8-52.4 14.4-12.7 11.99-36.6.1-47.7z"},child:[]},{tag:"circle",attr:{cx:"144",cy:"208",r:"32"},child:[]},{tag:"circle",attr:{cx:"152",cy:"311",r:"32"},child:[]},{tag:"circle",attr:{cx:"224",cy:"144",r:"32"},child:[]},{tag:"circle",attr:{cx:"256",cy:"367",r:"48"},child:[]},{tag:"circle",attr:{cx:"328",cy:"144",r:"32"},child:[]}]})(t)}function jg(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinejoin:"round",strokeWidth:"32",d:"M416 221.25V416a48 48 0 0 1-48 48H144a48 48 0 0 1-48-48V96a48 48 0 0 1 48-48h98.75a32 32 0 0 1 22.62 9.37l141.26 141.26a32 32 0 0 1 9.37 22.62z"},child:[]},{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M256 56v120a32 32 0 0 0 32 32h120m-232 80h160m-160 80h160"},child:[]}]})(t)}function Sg(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M255.66 112c-77.94 0-157.89 45.11-220.83 135.33a16 16 0 0 0-.27 17.77C82.92 340.8 161.8 400 255.66 400c92.84 0 173.34-59.38 221.79-135.25a16.14 16.14 0 0 0 0-17.47C428.89 172.28 347.8 112 255.66 112z"},child:[]},{tag:"circle",attr:{cx:"256",cy:"256",r:"80",fill:"none",strokeMiterlimit:"10",strokeWidth:"32"},child:[]}]})(t)}function Bi(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeMiterlimit:"10",strokeWidth:"32",d:"M248 64C146.39 64 64 146.39 64 248s82.39 184 184 184 184-82.39 184-184S349.61 64 248 64z"},child:[]},{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M220 220h32v116"},child:[]},{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeMiterlimit:"10",strokeWidth:"32",d:"M208 340h88"},child:[]},{tag:"path",attr:{d:"M248 130a26 26 0 1 0 26 26 26 26 0 0 0-26-26z"},child:[]}]})(t)}function zg(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M304 336v40a40 40 0 0 1-40 40H104a40 40 0 0 1-40-40V136a40 40 0 0 1 40-40h152c22.09 0 48 17.91 48 40v40m64 160 80-80-80-80m-192 80h256"},child:[]}]})(t)}function Eg(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeMiterlimit:"10",strokeWidth:"32",d:"M221.09 64a157.09 157.09 0 1 0 157.09 157.09A157.1 157.1 0 0 0 221.09 64z"},child:[]},{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeMiterlimit:"10",strokeWidth:"32",d:"M338.29 338.29 448 448"},child:[]}]})(t)}function Cg(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M208 512a24.84 24.84 0 0 1-23.34-16l-39.84-103.6a16.06 16.06 0 0 0-9.19-9.19L32 343.34a25 25 0 0 1 0-46.68l103.6-39.84a16.06 16.06 0 0 0 9.19-9.19L184.66 144a25 25 0 0 1 46.68 0l39.84 103.6a16.06 16.06 0 0 0 9.19 9.19l103 39.63a25.49 25.49 0 0 1 16.63 24.1 24.82 24.82 0 0 1-16 22.82l-103.6 39.84a16.06 16.06 0 0 0-9.19 9.19L231.34 496A24.84 24.84 0 0 1 208 512zm66.85-254.84zM88 176a14.67 14.67 0 0 1-13.69-9.4l-16.86-43.84a7.28 7.28 0 0 0-4.21-4.21L9.4 101.69a14.67 14.67 0 0 1 0-27.38l43.84-16.86a7.31 7.31 0 0 0 4.21-4.21L74.16 9.79A15 15 0 0 1 86.23.11a14.67 14.67 0 0 1 15.46 9.29l16.86 43.84a7.31 7.31 0 0 0 4.21 4.21l43.84 16.86a14.67 14.67 0 0 1 0 27.38l-43.84 16.86a7.28 7.28 0 0 0-4.21 4.21l-16.86 43.84A14.67 14.67 0 0 1 88 176zm312 80a16 16 0 0 1-14.93-10.26l-22.84-59.37a8 8 0 0 0-4.6-4.6l-59.37-22.84a16 16 0 0 1 0-29.86l59.37-22.84a8 8 0 0 0 4.6-4.6l22.67-58.95a16.45 16.45 0 0 1 13.17-10.57 16 16 0 0 1 16.86 10.15l22.84 59.37a8 8 0 0 0 4.6 4.6l59.37 22.84a16 16 0 0 1 0 29.86l-59.37 22.84a8 8 0 0 0-4.6 4.6l-22.84 59.37A16 16 0 0 1 400 256z"},child:[]}]})(t)}function Tg(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",d:"M296 64h-80a7.91 7.91 0 0 0-8 8v24h96V72a7.91 7.91 0 0 0-8-8z"},child:[]},{tag:"path",attr:{d:"M432 96h-96V72a40 40 0 0 0-40-40h-80a40 40 0 0 0-40 40v24H80a16 16 0 0 0 0 32h17l19 304.92c1.42 26.85 22 47.08 48 47.08h184c26.13 0 46.3-19.78 48-47l19-305h17a16 16 0 0 0 0-32zM192.57 416H192a16 16 0 0 1-16-15.43l-8-224a16 16 0 1 1 32-1.14l8 224A16 16 0 0 1 192.57 416zM272 400a16 16 0 0 1-32 0V176a16 16 0 0 1 32 0zm32-304h-96V72a7.91 7.91 0 0 1 8-8h80a7.91 7.91 0 0 1 8 8zm32 304.57A16 16 0 0 1 320 416h-.58A16 16 0 0 1 304 399.43l8-224a16 16 0 1 1 32 1.14z"},child:[]}]})(t)}function Ag(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M12 5v14"},child:[]},{tag:"path",attr:{d:"m19 12-7 7-7-7"},child:[]}]})(t)}function Og(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"m21 16-4 4-4-4"},child:[]},{tag:"path",attr:{d:"M17 20V4"},child:[]},{tag:"path",attr:{d:"m3 8 4-4 4 4"},child:[]},{tag:"path",attr:{d:"M7 4v16"},child:[]}]})(t)}function Rg(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"m5 12 7-7 7 7"},child:[]},{tag:"path",attr:{d:"M12 19V5"},child:[]}]})(t)}function Wt(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"rect",attr:{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2"},child:[]},{tag:"path",attr:{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"},child:[]}]})(t)}function Lg(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"},child:[]},{tag:"path",attr:{d:"m15 5 4 4"},child:[]}]})(t)}function bu(t,e){if(Object.is(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;if(t instanceof Map&&e instanceof Map){if(t.size!==e.size)return!1;for(const[n,r]of t)if(!Object.is(r,e.get(n)))return!1;return!0}if(t instanceof Set&&e instanceof Set){if(t.size!==e.size)return!1;for(const n of t)if(!e.has(n))return!1;return!0}const a=Object.keys(t);if(a.length!==Object.keys(e).length)return!1;for(const n of a)if(!Object.prototype.hasOwnProperty.call(e,n)||!Object.is(t[n],e[n]))return!1;return!0}const{useRef:wu}=W;function Ut(t){const e=wu();return a=>{const n=t(a);return bu(e.current,n)?e.current:e.current=n}}const xu=W.createContext({}),Ui=!0;function _u({baseColor:t,highlightColor:e,width:a,height:n,borderRadius:r,circle:s,direction:i,duration:o,enableAnimation:l=Ui}){const c={};return i==="rtl"&&(c["--animation-direction"]="reverse"),typeof o=="number"&&(c["--animation-duration"]=`${o}s`),l||(c["--pseudo-element-display"]="none"),(typeof a=="string"||typeof a=="number")&&(c.width=a),(typeof n=="string"||typeof n=="number")&&(c.height=n),(typeof r=="string"||typeof r=="number")&&(c.borderRadius=r),s&&(c.borderRadius="50%"),typeof t<"u"&&(c["--base-color"]=t),typeof e<"u"&&(c["--highlight-color"]=e),c}function ju({count:t=1,wrapper:e,className:a,containerClassName:n,containerTestId:r,circle:s=!1,style:i,...o}){var l,c,d;const m=W.useContext(xu),u={...o};for(const[z,T]of Object.entries(o))typeof T>"u"&&delete u[z];const p={...m,...u,circle:s},g={...i,..._u(p)};let _="react-loading-skeleton";a&&(_+=` ${a}`);const S=(l=p.inline)!==null&&l!==void 0?l:!1,w=[],j=Math.ceil(t);for(let z=0;z<j;z++){let T=g;if(j>t&&z===j-1){const M=(c=T.width)!==null&&c!==void 0?c:"100%",V=t%1,O=typeof M=="number"?M*V:`calc(${M} * ${V})`;T={...T,width:O}}const E=W.createElement("span",{className:_,style:T,key:z},"‌");S?w.push(E):w.push(W.createElement(W.Fragment,{key:z},E,W.createElement("br",null)))}return W.createElement("span",{className:n,"data-testid":r,"aria-live":"polite","aria-busy":(d=p.enableAnimation)!==null&&d!==void 0?d:Ui},e?w.map((z,T)=>W.createElement(e,{key:T},z)):w)}const Su=({count:t=1,...e})=>h.jsx(ju,{count:t,containerClassName:"mrdn-skeleton-container",...e}),aa=Su,Ea=Math.min,na=Math.max,Gn=Math.round,Nn=Math.floor,_t=t=>({x:t,y:t}),zu={left:"right",right:"left",bottom:"top",top:"bottom"},Eu={start:"end",end:"start"};function us(t,e,a){return na(t,Ea(e,a))}function _n(t,e){return typeof t=="function"?t(e):t}function sa(t){return t.split("-")[0]}function jn(t){return t.split("-")[1]}function Hi(t){return t==="x"?"y":"x"}function ks(t){return t==="y"?"height":"width"}function Ca(t){return["top","bottom"].includes(sa(t))?"y":"x"}function bs(t){return Hi(Ca(t))}function Cu(t,e,a){a===void 0&&(a=!1);const n=jn(t),r=bs(t),s=ks(r);let i=r==="x"?n===(a?"end":"start")?"right":"left":n==="start"?"bottom":"top";return e.reference[s]>e.floating[s]&&(i=Yn(i)),[i,Yn(i)]}function Tu(t){const e=Yn(t);return[ds(t),e,ds(e)]}function ds(t){return t.replace(/start|end/g,e=>Eu[e])}function Au(t,e,a){const n=["left","right"],r=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(t){case"top":case"bottom":return a?e?r:n:e?n:r;case"left":case"right":return e?s:i;default:return[]}}function Ou(t,e,a,n){const r=jn(t);let s=Au(sa(t),a==="start",n);return r&&(s=s.map(i=>i+"-"+r),e&&(s=s.concat(s.map(ds)))),s}function Yn(t){return t.replace(/left|right|bottom|top/g,e=>zu[e])}function Ru(t){return{top:0,right:0,bottom:0,left:0,...t}}function Wi(t){return typeof t!="number"?Ru(t):{top:t,right:t,bottom:t,left:t}}function Jn(t){const{x:e,y:a,width:n,height:r}=t;return{width:n,height:r,top:a,left:e,right:e+n,bottom:a+r,x:e,y:a}}function Ws(t,e,a){let{reference:n,floating:r}=t;const s=Ca(e),i=bs(e),o=ks(i),l=sa(e),c=s==="y",d=n.x+n.width/2-r.width/2,m=n.y+n.height/2-r.height/2,u=n[o]/2-r[o]/2;let p;switch(l){case"top":p={x:d,y:n.y-r.height};break;case"bottom":p={x:d,y:n.y+n.height};break;case"right":p={x:n.x+n.width,y:m};break;case"left":p={x:n.x-r.width,y:m};break;default:p={x:n.x,y:n.y}}switch(jn(e)){case"start":p[i]-=u*(a&&c?-1:1);break;case"end":p[i]+=u*(a&&c?-1:1);break}return p}const Lu=async(t,e,a)=>{const{placement:n="bottom",strategy:r="absolute",middleware:s=[],platform:i}=a,o=s.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(e));let c=await i.getElementRects({reference:t,floating:e,strategy:r}),{x:d,y:m}=Ws(c,n,l),u=n,p={},g=0;for(let _=0;_<o.length;_++){const{name:S,fn:w}=o[_],{x:j,y:z,data:T,reset:E}=await w({x:d,y:m,initialPlacement:n,placement:u,strategy:r,middlewareData:p,rects:c,platform:i,elements:{reference:t,floating:e}});d=j??d,m=z??m,p={...p,[S]:{...p[S],...T}},E&&g<=50&&(g++,typeof E=="object"&&(E.placement&&(u=E.placement),E.rects&&(c=E.rects===!0?await i.getElementRects({reference:t,floating:e,strategy:r}):E.rects),{x:d,y:m}=Ws(c,u,l)),_=-1)}return{x:d,y:m,placement:u,strategy:r,middlewareData:p}};async function Ki(t,e){var a;e===void 0&&(e={});const{x:n,y:r,platform:s,rects:i,elements:o,strategy:l}=t,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:m="floating",altBoundary:u=!1,padding:p=0}=_n(e,t),g=Wi(p),S=o[u?m==="floating"?"reference":"floating":m],w=Jn(await s.getClippingRect({element:(a=await(s.isElement==null?void 0:s.isElement(S)))==null||a?S:S.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(o.floating)),boundary:c,rootBoundary:d,strategy:l})),j=m==="floating"?{x:n,y:r,width:i.floating.width,height:i.floating.height}:i.reference,z=await(s.getOffsetParent==null?void 0:s.getOffsetParent(o.floating)),T=await(s.isElement==null?void 0:s.isElement(z))?await(s.getScale==null?void 0:s.getScale(z))||{x:1,y:1}:{x:1,y:1},E=Jn(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:o,rect:j,offsetParent:z,strategy:l}):j);return{top:(w.top-E.top+g.top)/T.y,bottom:(E.bottom-w.bottom+g.bottom)/T.y,left:(w.left-E.left+g.left)/T.x,right:(E.right-w.right+g.right)/T.x}}const Nu=t=>({name:"arrow",options:t,async fn(e){const{x:a,y:n,placement:r,rects:s,platform:i,elements:o,middlewareData:l}=e,{element:c,padding:d=0}=_n(t,e)||{};if(c==null)return{};const m=Wi(d),u={x:a,y:n},p=bs(r),g=ks(p),_=await i.getDimensions(c),S=p==="y",w=S?"top":"left",j=S?"bottom":"right",z=S?"clientHeight":"clientWidth",T=s.reference[g]+s.reference[p]-u[p]-s.floating[g],E=u[p]-s.reference[p],M=await(i.getOffsetParent==null?void 0:i.getOffsetParent(c));let V=M?M[z]:0;(!V||!await(i.isElement==null?void 0:i.isElement(M)))&&(V=o.floating[z]||s.floating[g]);const O=T/2-E/2,k=V/2-_[g]/2-1,Z=Ea(m[w],k),Y=Ea(m[j],k),N=Z,L=V-_[g]-Y,F=V/2-_[g]/2+O,X=us(N,F,L),ee=!l.arrow&&jn(r)!=null&&F!==X&&s.reference[g]/2-(F<N?Z:Y)-_[g]/2<0,I=ee?F<N?F-N:F-L:0;return{[p]:u[p]+I,data:{[p]:X,centerOffset:F-X-I,...ee&&{alignmentOffset:I}},reset:ee}}}),Mu=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(e){var a,n;const{placement:r,middlewareData:s,rects:i,initialPlacement:o,platform:l,elements:c}=e,{mainAxis:d=!0,crossAxis:m=!0,fallbackPlacements:u,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:_=!0,...S}=_n(t,e);if((a=s.arrow)!=null&&a.alignmentOffset)return{};const w=sa(r),j=Ca(o),z=sa(o)===o,T=await(l.isRTL==null?void 0:l.isRTL(c.floating)),E=u||(z||!_?[Yn(o)]:Tu(o)),M=g!=="none";!u&&M&&E.push(...Ou(o,_,g,T));const V=[o,...E],O=await Ki(e,S),k=[];let Z=((n=s.flip)==null?void 0:n.overflows)||[];if(d&&k.push(O[w]),m){const F=Cu(r,i,T);k.push(O[F[0]],O[F[1]])}if(Z=[...Z,{placement:r,overflows:k}],!k.every(F=>F<=0)){var Y,N;const F=(((Y=s.flip)==null?void 0:Y.index)||0)+1,X=V[F];if(X)return{data:{index:F,overflows:Z},reset:{placement:X}};let ee=(N=Z.filter(I=>I.overflows[0]<=0).sort((I,G)=>I.overflows[1]-G.overflows[1])[0])==null?void 0:N.placement;if(!ee)switch(p){case"bestFit":{var L;const I=(L=Z.filter(G=>{if(M){const J=Ca(G.placement);return J===j||J==="y"}return!0}).map(G=>[G.placement,G.overflows.filter(J=>J>0).reduce((J,U)=>J+U,0)]).sort((G,J)=>G[1]-J[1])[0])==null?void 0:L[0];I&&(ee=I);break}case"initialPlacement":ee=o;break}if(r!==ee)return{reset:{placement:ee}}}return{}}}};async function Iu(t,e){const{placement:a,platform:n,elements:r}=t,s=await(n.isRTL==null?void 0:n.isRTL(r.floating)),i=sa(a),o=jn(a),l=Ca(a)==="y",c=["left","top"].includes(i)?-1:1,d=s&&l?-1:1,m=_n(e,t);let{mainAxis:u,crossAxis:p,alignmentAxis:g}=typeof m=="number"?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return o&&typeof g=="number"&&(p=o==="end"?g*-1:g),l?{x:p*d,y:u*c}:{x:u*c,y:p*d}}const Fu=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(e){var a,n;const{x:r,y:s,placement:i,middlewareData:o}=e,l=await Iu(e,t);return i===((a=o.offset)==null?void 0:a.placement)&&(n=o.arrow)!=null&&n.alignmentOffset?{}:{x:r+l.x,y:s+l.y,data:{...l,placement:i}}}}},Pu=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(e){const{x:a,y:n,placement:r}=e,{mainAxis:s=!0,crossAxis:i=!1,limiter:o={fn:S=>{let{x:w,y:j}=S;return{x:w,y:j}}},...l}=_n(t,e),c={x:a,y:n},d=await Ki(e,l),m=Ca(sa(r)),u=Hi(m);let p=c[u],g=c[m];if(s){const S=u==="y"?"top":"left",w=u==="y"?"bottom":"right",j=p+d[S],z=p-d[w];p=us(j,p,z)}if(i){const S=m==="y"?"top":"left",w=m==="y"?"bottom":"right",j=g+d[S],z=g-d[w];g=us(j,g,z)}const _=o.fn({...e,[u]:p,[m]:g});return{..._,data:{x:_.x-a,y:_.y-n,enabled:{[u]:s,[m]:i}}}}}};function Ar(){return typeof window<"u"}function Na(t){return Zi(t)?(t.nodeName||"").toLowerCase():"#document"}function st(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function Ct(t){var e;return(e=(Zi(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function Zi(t){return Ar()?t instanceof Node||t instanceof st(t).Node:!1}function vt(t){return Ar()?t instanceof Element||t instanceof st(t).Element:!1}function St(t){return Ar()?t instanceof HTMLElement||t instanceof st(t).HTMLElement:!1}function Ks(t){return!Ar()||typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof st(t).ShadowRoot}function Sn(t){const{overflow:e,overflowX:a,overflowY:n,display:r}=yt(t);return/auto|scroll|overlay|hidden|clip/.test(e+n+a)&&!["inline","contents"].includes(r)}function $u(t){return["table","td","th"].includes(Na(t))}function Or(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch{return!1}})}function ws(t){const e=xs(),a=vt(t)?yt(t):t;return["transform","translate","scale","rotate","perspective"].some(n=>a[n]?a[n]!=="none":!1)||(a.containerType?a.containerType!=="normal":!1)||!e&&(a.backdropFilter?a.backdropFilter!=="none":!1)||!e&&(a.filter?a.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(n=>(a.willChange||"").includes(n))||["paint","layout","strict","content"].some(n=>(a.contain||"").includes(n))}function Vu(t){let e=Kt(t);for(;St(e)&&!Ta(e);){if(ws(e))return e;if(Or(e))return null;e=Kt(e)}return null}function xs(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Ta(t){return["html","body","#document"].includes(Na(t))}function yt(t){return st(t).getComputedStyle(t)}function Rr(t){return vt(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function Kt(t){if(Na(t)==="html")return t;const e=t.assignedSlot||t.parentNode||Ks(t)&&t.host||Ct(t);return Ks(e)?e.host:e}function Gi(t){const e=Kt(t);return Ta(e)?t.ownerDocument?t.ownerDocument.body:t.body:St(e)&&Sn(e)?e:Gi(e)}function Xa(t,e,a){var n;e===void 0&&(e=[]),a===void 0&&(a=!0);const r=Gi(t),s=r===((n=t.ownerDocument)==null?void 0:n.body),i=st(r);if(s){const o=ms(i);return e.concat(i,i.visualViewport||[],Sn(r)?r:[],o&&a?Xa(o):[])}return e.concat(r,Xa(r,[],a))}function ms(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function Yi(t){const e=yt(t);let a=parseFloat(e.width)||0,n=parseFloat(e.height)||0;const r=St(t),s=r?t.offsetWidth:a,i=r?t.offsetHeight:n,o=Gn(a)!==s||Gn(n)!==i;return o&&(a=s,n=i),{width:a,height:n,$:o}}function _s(t){return vt(t)?t:t.contextElement}function xa(t){const e=_s(t);if(!St(e))return _t(1);const a=e.getBoundingClientRect(),{width:n,height:r,$:s}=Yi(e);let i=(s?Gn(a.width):a.width)/n,o=(s?Gn(a.height):a.height)/r;return(!i||!Number.isFinite(i))&&(i=1),(!o||!Number.isFinite(o))&&(o=1),{x:i,y:o}}const Du=_t(0);function Ji(t){const e=st(t);return!xs()||!e.visualViewport?Du:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function qu(t,e,a){return e===void 0&&(e=!1),!a||e&&a!==st(t)?!1:e}function ia(t,e,a,n){e===void 0&&(e=!1),a===void 0&&(a=!1);const r=t.getBoundingClientRect(),s=_s(t);let i=_t(1);e&&(n?vt(n)&&(i=xa(n)):i=xa(t));const o=qu(s,a,n)?Ji(s):_t(0);let l=(r.left+o.x)/i.x,c=(r.top+o.y)/i.y,d=r.width/i.x,m=r.height/i.y;if(s){const u=st(s),p=n&&vt(n)?st(n):n;let g=u,_=ms(g);for(;_&&n&&p!==g;){const S=xa(_),w=_.getBoundingClientRect(),j=yt(_),z=w.left+(_.clientLeft+parseFloat(j.paddingLeft))*S.x,T=w.top+(_.clientTop+parseFloat(j.paddingTop))*S.y;l*=S.x,c*=S.y,d*=S.x,m*=S.y,l+=z,c+=T,g=st(_),_=ms(g)}}return Jn({width:d,height:m,x:l,y:c})}function js(t,e){const a=Rr(t).scrollLeft;return e?e.left+a:ia(Ct(t)).left+a}function Qi(t,e,a){a===void 0&&(a=!1);const n=t.getBoundingClientRect(),r=n.left+e.scrollLeft-(a?0:js(t,n)),s=n.top+e.scrollTop;return{x:r,y:s}}function Bu(t){let{elements:e,rect:a,offsetParent:n,strategy:r}=t;const s=r==="fixed",i=Ct(n),o=e?Or(e.floating):!1;if(n===i||o&&s)return a;let l={scrollLeft:0,scrollTop:0},c=_t(1);const d=_t(0),m=St(n);if((m||!m&&!s)&&((Na(n)!=="body"||Sn(i))&&(l=Rr(n)),St(n))){const p=ia(n);c=xa(n),d.x=p.x+n.clientLeft,d.y=p.y+n.clientTop}const u=i&&!m&&!s?Qi(i,l,!0):_t(0);return{width:a.width*c.x,height:a.height*c.y,x:a.x*c.x-l.scrollLeft*c.x+d.x+u.x,y:a.y*c.y-l.scrollTop*c.y+d.y+u.y}}function Uu(t){return Array.from(t.getClientRects())}function Hu(t){const e=Ct(t),a=Rr(t),n=t.ownerDocument.body,r=na(e.scrollWidth,e.clientWidth,n.scrollWidth,n.clientWidth),s=na(e.scrollHeight,e.clientHeight,n.scrollHeight,n.clientHeight);let i=-a.scrollLeft+js(t);const o=-a.scrollTop;return yt(n).direction==="rtl"&&(i+=na(e.clientWidth,n.clientWidth)-r),{width:r,height:s,x:i,y:o}}function Wu(t,e){const a=st(t),n=Ct(t),r=a.visualViewport;let s=n.clientWidth,i=n.clientHeight,o=0,l=0;if(r){s=r.width,i=r.height;const c=xs();(!c||c&&e==="fixed")&&(o=r.offsetLeft,l=r.offsetTop)}return{width:s,height:i,x:o,y:l}}function Ku(t,e){const a=ia(t,!0,e==="fixed"),n=a.top+t.clientTop,r=a.left+t.clientLeft,s=St(t)?xa(t):_t(1),i=t.clientWidth*s.x,o=t.clientHeight*s.y,l=r*s.x,c=n*s.y;return{width:i,height:o,x:l,y:c}}function Zs(t,e,a){let n;if(e==="viewport")n=Wu(t,a);else if(e==="document")n=Hu(Ct(t));else if(vt(e))n=Ku(e,a);else{const r=Ji(t);n={x:e.x-r.x,y:e.y-r.y,width:e.width,height:e.height}}return Jn(n)}function Xi(t,e){const a=Kt(t);return a===e||!vt(a)||Ta(a)?!1:yt(a).position==="fixed"||Xi(a,e)}function Zu(t,e){const a=e.get(t);if(a)return a;let n=Xa(t,[],!1).filter(o=>vt(o)&&Na(o)!=="body"),r=null;const s=yt(t).position==="fixed";let i=s?Kt(t):t;for(;vt(i)&&!Ta(i);){const o=yt(i),l=ws(i);!l&&o.position==="fixed"&&(r=null),(s?!l&&!r:!l&&o.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||Sn(i)&&!l&&Xi(t,i))?n=n.filter(d=>d!==i):r=o,i=Kt(i)}return e.set(t,n),n}function Gu(t){let{element:e,boundary:a,rootBoundary:n,strategy:r}=t;const i=[...a==="clippingAncestors"?Or(e)?[]:Zu(e,this._c):[].concat(a),n],o=i[0],l=i.reduce((c,d)=>{const m=Zs(e,d,r);return c.top=na(m.top,c.top),c.right=Ea(m.right,c.right),c.bottom=Ea(m.bottom,c.bottom),c.left=na(m.left,c.left),c},Zs(e,o,r));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function Yu(t){const{width:e,height:a}=Yi(t);return{width:e,height:a}}function Ju(t,e,a){const n=St(e),r=Ct(e),s=a==="fixed",i=ia(t,!0,s,e);let o={scrollLeft:0,scrollTop:0};const l=_t(0);if(n||!n&&!s)if((Na(e)!=="body"||Sn(r))&&(o=Rr(e)),n){const u=ia(e,!0,s,e);l.x=u.x+e.clientLeft,l.y=u.y+e.clientTop}else r&&(l.x=js(r));const c=r&&!n&&!s?Qi(r,o):_t(0),d=i.left+o.scrollLeft-l.x-c.x,m=i.top+o.scrollTop-l.y-c.y;return{x:d,y:m,width:i.width,height:i.height}}function Kr(t){return yt(t).position==="static"}function Gs(t,e){if(!St(t)||yt(t).position==="fixed")return null;if(e)return e(t);let a=t.offsetParent;return Ct(t)===a&&(a=a.ownerDocument.body),a}function eo(t,e){const a=st(t);if(Or(t))return a;if(!St(t)){let r=Kt(t);for(;r&&!Ta(r);){if(vt(r)&&!Kr(r))return r;r=Kt(r)}return a}let n=Gs(t,e);for(;n&&$u(n)&&Kr(n);)n=Gs(n,e);return n&&Ta(n)&&Kr(n)&&!ws(n)?a:n||Vu(t)||a}const Qu=async function(t){const e=this.getOffsetParent||eo,a=this.getDimensions,n=await a(t.floating);return{reference:Ju(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}};function Xu(t){return yt(t).direction==="rtl"}const ed={convertOffsetParentRelativeRectToViewportRelativeRect:Bu,getDocumentElement:Ct,getClippingRect:Gu,getOffsetParent:eo,getElementRects:Qu,getClientRects:Uu,getDimensions:Yu,getScale:xa,isElement:vt,isRTL:Xu};function to(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function td(t,e){let a=null,n;const r=Ct(t);function s(){var o;clearTimeout(n),(o=a)==null||o.disconnect(),a=null}function i(o,l){o===void 0&&(o=!1),l===void 0&&(l=1),s();const c=t.getBoundingClientRect(),{left:d,top:m,width:u,height:p}=c;if(o||e(),!u||!p)return;const g=Nn(m),_=Nn(r.clientWidth-(d+u)),S=Nn(r.clientHeight-(m+p)),w=Nn(d),z={rootMargin:-g+"px "+-_+"px "+-S+"px "+-w+"px",threshold:na(0,Ea(1,l))||1};let T=!0;function E(M){const V=M[0].intersectionRatio;if(V!==l){if(!T)return i();V?i(!1,V):n=setTimeout(()=>{i(!1,1e-7)},1e3)}V===1&&!to(c,t.getBoundingClientRect())&&i(),T=!1}try{a=new IntersectionObserver(E,{...z,root:r.ownerDocument})}catch{a=new IntersectionObserver(E,z)}a.observe(t)}return i(!0),s}function ad(t,e,a,n){n===void 0&&(n={});const{ancestorScroll:r=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:o=typeof IntersectionObserver=="function",animationFrame:l=!1}=n,c=_s(t),d=r||s?[...c?Xa(c):[],...Xa(e)]:[];d.forEach(w=>{r&&w.addEventListener("scroll",a,{passive:!0}),s&&w.addEventListener("resize",a)});const m=c&&o?td(c,a):null;let u=-1,p=null;i&&(p=new ResizeObserver(w=>{let[j]=w;j&&j.target===c&&p&&(p.unobserve(e),cancelAnimationFrame(u),u=requestAnimationFrame(()=>{var z;(z=p)==null||z.observe(e)})),a()}),c&&!l&&p.observe(c),p.observe(e));let g,_=l?ia(t):null;l&&S();function S(){const w=ia(t);_&&!to(_,w)&&a(),_=w,g=requestAnimationFrame(S)}return a(),()=>{var w;d.forEach(j=>{r&&j.removeEventListener("scroll",a),s&&j.removeEventListener("resize",a)}),m==null||m(),(w=p)==null||w.disconnect(),p=null,l&&cancelAnimationFrame(g)}}const nd=Fu,rd=Pu,sd=Mu,id=Nu,Ys=(t,e,a)=>{const n=new Map,r={platform:ed,...a},s={...r.platform,_c:n};return Lu(t,e,{...r,platform:s})};/*
* React Tooltip
* {@link https://github.com/ReactTooltip/react-tooltip}
* @copyright ReactTooltip Team
* @license MIT
*/const od="react-tooltip-core-styles",ld="react-tooltip-base-styles",Js={core:!1,base:!1};function Qs({css:t,id:e=ld,type:a="base",ref:n}){var r,s;if(!t||typeof document>"u"||Js[a]||a==="core"&&typeof process<"u"&&(!((r=process==null?void 0:process.env)===null||r===void 0)&&r.REACT_TOOLTIP_DISABLE_CORE_STYLES)||a!=="base"&&typeof process<"u"&&(!((s=process==null?void 0:process.env)===null||s===void 0)&&s.REACT_TOOLTIP_DISABLE_BASE_STYLES))return;a==="core"&&(e=od),n||(n={});const{insertAt:i}=n;if(document.getElementById(e))return void console.warn(`[react-tooltip] Element with id '${e}' already exists. Call \`removeStyle()\` first`);const o=document.head||document.getElementsByTagName("head")[0],l=document.createElement("style");l.id=e,l.type="text/css",i==="top"&&o.firstChild?o.insertBefore(l,o.firstChild):o.appendChild(l),l.styleSheet?l.styleSheet.cssText=t:l.appendChild(document.createTextNode(t)),Js[a]=!0}const Xs=async({elementReference:t=null,tooltipReference:e=null,tooltipArrowReference:a=null,place:n="top",offset:r=10,strategy:s="absolute",middlewares:i=[nd(Number(r)),sd({fallbackAxisSideDirection:"start"}),rd({padding:5})],border:o})=>{if(!t)return{tooltipStyles:{},tooltipArrowStyles:{},place:n};if(e===null)return{tooltipStyles:{},tooltipArrowStyles:{},place:n};const l=i;return a?(l.push(id({element:a,padding:5})),Ys(t,e,{placement:n,strategy:s,middleware:l}).then(({x:c,y:d,placement:m,middlewareData:u})=>{var p,g;const _={left:`${c}px`,top:`${d}px`,border:o},{x:S,y:w}=(p=u.arrow)!==null&&p!==void 0?p:{x:0,y:0},j=(g={top:"bottom",right:"left",bottom:"top",left:"right"}[m.split("-")[0]])!==null&&g!==void 0?g:"bottom",z=o&&{borderBottom:o,borderRight:o};let T=0;if(o){const E=`${o}`.match(/(\d+)px/);T=E!=null&&E[1]?Number(E[1]):1}return{tooltipStyles:_,tooltipArrowStyles:{left:S!=null?`${S}px`:"",top:w!=null?`${w}px`:"",right:"",bottom:"",...z,[j]:`-${4+T}px`},place:m}})):Ys(t,e,{placement:"bottom",strategy:s,middleware:l}).then(({x:c,y:d,placement:m})=>({tooltipStyles:{left:`${c}px`,top:`${d}px`},tooltipArrowStyles:{},place:m}))},ei=(t,e)=>!("CSS"in window&&"supports"in window.CSS)||window.CSS.supports(t,e),ti=(t,e,a)=>{let n=null;const r=function(...s){const i=()=>{n=null,a||t.apply(this,s)};a&&!n&&(t.apply(this,s),n=setTimeout(i,e)),a||(n&&clearTimeout(n),n=setTimeout(i,e))};return r.cancel=()=>{n&&(clearTimeout(n),n=null)},r},ai=t=>t!==null&&!Array.isArray(t)&&typeof t=="object",hs=(t,e)=>{if(t===e)return!0;if(Array.isArray(t)&&Array.isArray(e))return t.length===e.length&&t.every((r,s)=>hs(r,e[s]));if(Array.isArray(t)!==Array.isArray(e))return!1;if(!ai(t)||!ai(e))return t===e;const a=Object.keys(t),n=Object.keys(e);return a.length===n.length&&a.every(r=>hs(t[r],e[r]))},cd=t=>{if(!(t instanceof HTMLElement||t instanceof SVGElement))return!1;const e=getComputedStyle(t);return["overflow","overflow-x","overflow-y"].some(a=>{const n=e.getPropertyValue(a);return n==="auto"||n==="scroll"})},ni=t=>{if(!t)return null;let e=t.parentElement;for(;e;){if(cd(e))return e;e=e.parentElement}return document.scrollingElement||document.documentElement},ud=typeof window<"u"?b.useLayoutEffect:b.useEffect,dd="DEFAULT_TOOLTIP_ID",md={anchorRefs:new Set,activeAnchor:{current:null},attach:()=>{},detach:()=>{},setActiveAnchor:()=>{}},hd=b.createContext({getTooltipData:()=>md});function ao(t=dd){return b.useContext(hd).getTooltipData(t)}var ga={tooltip:"core-styles-module_tooltip__3vRRp",fixed:"core-styles-module_fixed__pcSol",arrow:"core-styles-module_arrow__cvMwQ",noArrow:"core-styles-module_noArrow__xock6",clickable:"core-styles-module_clickable__ZuTTB",show:"core-styles-module_show__Nt9eE",closing:"core-styles-module_closing__sGnxF"},Zr={tooltip:"styles-module_tooltip__mnnfp",arrow:"styles-module_arrow__K0L3T",dark:"styles-module_dark__xNqje",light:"styles-module_light__Z6W-X",success:"styles-module_success__A2AKt",warning:"styles-module_warning__SCK0X",error:"styles-module_error__JvumD",info:"styles-module_info__BWdHW"};const fd=({forwardRef:t,id:e,className:a,classNameArrow:n,variant:r="dark",anchorId:s,anchorSelect:i,place:o="top",offset:l=10,events:c=["hover"],openOnClick:d=!1,positionStrategy:m="absolute",middlewares:u,wrapper:p,delayShow:g=0,delayHide:_=0,float:S=!1,hidden:w=!1,noArrow:j=!1,clickable:z=!1,closeOnEsc:T=!1,closeOnScroll:E=!1,closeOnResize:M=!1,openEvents:V,closeEvents:O,globalCloseEvents:k,imperativeModeOnly:Z,style:Y,position:N,afterShow:L,afterHide:F,content:X,contentWrapperRef:ee,isOpen:I,defaultIsOpen:G=!1,setIsOpen:J,activeAnchor:U,setActiveAnchor:ie,border:ue,opacity:Ee,arrowColor:Pe,role:ke="tooltip"})=>{var Ae;const be=b.useRef(null),Re=b.useRef(null),K=b.useRef(null),ye=b.useRef(null),_e=b.useRef(null),[Ce,fe]=b.useState({tooltipStyles:{},tooltipArrowStyles:{},place:o}),[we,je]=b.useState(!1),[Me,Ie]=b.useState(!1),[f,v]=b.useState(null),y=b.useRef(!1),C=b.useRef(null),{anchorRefs:A,setActiveAnchor:x}=ao(e),$=b.useRef(!1),[te,le]=b.useState([]),me=b.useRef(!1),$e=d||c.includes("click"),Ue=$e||(V==null?void 0:V.click)||(V==null?void 0:V.dblclick)||(V==null?void 0:V.mousedown),ot=V?{...V}:{mouseenter:!0,focus:!0,click:!1,dblclick:!1,mousedown:!1};!V&&$e&&Object.assign(ot,{mouseenter:!1,focus:!1,click:!0});const Tt=O?{...O}:{mouseleave:!0,blur:!0,click:!1,dblclick:!1,mouseup:!1};!O&&$e&&Object.assign(Tt,{mouseleave:!1,blur:!1});const tt=k?{...k}:{escape:T||!1,scroll:E||!1,resize:M||!1,clickOutsideAnchor:Ue||!1};Z&&(Object.assign(ot,{mouseenter:!1,focus:!1,click:!1,dblclick:!1,mousedown:!1}),Object.assign(Tt,{mouseleave:!1,blur:!1,click:!1,dblclick:!1,mouseup:!1}),Object.assign(tt,{escape:!1,scroll:!1,resize:!1,clickOutsideAnchor:!1})),ud(()=>(me.current=!0,()=>{me.current=!1}),[]);const Le=B=>{me.current&&(B&&Ie(!0),setTimeout(()=>{me.current&&(J==null||J(B),I===void 0&&je(B))},10))};b.useEffect(()=>{if(I===void 0)return()=>null;I&&Ie(!0);const B=setTimeout(()=>{je(I)},10);return()=>{clearTimeout(B)}},[I]),b.useEffect(()=>{if(we!==y.current)if(_e.current&&clearTimeout(_e.current),y.current=we,we)L==null||L();else{const B=(oe=>{const ce=oe.match(/^([\d.]+)(ms|s)$/);if(!ce)return 0;const[,qe,He]=ce;return Number(qe)*(He==="ms"?1:1e3)})(getComputedStyle(document.body).getPropertyValue("--rt-transition-show-delay"));_e.current=setTimeout(()=>{Ie(!1),v(null),F==null||F()},B+25)}},[we]);const ua=B=>{fe(oe=>hs(oe,B)?oe:B)},$t=(B=g)=>{K.current&&clearTimeout(K.current),Me?Le(!0):K.current=setTimeout(()=>{Le(!0)},B)},da=(B=_)=>{ye.current&&clearTimeout(ye.current),ye.current=setTimeout(()=>{$.current||Le(!1)},B)},Ia=B=>{var oe;if(!B)return;const ce=(oe=B.currentTarget)!==null&&oe!==void 0?oe:B.target;if(!(ce!=null&&ce.isConnected))return ie(null),void x({current:null});g?$t():Le(!0),ie(ce),x({current:ce}),ye.current&&clearTimeout(ye.current)},ma=()=>{z?da(_||100):_?da():Le(!1),K.current&&clearTimeout(K.current)},ha=({x:B,y:oe})=>{var ce;const qe={getBoundingClientRect:()=>({x:B,y:oe,width:0,height:0,top:oe,left:B,right:B,bottom:oe})};Xs({place:(ce=f==null?void 0:f.place)!==null&&ce!==void 0?ce:o,offset:l,elementReference:qe,tooltipReference:be.current,tooltipArrowReference:Re.current,strategy:m,middlewares:u,border:ue}).then(He=>{ua(He)})},fa=B=>{if(!B)return;const oe=B,ce={x:oe.clientX,y:oe.clientY};ha(ce),C.current=ce},Fa=B=>{var oe;if(!we)return;const ce=B.target;ce.isConnected&&(!((oe=be.current)===null||oe===void 0)&&oe.contains(ce)||[document.querySelector(`[id='${s}']`),...te].some(qe=>qe==null?void 0:qe.contains(ce))||(Le(!1),K.current&&clearTimeout(K.current)))},Tn=ti(Ia,50,!0),De=ti(ma,50,!0),lt=B=>{De.cancel(),Tn(B)},ae=()=>{Tn.cancel(),De()},pe=b.useCallback(()=>{var B,oe;const ce=(B=f==null?void 0:f.position)!==null&&B!==void 0?B:N;ce?ha(ce):S?C.current&&ha(C.current):U!=null&&U.isConnected&&Xs({place:(oe=f==null?void 0:f.place)!==null&&oe!==void 0?oe:o,offset:l,elementReference:U,tooltipReference:be.current,tooltipArrowReference:Re.current,strategy:m,middlewares:u,border:ue}).then(qe=>{me.current&&ua(qe)})},[we,U,X,Y,o,f==null?void 0:f.place,l,m,N,f==null?void 0:f.position,S]);b.useEffect(()=>{var B,oe;const ce=new Set(A);te.forEach(Te=>{ce.add({current:Te})});const qe=document.querySelector(`[id='${s}']`);qe&&ce.add({current:qe});const He=()=>{Le(!1)},bt=ni(U),wt=ni(be.current);tt.scroll&&(window.addEventListener("scroll",He),bt==null||bt.addEventListener("scroll",He),wt==null||wt.addEventListener("scroll",He));let Ke=null;tt.resize?window.addEventListener("resize",He):U&&be.current&&(Ke=ad(U,be.current,pe,{ancestorResize:!0,elementResize:!0,layoutShift:!0}));const ct=Te=>{Te.key==="Escape"&&Le(!1)};tt.escape&&window.addEventListener("keydown",ct),tt.clickOutsideAnchor&&window.addEventListener("click",Fa);const Oe=[],Pa=Te=>{we&&(Te==null?void 0:Te.target)===U||Ia(Te)},Wo=Te=>{we&&(Te==null?void 0:Te.target)===U&&ma()},Ls=["mouseenter","mouseleave","focus","blur"],Ns=["click","dblclick","mousedown","mouseup"];Object.entries(ot).forEach(([Te,At])=>{At&&(Ls.includes(Te)?Oe.push({event:Te,listener:lt}):Ns.includes(Te)&&Oe.push({event:Te,listener:Pa}))}),Object.entries(Tt).forEach(([Te,At])=>{At&&(Ls.includes(Te)?Oe.push({event:Te,listener:ae}):Ns.includes(Te)&&Oe.push({event:Te,listener:Wo}))}),S&&Oe.push({event:"pointermove",listener:fa});const Ms=()=>{$.current=!0},Is=()=>{$.current=!1,ma()};return z&&!Ue&&((B=be.current)===null||B===void 0||B.addEventListener("mouseenter",Ms),(oe=be.current)===null||oe===void 0||oe.addEventListener("mouseleave",Is)),Oe.forEach(({event:Te,listener:At})=>{ce.forEach(Vr=>{var $a;($a=Vr.current)===null||$a===void 0||$a.addEventListener(Te,At)})}),()=>{var Te,At;tt.scroll&&(window.removeEventListener("scroll",He),bt==null||bt.removeEventListener("scroll",He),wt==null||wt.removeEventListener("scroll",He)),tt.resize?window.removeEventListener("resize",He):Ke==null||Ke(),tt.clickOutsideAnchor&&window.removeEventListener("click",Fa),tt.escape&&window.removeEventListener("keydown",ct),z&&!Ue&&((Te=be.current)===null||Te===void 0||Te.removeEventListener("mouseenter",Ms),(At=be.current)===null||At===void 0||At.removeEventListener("mouseleave",Is)),Oe.forEach(({event:Vr,listener:$a})=>{ce.forEach(Ko=>{var Dr;(Dr=Ko.current)===null||Dr===void 0||Dr.removeEventListener(Vr,$a)})})}},[U,pe,Me,A,te,V,O,k,$e,g,_]),b.useEffect(()=>{var B,oe;let ce=(oe=(B=f==null?void 0:f.anchorSelect)!==null&&B!==void 0?B:i)!==null&&oe!==void 0?oe:"";!ce&&e&&(ce=`[data-tooltip-id='${e}']`);const qe=new MutationObserver(He=>{const bt=[],wt=[];He.forEach(Ke=>{if(Ke.type==="attributes"&&Ke.attributeName==="data-tooltip-id"&&(Ke.target.getAttribute("data-tooltip-id")===e?bt.push(Ke.target):Ke.oldValue===e&&wt.push(Ke.target)),Ke.type==="childList"){if(U){const ct=[...Ke.removedNodes].filter(Oe=>Oe.nodeType===1);if(ce)try{wt.push(...ct.filter(Oe=>Oe.matches(ce))),wt.push(...ct.flatMap(Oe=>[...Oe.querySelectorAll(ce)]))}catch{}ct.some(Oe=>{var Pa;return!!(!((Pa=Oe==null?void 0:Oe.contains)===null||Pa===void 0)&&Pa.call(Oe,U))&&(Ie(!1),Le(!1),ie(null),K.current&&clearTimeout(K.current),ye.current&&clearTimeout(ye.current),!0)})}if(ce)try{const ct=[...Ke.addedNodes].filter(Oe=>Oe.nodeType===1);bt.push(...ct.filter(Oe=>Oe.matches(ce))),bt.push(...ct.flatMap(Oe=>[...Oe.querySelectorAll(ce)]))}catch{}}}),(bt.length||wt.length)&&le(Ke=>[...Ke.filter(ct=>!wt.includes(ct)),...bt])});return qe.observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-tooltip-id"],attributeOldValue:!0}),()=>{qe.disconnect()}},[e,i,f==null?void 0:f.anchorSelect,U]),b.useEffect(()=>{pe()},[pe]),b.useEffect(()=>{if(!(ee!=null&&ee.current))return()=>null;const B=new ResizeObserver(()=>{setTimeout(()=>pe())});return B.observe(ee.current),()=>{B.disconnect()}},[X,ee==null?void 0:ee.current]),b.useEffect(()=>{var B;const oe=document.querySelector(`[id='${s}']`),ce=[...te,oe];U&&ce.includes(U)||ie((B=te[0])!==null&&B!==void 0?B:oe)},[s,te,U]),b.useEffect(()=>(G&&Le(!0),()=>{K.current&&clearTimeout(K.current),ye.current&&clearTimeout(ye.current)}),[]),b.useEffect(()=>{var B;let oe=(B=f==null?void 0:f.anchorSelect)!==null&&B!==void 0?B:i;if(!oe&&e&&(oe=`[data-tooltip-id='${e}']`),oe)try{const ce=Array.from(document.querySelectorAll(oe));le(ce)}catch{le([])}},[e,i,f==null?void 0:f.anchorSelect]),b.useEffect(()=>{K.current&&(clearTimeout(K.current),$t(g))},[g]);const at=(Ae=f==null?void 0:f.content)!==null&&Ae!==void 0?Ae:X,Xt=we&&Object.keys(Ce.tooltipStyles).length>0;return b.useImperativeHandle(t,()=>({open:B=>{if(B!=null&&B.anchorSelect)try{document.querySelector(B.anchorSelect)}catch{return void console.warn(`[react-tooltip] "${B.anchorSelect}" is not a valid CSS selector`)}v(B??null),B!=null&&B.delay?$t(B.delay):Le(!0)},close:B=>{B!=null&&B.delay?da(B.delay):Le(!1)},activeAnchor:U,place:Ce.place,isOpen:!!(Me&&!w&&at&&Xt)})),Me&&!w&&at?W.createElement(p,{id:e,role:ke,className:et("react-tooltip",ga.tooltip,Zr.tooltip,Zr[r],a,`react-tooltip__place-${Ce.place}`,ga[Xt?"show":"closing"],Xt?"react-tooltip__show":"react-tooltip__closing",m==="fixed"&&ga.fixed,z&&ga.clickable),onTransitionEnd:B=>{_e.current&&clearTimeout(_e.current),we||B.propertyName!=="opacity"||(Ie(!1),v(null),F==null||F())},style:{...Y,...Ce.tooltipStyles,opacity:Ee!==void 0&&Xt?Ee:void 0},ref:be},at,W.createElement(p,{className:et("react-tooltip-arrow",ga.arrow,Zr.arrow,n,j&&ga.noArrow),style:{...Ce.tooltipArrowStyles,background:Pe?`linear-gradient(to right bottom, transparent 50%, ${Pe} 50%)`:void 0},ref:Re})):null},pd=({content:t})=>W.createElement("span",{dangerouslySetInnerHTML:{__html:t}}),Ss=W.forwardRef(({id:t,anchorId:e,anchorSelect:a,content:n,html:r,render:s,className:i,classNameArrow:o,variant:l="dark",place:c="top",offset:d=10,wrapper:m="div",children:u=null,events:p=["hover"],openOnClick:g=!1,positionStrategy:_="absolute",middlewares:S,delayShow:w=0,delayHide:j=0,float:z=!1,hidden:T=!1,noArrow:E=!1,clickable:M=!1,closeOnEsc:V=!1,closeOnScroll:O=!1,closeOnResize:k=!1,openEvents:Z,closeEvents:Y,globalCloseEvents:N,imperativeModeOnly:L=!1,style:F,position:X,isOpen:ee,defaultIsOpen:I=!1,disableStyleInjection:G=!1,border:J,opacity:U,arrowColor:ie,setIsOpen:ue,afterShow:Ee,afterHide:Pe,role:ke="tooltip"},Ae)=>{const[be,Re]=b.useState(n),[K,ye]=b.useState(r),[_e,Ce]=b.useState(c),[fe,we]=b.useState(l),[je,Me]=b.useState(d),[Ie,f]=b.useState(w),[v,y]=b.useState(j),[C,A]=b.useState(z),[x,$]=b.useState(T),[te,le]=b.useState(m),[me,$e]=b.useState(p),[Ue,ot]=b.useState(_),[Tt,tt]=b.useState(null),[Le,ua]=b.useState(null),$t=b.useRef(G),{anchorRefs:da,activeAnchor:Ia}=ao(t),ma=De=>De==null?void 0:De.getAttributeNames().reduce((lt,ae)=>{var pe;return ae.startsWith("data-tooltip-")&&(lt[ae.replace(/^data-tooltip-/,"")]=(pe=De==null?void 0:De.getAttribute(ae))!==null&&pe!==void 0?pe:null),lt},{}),ha=De=>{const lt={place:ae=>{var pe;Ce((pe=ae)!==null&&pe!==void 0?pe:c)},content:ae=>{Re(ae??n)},html:ae=>{ye(ae??r)},variant:ae=>{var pe;we((pe=ae)!==null&&pe!==void 0?pe:l)},offset:ae=>{Me(ae===null?d:Number(ae))},wrapper:ae=>{var pe;le((pe=ae)!==null&&pe!==void 0?pe:m)},events:ae=>{const pe=ae==null?void 0:ae.split(" ");$e(pe??p)},"position-strategy":ae=>{var pe;ot((pe=ae)!==null&&pe!==void 0?pe:_)},"delay-show":ae=>{f(ae===null?w:Number(ae))},"delay-hide":ae=>{y(ae===null?j:Number(ae))},float:ae=>{A(ae===null?z:ae==="true")},hidden:ae=>{$(ae===null?T:ae==="true")},"class-name":ae=>{tt(ae)}};Object.values(lt).forEach(ae=>ae(null)),Object.entries(De).forEach(([ae,pe])=>{var at;(at=lt[ae])===null||at===void 0||at.call(lt,pe)})};b.useEffect(()=>{Re(n)},[n]),b.useEffect(()=>{ye(r)},[r]),b.useEffect(()=>{Ce(c)},[c]),b.useEffect(()=>{we(l)},[l]),b.useEffect(()=>{Me(d)},[d]),b.useEffect(()=>{f(w)},[w]),b.useEffect(()=>{y(j)},[j]),b.useEffect(()=>{A(z)},[z]),b.useEffect(()=>{$(T)},[T]),b.useEffect(()=>{ot(_)},[_]),b.useEffect(()=>{$t.current!==G&&console.warn("[react-tooltip] Do not change `disableStyleInjection` dynamically.")},[G]),b.useEffect(()=>{typeof window<"u"&&window.dispatchEvent(new CustomEvent("react-tooltip-inject-styles",{detail:{disableCore:G==="core",disableBase:G}}))},[]),b.useEffect(()=>{var De;const lt=new Set(da);let ae=a;if(!ae&&t&&(ae=`[data-tooltip-id='${t}']`),ae)try{document.querySelectorAll(ae).forEach(oe=>{lt.add({current:oe})})}catch{console.warn(`[react-tooltip] "${ae}" is not a valid CSS selector`)}const pe=document.querySelector(`[id='${e}']`);if(pe&&lt.add({current:pe}),!lt.size)return()=>null;const at=(De=Le??pe)!==null&&De!==void 0?De:Ia.current,Xt=new MutationObserver(oe=>{oe.forEach(ce=>{var qe;if(!at||ce.type!=="attributes"||!(!((qe=ce.attributeName)===null||qe===void 0)&&qe.startsWith("data-tooltip-")))return;const He=ma(at);ha(He)})}),B={attributes:!0,childList:!1,subtree:!1};if(at){const oe=ma(at);ha(oe),Xt.observe(at,B)}return()=>{Xt.disconnect()}},[da,Ia,Le,e,a]),b.useEffect(()=>{F!=null&&F.border&&console.warn("[react-tooltip] Do not set `style.border`. Use `border` prop instead."),J&&!ei("border",`${J}`)&&console.warn(`[react-tooltip] "${J}" is not a valid \`border\`.`),F!=null&&F.opacity&&console.warn("[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead."),U&&!ei("opacity",`${U}`)&&console.warn(`[react-tooltip] "${U}" is not a valid \`opacity\`.`)},[]);let fa=u;const Fa=b.useRef(null);if(s){const De=s({content:(Le==null?void 0:Le.getAttribute("data-tooltip-content"))||be||null,activeAnchor:Le});fa=De?W.createElement("div",{ref:Fa,className:"react-tooltip-content-wrapper"},De):null}else be&&(fa=be);K&&(fa=W.createElement(pd,{content:K}));const Tn={forwardRef:Ae,id:t,anchorId:e,anchorSelect:a,className:et(i,Tt),classNameArrow:o,content:fa,contentWrapperRef:Fa,place:_e,variant:fe,offset:je,wrapper:te,events:me,openOnClick:g,positionStrategy:Ue,middlewares:S,delayShow:Ie,delayHide:v,float:C,hidden:x,noArrow:E,clickable:M,closeOnEsc:V,closeOnScroll:O,closeOnResize:k,openEvents:Z,closeEvents:Y,globalCloseEvents:N,imperativeModeOnly:L,style:F,position:X,isOpen:ee,defaultIsOpen:I,border:J,opacity:U,arrowColor:ie,setIsOpen:ue,afterShow:Ee,afterHide:Pe,activeAnchor:Le,setActiveAnchor:De=>ua(De),role:ke};return W.createElement(fd,{...Tn})});typeof window<"u"&&window.addEventListener("react-tooltip-inject-styles",t=>{t.detail.disableCore||Qs({css:":root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}",type:"core"}),t.detail.disableBase||Qs({css:`
.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:8px;height:8px}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}`,type:"base"})});const gd=({tooltipTitle:t="",extraClass:e="",onClickHandler:a=()=>{},children:n,isButtonType:r=!0,isDisabled:s=!1,tooltipId:i,tooltipPlace:o="top",tooltipChildren:l=null,tooltipProps:c={}})=>h.jsxs(h.Fragment,{children:[r?h.jsx("button",{type:"button",className:`btn d-flex-important justify-center align-items-center btn-sm text-white restricted-section ${e}`,"data-tooltip-id":i,onClick:a,disabled:s,children:n}):h.jsx("div",{className:`btn btn-sm cursor-pointer restricted-section ${e}`,onClick:a,"data-tooltip-id":i,children:n}),h.jsx(Ss,{id:i,className:"mrdn-tooltip montserrat-regular",content:t,children:t||l,...c,place:o})]}),ft=gd,Lr=({copyTextTitle:t,toastOptions:e={}})=>{const[a,n]=W.useState(!1),r=b.useCallback(async s=>{n(!0),(await el(s)).success&&(e!=null&&e.id?Ze.success(t,e):Ze.success(t),n(!1))},[]);return{copying:a,copyTextToClipboard:r}},ri=t=>{let e;const a=new Set,n=(d,m)=>{const u=typeof d=="function"?d(e):d;if(!Object.is(u,e)){const p=e;e=m??(typeof u!="object"||u===null)?u:Object.assign({},e,u),a.forEach(g=>g(e,p))}},r=()=>e,l={setState:n,getState:r,getInitialState:()=>c,subscribe:d=>(a.add(d),()=>a.delete(d)),destroy:()=>{a.clear()}},c=e=t(n,r,l);return l},vd=t=>t?ri(t):ri;var no={exports:{}},ro={},so={exports:{}},io={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Aa=b;function yd(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var kd=typeof Object.is=="function"?Object.is:yd,bd=Aa.useState,wd=Aa.useEffect,xd=Aa.useLayoutEffect,_d=Aa.useDebugValue;function jd(t,e){var a=e(),n=bd({inst:{value:a,getSnapshot:e}}),r=n[0].inst,s=n[1];return xd(function(){r.value=a,r.getSnapshot=e,Gr(r)&&s({inst:r})},[t,a,e]),wd(function(){return Gr(r)&&s({inst:r}),t(function(){Gr(r)&&s({inst:r})})},[t]),_d(a),a}function Gr(t){var e=t.getSnapshot;t=t.value;try{var a=e();return!kd(t,a)}catch{return!0}}function Sd(t,e){return e()}var zd=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?Sd:jd;io.useSyncExternalStore=Aa.useSyncExternalStore!==void 0?Aa.useSyncExternalStore:zd;so.exports=io;var Ed=so.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Nr=b,Cd=Ed;function Td(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Ad=typeof Object.is=="function"?Object.is:Td,Od=Cd.useSyncExternalStore,Rd=Nr.useRef,Ld=Nr.useEffect,Nd=Nr.useMemo,Md=Nr.useDebugValue;ro.useSyncExternalStoreWithSelector=function(t,e,a,n,r){var s=Rd(null);if(s.current===null){var i={hasValue:!1,value:null};s.current=i}else i=s.current;s=Nd(function(){function l(p){if(!c){if(c=!0,d=p,p=n(p),r!==void 0&&i.hasValue){var g=i.value;if(r(g,p))return m=g}return m=p}if(g=m,Ad(d,p))return g;var _=n(p);return r!==void 0&&r(g,_)?g:(d=p,m=_)}var c=!1,d,m,u=a===void 0?null:a;return[function(){return l(e())},u===null?void 0:function(){return l(u())}]},[e,a,n,r]);var o=Od(t,s[0],s[1]);return Ld(function(){i.hasValue=!0,i.value=o},[o]),Md(o),o};no.exports=ro;var Id=no.exports;const Fd=Jo(Id),{useDebugValue:Pd}=W,{useSyncExternalStoreWithSelector:$d}=Fd;const Vd=t=>t;function Dd(t,e=Vd,a){const n=$d(t.subscribe,t.getState,t.getServerState||t.getInitialState,e,a);return Pd(n),n}const si=t=>{const e=typeof t=="function"?vd(t):t,a=(n,r)=>Dd(e,n,r);return Object.assign(a,e),a},Ma=t=>t?si(t):si,oo=Ma(t=>({keyword:"",isModalOpen:!1,setModalState:(e,a="")=>t({keyword:a,isModalOpen:e})})),qd=b.memo(({isTableFormat:t,keywordName:e,keywordClickHandler:a,shouldSplitWord:n,handleCopyKeyword:r})=>{const s=b.useCallback(()=>{const o=n?e.split(" ")[0]:e;a(o)},[]),i=()=>{const o=n?e.split(" ")[0]:e;r(o)};return t?h.jsx("tr",{className:"table-border-bottom",children:h.jsx("td",{children:h.jsxs("div",{className:"d-flex py-1 gap-1 justify-between text-capitalize cursor-pointer restricted-section",children:[h.jsx("div",{onClick:s,children:e}),h.jsx("div",{className:"d-flex align-items-center cursor-pointer",onClick:i,children:h.jsx(Wt,{size:10})})]})})}):h.jsxs("div",{className:"d-flex keyword-section gap-2 align-items-center main-text cursor-pointer restricted-section",children:[h.jsx("div",{onClick:s,children:e}),h.jsx("div",{className:"d-flex align-items-center cursor-pointer",onClick:i,children:h.jsx(Wt,{size:10})})]})}),Bd=({directKeywords:t,isTableFormat:e=!1,shouldSplitWord:a=!1,hasPermission:n,hasProductSearchPermission:r})=>{const s=t==null?void 0:t.split(","),{setModalState:i}=oo(Ut(m=>m)),o=m=>{!n||!r||i(!0,m)},{copying:l,copyTextToClipboard:c}=Lr({copyTextTitle:"Keyword copied to clipboard!"}),d=async m=>{!n||!m||l||await c(m)};return h.jsx(h.Fragment,{children:(s==null?void 0:s.map((m,u)=>h.jsx(qd,{keywordName:m,keywordClickHandler:o,isTableFormat:e,shouldSplitWord:a,handleCopyKeyword:d},m+u)))||null})},Ud=b.memo(Bd),Hd=({isTableFormat:t=!1,keywordName:e,keywordsCount:a,keywordClickHandler:n,handleCopyKeyword:r})=>{const s=b.useCallback(()=>{n(e)},[]),i=b.useCallback(()=>{r(e)},[]);return t?h.jsxs("tr",{className:"table-border-bottom restricted-section",children:[h.jsx("td",{children:h.jsx("div",{className:"d-flex py-1 text-capitalize cursor-pointer",onClick:s,children:e})}),h.jsx("td",{children:h.jsxs("div",{className:"d-flex gap-2 py-1",children:[a,h.jsx("div",{className:"d-flex align-items-center cursor-pointer",onClick:i,children:h.jsx(Wt,{size:10})})]})})]}):h.jsxs("div",{className:"keyword-section gap-2 main-text cursor-pointer restricted-section",children:[h.jsxs("div",{onClick:s,children:[e," ",a?`(${a})`:null]}),h.jsx("div",{className:"d-flex align-items-center cursor-pointer",onClick:i,children:h.jsx(Wt,{size:10})})]})},Wd=({keywords:t,isTableFormat:e=!1,hasPermission:a,hasProductSearchPermission:n})=>{const r=Array.from(t.keys()),{setModalState:s}=oo(Ut(d=>d)),i=d=>{!a||!n||s(!0,d)},{copying:o,copyTextToClipboard:l}=Lr({copyTextTitle:"Keyword copied to clipboard!"}),c=async d=>{!a||!d||o||await l(d)};return h.jsx(h.Fragment,{children:r!=null&&r.length?r.map((d,m)=>{const u=t.get(d)||1;return h.jsx(Hd,{isTableFormat:e,keywordName:d,keywordsCount:u,keywordClickHandler:i,handleCopyKeyword:c},d+m)}):null})},Kd=Wd,Zd=({text:t,minLineCount:e=3,maxHeight:a="6.0em",extraClass:n=""})=>{const[r,s]=b.useState(!1),[i,o]=b.useState(!0),l=b.useRef(null);b.useEffect(()=>{if(l.current){const u=l.current.clientHeight,p=parseFloat(getComputedStyle(l.current).lineHeight),g=Math.floor(u/p);o(g>e)}},[]);const c=u=>{u.stopPropagation(),u.preventDefault(),s(!r)},d={overflow:"hidden",display:"-webkit-box",WebkitBoxOrient:"vertical",lineHeight:"1.5em",...i?{WebkitLineClamp:r?"initial":e,maxHeight:r?"initial":a}:{}},m=r?"See Less":"See More";return h.jsxs(W.Fragment,{children:[h.jsx("div",{style:d,ref:l,className:`see-more-less ${n}`,children:t}),t&&i?h.jsx("span",{className:"cursor-pointer bg-merch-dominator color-white border-r-1 w-fit px-2 py-1 see-more-less-btn",onClick:c,children:m}):null]})},Za=b.memo(Zd),Gd=({title:t,keywords:e={},extraClass:a="",infoTitle:n="",directKeywords:r="",isFetching:s,uniqueKey:i,hasPermission:o,hasProductSearchPermission:l,directChildren:c=null,handleCopyKeywords:d})=>{const m=e instanceof Map,u=!o,p=()=>{if(u)return;const g=m?[...e.keys()].join(", "):r;d(g)};return h.jsxs("div",{id:i,className:`mrdn-research-entry mrdn-research-keywords gap-y-1 flex-col ${a}`,children:[h.jsxs("span",{className:"with-button-section gap-x-2 d-flex align-items-center",children:[t,":",h.jsxs("div",{className:"d-flex gap-x-2 align-items-center",children:[h.jsx(ft,{tooltipTitle:"Click to copy to clipboard",extraClass:"mrdn-research-info i-p-0",isButtonType:!1,children:h.jsx(Wt,{size:10}),onClickHandler:p,isDisabled:s,tooltipId:wa()}),h.jsx(ft,{tooltipTitle:n,extraClass:"mrdn-research-info i-p-0 d-flex-important align-items-center",isButtonType:!1,children:h.jsx(Bi,{size:14}),tooltipId:wa()})]})]}),c||h.jsx("div",{className:"mrdn-keywords d-flex gap-2 flex-wrap",children:s?h.jsx(aa,{}):r?h.jsx(Za,{text:h.jsx(Ud,{directKeywords:r,hasPermission:o,hasProductSearchPermission:l}),extraClass:"d-flex-important gap-2 flex-wrap",maxHeight:"6.0rem"},`${t}-${i}`):m?h.jsx(Kd,{keywords:e,isTableFormat:!1,hasPermission:o,hasProductSearchPermission:l}):null})]})},Mn=b.memo(Gd),Yd=({show:t,component:e})=>h.jsx(W.Fragment,{children:t?e:null}),Vt=Yd,Jd=({title:t,uniqueKey:e,shouldShow:a=!0,loading:n,children:r,mainContainerClass:s="",valueClass:i=""})=>h.jsx(Vt,{show:a,component:h.jsxs("div",{id:`${t}-${e}`,className:`mrdn-research-entry ${s}`,children:[h.jsxs("span",{className:"w-fit text-nowrap",children:[t,": "]}),h.jsxs("span",{className:`montserrat-bold ${i} ${n?"mrdn-skeleton":""}`,children:[" ",n?h.jsx(aa,{}):r]})]})},e),Qe=Jd,lo=({value:t,decimal:e=0,isPercentValue:a=!1})=>{const n=e?+t.toFixed(e):t;return h.jsxs(W.Fragment,{children:[Ya(n),a?"%":null]})};var Ft={},co={};co.dutch=["aan","af","al","alles","als","altijd","andere","ben","bij","daar","dan","dat","de","der","deze","die","dit","doch","doen","door","dus","een","eens","en","er","ge","geen","geweest","haar","had","heb","hebben","heeft","hem","het","hier","hij ","hoe","hun","iemand","iets","ik","in","is","ja","je ","kan","kon","kunnen","maar","me","meer","men","met","mij","mijn","moet","na","naar","niet","niets","nog","nu","of","om","omdat","ons","ook","op","over","reeds","te","tegen","toch","toen","tot","u","uit","uw","van","veel","voor","want","waren","was","wat","we","wel","werd","wezen","wie","wij","wil","worden","zal","ze","zei","zelf","zich","zij","zijn","zo","zonder","zou"];var uo={};uo.english=["a","able","about","above","abroad","according","accordingly","across","actually","adj","after","afterwards","again","against","ago","ahead","aint","all","allow","allows","almost","alone","along","alongside","already","also","although","always","am","amid","amidst","among","amongst","an","and","another","any","anybody","anyhow","anyone","anything","anyway","anyways","anywhere","apart","appear","appreciate","appropriate","are","arent","around","as","as","aside","ask","asking","associated","at","available","away","awfully","b","back","backward","backwards","be","became","because","become","becomes","becoming","been","before","beforehand","begin","behind","being","believe","below","beside","besides","best","better","between","beyond","both","brief","but","by","c","came","can","cannot","cant","cant","caption","cause","causes","certain","certainly","changes","clearly","cmon","co","co.","com","come","comes","concerning","consequently","consider","considering","contain","containing","contains","corresponding","could","couldnt","course","cs","currently","d","dare","darent","definitely","described","despite","did","didnt","different","directly","do","does","doesnt","doing","done","dont","down","downwards","during","e","each","edu","eg","eight","eighty","either","else","elsewhere","end","ending","enough","entirely","especially","et","etc","even","ever","evermore","every","everybody","everyone","everything","everywhere","ex","exactly","example","except","f","fairly","far","farther","few","fewer","fifth","first","five","followed","following","follows","for","forever","former","formerly","forth","forward","found","four","from","further","furthermore","g","get","gets","getting","given","gives","go","goes","going","gone","got","gotten","greetings","h","had","hadnt","half","happens","hardly","has","hasnt","have","havent","having","he","hed","hell","hello","help","hence","her","here","hereafter","hereby","herein","heres","hereupon","hers","herself","hes","hi","him","himself","his","hither","hopefully","how","howbeit","however","hundred","i","id","ie","if","ignored","ill","im","immediate","in","inasmuch","inc","inc.","indeed","indicate","indicated","indicates","inner","inside","insofar","instead","into","inward","is","isnt","it","itd","itll","its","its","itself","ive","j","just","k","keep","keeps","kept","know","known","knows","l","last","lately","later","latter","latterly","least","less","lest","let","lets","like","liked","likely","likewise","little","look","looking","looks","low","lower","ltd","m","made","mainly","make","makes","many","may","maybe","maynt","me","mean","meantime","meanwhile","merely","might","mightnt","mine","minus","miss","more","moreover","most","mostly","mr","mrs","much","must","mustnt","my","myself","n","name","namely","nd","near","nearly","necessary","need","neednt","needs","neither","never","neverf","neverless","nevertheless","new","next","nine","ninety","no","nobody","non","none","nonetheless","noone","no-one","nor","normally","not","nothing","notwithstanding","novel","now","nowhere","o","obviously","of","off","often","oh","ok","okay","old","on","once","one","ones","ones","only","onto","opposite","or","other","others","otherwise","ought","oughtnt","our","ours","ourselves","out","outside","over","overall","own","p","particular","particularly","past","per","perhaps","placed","please","plus","possible","presumably","probably","provided","provides","q","que","quite","qv","r","rather","rd","re","really","reasonably","recent","recently","regarding","regardless","regards","relatively","respectively","right","round","s","said","same","saw","say","saying","says","second","secondly","see","seeing","seem","seemed","seeming","seems","seen","self","selves","sensible","sent","serious","seriously","seven","several","shall","shant","she","shed","shell","shes","should","shouldnt","since","six","so","some","somebody","someday","somehow","someone","something","sometime","sometimes","somewhat","somewhere","soon","sorry","specified","specify","specifying","still","sub","such","sup","sure","t","take","taken","taking","tell","tends","th","than","thank","thanks","thanx","that","thatll","thats","thats","thatve","the","their","theirs","them","themselves","then","thence","there","thereafter","thereby","thered","therefore","therein","therell","therere","theres","theres","thereupon","thereve","these","they","theyd","theyll","theyre","theyve","thing","things","think","third","thirty","this","thorough","thoroughly","those","though","three","through","throughout","thru","thus","till","to","together","too","took","toward","towards","tried","tries","truly","try","trying","ts","twice","two","u","un","under","underneath","undoing","unfortunately","unless","unlike","unlikely","until","unto","up","upon","upwards","us","use","used","useful","uses","using","usually","v","value","various","versus","very","via","viz","vs","w","want","wants","was","wasnt","way","we","wed","welcome","well","well","went","were","were","werent","weve","what","whatever","whatll","whats","whatve","when","whence","whenever","where","whereafter","whereas","whereby","wherein","wheres","whereupon","wherever","whether","which","whichever","while","whilst","whither","who","whod","whoever","whole","wholl","whom","whomever","whos","whose","why","will","willing","wish","with","within","without","wonder","wont","would","wouldnt","x","y","yes","yet","you","youd","youll","your","youre","yours","yourself","yourselves","youve","z","zero"];var mo={};mo.french=["au","aux","avec","ce","ces","dans","de","des","du","elle","en","et","eux","il","je","la","le","leur","lui","ma","mais","me","même","mes","moi","mon","ne","nos","notre","nous","on","ou","par","pas","pour","qu","que","qui","sa","se","ses","son","sur","ta","te","tes","toi","ton","tu","un","une","vos","votre","vous","c","d","j","l","à","m","n","s","t","y","été","étée","étées","étés","étant","suis","es","est","sommes","êtes","sont","serai","seras","sera","serons","serez","seront","serais","serait","serions","seriez","seraient","étais","était","étions","étiez","étaient","fus","fut","fûmes","fûtes","furent","sois","soit","soyons","soyez","soient","fusse","fusses","fût","fussions","fussiez","fussent","ayant","eu","eue","eues","eus","ai","as","avons","avez","ont","aurai","auras","aura","aurons","aurez","auront","aurais","aurait","aurions","auriez","auraient","avais","avait","avions","aviez","avaient","eut","eûmes","eûtes","eurent","aie","aies","ait","ayons","ayez","aient","eusse","eusses","eût","eussions","eussiez","eussent","ceci","cela","celà","cet","cette","ici","ils","les","leurs","quel","quels","quelle","quelles","sans","soi"];var ho={};ho.german=["aber","alle","allem","allen","aller","alles","als","also","am","an","ander","andere","anderem","anderen","anderer","anderes","anderm","andern","anderr","anders","auch","auf","aus","bei","bin","bis","bist","da","dadurch","daher","damit","dann","darum","das","dass","dasselbe","dazu","daß","dein","deine","deinem","deinen","deiner","deines","dem","demselben","den","denn","denselben","der","derer","derselbe","derselben","des","deshalb","desselben","dessen","dich","die","dies","diese","dieselbe","dieselben","diesem","diesen","dieser","dieses","dir","doch","dort","du","durch","ein","eine","einem","einen","einer","eines","einig","einige","einigem","einigen","einiger","einiges","einmal","er","es","etwas","euch","euer","eure","eurem","euren","eurer","eures","für","gegen","gewesen","hab","habe","haben","hat","hatte","hatten","hattest","hattet","hier","hin","hinter","ich","ihm","ihn","ihnen","ihr","ihre","ihrem","ihren","ihrer","ihres","im","in","indem","ins","ist","ja","jede","jedem","jeden","jeder","jedes","jene","jenem","jenen","jener","jenes","jetzt","kann","kannst","kein","keine","keinem","keinen","keiner","keines","können","könnt","könnte","machen","man","manche","manchem","manchen","mancher","manches","mein","meine","meinem","meinen","meiner","meines","mich","mir","mit","muss","musst","musste","muß","mußt","müssen","müßt","nach","nachdem","nein","nicht","nichts","noch","nun","nur","ob","oder","ohne","sehr","seid","sein","seine","seinem","seinen","seiner","seines","selbst","sich","sie","sind","so","solche","solchem","solchen","solcher","solches","soll","sollen","sollst","sollt","sollte","sondern","sonst","soweit","sowie","um","und","uns","unse","unsem","unsen","unser","unsere","unses","unter","viel","vom","von","vor","wann","war","waren","warst","warum","was","weg","weil","weiter","weitere","welche","welchem","welchen","welcher","welches","wenn","wer","werde","werden","werdet","weshalb","wie","wieder","wieso","will","wir","wird","wirst","wo","woher","wohin","wollen","wollte","während","würde","würden","zu","zum","zur","zwar","zwischen","über"];var fo={};fo.spanish=["a","un","una","unas","unos","uno","sobre","de","todo","también","tras","otro","algún","alguno","alguna","algunos","algunas","ser","es","soy","eres","somos","sois","esto","estoy","esta","estamos","estais","estan","como","en","para","atras","porque","por qué","estado","estaba","ante","antes","siendo","ambos","pero","por","no","poder","sal","al","puede","puedo","más","ya","le","o","me","hasta","durante","ni","ese","contra","eso","mí","mi","el","él","podemos","podeis","pueden","fui","fue","fuimos","fueron","hacer","hago","hace","hacemos","haceis","hacen","cada","fin","incluso","primero","desde","conseguir","consigo","consigue","consigues","conseguimos","consiguen","ir","voy","va","vamos","vais","van","vaya","gueno","ha","tener","tengo","tiene","tenemos","teneis","tienen","la","lo","las","los","su","aqui","mio","poco","tu","tú","te","si","sí","tuyo","ellos","ella","y","del","se","ellas","nos","nosotros","vosotros","vosotras","si","dentro","solo","solamente","saber","sabes","sabe","sabemos","sabeis","saben","ultimo","largo","bastante","haces","muchos","aquellos","aquellas","sus","entonces","tiempo","verdad","verdadero","verdadera","cierto","ciertos","cierta","ciertas","intentar","intento","intenta","intentas","intentamos","intentais","intentan","dos","bajo","arriba","encima","usar","uso","usas","usa","usamos","usais","usan","emplear","empleo","empleas","emplean","ampleamos","empleais","valor","muy","era","eras","eramos","eran","modo","bien","cual","cuando","donde","mientras","quien","con","entre","sin","trabajo","trabajar","trabajas","trabaja","trabajamos","trabajais","trabajan","podria","podrias","podriamos","podrian","podriais","yo","aquel","que","1","2","3","4","5","6","7","8","9","0"];var po={};po.italian=["a","abbastanza","abbia","abbiamo","abbiano","abbiate","accidenti","ad","adesso","affinche","agl","agli","ahime","ahimè","ahimé","ai","al","alcuna","alcuni","alcuno","all","alla","alle","allo","allora","altre","altri","altrimenti","altro","altrove","altrui","anche","ancora","anni","anno","ansa","anticipo","assai","attesa","attraverso","avanti","avemmo","avendo","avente","aver","avere","averlo","avesse","avessero","avessi","avessimo","aveste","avesti","avete","aveva","avevamo","avevano","avevate","avevi","avevo","avra","avrai","avranno","avrebbe","avrebbero","avrei","avremmo","avremo","avreste","avresti","avrete","avro","avrà","avrò","avuta","avute","avuti","avuto","basta","ben","bene","benissimo","brava","bravo","buono","c","casa","caso","cento","certa","certe","certi","certo","che","chi","chicchessia","chiunque","ci","ciascuna","ciascuno","cima","cinque","cio","cioe","cioè","cioé","circa","citta","città","ciò","co","codesta","codesti","codesto","cogli","coi","col","colei","coll","coloro","colui","come","cominci","comprare","comunque","con","concernente","conciliarsi","conclusione","consecutivi","consecutivo","consiglio","contro","cortesia","cos","cosa","cosi","così","cui","d","da","dagl","dagli","dai","dal","dall","dalla","dalle","dallo","dappertutto","davanti","debba","degl","degli","dei","del","dell","della","delle","dello","dentro","detto","deve","devi","devo","di","dice","dietro","dire","dirimpetto","diventa","diventare","diventato","dobbiamo","dobbiate","dopo","doppio","dov","dove","dovemmo","dovendo","dovere","dovesse","dovessero","dovessi","dovessimo","doveste","dovesti","dovete","dovette","dovettero","dovetti","doveva","dovevamo","dovevano","dovevate","dovevi","dovevo","dovra","dovrai","dovranno","dovrebbe","dovrebbero","dovrei","dovremmo","dovremo","dovreste","dovresti","dovrete","dovrà","dovrò","dovunque","due","dunque","durante","e","ebbe","ebbero","ebbi","ecc","ecco","ed","effettivamente","egli","ella","entrambi","eppure","era","erano","eravamo","eravate","eri","ero","esempio","esse","essendo","esser","essere","essi","ex","fa","faccia","facciamo","facciano","facciate","faccio","facemmo","facendo","facesse","facessero","facessi","facessimo","faceste","facesti","faceva","facevamo","facevano","facevate","facevi","facevo","fai","fanno","farai","faranno","fare","farebbe","farebbero","farei","faremmo","faremo","fareste","faresti","farete","farà","farò","fatto","favore","fece","fecero","feci","fin","finalmente","finche","fine","fino","forse","forza","fosse","fossero","fossi","fossimo","foste","fosti","fra","frattempo","fu","fui","fummo","fuori","furono","futuro","generale","gente","gia","giacche","giorni","giorno","giu","già","giã","gli","gliela","gliele","glieli","glielo","gliene","governo","grande","grazie","gruppo","ha","haha","hai","hanno","ho","i","ie","ieri","il","improvviso","in","inc","indietro","infatti","inoltre","insieme","intanto","intorno","invece","io","l","là","lasciato","lato","lavoro","le","lei","li","lo","lontano","loro","lui","lungo","luogo","lì","ma","macche","magari","maggior","mai","male","malgrado","malissimo","mancanza","marche","me","medesimo","mediante","meglio","meno","mentre","mesi","mezzo","mi","mia","mie","miei","mila","miliardi","milioni","minimi","ministro","mio","modo","molta","molte","molti","moltissimo","molto","momento","mondo","mosto","nazionale","ne","negl","negli","nei","nel","nell","nella","nelle","nello","nemmeno","neppure","nessun","nessuna","nessuno","niente","no","noi","nome","non","nondimeno","nonostante","nonsia","nostra","nostre","nostri","nostro","novanta","nove","nulla","nuovi","nuovo","o","od","oggi","ogni","ognuna","ognuno","oltre","oppure","ora","ore","osi","ossia","ottanta","otto","paese","parecchi","parecchie","parecchio","parte","partendo","peccato","peggio","per","perche","perchè","perché","percio","perciò","perfino","pero","persino","persone","però","piedi","pieno","piglia","piu","piuttosto","più","po","pochissimo","poco","poi","poiche","possa","possedere","posteriore","posto","potrebbe","preferibilmente","presa","press","prima","primo","principalmente","probabilmente","promesso","proprio","puo","pure","purtroppo","può","qua","qualche","qualcosa","qualcuna","qualcuno","quale","quali","qualunque","quando","quanta","quante","quanti","quanto","quantunque","quarto","quasi","quattro","quel","quella","quelle","quelli","quello","quest","questa","queste","questi","questo","qui","quindi","quinto","realmente","recente","recentemente","registrazione","relativo","riecco","rispetto","salvo","sara","sarai","saranno","sarebbe","sarebbero","sarei","saremmo","saremo","sareste","saresti","sarete","saro","sarà","sarò","scola","scopo","scorso","se","secondo","seguente","seguito","sei","sembra","sembrare","sembrato","sembrava","sembri","sempre","senza","sette","si","sia","siamo","siano","siate","siete","sig","solito","solo","soltanto","sono","sopra","soprattutto","sotto","spesso","srl","sta","stai","stando","stanno","starai","staranno","starebbe","starebbero","starei","staremmo","staremo","stareste","staresti","starete","starà","starò","stata","state","stati","stato","stava","stavamo","stavano","stavate","stavi","stavo","stemmo","stessa","stesse","stessero","stessi","stessimo","stesso","steste","stesti","stette","stettero","stetti","stia","stiamo","stiano","stiate","sto","su","sua","subito","successivamente","successivo","sue","sugl","sugli","sui","sul","sull","sulla","sulle","sullo","suo","suoi","tale","tali","talvolta","tanto","te","tempo","terzo","th","ti","titolo","torino","tra","tranne","tre","trenta","triplo","troppo","trovato","tu","tua","tue","tuo","tuoi","tutta","tuttavia","tutte","tutti","tutto","uguali","ulteriore","ultimo","un","una","uno","uomo","va","vai","vale","vari","varia","varie","vario","verso","vi","via","vicino","visto","vita","voi","volta","volte","vostra","vostre","vostri","vostro","è","è"];var Qd=Ft.dutch=co.dutch,Xd=Ft.english=uo.english,em=Ft.french=mo.french,tm=Ft.german=ho.german,am=Ft.spanish=fo.spanish,nm=Ft.italian=po.italian;const rm=uc({__proto__:null,default:Ft,dutch:Qd,english:Xd,french:em,german:tm,italian:nm,spanish:am},[Ft]),sm=["0","1","2","3","4","5","6","7","8","9"],im=["۱","۲","۳","۴","۵","۶","۷","۸","۹","۰"],om=["０","１","２","３","４","５","６","７","８","９"],lm=["၀","၁","၂","၃","၄","၅","၆","၇","၈","၉"],cm=["౦","౧","౨","౩","౪","౫","౬","౭","౮","౯"],um=[...sm,...im,...om,...lm,...cm],dm=["die","het","en","sy","nie","was","hy","te","is","ek","om","hulle","in","my","'n","vir","toe","haar","van","dit","op","se","wat","met","gaan","baie","ons","jy","na","maar","hom","so","n","huis","kan","aan","dat","daar","sal","jou","gesê","by","kom","een","ma","as","son","groot","begin","al"],mm=["،","ّآض","آمينَ","آه","آهاً","آي","أ","أب","أجل","أجمع","أخ","أخذ","أصبح","أضحى","أقبل","أقل","أكثر","ألا","أم","أما","أمامك","أمامكَ","أمسى","أمّا","أن","أنا","أنت","أنتم","أنتما","أنتن","أنتِ","أنشأ","أنّى","أو","أوشك","أولئك","أولئكم","أولاء","أولالك","أوّهْ","أي","أيا","أين","أينما","أيّ","أَنَّ","أََيُّ","أُفٍّ","إذ","إذا","إذاً","إذما","إذن","إلى","إليكم","إليكما","إليكنّ","إليكَ","إلَيْكَ","إلّا","إمّا","إن","إنّما","إي","إياك","إياكم","إياكما","إياكن","إيانا","إياه","إياها","إياهم","إياهما","إياهن","إياي","إيهٍ","إِنَّ","ا","ابتدأ","اثر","اجل","احد","اخرى","اخلولق","اذا","اربعة","ارتدّ","استحال","اطار","اعادة","اعلنت","اف","اكثر","اكد","الألاء","الألى","الا","الاخيرة","الان","الاول","الاولى","التى","التي","الثاني","الثانية","الذاتي","الذى","الذي","الذين","السابق","الف","اللائي","اللاتي","اللتان","اللتيا","اللتين","اللذان","اللذين","اللواتي","الماضي","المقبل","الوقت","الى","اليوم","اما","امام","امس","ان","انبرى","انقلب","انه","انها","او","اول","اي","ايار","ايام","ايضا","ب","بات","باسم","بان","بخٍ","برس","بسبب","بسّ","بشكل","بضع","بطآن","بعد","بعض","بك","بكم","بكما","بكن","بل","بلى","بما","بماذا","بمن","بن","بنا","به","بها","بي","بيد","بين","بَسْ","بَلْهَ","بِئْسَ","تانِ","تانِك","تبدّل","تجاه","تحوّل","تلقاء","تلك","تلكم","تلكما","تم","تينك","تَيْنِ","تِه","تِي","ثلاثة","ثم","ثمّ","ثمّة","ثُمَّ","جعل","جلل","جميع","جير","حار","حاشا","حاليا","حاي","حتى","حرى","حسب","حم","حوالى","حول","حيث","حيثما","حين","حيَّ","حَبَّذَا","حَتَّى","حَذارِ","خلا","خلال","دون","دونك","ذا","ذات","ذاك","ذانك","ذانِ","ذلك","ذلكم","ذلكما","ذلكن","ذو","ذوا","ذواتا","ذواتي","ذيت","ذينك","ذَيْنِ","ذِه","ذِي","راح","رجع","رويدك","ريث","رُبَّ","زيارة","سبحان","سرعان","سنة","سنوات","سوف","سوى","سَاءَ","سَاءَمَا","شبه","شخصا","شرع","شَتَّانَ","صار","صباح","صفر","صهٍ","صهْ","ضد","ضمن","طاق","طالما","طفق","طَق","ظلّ","عاد","عام","عاما","عامة","عدا","عدة","عدد","عدم","عسى","عشر","عشرة","علق","على","عليك","عليه","عليها","علًّ","عن","عند","عندما","عوض","عين","عَدَسْ","عَمَّا","غدا","غير","ـ","ف","فان","فلان","فو","فى","في","فيم","فيما","فيه","فيها","قال","قام","قبل","قد","قطّ","قلما","قوة","كأنّما","كأين","كأيّ","كأيّن","كاد","كان","كانت","كذا","كذلك","كرب","كل","كلا","كلاهما","كلتا","كلم","كليكما","كليهما","كلّما","كلَّا","كم","كما","كي","كيت","كيف","كيفما","كَأَنَّ","كِخ","لئن","لا","لات","لاسيما","لدن","لدى","لعمر","لقاء","لك","لكم","لكما","لكن","لكنَّما","لكي","لكيلا","للامم","لم","لما","لمّا","لن","لنا","له","لها","لو","لوكالة","لولا","لوما","لي","لَسْتَ","لَسْتُ","لَسْتُم","لَسْتُمَا","لَسْتُنَّ","لَسْتِ","لَسْنَ","لَعَلَّ","لَكِنَّ","لَيْتَ","لَيْسَ","لَيْسَا","لَيْسَتَا","لَيْسَتْ","لَيْسُوا","لَِسْنَا","ما","ماانفك","مابرح","مادام","ماذا","مازال","مافتئ","مايو","متى","مثل","مذ","مساء","مع","معاذ","مقابل","مكانكم","مكانكما","مكانكنّ","مكانَك","مليار","مليون","مما","ممن","من","منذ","منها","مه","مهما","مَنْ","مِن","نحن","نحو","نعم","نفس","نفسه","نهاية","نَخْ","نِعِمّا","نِعْمَ","ها","هاؤم","هاكَ","هاهنا","هبّ","هذا","هذه","هكذا","هل","هلمَّ","هلّا","هم","هما","هن","هنا","هناك","هنالك","هو","هي","هيا","هيت","هيّا","هَؤلاء","هَاتانِ","هَاتَيْنِ","هَاتِه","هَاتِي","هَجْ","هَذا","هَذانِ","هَذَيْنِ","هَذِه","هَذِي","هَيْهَاتَ","و","وا","واحد","واضاف","واضافت","واكد","وان","واهاً","واوضح","وراءَك","وفي","وقال","وقالت","وقد","وقف","وكان","وكانت","ولا","ولم","ومن","وهو","وهي","ويكأنّ","وَيْ","وُشْكَانََ","يكون","يمكن","يوم","ّأيّان"],hm=["այդ","այլ","այն","այս","դու","դուք","եմ","են","ենք","ես","եք","է","էի","էին","էինք","էիր","էիք","էր","ըստ","թ","ի","ին","իսկ","իր","կամ","համար","հետ","հետո","մենք","մեջ","մի","ն","նա","նաև","նրա","նրանք","որ","որը","որոնք","որպես","ու","ում","պիտի","վրա","և"],fm=["al","anitz","arabera","asko","baina","bat","batean","batek","bati","batzuei","batzuek","batzuetan","batzuk","bera","beraiek","berau","berauek","bere","berori","beroriek","beste","bezala","da","dago","dira","ditu","du","dute","edo","egin","ere","eta","eurak","ez","gainera","gu","gutxi","guzti","haiei","haiek","haietan","hainbeste","hala","han","handik","hango","hara","hari","hark","hartan","hau","hauei","hauek","hauetan","hemen","hemendik","hemengo","hi","hona","honek","honela","honetan","honi","hor","hori","horiei","horiek","horietan","horko","horra","horrek","horrela","horretan","horri","hortik","hura","izan","ni","noiz","nola","non","nondik","nongo","nor","nora","ze","zein","zen","zenbait","zenbat","zer","zergatik","ziren","zituen","zu","zuek","zuen","zuten"],pm=["অতএব","অথচ","অথবা","অনুযায়ী","অনেক","অনেকে","অনেকেই","অন্তত","অন্য","অবধি","অবশ্য","অর্থাত","আই","আগামী","আগে","আগেই","আছে","আজ","আদ্যভাগে","আপনার","আপনি","আবার","আমরা","আমাকে","আমাদের","আমার","আমি","আর","আরও","ই","ইত্যাদি","ইহা","উচিত","উত্তর","উনি","উপর","উপরে","এ","এঁদের","এঁরা","এই","একই","একটি","একবার","একে","এক্","এখন","এখনও","এখানে","এখানেই","এটা","এটাই","এটি","এত","এতটাই","এতে","এদের","এব","এবং","এবার","এমন","এমনকী","এমনি","এর","এরা","এল","এস","এসে","ঐ","ও","ওঁদের","ওঁর","ওঁরা","ওই","ওকে","ওখানে","ওদের","ওর","ওরা","কখনও","কত","কবে","কমনে","কয়েক","কয়েকটি","করছে","করছেন","করতে","করবে","করবেন","করলে","করলেন","করা","করাই","করায়","করার","করি","করিতে","করিয়া","করিয়ে","করে","করেই","করেছিলেন","করেছে","করেছেন","করেন","কাউকে","কাছ","কাছে","কাজ","কাজে","কারও","কারণ","কি","কিংবা","কিছু","কিছুই","কিন্তু","কী","কে","কেউ","কেউই","কেখা","কেন","কোটি","কোন","কোনও","কোনো","ক্ষেত্রে","কয়েক","খুব","গিয়ে","গিয়েছে","গিয়ে","গুলি","গেছে","গেল","গেলে","গোটা","চলে","চান","চায়","চার","চালু","চেয়ে","চেষ্টা","ছাড়া","ছাড়াও","ছিল","ছিলেন","জন","জনকে","জনের","জন্য","জন্যওজে","জানতে","জানা","জানানো","জানায়","জানিয়ে","জানিয়েছে","জে","জ্নজন","টি","ঠিক","তখন","তত","তথা","তবু","তবে","তা","তাঁকে","তাঁদের","তাঁর","তাঁরা","তাঁাহারা","তাই","তাও","তাকে","তাতে","তাদের","তার","তারপর","তারা","তারৈ","তাহলে","তাহা","তাহাতে","তাহার","তিনঐ","তিনি","তিনিও","তুমি","তুলে","তেমন","তো","তোমার","থাকবে","থাকবেন","থাকা","থাকায়","থাকে","থাকেন","থেকে","থেকেই","থেকেও","দিকে","দিতে","দিন","দিয়ে","দিয়েছে","দিয়েছেন","দিলেন","দু","দুই","দুটি","দুটো","দেওয়া","দেওয়ার","দেওয়া","দেখতে","দেখা","দেখে","দেন","দেয়","দ্বারা","ধরা","ধরে","ধামার","নতুন","নয়","না","নাই","নাকি","নাগাদ","নানা","নিজে","নিজেই","নিজেদের","নিজের","নিতে","নিয়ে","নিয়ে","নেই","নেওয়া","নেওয়ার","নেওয়া","নয়","পক্ষে","পর","পরে","পরেই","পরেও","পর্যন্ত","পাওয়া","পাচ","পারি","পারে","পারেন","পি","পেয়ে","পেয়্র্","প্রতি","প্রথম","প্রভৃতি","প্রযন্ত","প্রাথমিক","প্রায়","প্রায়","ফলে","ফিরে","ফের","বক্তব্য","বদলে","বন","বরং","বলতে","বলল","বললেন","বলা","বলে","বলেছেন","বলেন","বসে","বহু","বা","বাদে","বার","বি","বিনা","বিভিন্ন","বিশেষ","বিষয়টি","বেশ","বেশি","ব্যবহার","ব্যাপারে","ভাবে","ভাবেই","মতো","মতোই","মধ্যভাগে","মধ্যে","মধ্যেই","মধ্যেও","মনে","মাত্র","মাধ্যমে","মোট","মোটেই","যখন","যত","যতটা","যথেষ্ট","যদি","যদিও","যা","যাঁর","যাঁরা","যাওয়া","যাওয়ার","যাওয়া","যাকে","যাচ্ছে","যাতে","যাদের","যান","যাবে","যায়","যার","যারা","যিনি","যে","যেখানে","যেতে","যেন","যেমন","র","রকম","রয়েছে","রাখা","রেখে","লক্ষ","শুধু","শুরু","সঙ্গে","সঙ্গেও","সব","সবার","সমস্ত","সম্প্রতি","সহ","সহিত","সাধারণ","সামনে","সি","সুতরাং","সে","সেই","সেখান","সেখানে","সেটা","সেটাই","সেটাও","সেটি","স্পষ্ট","স্বয়ং","হইতে","হইবে","হইয়া","হওয়া","হওয়ায়","হওয়ার","হচ্ছে","হত","হতে","হতেই","হন","হবে","হবেন","হয়","হয়তো","হয়নি","হয়ে","হয়েই","হয়েছিল","হয়েছে","হয়েছেন","হল","হলে","হলেই","হলেও","হলো","হাজার","হিসাবে","হৈলে","হোক","হয়"],gm=["a","ainda","alem","ambas","ambos","antes","ao","aonde","aos","apos","aquele","aqueles","as","assim","com","como","contra","contudo","cuja","cujas","cujo","cujos","da","das","de","dela","dele","deles","demais","depois","desde","desta","deste","dispoe","dispoem","diversa","diversas","diversos","do","dos","durante","e","ela","elas","ele","eles","em","entao","entre","essa","essas","esse","esses","esta","estas","este","estes","ha","isso","isto","logo","mais","mas","mediante","menos","mesma","mesmas","mesmo","mesmos","na","nao","nas","nem","nesse","neste","nos","o","os","ou","outra","outras","outro","outros","pelas","pelo","pelos","perante","pois","por","porque","portanto","propios","proprio","quais","qual","qualquer","quando","quanto","que","quem","quer","se","seja","sem","sendo","seu","seus","sob","sobre","sua","suas","tal","tambem","teu","teus","toda","todas","todo","todos","tua","tuas","tudo","um","uma","umas","uns"],vm=["а","автентичен","аз","ако","ала","бе","без","беше","би","бивш","бивша","бившо","бил","била","били","било","благодаря","близо","бъдат","бъде","бяха","в","вас","ваш","ваша","вероятно","вече","взема","ви","вие","винаги","внимава","време","все","всеки","всички","всичко","всяка","във","въпреки","върху","г","ги","главен","главна","главно","глас","го","година","години","годишен","д","да","дали","два","двама","двамата","две","двете","ден","днес","дни","до","добра","добре","добро","добър","докато","докога","дори","досега","доста","друг","друга","други","е","евтин","едва","един","една","еднаква","еднакви","еднакъв","едно","екип","ето","живот","за","забавям","зад","заедно","заради","засега","заспал","затова","защо","защото","и","из","или","им","има","имат","иска","й","каза","как","каква","какво","както","какъв","като","кога","когато","което","които","кой","който","колко","която","къде","където","към","лесен","лесно","ли","лош","м","май","малко","ме","между","мек","мен","месец","ми","много","мнозина","мога","могат","може","мокър","моля","момента","му","н","на","над","назад","най","направи","напред","например","нас","не","него","нещо","нея","ни","ние","никой","нито","нищо","но","нов","нова","нови","новина","някои","някой","няколко","няма","обаче","около","освен","особено","от","отгоре","отново","още","пак","по","повече","повечето","под","поне","поради","после","почти","прави","пред","преди","през","при","пък","първата","първи","първо","пъти","равен","равна","с","са","сам","само","се","сега","си","син","скоро","след","следващ","сме","смях","според","сред","срещу","сте","съм","със","също","т","т.н.","тази","така","такива","такъв","там","твой","те","тези","ти","то","това","тогава","този","той","толкова","точно","три","трябва","тук","тъй","тя","тях","у","утре","харесва","хиляди","ч","часа","че","често","чрез","ще","щом","юмрук","я","як"],ym=["a","abans","ací","ah","així","això","al","aleshores","algun","alguna","algunes","alguns","alhora","allà","allí","allò","als","altra","altre","altres","amb","ambdues","ambdós","apa","aquell","aquella","aquelles","aquells","aquest","aquesta","aquestes","aquests","aquí","baix","cada","cadascuna","cadascunes","cadascuns","cadascú","com","contra","d'un","d'una","d'unes","d'uns","dalt","de","del","dels","des","després","dins","dintre","donat","doncs","durant","e","eh","el","els","em","en","encara","ens","entre","eren","es","esta","estaven","esteu","està","estàvem","estàveu","et","etc","ets","fins","fora","gairebé","ha","han","has","havia","he","hem","heu","hi","ho","i","igual","iguals","ja","l'hi","la","les","li","li'n","llavors","m'he","ma","mal","malgrat","mateix","mateixa","mateixes","mateixos","me","mentre","meu","meus","meva","meves","molt","molta","moltes","molts","mon","mons","més","n'he","n'hi","ne","ni","no","nogensmenys","només","nosaltres","nostra","nostre","nostres","o","oh","oi","on","pas","pel","pels","per","perquè","però","poc","poca","pocs","poques","potser","propi","qual","quals","quan","quant","que","quelcom","qui","quin","quina","quines","quins","què","s'ha","s'han","sa","semblant","semblants","ses","seu","seus","seva","seves","si","sobre","sobretot","solament","sols","son","sons","sota","sou","sóc","són","t'ha","t'han","t'he","ta","tal","també","tampoc","tan","tant","tanta","tantes","teu","teus","teva","teves","ton","tons","tot","tota","totes","tots","un","una","unes","uns","us","va","vaig","vam","van","vas","veu","vosaltres","vostra","vostre","vostres","érem","éreu","és"],km=["的","地","得","和","跟","与","及","向","并","等","更","已","含","做","我","你","他","她","们","某","该","各","每","这","那","哪","什","么","谁","年","月","日","时","分","秒","几","多","来","在","就","又","很","呢","吧","吗","了","嘛","哇","儿","哼","啊","嗯","是","着","都","不","说","也","看","把","还","个","有","小","到","一","为","中","于","对","会","之","第","此","或","共","按","请"],bm=["a","ako","ali","bi","bih","bila","bili","bilo","bio","bismo","biste","biti","bumo","da","do","duž","ga","hoće","hoćemo","hoćete","hoćeš","hoću","i","iako","ih","ili","iz","ja","je","jedna","jedne","jedno","jer","jesam","jesi","jesmo","jest","jeste","jesu","jim","joj","još","ju","kada","kako","kao","koja","koje","koji","kojima","koju","kroz","li","me","mene","meni","mi","mimo","moj","moja","moje","mu","na","nad","nakon","nam","nama","nas","naš","naša","naše","našeg","ne","nego","neka","neki","nekog","neku","nema","netko","neće","nećemo","nećete","nećeš","neću","nešto","ni","nije","nikoga","nikoje","nikoju","nisam","nisi","nismo","niste","nisu","njega","njegov","njegova","njegovo","njemu","njezin","njezina","njezino","njih","njihov","njihova","njihovo","njim","njima","njoj","nju","no","o","od","odmah","on","ona","oni","ono","ova","pa","pak","po","pod","pored","prije","s","sa","sam","samo","se","sebe","sebi","si","smo","ste","su","sve","svi","svog","svoj","svoja","svoje","svom","ta","tada","taj","tako","te","tebe","tebi","ti","to","toj","tome","tu","tvoj","tvoja","tvoje","u","uz","vam","vama","vas","vaš","vaša","vaše","već","vi","vrlo","za","zar","će","ćemo","ćete","ćeš","ću","što"],wm=["a","aby","ahoj","aj","ale","anebo","ani","ano","asi","aspoň","atd","atp","ačkoli","až","bez","beze","blízko","bohužel","brzo","bude","budem","budeme","budete","budeš","budou","budu","by","byl","byla","byli","bylo","byly","bys","být","během","chce","chceme","chcete","chceš","chci","chtít","chtějí","chut'","chuti","co","což","cz","daleko","další","den","deset","devatenáct","devět","dnes","do","dobrý","docela","dva","dvacet","dvanáct","dvě","dál","dále","děkovat","děkujeme","děkuji","ho","hodně","i","jak","jakmile","jako","jakož","jde","je","jeden","jedenáct","jedna","jedno","jednou","jedou","jeho","jehož","jej","jejich","její","jelikož","jemu","jen","jenom","jestli","jestliže","ještě","jež","ji","jich","jimi","jinak","jiné","již","jsem","jseš","jsi","jsme","jsou","jste","já","jí","jím","jíž","k","kam","kde","kdo","kdy","když","ke","kolik","kromě","kterou","která","které","který","kteří","kvůli","mají","mezi","mi","mne","mnou","mně","moc","mohl","mohou","moje","moji","možná","musí","my","má","málo","mám","máme","máte","máš","mé","mí","mít","mě","můj","může","na","nad","nade","napište","naproti","načež","naše","naši","ne","nebo","nebyl","nebyla","nebyli","nebyly","nedělají","nedělá","nedělám","neděláme","neděláte","neděláš","neg","nejsi","nejsou","nemají","nemáme","nemáte","neměl","není","nestačí","nevadí","než","nic","nich","nimi","nové","nový","nula","nám","námi","nás","náš","ním","ně","něco","nějak","někde","někdo","němu","němuž","o","od","ode","on","ona","oni","ono","ony","osm","osmnáct","pak","patnáct","po","pod","podle","pokud","potom","pouze","pozdě","pořád","pravé","pro","prostě","prosím","proti","proto","protože","proč","první","pta","pět","před","přes","přese","při","přičemž","re","rovně","s","se","sedm","sedmnáct","si","skoro","smí","smějí","snad","spolu","sta","sto","strana","sté","své","svých","svým","svými","ta","tady","tak","takhle","taky","také","takže","tam","tamhle","tamhleto","tamto","tato","tebe","tebou","ted'","tedy","ten","tento","teto","ti","tipy","tisíc","tisíce","to","tobě","tohle","toho","tohoto","tom","tomto","tomu","tomuto","toto","trošku","tu","tuto","tvoje","tvá","tvé","tvůj","ty","tyto","téma","tím","tímto","tě","těm","těmu","třeba","tři","třináct","u","určitě","už","v","vaše","vaši","ve","vedle","večer","vlastně","vy","vám","vámi","vás","váš","více","však","všechno","všichni","vůbec","vždy","z","za","zatímco","zač","zda","zde","ze","zprávy","zpět","čau","či","článku","články","čtrnáct","čtyři","šest","šestnáct","že"],xm=["er","jeg","det","du","ikke","i","at","en","og","har","vi","til","på","hvad","med","mig","så","for","de","dig","der","den","han","kan","af","vil","var","her","et","skal","ved","nu","men","om","ja","som","nej","min","noget","ham","hun","bare","kom","være","din","hvor","dem","ud","os","hvis","må","se","godt","have","fra","ville","okay","lige","op","alle","lad","hvorfor","sig","hvordan","få","kunne","eller","hvem","man","bliver","havde","da","ingen","efter","når","alt","jo","to","mit","ind","hej","aldrig","lidt","nogen","over","også","mand","far","skulle","selv","får","hans","ser","vores","jer","sådan","dit","kun","deres","ned","mine","komme","tage","denne","sige","dette","blive","helt","fordi","end","tag","før","fik","dine"],_m=["aan","af","al","alles","als","altijd","andere","ben","bij","daar","dan","dat","de","der","deze","die","dit","doch","doen","door","dus","een","eens","en","er","ge","geen","geweest","haar","had","heb","hebben","heeft","hem","het","hier","hij","hoe","hun","iemand","iets","ik","in","is","ja","je ","kan","kon","kunnen","maar","me","meer","men","met","mij","mijn","moet","na","naar","niet","niets","nog","nu","of","om","omdat","ons","ook","op","over","reeds","te","tegen","toch","toen","tot","u","uit","uw","van","veel","voor","want","waren","was","wat","we","wel","werd","wezen","wie","wij","wil","worden","zal","ze","zei","zelf","zich","zij","zijn","zo","zonder","zou"],go=["about","after","all","also","am","an","and","another","any","are","as","at","be","because","been","before","being","between","both","but","by","came","can","come","could","did","do","each","for","from","get","got","has","had","he","have","her","here","him","himself","his","how","if","in","into","is","it","like","make","many","me","might","more","most","much","must","my","never","now","of","on","only","or","other","our","out","over","said","same","should","since","some","still","such","take","than","that","the","their","them","then","there","these","they","this","those","through","to","too","under","up","very","was","way","we","well","were","what","where","which","while","who","with","would","you","your","a","i"],jm=["adiaŭ","ajn","al","ankoraŭ","antaŭ","aŭ","bonan","bonvole","bonvolu","bv","ci","cia","cian","cin","d-ro","da","de","dek","deka","do","doktor'","doktoro","du","dua","dum","eble","ekz","ekzemple","en","estas","estis","estos","estu","estus","eĉ","f-no","feliĉan","for","fraŭlino","ha","havas","havis","havos","havu","havus","he","ho","hu","ili","ilia","ilian","ilin","inter","io","ion","iu","iujn","iun","ja","jam","je","jes","k","kaj","ke","kio","kion","kiu","kiujn","kiun","kvankam","kvar","kvara","kvazaŭ","kvin","kvina","la","li","lia","lian","lin","malantaŭ","male","malgraŭ","mem","mi","mia","mian","min","minus","naŭ","naŭa","ne","nek","nenio","nenion","neniu","neniun","nepre","ni","nia","nian","nin","nu","nun","nur","ok","oka","oni","onia","onian","onin","plej","pli","plu","plus","por","post","preter","s-no","s-ro","se","sed","sep","sepa","ses","sesa","si","sia","sian","sin","sinjor'","sinjorino","sinjoro","sub","super","supren","sur","tamen","tio","tion","tiu","tiujn","tiun","tra","tri","tria","tuj","tute","unu","unua","ve","verŝajne","vi","via","vian","vin","ĉi","ĉio","ĉion","ĉiu","ĉiujn","ĉiun","ĉu","ĝi","ĝia","ĝian","ĝin","ĝis","ĵus","ŝi","ŝia","ŝin"],Sm=["aga","ei","et","ja","jah","kas","kui","kõik","ma","me","mida","midagi","mind","minu","mis","mu","mul","mulle","nad","nii","oled","olen","oli","oma","on","pole","sa","seda","see","selle","siin","siis","ta","te","ära"],zm=["ja","on","oli","hän","vuonna","myös","joka","se","sekä","sen","mutta","ei","ovat","hänen","n","kanssa","vuoden","jälkeen","että","s","tai","jonka","jossa","mukaan","kun","muun","muassa","hänet","olivat","kuitenkin","noin","vuosina","aikana","lisäksi","kaksi","kuin","ollut","the","myöhemmin","eli","vain","teki","mm","jotka","ennen","ensimmäinen","a","9","jo","kuten","yksi","ensimmäisen","vastaan","tämän","vuodesta","sitä","voi","luvun","luvulla","of","ole","kauden","osa","esimerkiksi","jolloin","yli","de","kaudella","eri","sillä","kolme","he","vuotta"],Em=["être","avoir","faire","a","au","aux","avec","ce","ces","dans","de","des","du","elle","en","et","eux","il","je","la","le","leur","lui","ma","mais","me","même","mes","moi","mon","ne","nos","notre","nous","on","ou","où","par","pas","pour","qu","que","qui","sa","se","ses","son","sur","ta","te","tes","toi","ton","tu","un","une","vos","votre","vous","c","d","j","l","à","m","n","s","t","y","été","étée","étées","étés","étant","suis","es","est","sommes","êtes","sont","serai","seras","sera","serons","serez","seront","serais","serait","serions","seriez","seraient","étais","était","étions","étiez","étaient","fus","fut","fûmes","fûtes","furent","sois","soit","soyons","soyez","soient","fusse","fusses","fût","fussions","fussiez","fussent","ayant","eu","eue","eues","eus","ai","as","avons","avez","ont","aurai","auras","aura","aurons","aurez","auront","aurais","aurait","aurions","auriez","auraient","avais","avait","avions","aviez","avaient","eut","eûmes","eûtes","eurent","aie","aies","ait","ayons","ayez","aient","eusse","eusses","eût","eussions","eussiez","eussent","ceci","cela","cet","cette","ici","ils","les","leurs","quel","quels","quelle","quelles","sans","soi"],Cm=["a","alí","ao","aos","aquel","aquela","aquelas","aqueles","aquilo","aquí","as","así","aínda","ben","cando","che","co","coa","coas","comigo","con","connosco","contigo","convosco","cos","cun","cunha","cunhas","cuns","da","dalgunha","dalgunhas","dalgún","dalgúns","das","de","del","dela","delas","deles","desde","deste","do","dos","dun","dunha","dunhas","duns","e","el","ela","elas","eles","en","era","eran","esa","esas","ese","eses","esta","estaba","estar","este","estes","estiven","estou","está","están","eu","facer","foi","foron","fun","había","hai","iso","isto","la","las","lle","lles","lo","los","mais","me","meu","meus","min","miña","miñas","moi","na","nas","neste","nin","no","non","nos","nosa","nosas","noso","nosos","nun","nunha","nunhas","nuns","nós","o","os","ou","para","pero","pode","pois","pola","polas","polo","polos","por","que","se","senón","ser","seu","seus","sexa","sido","sobre","súa","súas","tamén","tan","te","ten","ter","teu","teus","teñen","teño","ti","tido","tiven","tiña","túa","túas","un","unha","unhas","uns","vos","vosa","vosas","voso","vosos","vós","á","é","ó","ós"],Tm=["a","ab","aber","ach","acht","achte","achten","achter","achtes","ag","alle","allein","allem","allen","aller","allerdings","alles","allgemeinen","als","also","am","an","ander","andere","anderem","anderen","anderer","anderes","anderm","andern","anderr","anders","au","auch","auf","aus","ausser","ausserdem","außer","außerdem","b","bald","bei","beide","beiden","beim","beispiel","bekannt","bereits","besonders","besser","besten","bin","bis","bisher","bist","c","d","d.h","da","dabei","dadurch","dafür","dagegen","daher","dahin","dahinter","damals","damit","danach","daneben","dank","dann","daran","darauf","daraus","darf","darfst","darin","darum","darunter","darüber","das","dasein","daselbst","dass","dasselbe","davon","davor","dazu","dazwischen","daß","dein","deine","deinem","deinen","deiner","deines","dem","dementsprechend","demgegenüber","demgemäss","demgemäß","demselben","demzufolge","den","denen","denn","denselben","der","deren","derer","derjenige","derjenigen","dermassen","dermaßen","derselbe","derselben","des","deshalb","desselben","dessen","deswegen","dich","die","diejenige","diejenigen","dies","diese","dieselbe","dieselben","diesem","diesen","dieser","dieses","dir","doch","dort","drei","drin","dritte","dritten","dritter","drittes","du","durch","durchaus","durfte","durften","dürfen","dürft","e","eben","ebenso","ehrlich","ei","ei, ","eigen","eigene","eigenen","eigener","eigenes","ein","einander","eine","einem","einen","einer","eines","einig","einige","einigem","einigen","einiger","einiges","einmal","eins","elf","en","ende","endlich","entweder","er","ernst","erst","erste","ersten","erster","erstes","es","etwa","etwas","euch","euer","eure","eurem","euren","eurer","eures","f","folgende","früher","fünf","fünfte","fünften","fünfter","fünftes","für","g","gab","ganz","ganze","ganzen","ganzer","ganzes","gar","gedurft","gegen","gegenüber","gehabt","gehen","geht","gekannt","gekonnt","gemacht","gemocht","gemusst","genug","gerade","gern","gesagt","geschweige","gewesen","gewollt","geworden","gibt","ging","gleich","gott","gross","grosse","grossen","grosser","grosses","groß","große","großen","großer","großes","gut","gute","guter","gutes","h","hab","habe","haben","habt","hast","hat","hatte","hatten","hattest","hattet","heisst","her","heute","hier","hin","hinter","hoch","hätte","hätten","i","ich","ihm","ihn","ihnen","ihr","ihre","ihrem","ihren","ihrer","ihres","im","immer","in","indem","infolgedessen","ins","irgend","ist","j","ja","jahr","jahre","jahren","je","jede","jedem","jeden","jeder","jedermann","jedermanns","jedes","jedoch","jemand","jemandem","jemanden","jene","jenem","jenen","jener","jenes","jetzt","k","kam","kann","kannst","kaum","kein","keine","keinem","keinen","keiner","keines","kleine","kleinen","kleiner","kleines","kommen","kommt","konnte","konnten","kurz","können","könnt","könnte","l","lang","lange","leicht","leide","lieber","los","m","machen","macht","machte","mag","magst","mahn","mal","man","manche","manchem","manchen","mancher","manches","mann","mehr","mein","meine","meinem","meinen","meiner","meines","mensch","menschen","mich","mir","mit","mittel","mochte","mochten","morgen","muss","musst","musste","mussten","muß","mußt","möchte","mögen","möglich","mögt","müssen","müsst","müßt","n","na","nach","nachdem","nahm","natürlich","neben","nein","neue","neuen","neun","neunte","neunten","neunter","neuntes","nicht","nichts","nie","niemand","niemandem","niemanden","noch","nun","nur","o","ob","oben","oder","offen","oft","ohne","ordnung","p","q","r","recht","rechte","rechten","rechter","rechtes","richtig","rund","s","sa","sache","sagt","sagte","sah","satt","schlecht","schluss","schon","sechs","sechste","sechsten","sechster","sechstes","sehr","sei","seid","seien","sein","seine","seinem","seinen","seiner","seines","seit","seitdem","selbst","sich","sie","sieben","siebente","siebenten","siebenter","siebentes","sind","so","solang","solche","solchem","solchen","solcher","solches","soll","sollen","sollst","sollt","sollte","sollten","sondern","sonst","soweit","sowie","später","startseite","statt","steht","suche","t","tag","tage","tagen","tat","teil","tel","tritt","trotzdem","tun","u","uhr","um","und","und?","uns","unse","unsem","unsen","unser","unsere","unserer","unses","unter","v","vergangenen","viel","viele","vielem","vielen","vielleicht","vier","vierte","vierten","vierter","viertes","vom","von","vor","w","wahr?","wann","war","waren","warst","wart","warum","was","weg","wegen","weil","weit","weiter","weitere","weiteren","weiteres","welche","welchem","welchen","welcher","welches","wem","wen","wenig","wenige","weniger","weniges","wenigstens","wenn","wer","werde","werden","werdet","weshalb","wessen","wie","wieder","wieso","will","willst","wir","wird","wirklich","wirst","wissen","wo","woher","wohin","wohl","wollen","wollt","wollte","wollten","worden","wurde","wurden","während","währenddem","währenddessen","wäre","würde","würden","x","y","z","z.b","zehn","zehnte","zehnten","zehnter","zehntes","zeit","zu","zuerst","zugleich","zum","zunächst","zur","zurück","zusammen","zwanzig","zwar","zwei","zweite","zweiten","zweiter","zweites","zwischen","zwölf","über","überhaupt","übrigens"],Am=["αλλα","αν","αντι","απο","αυτα","αυτεσ","αυτη","αυτο","αυτοι","αυτοσ","αυτουσ","αυτων","για","δε","δεν","εαν","ειμαι","ειμαστε","ειναι","εισαι","ειστε","εκεινα","εκεινεσ","εκεινη","εκεινο","εκεινοι","εκεινοσ","εκεινουσ","εκεινων","ενω","επι","η","θα","ισωσ","κ","και","κατα","κι","μα","με","μετα","μη","μην","να","ο","οι","ομωσ","οπωσ","οσο","οτι","παρα","ποια","ποιεσ","ποιο","ποιοι","ποιοσ","ποιουσ","ποιων","που","προσ","πωσ","σε","στη","στην","στο","στον","τα","την","τησ","το","τον","τοτε","του","των","ωσ"],Om=["અંગે","અંદર","અથવા","અને","અમને","અમારું","અમે","અહીં","આ","આગળ","આથી","આનું","આને","આપણને","આપણું","આપણે","આપી","આર","આવી","આવે","ઉપર","ઉભા","ઊંચે","ઊભું","એ","એક","એન","એના","એનાં","એની","એનું","એને","એનો","એમ","એવા","એવાં","એવી","એવું","એવો","ઓછું","કંઈક","કઈ","કયું","કયો","કરતાં","કરવું","કરી","કરીએ","કરું","કરે","કરેલું","કર્યા","કર્યાં","કર્યું","કર્યો","કાંઈ","કે","કેટલું","કેમ","કેવી","કેવું","કોઈ","કોઈક","કોણ","કોણે","કોને","ક્યાં","ક્યારે","ખૂબ","ગઈ","ગયા","ગયાં","ગયું","ગયો","ઘણું","છ","છતાં","છીએ","છું","છે","છેક","છો","જ","જાય","જી","જે","જેટલું","જેને","જેમ","જેવી","જેવું","જેવો","જો","જોઈએ","જ્યાં","જ્યારે","ઝાઝું","તને","તમને","તમારું","તમે","તા","તારાથી","તારામાં","તારું","તું","તે","તેં","તેઓ","તેણે","તેથી","તેના","તેની","તેનું","તેને","તેમ","તેમનું","તેમને","તેવી","તેવું","તો","ત્યાં","ત્યારે","થઇ","થઈ","થઈએ","થતા","થતાં","થતી","થતું","થતો","થયા","થયાં","થયું","થયેલું","થયો","થવું","થાઉં","થાઓ","થાય","થી","થોડું","દરેક","ન","નં","નં.","નથી","નહિ","નહી","નહીં","ના","ની","નીચે","નું","ને","નો","પછી","પણ","પર","પરંતુ","પહેલાં","પાછળ","પાસે","પોતાનું","પ્રત્યેક","ફક્ત","ફરી","ફરીથી","બંને","બધા","બધું","બની","બહાર","બહુ","બાદ","બે","મને","મા","માં","માટે","માત્ર","મારું","મી","મૂકવું","મૂકી","મૂક્યા","મૂક્યાં","મૂક્યું","મેં","રહી","રહે","રહેવું","રહ્યા","રહ્યાં","રહ્યો","રીતે","રૂ.","રૂા","લેતા","લેતું","લેવા","વગેરે","વધુ","શકે","શા","શું","સરખું","સામે","સુધી","હતા","હતાં","હતી","હતું","હવે","હશે","હશો","હા","હું","હો","હોઈ","હોઈશ","હોઈશું","હોય","હોવા"],Rm=["ta","da","ya","sai","ba","yi","na","kuma","ma","ji","cikin","in","ni","wata","wani","ce","tana","don","za","sun","amma","ga","ina","ne","tselane","mai","suka","wannan","a","ko","lokacin","su","take","kaka","shi","yake","yana","mulongo","mata","ka","ban","ita","tafi","shanshani","kai","daɗi","mi","ƙato","fara","rana"],Lm=["אבל","או","אולי","אותה","אותו","אותי","אותך","אותם","אותן","אותנו","אז","אחר","אחרות","אחרי","אחריכן","אחרים","אחרת","אי","איזה","איך","אין","איפה","איתה","איתו","איתי","איתך","איתכם","איתכן","איתם","איתן","איתנו","אך","אל","אלה","אלו","אם","אנחנו","אני","אס","אף","אצל","אשר","את","אתה","אתכם","אתכן","אתם","אתן","באיזומידה","באמצע","באמצעות","בגלל","בין","בלי","במידה","במקוםשבו","ברם","בשביל","בשעהש","בתוך","גם","דרך","הוא","היא","היה","היכן","היתה","היתי","הם","הן","הנה","הסיבהשבגללה","הרי","ואילו","ואת","זאת","זה","זות","יהיה","יוכל","יוכלו","יותרמדי","יכול","יכולה","יכולות","יכולים","יכל","יכלה","יכלו","יש","כאן","כאשר","כולם","כולן","כזה","כי","כיצד","כך","ככה","כל","כלל","כמו","כן","כפי","כש","לא","לאו","לאיזותכלית","לאן","לבין","לה","להיות","להם","להן","לו","לי","לכם","לכן","למה","למטה","למעלה","למקוםשבו","למרות","לנו","לעבר","לעיכן","לפיכך","לפני","מאד","מאחורי","מאיזוסיבה","מאין","מאיפה","מבלי","מבעד","מדוע","מה","מהיכן","מול","מחוץ","מי","מכאן","מכיוון","מלבד","מן","מנין","מסוגל","מעט","מעטים","מעל","מצד","מקוםבו","מתחת","מתי","נגד","נגר","נו","עד","עז","על","עלי","עליה","עליהם","עליהן","עליו","עליך","עליכם","עלינו","עם","עצמה","עצמהם","עצמהן","עצמו","עצמי","עצמם","עצמן","עצמנו","פה","רק","שוב","של","שלה","שלהם","שלהן","שלו","שלי","שלך","שלכה","שלכם","שלכן","שלנו","שם","תהיה","תחת"],Nm=["अंदर","अत","अदि","अप","अपना","अपनि","अपनी","अपने","अभि","अभी","आदि","आप","इंहिं","इंहें","इंहों","इतयादि","इत्यादि","इन","इनका","इन्हीं","इन्हें","इन्हों","इस","इसका","इसकि","इसकी","इसके","इसमें","इसि","इसी","इसे","उंहिं","उंहें","उंहों","उन","उनका","उनकि","उनकी","उनके","उनको","उन्हीं","उन्हें","उन्हों","उस","उसके","उसि","उसी","उसे","एक","एवं","एस","एसे","ऐसे","ओर","और","कइ","कई","कर","करता","करते","करना","करने","करें","कहते","कहा","का","काफि","काफ़ी","कि","किंहें","किंहों","कितना","किन्हें","किन्हों","किया","किर","किस","किसि","किसी","किसे","की","कुछ","कुल","के","को","कोइ","कोई","कोन","कोनसा","कौन","कौनसा","गया","घर","जब","जहाँ","जहां","जा","जिंहें","जिंहों","जितना","जिधर","जिन","जिन्हें","जिन्हों","जिस","जिसे","जीधर","जेसा","जेसे","जैसा","जैसे","जो","तक","तब","तरह","तिंहें","तिंहों","तिन","तिन्हें","तिन्हों","तिस","तिसे","तो","था","थि","थी","थे","दबारा","दवारा","दिया","दुसरा","दुसरे","दूसरे","दो","द्वारा","न","नहिं","नहीं","ना","निचे","निहायत","नीचे","ने","पर","पहले","पुरा","पूरा","पे","फिर","बनि","बनी","बहि","बही","बहुत","बाद","बाला","बिलकुल","भि","भितर","भी","भीतर","मगर","मानो","मे","में","यदि","यह","यहाँ","यहां","यहि","यही","या","यिह","ये","रखें","रवासा","रहा","रहे","ऱ्वासा","लिए","लिये","लेकिन","व","वगेरह","वरग","वर्ग","वह","वहाँ","वहां","वहिं","वहीं","वाले","वुह","वे","वग़ैरह","संग","सकता","सकते","सबसे","सभि","सभी","साथ","साबुत","साभ","सारा","से","सो","हि","ही","हुअ","हुआ","हुइ","हुई","हुए","हे","हें","है","हैं","हो","होता","होति","होती","होते","होना","होने"],Mm=["a","ach","ag","agus","an","aon","ar","arna","as","b'","ba","beirt","bhúr","caoga","ceathair","ceathrar","chomh","chtó","chuig","chun","cois","céad","cúig","cúigear","d'","daichead","dar","de","deich","deichniúr","den","dhá","do","don","dtí","dá","dár","dó","faoi","faoin","faoina","faoinár","fara","fiche","gach","gan","go","gur","haon","hocht","i","iad","idir","in","ina","ins","inár","is","le","leis","lena","lenár","m'","mar","mo","mé","na","nach","naoi","naonúr","ná","ní","níor","nó","nócha","ocht","ochtar","os","roimh","sa","seacht","seachtar","seachtó","seasca","seisear","siad","sibh","sinn","sna","sé","sí","tar","thar","thú","triúr","trí","trína","trínár","tríocha","tú","um","ár","é","éis","í","ó","ón","óna","ónár"],Im=["a","abba","abban","abból","addig","ahhoz","ahogy","ahol","aki","akik","akkor","akár","alapján","alatt","alatta","alattad","alattam","alattatok","alattuk","alattunk","alá","alád","alájuk","alám","alánk","alátok","alól","alóla","alólad","alólam","alólatok","alóluk","alólunk","amely","amelyből","amelyek","amelyekben","amelyeket","amelyet","amelyik","amelynek","ami","amikor","amit","amolyan","amott","amíg","annak","annál","arra","arról","attól","az","aznap","azok","azokat","azokba","azokban","azokból","azokhoz","azokig","azokkal","azokká","azoknak","azoknál","azokon","azokra","azokról","azoktól","azokért","azon","azonban","azonnal","azt","aztán","azután","azzal","azzá","azért","bal","balra","ban","be","belé","beléd","beléjük","belém","belénk","belétek","belül","belőle","belőled","belőlem","belőletek","belőlük","belőlünk","ben","benne","benned","bennem","bennetek","bennük","bennünk","bár","bárcsak","bármilyen","búcsú","cikk","cikkek","cikkeket","csak","csakhogy","csupán","de","dehogy","e","ebbe","ebben","ebből","eddig","egy","egyebek","egyebet","egyedül","egyelőre","egyes","egyet","egyetlen","egyik","egymás","egyre","egyszerre","egyéb","együtt","egész","egészen","ehhez","ekkor","el","eleinte","ellen","ellenes","elleni","ellenére","elmondta","első","elsők","elsősorban","elsőt","elé","eléd","elég","eléjük","elém","elénk","elétek","elő","előbb","elől","előle","előled","előlem","előletek","előlük","előlünk","először","előtt","előtte","előtted","előttem","előttetek","előttük","előttünk","előző","emilyen","engem","ennek","ennyi","ennél","enyém","erre","erről","esetben","ettől","ez","ezek","ezekbe","ezekben","ezekből","ezeken","ezeket","ezekhez","ezekig","ezekkel","ezekké","ezeknek","ezeknél","ezekre","ezekről","ezektől","ezekért","ezen","ezentúl","ezer","ezret","ezt","ezután","ezzel","ezzé","ezért","fel","fele","felek","felet","felett","felé","fent","fenti","fél","fölé","gyakran","ha","halló","hamar","hanem","harmadik","harmadikat","harminc","hat","hatodik","hatodikat","hatot","hatvan","helyett","hetedik","hetediket","hetet","hetven","hirtelen","hiszen","hiába","hogy","hogyan","hol","holnap","holnapot","honnan","hova","hozzá","hozzád","hozzájuk","hozzám","hozzánk","hozzátok","hurrá","huszadik","hány","hányszor","hármat","három","hát","hátha","hátulsó","hét","húsz","ide","ide-оda","idén","igazán","igen","ill","illetve","ilyen","ilyenkor","immár","inkább","is","ismét","ison","itt","jelenleg","jobban","jobbra","jó","jól","jólesik","jóval","jövőre","kell","kellene","kellett","kelljen","keressünk","keresztül","ketten","kettő","kettőt","kevés","ki","kiben","kiből","kicsit","kicsoda","kihez","kik","kikbe","kikben","kikből","kiken","kiket","kikhez","kikkel","kikké","kiknek","kiknél","kikre","kikről","kiktől","kikért","kilenc","kilencedik","kilencediket","kilencet","kilencven","kin","kinek","kinél","kire","kiről","kit","kitől","kivel","kivé","kié","kiért","korábban","képest","kérem","kérlek","kész","késő","később","későn","két","kétszer","kívül","körül","köszönhetően","köszönöm","közben","közel","közepesen","közepén","közé","között","közül","külön","különben","különböző","különbözőbb","különbözőek","lassan","le","legalább","legyen","lehet","lehetetlen","lehetett","lehetőleg","lehetőség","lenne","lenni","lennék","lennének","lesz","leszek","lesznek","leszünk","lett","lettek","lettem","lettünk","lévő","ma","maga","magad","magam","magatokat","magukat","magunkat","magát","mai","majd","majdnem","manapság","meg","megcsinál","megcsinálnak","megint","megvan","mellett","mellette","melletted","mellettem","mellettetek","mellettük","mellettünk","mellé","melléd","melléjük","mellém","mellénk","mellétek","mellől","mellőle","mellőled","mellőlem","mellőletek","mellőlük","mellőlünk","mely","melyek","melyik","mennyi","mert","mi","miatt","miatta","miattad","miattam","miattatok","miattuk","miattunk","mibe","miben","miből","mihez","mik","mikbe","mikben","mikből","miken","miket","mikhez","mikkel","mikké","miknek","miknél","mikor","mikre","mikről","miktől","mikért","milyen","min","mind","mindegyik","mindegyiket","minden","mindenesetre","mindenki","mindent","mindenütt","mindig","mindketten","minek","minket","mint","mintha","minél","mire","miről","mit","mitől","mivel","mivé","miért","mondta","most","mostanáig","már","más","másik","másikat","másnap","második","másodszor","mások","másokat","mást","még","mégis","míg","mögé","mögéd","mögéjük","mögém","mögénk","mögétek","mögött","mögötte","mögötted","mögöttem","mögöttetek","mögöttük","mögöttünk","mögül","mögüle","mögüled","mögülem","mögületek","mögülük","mögülünk","múltkor","múlva","na","nagy","nagyobb","nagyon","naponta","napot","ne","negyedik","negyediket","negyven","neked","nekem","neki","nekik","nektek","nekünk","nem","nemcsak","nemrég","nincs","nyolc","nyolcadik","nyolcadikat","nyolcat","nyolcvan","nála","nálad","nálam","nálatok","náluk","nálunk","négy","négyet","néha","néhány","nélkül","o","oda","ok","olyan","onnan","ott","pedig","persze","pár","például","rajta","rajtad","rajtam","rajtatok","rajtuk","rajtunk","rendben","rosszul","rá","rád","rájuk","rám","ránk","rátok","régen","régóta","részére","róla","rólad","rólam","rólatok","róluk","rólunk","rögtön","s","saját","se","sem","semmi","semmilyen","semmiség","senki","soha","sok","sokan","sokat","sokkal","sokszor","sokáig","során","stb.","szemben","szerbusz","szerint","szerinte","szerinted","szerintem","szerintetek","szerintük","szerintünk","szervusz","szinte","számára","száz","századik","százat","szépen","szét","szíves","szívesen","szíveskedjék","sőt","talán","tavaly","te","tegnap","tegnapelőtt","tehát","tele","teljes","tessék","ti","tied","titeket","tizedik","tizediket","tizenegy","tizenegyedik","tizenhat","tizenhárom","tizenhét","tizenkettedik","tizenkettő","tizenkilenc","tizenkét","tizennyolc","tizennégy","tizenöt","tizet","tovább","további","továbbá","távol","téged","tényleg","tíz","több","többi","többször","túl","tőle","tőled","tőlem","tőletek","tőlük","tőlünk","ugyanakkor","ugyanez","ugyanis","ugye","urak","uram","urat","utoljára","utolsó","után","utána","vagy","vagyis","vagyok","vagytok","vagyunk","vajon","valahol","valaki","valakit","valamelyik","valami","valamint","való","van","vannak","vele","veled","velem","veletek","velük","velünk","vissza","viszlát","viszont","viszontlátásra","volna","volnának","volnék","volt","voltak","voltam","voltunk","végre","végén","végül","által","általában","ám","át","éljen","én","éppen","érte","érted","értem","értetek","értük","értünk","és","év","évben","éve","évek","éves","évi","évvel","így","óta","ön","önbe","önben","önből","önhöz","önnek","önnel","önnél","önre","önről","önt","öntől","önért","önök","önökbe","önökben","önökből","önöket","önökhöz","önökkel","önöknek","önöknél","önökre","önökről","önöktől","önökért","önökön","önön","össze","öt","ötven","ötödik","ötödiket","ötöt","úgy","úgyis","úgynevezett","új","újabb","újra","úr","ő","ők","őket","őt"],Fm=["ada","adalah","adanya","adapun","agak","agaknya","agar","akan","akankah","akhir","akhiri","akhirnya","aku","akulah","amat","amatlah","anda","andalah","antar","antara","antaranya","apa","apaan","apabila","apakah","apalagi","apatah","artinya","asal","asalkan","atas","atau","ataukah","ataupun","awal","awalnya","bagai","bagaikan","bagaimana","bagaimanakah","bagaimanapun","bagi","bagian","bahkan","bahwa","bahwasanya","bakal","bakalan","balik","banyak","bapak","baru","bawah","beberapa","begini","beginian","beginikah","beginilah","begitu","begitukah","begitulah","begitupun","bekerja","belakang","belakangan","belum","belumlah","benar","benarkah","benarlah","berada","berakhir","berakhirlah","berakhirnya","berapa","berapakah","berapalah","berapapun","berarti","berawal","berbagai","berdatangan","beri","berikan","berikut","berikutnya","berjumlah","berkali-kali","berkata","berkehendak","berkeinginan","berkenaan","berlainan","berlalu","berlangsung","berlebihan","bermacam","bermacam-macam","bermaksud","bermula","bersama","bersama-sama","bersiap","bersiap-siap","bertanya","bertanya-tanya","berturut","berturut-turut","bertutur","berujar","berupa","besar","betul","betulkah","biasa","biasanya","bila","bilakah","bisa","bisakah","boleh","bolehkah","bolehlah","buat","bukan","bukankah","bukanlah","bukannya","bulan","bung","cara","caranya","cukup","cukupkah","cukuplah","cuma","dahulu","dalam","dan","dapat","dari","daripada","datang","dekat","demi","demikian","demikianlah","dengan","depan","di","dia","diakhiri","diakhirinya","dialah","diantara","diantaranya","diberi","diberikan","diberikannya","dibuat","dibuatnya","didapat","didatangkan","digunakan","diibaratkan","diibaratkannya","diingat","diingatkan","diinginkan","dijawab","dijelaskan","dijelaskannya","dikarenakan","dikatakan","dikatakannya","dikerjakan","diketahui","diketahuinya","dikira","dilakukan","dilalui","dilihat","dimaksud","dimaksudkan","dimaksudkannya","dimaksudnya","diminta","dimintai","dimisalkan","dimulai","dimulailah","dimulainya","dimungkinkan","dini","dipastikan","diperbuat","diperbuatnya","dipergunakan","diperkirakan","diperlihatkan","diperlukan","diperlukannya","dipersoalkan","dipertanyakan","dipunyai","diri","dirinya","disampaikan","disebut","disebutkan","disebutkannya","disini","disinilah","ditambahkan","ditandaskan","ditanya","ditanyai","ditanyakan","ditegaskan","ditujukan","ditunjuk","ditunjuki","ditunjukkan","ditunjukkannya","ditunjuknya","dituturkan","dituturkannya","diucapkan","diucapkannya","diungkapkan","dong","dulu","empat","enggak","enggaknya","entah","entahlah","guna","gunakan","hal","hampir","hanya","hanyalah","harus","haruslah","harusnya","hendak","hendaklah","hendaknya","hingga","ia","ialah","ibarat","ibaratkan","ibaratnya","ikut","ingat","ingat-ingat","ingin","inginkah","inginkan","ini","inikah","inilah","itu","itukah","itulah","jadi","jadilah","jadinya","jangan","jangankan","janganlah","jauh","jawab","jawaban","jawabnya","jelas","jelaskan","jelaslah","jelasnya","jika","jikalau","juga","jumlah","jumlahnya","justru","kala","kalau","kalaulah","kalaupun","kalian","kami","kamilah","kamu","kamulah","kan","kapan","kapankah","kapanpun","karena","karenanya","kasus","kata","katakan","katakanlah","katanya","ke","keadaan","kebetulan","kecil","kedua","keduanya","keinginan","kelamaan","kelihatan","kelihatannya","kelima","keluar","kembali","kemudian","kemungkinan","kemungkinannya","kenapa","kepada","kepadanya","kesampaian","keseluruhan","keseluruhannya","keterlaluan","ketika","khususnya","kini","kinilah","kira","kira-kira","kiranya","kita","kitalah","kok","kurang","lagi","lagian","lah","lain","lainnya","lalu","lama","lamanya","lanjut","lanjutnya","lebih","lewat","lima","luar","macam","maka","makanya","makin","malah","malahan","mampu","mampukah","mana","manakala","manalagi","masa","masalah","masalahnya","masih","masihkah","masing","masing-masing","mau","maupun","melainkan","melakukan","melalui","melihat","melihatnya","memang","memastikan","memberi","memberikan","membuat","memerlukan","memihak","meminta","memintakan","memisalkan","memperbuat","mempergunakan","memperkirakan","memperlihatkan","mempersiapkan","mempersoalkan","mempertanyakan","mempunyai","memulai","memungkinkan","menaiki","menambahkan","menandaskan","menanti","menanti-nanti","menantikan","menanya","menanyai","menanyakan","mendapat","mendapatkan","mendatang","mendatangi","mendatangkan","menegaskan","mengakhiri","mengapa","mengatakan","mengatakannya","mengenai","mengerjakan","mengetahui","menggunakan","menghendaki","mengibaratkan","mengibaratkannya","mengingat","mengingatkan","menginginkan","mengira","mengucapkan","mengucapkannya","mengungkapkan","menjadi","menjawab","menjelaskan","menuju","menunjuk","menunjuki","menunjukkan","menunjuknya","menurut","menuturkan","menyampaikan","menyangkut","menyatakan","menyebutkan","menyeluruh","menyiapkan","merasa","mereka","merekalah","merupakan","meski","meskipun","meyakini","meyakinkan","minta","mirip","misal","misalkan","misalnya","mula","mulai","mulailah","mulanya","mungkin","mungkinkah","nah","naik","namun","nanti","nantinya","nyaris","nyatanya","oleh","olehnya","pada","padahal","padanya","paling","panjang","pantas","para","pasti","pastilah","penting","pentingnya","per","percuma","perlu","perlukah","perlunya","pernah","persoalan","pertama","pertama-tama","pertanyaan","pertanyakan","pihak","pihaknya","pukul","pula","pun","punya","rasa","rasanya","rata","rupanya","saat","saatnya","saja","sajalah","saling","sama","sama-sama","sambil","sampai","sampai-sampai","sampaikan","sana","sangat","sangatlah","satu","saya","sayalah","se","sebab","sebabnya","sebagai","sebagaimana","sebagainya","sebagian","sebaik","sebaik-baiknya","sebaiknya","sebaliknya","sebanyak","sebegini","sebegitu","sebelum","sebelumnya","sebenarnya","seberapa","sebesar","sebetulnya","sebisanya","sebuah","sebut","sebutlah","sebutnya","secara","secukupnya","sedang","sedangkan","sedemikian","sedikit","sedikitnya","seenaknya","segala","segalanya","segera","seharusnya","sehingga","seingat","sejak","sejauh","sejenak","sejumlah","sekadar","sekadarnya","sekali","sekali-kali","sekalian","sekaligus","sekalipun","sekarang","sekarang","sekecil","seketika","sekiranya","sekitar","sekitarnya","sekurang-kurangnya","sekurangnya","sela","selain","selaku","selalu","selama","selama-lamanya","selamanya","selanjutnya","seluruh","seluruhnya","semacam","semakin","semampu","semampunya","semasa","semasih","semata","semata-mata","semaunya","sementara","semisal","semisalnya","sempat","semua","semuanya","semula","sendiri","sendirian","sendirinya","seolah","seolah-olah","seorang","sepanjang","sepantasnya","sepantasnyalah","seperlunya","seperti","sepertinya","sepihak","sering","seringnya","serta","serupa","sesaat","sesama","sesampai","sesegera","sesekali","seseorang","sesuatu","sesuatunya","sesudah","sesudahnya","setelah","setempat","setengah","seterusnya","setiap","setiba","setibanya","setidak-tidaknya","setidaknya","setinggi","seusai","sewaktu","siap","siapa","siapakah","siapapun","sini","sinilah","soal","soalnya","suatu","sudah","sudahkah","sudahlah","supaya","tadi","tadinya","tahu","tahun","tak","tambah","tambahnya","tampak","tampaknya","tandas","tandasnya","tanpa","tanya","tanyakan","tanyanya","tapi","tegas","tegasnya","telah","tempat","tengah","tentang","tentu","tentulah","tentunya","tepat","terakhir","terasa","terbanyak","terdahulu","terdapat","terdiri","terhadap","terhadapnya","teringat","teringat-ingat","terjadi","terjadilah","terjadinya","terkira","terlalu","terlebih","terlihat","termasuk","ternyata","tersampaikan","tersebut","tersebutlah","tertentu","tertuju","terus","terutama","tetap","tetapi","tiap","tiba","tiba-tiba","tidak","tidakkah","tidaklah","tiga","tinggi","toh","tunjuk","turut","tutur","tuturnya","ucap","ucapnya","ujar","ujarnya","umum","umumnya","ungkap","ungkapnya","untuk","usah","usai","waduh","wah","wahai","waktu","waktunya","walau","walaupun","wong","yaitu","yakin","yakni","yang"],Pm=["ad","al","allo","ai","agli","all","agl","alla","alle","con","col","coi","da","dal","dallo","dai","dagli","dall","dagl","dalla","dalle","di","del","dello","dei","degli","dell","degl","della","delle","in","nel","nello","nei","negli","nell","negl","nella","nelle","su","sul","sullo","sui","sugli","sull","sugl","sulla","sulle","per","tra","contro","io","tu","lui","lei","noi","voi","loro","mio","mia","miei","mie","tuo","tua","tuoi","tue","suo","sua","suoi","sue","nostro","nostra","nostri","nostre","vostro","vostra","vostri","vostre","mi","ti","ci","vi","lo","la","li","le","gli","ne","il","un","uno","una","ma","ed","se","perché","anche","come","dov","dove","che","chi","cui","non","più","quale","quanto","quanti","quanta","quante","quello","quelli","quella","quelle","questo","questi","questa","queste","si","tutto","tutti","a","c","e","i","l","o","ho","hai","ha","abbiamo","avete","hanno","abbia","abbiate","abbiano","avrò","avrai","avrà","avremo","avrete","avranno","avrei","avresti","avrebbe","avremmo","avreste","avrebbero","avevo","avevi","aveva","avevamo","avevate","avevano","ebbi","avesti","ebbe","avemmo","aveste","ebbero","avessi","avesse","avessimo","avessero","avendo","avuto","avuta","avuti","avute","sono","sei","è","siamo","siete","sia","siate","siano","sarò","sarai","sarà","saremo","sarete","saranno","sarei","saresti","sarebbe","saremmo","sareste","sarebbero","ero","eri","era","eravamo","eravate","erano","fui","fosti","fu","fummo","foste","furono","fossi","fosse","fossimo","fossero","essendo","faccio","fai","facciamo","fanno","faccia","facciate","facciano","farò","farai","farà","faremo","farete","faranno","farei","faresti","farebbe","faremmo","fareste","farebbero","facevo","facevi","faceva","facevamo","facevate","facevano","feci","facesti","fece","facemmo","faceste","fecero","facessi","facesse","facessimo","facessero","facendo","sto","stai","sta","stiamo","stanno","stia","stiate","stiano","starò","starai","starà","staremo","starete","staranno","starei","staresti","starebbe","staremmo","stareste","starebbero","stavo","stavi","stava","stavamo","stavate","stavano","stetti","stesti","stette","stemmo","steste","stettero","stessi","stesse","stessimo","stessero","stando"],$m=["の","に","は","を","た","が","で","て","と","し","れ","さ","ある","いる","も","する","から","な","こと","として","い","や","れる","など","なっ","ない","この","ため","その","あっ","よう","また","もの","という","あり","まで","られ","なる","へ","か","だ","これ","によって","により","おり","より","による","ず","なり","られる","において","ば","なかっ","なく","しかし","について","せ","だっ","その後","できる","それ","う","ので","なお","のみ","でき","き","つ","における","および","いう","さらに","でも","ら","たり","その他","に関する","たち","ます","ん","なら","に対して","特に","せる","及び","これら","とき","では","にて","ほか","ながら","うち","そして","とともに","ただし","かつて","それぞれ","または","お","ほど","ものの","に対する","ほとんど","と共に","といった","です","とも","ところ","ここ"],Vm=["가","가까스로","가령","각","각각","각자","각종","갖고말하자면","같다","같이","개의치않고","거니와","거바","거의","것","것과 같이","것들","게다가","게우다","겨우","견지에서","결과에 이르다","결국","결론을 낼 수 있다","겸사겸사","고려하면","고로","곧","공동으로","과","과연","관계가 있다","관계없이","관련이 있다","관하여","관한","관해서는","구","구체적으로","구토하다","그","그들","그때","그래","그래도","그래서","그러나","그러니","그러니까","그러면","그러므로","그러한즉","그런 까닭에","그런데","그런즉","그럼","그럼에도 불구하고","그렇게 함으로써","그렇지","그렇지 않다면","그렇지 않으면","그렇지만","그렇지않으면","그리고","그리하여","그만이다","그에 따르는","그위에","그저","그중에서","그치지 않다","근거로","근거하여","기대여","기점으로","기준으로","기타","까닭으로","까악","까지","까지 미치다","까지도","꽈당","끙끙","끼익","나","나머지는","남들","남짓","너","너희","너희들","네","넷","년","논하지 않다","놀라다","누가 알겠는가","누구","다른","다른 방면으로","다만","다섯","다소","다수","다시 말하자면","다시말하면","다음","다음에","다음으로","단지","답다","당신","당장","대로 하다","대하면","대하여","대해 말하자면","대해서","댕그","더구나","더군다나","더라도","더불어","더욱더","더욱이는","도달하다","도착하다","동시에","동안","된바에야","된이상","두번째로","둘","둥둥","뒤따라","뒤이어","든간에","들","등","등등","딩동","따라","따라서","따위","따지지 않다","딱","때","때가 되어","때문에","또","또한","뚝뚝","라 해도","령","로","로 인하여","로부터","로써","륙","를","마음대로","마저","마저도","마치","막론하고","만 못하다","만약","만약에","만은 아니다","만이 아니다","만일","만큼","말하자면","말할것도 없고","매","매번","메쓰겁다","몇","모","모두","무렵","무릎쓰고","무슨","무엇","무엇때문에","물론","및","바꾸어말하면","바꾸어말하자면","바꾸어서 말하면","바꾸어서 한다면","바꿔 말하면","바로","바와같이","밖에 안된다","반대로","반대로 말하자면","반드시","버금","보는데서","보다더","보드득","본대로","봐","봐라","부류의 사람들","부터","불구하고","불문하고","붕붕","비걱거리다","비교적","비길수 없다","비로소","비록","비슷하다","비추어 보아","비하면","뿐만 아니라","뿐만아니라","뿐이다","삐걱","삐걱거리다","사","삼","상대적으로 말하자면","생각한대로","설령","설마","설사","셋","소생","소인","솨","쉿","습니까","습니다","시각","시간","시작하여","시초에","시키다","실로","심지어","아","아니","아니나다를가","아니라면","아니면","아니었다면","아래윗","아무거나","아무도","아야","아울러","아이","아이고","아이구","아이야","아이쿠","아하","아홉","안 그러면","않기 위하여","않기 위해서","알 수 있다","알았어","앗","앞에서","앞의것","야","약간","양자","어","어기여차","어느","어느 년도","어느것","어느곳","어느때","어느쪽","어느해","어디","어때","어떠한","어떤","어떤것","어떤것들","어떻게","어떻해","어이","어째서","어쨋든","어쩔수 없다","어찌","어찌됏든","어찌됏어","어찌하든지","어찌하여","언제","언젠가","얼마","얼마 안 되는 것","얼마간","얼마나","얼마든지","얼마만큼","얼마큼","엉엉","에","에 가서","에 달려 있다","에 대해","에 있다","에 한하다","에게","에서","여","여기","여덟","여러분","여보시오","여부","여섯","여전히","여차","연관되다","연이서","영","영차","옆사람","예","예를 들면","예를 들자면","예컨대","예하면","오","오로지","오르다","오자마자","오직","오호","오히려","와","와 같은 사람들","와르르","와아","왜","왜냐하면","외에도","요만큼","요만한 것","요만한걸","요컨대","우르르","우리","우리들","우선","우에 종합한것과같이","운운","월","위에서 서술한바와같이","위하여","위해서","윙윙","육","으로","으로 인하여","으로서","으로써","을","응","응당","의","의거하여","의지하여","의해","의해되다","의해서","이","이 되다","이 때문에","이 밖에","이 외에","이 정도의","이것","이곳","이때","이라면","이래","이러이러하다","이러한","이런","이럴정도로","이렇게 많은 것","이렇게되면","이렇게말하자면","이렇구나","이로 인하여","이르기까지","이리하여","이만큼","이번","이봐","이상","이어서","이었다","이와 같다","이와 같은","이와 반대로","이와같다면","이외에도","이용하여","이유만으로","이젠","이지만","이쪽","이천구","이천육","이천칠","이천팔","인 듯하다","인젠","일","일것이다","일곱","일단","일때","일반적으로","일지라도","임에 틀림없다","입각하여","입장에서","잇따라","있다","자","자기","자기집","자마자","자신","잠깐","잠시","저","저것","저것만큼","저기","저쪽","저희","전부","전자","전후","점에서 보아","정도에 이르다","제","제각기","제외하고","조금","조차","조차도","졸졸","좀","좋아","좍좍","주룩주룩","주저하지 않고","줄은 몰랏다","줄은모른다","중에서","중의하나","즈음하여","즉","즉시","지든지","지만","지말고","진짜로","쪽으로","차라리","참","참나","첫번째로","쳇","총적으로","총적으로 말하면","총적으로 보면","칠","콸콸","쾅쾅","쿵","타다","타인","탕탕","토하다","통하여","툭","퉤","틈타","팍","팔","퍽","펄렁","하","하게될것이다","하게하다","하겠는가","하고 있다","하고있었다","하곤하였다","하구나","하기 때문에","하기 위하여","하기는한데","하기만 하면","하기보다는","하기에","하나","하느니","하는 김에","하는 편이 낫다","하는것도","하는것만 못하다","하는것이 낫다","하는바","하더라도","하도다","하도록시키다","하도록하다","하든지","하려고하다","하마터면","하면 할수록","하면된다","하면서","하물며","하여금","하여야","하자마자","하지 않는다면","하지 않도록","하지마","하지마라","하지만","하하","한 까닭에","한 이유는","한 후","한다면","한다면 몰라도","한데","한마디","한적이있다","한켠으로는","한항목","할 따름이다","할 생각이다","할 줄 안다","할 지경이다","할 힘이 있다","할때","할만하다","할망정","할뿐","할수있다","할수있어","할줄알다","할지라도","할지언정","함께","해도된다","해도좋다","해봐요","해서는 안된다","해야한다","해요","했어요","향하다","향하여","향해서","허","허걱","허허","헉","헉헉","헐떡헐떡","형식으로 쓰여","혹시","혹은","혼자","훨씬","휘익","휴","흐흐","흥","힘입어","︿","～","￥"],Dm=["ئێمە","ئێوە","ئەم","ئەو","ئەوان","ئەوەی","بۆ","بێ","بێجگە","بە","بەبێ","بەدەم","بەردەم","بەرلە","بەرەوی","بەرەوە","بەلای","بەپێی","تۆ","تێ","جگە","دوای","دوو","دە","دەکات","دەگەڵ","سەر","لێ","لە","لەبابەت","لەباتی","لەبارەی","لەبرێتی","لەبن","لەبەر","لەبەینی","لەدەم","لەرێ","لەرێگا","لەرەوی","لەسەر","لەلایەن","لەناو","لەنێو","لەو","لەپێناوی","لەژێر","لەگەڵ","من","ناو","نێوان","هەر","هەروەها","و","وەک","پاش","پێ","پێش","چەند","کرد","کە","ی"],qm=["a","ab","ac","ad","at","atque","aut","autem","cum","de","dum","e","erant","erat","est","et","etiam","ex","haec","hic","hoc","in","ita","me","nec","neque","non","per","qua","quae","quam","qui","quibus","quidem","quo","quod","re","rebus","rem","res","sed","si","sic","sunt","tamen","tandem","te","ut","vel"],Bm=["aiz","ap","apakš","apakšpus","ar","arī","augšpus","bet","bez","bija","biji","biju","bijām","bijāt","būs","būsi","būsiet","būsim","būt","būšu","caur","diemžēl","diezin","droši","dēļ","esam","esat","esi","esmu","gan","gar","iekam","iekams","iekām","iekāms","iekš","iekšpus","ik","ir","it","itin","iz","ja","jau","jeb","jebšu","jel","jo","jā","ka","kamēr","kaut","kolīdz","kopš","kā","kļuva","kļuvi","kļuvu","kļuvām","kļuvāt","kļūs","kļūsi","kļūsiet","kļūsim","kļūst","kļūstam","kļūstat","kļūsti","kļūstu","kļūt","kļūšu","labad","lai","lejpus","līdz","līdzko","ne","nebūt","nedz","nekā","nevis","nezin","no","nu","nē","otrpus","pa","par","pat","pie","pirms","pret","priekš","pār","pēc","starp","tad","tak","tapi","taps","tapsi","tapsiet","tapsim","tapt","tapāt","tapšu","taču","te","tiec","tiek","tiekam","tiekat","tieku","tik","tika","tikai","tiki","tikko","tiklab","tiklīdz","tiks","tiksiet","tiksim","tikt","tiku","tikvien","tikām","tikāt","tikšu","tomēr","topat","turpretim","turpretī","tā","tādēļ","tālab","tāpēc","un","uz","vai","var","varat","varēja","varēji","varēju","varējām","varējāt","varēs","varēsi","varēsiet","varēsim","varēt","varēšu","vien","virs","virspus","vis","viņpus","zem","ārpus","šaipus"],Um=["abi","abidvi","abiejose","abiejuose","abiejø","abiem","abigaliai","abipus","abu","abudu","ai","ana","anaiptol","anaisiais","anajai","anajam","anajame","anapus","anas","anasai","anasis","anei","aniedvi","anieji","aniesiems","anoji","anojo","anojoje","anokia","anoks","anosiomis","anosioms","anosios","anosiose","anot","ant","antai","anuodu","anuoju","anuosiuose","anuosius","anàja","anàjà","anàjá","anàsias","anøjø","apie","aplink","ar","arba","argi","arti","aukðèiau","að","be","bei","beje","bemaþ","bent","bet","betgi","beveik","dar","dargi","daugmaþ","deja","dëka","dël","dëlei","dëlto","ech","et","gal","galbût","galgi","gan","gana","gi","greta","idant","iki","ir","irgi","it","itin","ið","iðilgai","iðvis","jaisiais","jajai","jajam","jajame","jei","jeigu","ji","jiedu","jiedvi","jieji","jiesiems","jinai","jis","jisai","jog","joji","jojo","jojoje","jokia","joks","josiomis","josioms","josios","josiose","judu","judvi","juk","jumis","jums","jumyse","juodu","juoju","juosiuose","juosius","jus","jàja","jàjà","jàsias","jájá","jøjø","jûs","jûsiðkis","jûsiðkë","jûsø","kad","kada","kadangi","kai","kaip","kaipgi","kas","katra","katras","katriedvi","katruodu","kaþin","kaþkas","kaþkatra","kaþkatras","kaþkokia","kaþkoks","kaþkuri","kaþkuris","kiaurai","kiek","kiekvienas","kieno","kita","kitas","kitokia","kitoks","kodël","kokia","koks","kol","kolei","kone","kuomet","kur","kurgi","kuri","kuriedvi","kuris","kuriuodu","lai","lig","ligi","link","lyg","man","manaisiais","manajai","manajam","manajame","manas","manasai","manasis","mane","manieji","maniesiems","manim","manimi","maniðkis","maniðkë","mano","manoji","manojo","manojoje","manosiomis","manosioms","manosios","manosiose","manuoju","manuosiuose","manuosius","manyje","manàja","manàjà","manàjá","manàsias","manæs","manøjø","mat","maþdaug","maþne","mes","mudu","mudvi","mumis","mums","mumyse","mus","mûsiðkis","mûsiðkë","mûsø","na","nagi","ne","nebe","nebent","negi","negu","nei","nejau","nejaugi","nekaip","nelyginant","nes","net","netgi","netoli","neva","nors","nuo","në","o","ogi","oi","paeiliui","pagal","pakeliui","palaipsniui","palei","pas","pasak","paskos","paskui","paskum","pat","pati","patiems","paties","pats","patys","patá","paèiais","paèiam","paèiame","paèiu","paèiuose","paèius","paèiø","per","pernelyg","pirm","pirma","pirmiau","po","prie","prieð","prieðais","pro","pusiau","rasi","rodos","sau","savaisiais","savajai","savajam","savajame","savas","savasai","savasis","save","savieji","saviesiems","savimi","saviðkis","saviðkë","savo","savoji","savojo","savojoje","savosiomis","savosioms","savosios","savosiose","savuoju","savuosiuose","savuosius","savyje","savàja","savàjà","savàjá","savàsias","savæs","savøjø","skersai","skradþiai","staèiai","su","sulig","ta","tad","tai","taigi","taip","taipogi","taisiais","tajai","tajam","tajame","tamsta","tarp","tarsi","tartum","tarytum","tas","tasai","tau","tavaisiais","tavajai","tavajam","tavajame","tavas","tavasai","tavasis","tave","tavieji","taviesiems","tavimi","taviðkis","taviðkë","tavo","tavoji","tavojo","tavojoje","tavosiomis","tavosioms","tavosios","tavosiose","tavuoju","tavuosiuose","tavuosius","tavyje","tavàja","tavàjà","tavàjá","tavàsias","tavæs","tavøjø","taèiau","te","tegu","tegul","tiedvi","tieji","ties","tiesiems","tiesiog","tik","tikriausiai","tiktai","toji","tojo","tojoje","tokia","toks","tol","tolei","toliau","tosiomis","tosioms","tosios","tosiose","tu","tuodu","tuoju","tuosiuose","tuosius","turbût","tàja","tàjà","tàjá","tàsias","tøjø","tûlas","uþ","uþtat","uþvis","va","vai","viduj","vidury","vien","vienas","vienokia","vienoks","vietoj","virð","virðuj","virðum","vis","vis dëlto","visa","visas","visgi","visokia","visoks","vos","vël","vëlgi","ypaè","á","ákypai","ástriþai","ðalia","ðe","ði","ðiaisiais","ðiajai","ðiajam","ðiajame","ðiapus","ðiedvi","ðieji","ðiesiems","ðioji","ðiojo","ðiojoje","ðiokia","ðioks","ðiosiomis","ðiosioms","ðiosios","ðiosiose","ðis","ðisai","ðit","ðita","ðitas","ðitiedvi","ðitokia","ðitoks","ðituodu","ðiuodu","ðiuoju","ðiuosiuose","ðiuosius","ðiàja","ðiàjà","ðiàsias","ðiøjø","ðtai","ðájá","þemiau"],Hm=["́","̀","nɨ","mà","rɨ","dɨ","ɨ","́nɨ","èrɨ","́á'","sɨ","àzɨ","yɨ","rá","vɨ","nga","be","mɨ","à","dà","kʉ","bá"," ́lé","má","e","yo","̀yɨ","ma","kɨ","àlʉ","́mà","rʉ́","drɨ","patí","a","è","yó","te","̀á","mà","mâ","dálé","yí","̌","pɨ","e'yó","ndráa","bo","di","drìá"],Wm=["ma","ni","ri","eri","di","yi","si","ba","nga","i","ra","ku","be","yo","da","azini","dria","ru","azi","mu","te","ndra","diyi","ima","mi","alu","nde","alia","le","vile","dri","pati","aria","bo","e'yo","tu","kini","dii","ama","eyi","dika","pi","e","angu","e'do","pie","ka","ti","o'du","du"],Km=["abdul","abdullah","acara","ada","adalah","ahmad","air","akan","akhbar","akhir","aktiviti","alam","amat","amerika","anak","anggota","antara","antarabangsa","apa","apabila","april","as","asas","asean","asia","asing","atas","atau","australia","awal","awam","bagaimanapun","bagi","bahagian","bahan","baharu","bahawa","baik","bandar","bank","banyak","barangan","baru","baru-baru","bawah","beberapa","bekas","beliau","belum","berada","berakhir","berbanding","berdasarkan","berharap","berikutan","berjaya","berjumlah","berkaitan","berkata","berkenaan","berlaku","bermula","bernama","bernilai","bersama","berubah","besar","bhd","bidang","bilion","bn","boleh","bukan","bulan","bursa","cadangan","china","dagangan","dalam","dan","dana","dapat","dari","daripada","dasar","datang","datuk","demikian","dengan","depan","derivatives","dewan","di","diadakan","dibuka","dicatatkan","dijangka","diniagakan","dis","disember","ditutup","dolar","dr","dua","dunia","ekonomi","eksekutif","eksport","empat","enam","faedah","feb","global","hadapan","hanya","harga","hari","hasil","hingga","hubungan","ia","iaitu","ialah","indeks","india","indonesia","industri","ini","islam","isnin","isu","itu","jabatan","jalan","jan","jawatan","jawatankuasa","jepun","jika","jualan","juga","julai","jumaat","jumlah","jun","juta","kadar","kalangan","kali","kami","kata","katanya","kaunter","kawasan","ke","keadaan","kecil","kedua","kedua-dua","kedudukan","kekal","kementerian","kemudahan","kenaikan","kenyataan","kepada","kepentingan","keputusan","kerajaan","kerana","kereta","kerja","kerjasama","kes","keselamatan","keseluruhan","kesihatan","ketika","ketua","keuntungan","kewangan","khamis","kini","kira-kira","kita","klci","klibor","komposit","kontrak","kos","kuala","kuasa","kukuh","kumpulan","lagi","lain","langkah","laporan","lebih","lepas","lima","lot","luar","lumpur","mac","mahkamah","mahu","majlis","makanan","maklumat","malam","malaysia","mana","manakala","masa","masalah","masih","masing-masing","masyarakat","mata","media","mei","melalui","melihat","memandangkan","memastikan","membantu","membawa","memberi","memberikan","membolehkan","membuat","mempunyai","menambah","menarik","menawarkan","mencapai","mencatatkan","mendapat","mendapatkan","menerima","menerusi","mengadakan","mengambil","mengenai","menggalakkan","menggunakan","mengikut","mengumumkan","mengurangkan","meningkat","meningkatkan","menjadi","menjelang","menokok","menteri","menunjukkan","menurut","menyaksikan","menyediakan","mereka","merosot","merupakan","mesyuarat","minat","minggu","minyak","modal","mohd","mudah","mungkin","naik","najib","nasional","negara","negara-negara","negeri","niaga","nilai","nov","ogos","okt","oleh","operasi","orang","pada","pagi","paling","pameran","papan","para","paras","parlimen","parti","pasaran","pasukan","pegawai","pejabat","pekerja","pelabur","pelaburan","pelancongan","pelanggan","pelbagai","peluang","pembangunan","pemberita","pembinaan","pemimpin","pendapatan","pendidikan","penduduk","penerbangan","pengarah","pengeluaran","pengerusi","pengguna","pengurusan","peniaga","peningkatan","penting","peratus","perdagangan","perdana","peringkat","perjanjian","perkara","perkhidmatan","perladangan","perlu","permintaan","perniagaan","persekutuan","persidangan","pertama","pertubuhan","pertumbuhan","perusahaan","peserta","petang","pihak","pilihan","pinjaman","polis","politik","presiden","prestasi","produk","program","projek","proses","proton","pukul","pula","pusat","rabu","rakan","rakyat","ramai","rantau","raya","rendah","ringgit","rumah","sabah","sahaja","saham","sama","sarawak","satu","sawit","saya","sdn","sebagai","sebahagian","sebanyak","sebarang","sebelum","sebelumnya","sebuah","secara","sedang","segi","sehingga","sejak","sekarang","sektor","sekuriti","selain","selama","selasa","selatan","selepas","seluruh","semakin","semalam","semasa","sementara","semua","semula","sen","sendiri","seorang","sepanjang","seperti","sept","september","serantau","seri","serta","sesi","setiap","setiausaha","sidang","singapura","sini","sistem","sokongan","sri","sudah","sukan","suku","sumber","supaya","susut","syarikat","syed","tahap","tahun","tan","tanah","tanpa","tawaran","teknologi","telah","tempat","tempatan","tempoh","tenaga","tengah","tentang","terbaik","terbang","terbesar","terbuka","terdapat","terhadap","termasuk","tersebut","terus","tetapi","thailand","tiada","tidak","tiga","timbalan","timur","tindakan","tinggi","tun","tunai","turun","turut","umno","unit","untuk","untung","urus","usaha","utama","walaupun","wang","wanita","wilayah","yang"],Zm=["अधिक","अनेक","अशी","असलयाचे","असलेल्या","असा","असून","असे","आज","आणि","आता","आपल्या","आला","आली","आले","आहे","आहेत","एक","एका","कमी","करणयात","करून","का","काम","काय","काही","किवा","की","केला","केली","केले","कोटी","गेल्या","घेऊन","जात","झाला","झाली","झाले","झालेल्या","टा","डॉ","तर","तरी","तसेच","ता","ती","तीन","ते","तो","त्या","त्याचा","त्याची","त्याच्या","त्याना","त्यानी","त्यामुळे","त्री","दिली","दोन","न","नाही","निर्ण्य","पण","पम","परयतन","पाटील","म","मात्र","माहिती","मी","मुबी","म्हणजे","म्हणाले","म्हणून","या","याचा","याची","याच्या","याना","यानी","येणार","येत","येथील","येथे","लाख","व","व्यकत","सर्व","सागित्ले","सुरू","हजार","हा","ही","हे","होणार","होत","होता","होती","होते"],Gm=["အပေါ်","အနက်","အမြဲတမ်း","အတွင်းတွင်","မကြာမီ","မတိုင်မီ","ဒါ့အပြင်","အောက်မှာ","အထဲမှာ","ဘယ်တော့မျှ","မကြာခဏ","တော်တော်လေး","စဉ်တွင်","နှင့်အတူ","နှင့်","နှင့်တကွ","ကျွန်တော်","ကျွန်မ","ငါ","ကျုပ်","ကျွနု်ပ်","ကျနော်","ကျမ","သူ","သူမ","ထိုဟာ","ထိုအရာ","ဤအရာ","ထို","၄င်း","ကျွန်တော်တို့","ကျွန်မတို့","ငါတို့","ကျုပ်တို့","ကျွနု်ပ်တို့","ကျနော်တို့","ကျမတို့","သင်","သင်တို့","နင်တို့","မင်း","မင်းတို့","သူတို့","ကျွန်တော်အား","ကျွန်တော်ကို","ကျွန်မကို","ငါကို","ကျုပ်ကို","ကျွနု်ပ်ကို","သူ့ကို","သူမကို","ထိုအရာကို","သင့်ကို","သင်တို့ကို","နင်တို့ကို","မင်းကို","မင်းတို့ကို","ငါတို့ကို","ကျုပ်တို့ကို","ကျွနု်ပ်တို့ကို","မိမိကိုယ်တိုင်","မိမိဘာသာ","မင်းကိုယ်တိုင်","မင်းဘာသာ","မင်းတို့ကိုယ်တိုင်","မင်းတို့ဘာသာ","သူကိုယ်တိုင်","ကိုယ်တိုင်","သူမကိုယ်တိုင်","သူ့ဘာသာ","သူ့ကိုယ်ကို","ကိုယ့်ကိုယ်ကို","မိမိကိုယ်ကို","၄င်းပင်","ထိုအရာပင်","သည့်","မည့်","တဲ့","ကျွနု်ပ်၏","ကျွန်တော်၏","ကျွန်မ၏","ကျနော်၏","ကျမ၏","သူ၏","သူမ၏","ထိုအရာ၏","ထိုဟာ၏","ကျွနု်ပ်တို့၏","ငါတို့၏","ကျွန်တော်တို့၏","ကျွန်မတို့၏","ကျနော်တို့၏","ကျမတို့၏","သင်၏","သင်တို့၏","မင်း၏","မင်းတို့၏","သူတို့၏","ကျွန်တော့်ဟာ","ကျွန်မဟာ","ကျနော်၏ဟာ","ကျမ၏ဟာ","ကျမဟာ","ကျနော်ဟာ","သူဟာ","သူမဟာ","သူ့ဟာ","ကျွနု်ပ်တို့ဟာ","ကျွန်တော်တို့ဟာ","ကျွန်မတို့ဟာ","သင်တို့ဟာ","မင်းတို့ဟာ","သူတို့ဟာ","သူမတို့ဟာ","ဤအရာ","ဟောဒါ","ဟောဒီ","ဟောဒီဟာ","ဒီဟာ","ဒါ","ထိုအရာ","၄င်းအရာ","ယင်းအရာ","အဲဒါ","ဟိုဟာ","အချို့","တစ်ခုခု","အဘယ်မဆို","ဘယ်အရာမဆို","အဘယ်မည်သော","အကြင်","အရာရာတိုင်း","စိုးစဉ်မျှ","စိုးစဉ်းမျှ","ဘယ်လောက်မဆို","တစ်စုံတစ်ရာ","တစုံတရာ","အလျဉ်းမဟုတ်","မည်သည့်နည်းနှင့်မျှမဟုတ်","အလျဉ်းမရှိသော","အခြားဖြစ်သော","အခြားသော","အခြားတစ်ခု","အခြားတစ်ယောက်","အားလုံး","အရာရာတိုင်း","အကုန်လုံး","အလုံးစုံ","အရာခပ်သိမ်း","တစ်ခုစီ","အသီးသီး","တစ်ဦးဦး","တစ်ခုခု","ကိုယ်စီကိုယ်ငှ","ကိုယ်စီ","တစ်ဦးစီ","တစ်ယောက်စီ","တစ်ခုစီ","အကုန်","အပြည့်အစုံ","လုံးလုံး","နှစ်ခုလုံး","နှစ်ယောက်လုံး","နှစ်ဘက်လုံး","တစ်စုံတစ်ရာ","တစ်စုံတစ်ခု","တစုံတခု","တစ်စုံတစ်ယောက်","တစုံတယောက်","တစ်ယောက်ယောက်","မည်သူမဆို","ဘာမျှမရှိ","ဘာမှမရှိ","အဘယ်အရာမျှမရှိ","လူတိုင်း","လူတကာ","နှင့်","ပြီးလျှင်","၄င်းနောက်","သို့မဟုတ်","သို့တည်းမဟုတ်","သို့မဟုတ်လျှင်","ဒါမှမဟုတ်","ဖြစ်စေ","သို့စေကာမူ","ဒါပေမယ့်","ဒါပေမဲ့","မှတစ်ပါး","မှလွဲလျှင်","အဘယ်ကြောင့်ဆိုသော်","သောကြောင့်","သဖြင့်","၍","သည့်အတွက်ကြောင့်","လျှင်","ပါက","အကယ်၍","သော်ငြားလည်း","စေကာမူ","နည်းတူ","ပေမယ့်","ပေမဲ့","ထိုနည်းတူစွာ","ထိုနည်းတူ","ကဲ့သို့","သကဲ့သို့","ယင်းကဲ့သို့","ထိုကဲ့သို့","နှင့်စပ်လျဉ်း၍","ဤမျှ","ဤမျှလောက်","ဤကဲ့သို့","အခုလောက်ထိ","ဒါကတော့","အဘယ်ကဲ့သလို့","မည်ကဲ့သို့","မည်သည့်နည်းနှင့်","မည်သည့်နည်းဖြင့်","မည်သည့်နည့်နှင့်မဆို","မည်သည့်နည်းဖြင့်မဆို","မည်သို့","ဘယ်လိုလဲ","သို့ပေတည့်","သို့ပေမည့်","ဘယ်နည်းနှင့်","မည်ရွေ့မည်မျှ","အဘယ်မျှလောက်","ဘယ်လောက်","မည်သူ","ဘယ်သူ","မည်သည့်အကြောင်းကြောင့်","ဘာအတွက်ကြောင့်","အဘယ်ကြောင့်","မည်သည့်အတွက်ကြောင့်","ဘာကြောင့်","ဘာအတွက်နဲ့လဲ","မည်သည်","ဘာလဲ","အဘယ်အရာနည်း","မည်သည့်အရပ်မှာ","ဘယ်နေရာတွင်","မည်သည့်နေရာတွင်","မည်သည့်နေရာသို့","ဘယ်နေရာသို့","ဘယ်နေရာမှာ","ဘယ်သူ၏","မည်သည့်အရာ၏","မည်သည့်အခါ","ဘယ်အချိန်","ဘယ်အခါ","မည်သည့်အချိန်","ဘယ်တော့","မည်သူကို","မည်သူက","ဘယ်သူ့ကို","မည်သူမည်ဝါ","မည်သည့်အရာ","ဘယ်အရာ","မည်သို့ပင်ဖြစ်စေ","ဘယ်လိုပဲဖြစ်ဖြစ်","မည်ရွေ့မည်မျှဖြစ်စေ","မည်သည့်နည်းနှင့်မဆို","ဘယ်နည်းနဲ့ဖြစ်ဖြစ်","မည်သူမဆို","ဘယ်သူမဆို","အဘယ်သူမဆို","မည်သည့်အရာမဆို","ဘာဖြစ်ဖြစ်","မည်သည့်အရာဖြစ်ဖြစ်","မည်သည့်အရပ်၌မဆို","မည်သည့်နေရာမဆို","ဘယ်အခါမဆို","ဘယ်အချိန်မဆို","ဘယ်အခါဖြစ်ဖြစ်","အချိန်အခါမရွေး"],Ym=["og","i","jeg","det","at","en","et","den","til","er","som","på","de","med","han","av","ikke","der","så","var","meg","seg","men","ett","har","om","vi","min","mitt","ha","hadde","hun","nå","over","da","ved","fra","du","ut","sin","dem","oss","opp","man","kan","hans","hvor","eller","hva","skal","selv","sjøl","her","alle","vil","bli","ble","blitt","kunne","inn","når","kom","noen","noe","ville","dere","som","deres","kun","ja","etter","ned","skulle","denne","for","deg","si","sine","sitt","mot","å","meget","hvorfor","dette","disse","uten","hvordan","ingen","din","ditt","blir","samme","hvilken","hvilke","sånn","inni","mellom","vår","hver","hvem","vors","hvis","både","bare","enn","fordi","før","mange","også","slik","vært","være","begge","siden","henne","hennar","hennes"],Jm=["ਦੇ","ਵਿੱਚ","ਦਾ","ਅਤੇ","ਦੀ","ਇੱਕ","ਨੂੰ","ਹੈ","ਤੋਂ","ਇਸ","ਇਹ","ਨੇ","ਤੇ","ਨਾਲ","ਲਈ","ਵੀ","ਸੀ","ਵਿਚ","ਕਿ","ਜੋ","ਉਹ","ਉਸ","ਹਨ","ਜਾਂਦਾ","ਕੀਤਾ","ਗਿਆ","ਹੀ","ਕੇ","ਜਾਂ","ਦੀਆਂ","ਜਿਸ","ਕਰਨ","ਹੋ","ਕਰ","ਆਪਣੇ","ਕੀਤੀ","ਤੌਰ","ਬਾਅਦ","ਨਹੀਂ","ਭਾਰਤੀ","ਪਿੰਡ","ਸਿੰਘ","ਉੱਤੇ","ਸਾਲ","।","ਪੰਜਾਬ","ਸਭ","ਭਾਰਤ","ਉਨ੍ਹਾਂ","ਹੁੰਦਾ","ਤੱਕ","ਇਕ","ਹੋਇਆ","ਜਨਮ","ਬਹੁਤ","ਪਰ","ਦੁਆਰਾ","ਰੂਪ","ਹੋਰ","ਕੰਮ","ਆਪਣੀ","ਤਾਂ","ਸਮੇਂ","ਪੰਜਾਬੀ","ਗਈ","ਦਿੱਤਾ","ਦੋ","ਕਿਸੇ","ਕਈ","ਜਾ","ਵਾਲੇ","ਸ਼ੁਰੂ","ਉਸਨੇ","ਕਿਹਾ","ਹੋਣ","ਲੋਕ","ਜਾਂਦੀ","ਵਿੱਚੋਂ","ਨਾਮ","ਜਦੋਂ","ਪਹਿਲਾਂ","ਕਰਦਾ","ਹੁੰਦੀ","ਹੋਏ","ਸਨ","ਵਜੋਂ","ਰਾਜ","ਮੁੱਖ","ਕਰਦੇ","ਕੁਝ","ਸਾਰੇ","ਹੁੰਦੇ","ਸ਼ਹਿਰ","ਭਾਸ਼ਾ","ਹੋਈ","ਅਨੁਸਾਰ","ਸਕਦਾ","ਆਮ","ਵੱਖ","ਕੋਈ","ਵਾਰ","ਗਏ","ਖੇਤਰ","ਜੀ","ਕਾਰਨ","ਕਰਕੇ","ਜਿਵੇਂ","ਜ਼ਿਲ੍ਹੇ","ਲੋਕਾਂ","ਚ","ਸਾਹਿਤ","ਸਦੀ","ਬਾਰੇ","ਜਾਂਦੇ","ਵਾਲਾ","ਜਾਣ","ਪਹਿਲੀ","ਪ੍ਰਾਪਤ","ਰਿਹਾ","ਵਾਲੀ","ਨਾਂ","ਦੌਰਾਨ","ਤਰ੍ਹਾਂ","ਯੂਨੀਵਰਸਿਟੀ","ਨਾ","ਏ","ਤਿੰਨ","ਇਨ੍ਹਾਂ","ਗੁਰੂ","ਇਸਨੂੰ","ਇਹਨਾਂ","ਪਿਤਾ","ਲਿਆ","ਸ਼ਾਮਲ","ਸ਼ਬਦ","ਅੰਗਰੇਜ਼ੀ","ਉਸਨੂੰ","ਉਹਨਾਂ","ਸਥਿਤ","ਫਿਰ","ਜੀਵਨ","ਸਕੂਲ","ਹੁਣ","ਦਿਨ","ਕੀਤੇ","ਆਦਿ","ਵੱਧ","ਲੈ","ਘਰ","ਵੱਲ","ਦੇਸ਼","ਵਲੋਂ","ਬਣ","ਵੀਂ","ਫਿਲਮ","ਉਮਰ","ਬਲਾਕ","ਰਹੇ","ਸਾਹਿਬ","ਕਰਦੀ","ਹਰ","ਪੈਦਾ","ਘੱਟ","ਲੇਖਕ","ਹਿੱਸਾ","ਫ਼ਿਲਮ","ਮੌਤ","ਜਿੱਥੇ","ਵੱਡਾ","ਵਿਖੇ","ਆਪਣਾ","ਪਹਿਲਾ","ਵਰਤੋਂ","ਆਪ","ਕਰਨਾ","ਵਿਆਹ","ਰਹੀ","ਰਾਹੀਂ","ਦਿੱਤੀ","ਉਸਦੇ","ਪਰਿਵਾਰ","ਆ","ਦੂਜੇ","ਅਮਰੀਕਾ","ਮੰਨਿਆ","ਇਸਦੇ","ਈ","ਕਾਲਜ","ਸਰਕਾਰ","ਇੱਥੇ","ਪਾਕਿਸਤਾਨ","ਸ਼ਾਮਿਲ","ਵਿਗਿਆਨ","ਉਸਦੀ","ਪੇਸ਼","ਕਿਉਂਕਿ","ਪਹਿਲੇ","ਧਰਮ","ਮਸ਼ਹੂਰ","ਅੰਦਰ","ਵਿਚੋਂ","ਜਿਨ੍ਹਾਂ","ਜਾਣਿਆ","ਪਾਣੀ","ਇਲਾਵਾ","ਅਰਥ","ਚਾਰ","ਪ੍ਰਸਿੱਧ","ਨਾਵਲ","ਵੱਡੇ","ਵੱਲੋਂ","ਕਹਾਣੀ","ਵਿਸ਼ਵ","ਮੂਲ","ਅਮਰੀਕੀ","ਸਥਾਨ","ਇਤਿਹਾਸ","ਕੁੱਝ","ਵਿਕਾਸ","ਉੱਤਰ","ਸਿੱਖਿਆ","ਹਿੰਦੀ","ਪ੍ਰਮੁੱਖ","ਰਚਨਾ","ਬਣਾਇਆ","ਵਿਸ਼ੇਸ਼","ਡਾ","ਉੱਪਰ","ਪੱਛਮੀ","ਦੇਣ","ਇਸਦਾ","ਸਕਦੇ","ਰੱਖਿਆ","ਕਵੀ","ਦਿੱਲੀ","ਵੱਡੀ","ਭੂਮਿਕਾ","ਸਮਾਜ","ਕਾਵਿ","ਕੀ","ਕੋਲ","ਦ","ਗੱਲ","ਸੰਸਾਰ","ਭਾਗ","ਆਈ","ਦੱਖਣ","ਅੱਜ","ਸਿੱਖ","ਕਹਿੰਦੇ","ਸੰਗੀਤ","ਕਿਲੋਮੀਟਰ","ਜਿਹਨਾਂ","ਸਭਾ","ਜਿਸਦਾ","ਜਨਵਰੀ","ਕਵਿਤਾ","ਮੈਂਬਰ","ਲਿਖਿਆ","ਮਾਂ","ਕਲਾ","ਪੰਜ","ਥਾਂ","ਹੇਠ","ਜਿਆਦਾ","ਵਰਤਿਆ","ਮਾਰਚ","ਡੀ","ਅਕਤੂਬਰ","ਤਕ","ਨਾਟਕ","ਬੀ","ਖਾਸ","ਇਸੇ","ਆਧੁਨਿਕ","ਅਗਸਤ","ਤਿਆਰ","ਮਾਤਾ","ਬਣਾਉਣ","ਨਵੰਬਰ","ਵਿਅਕਤੀ","ਦੱਖਣੀ","ਦਸੰਬਰ","ਆਫ","ਗੀਤ","ਗਿਣਤੀ","ਕਾਲ","ਖੋਜ","ਸਾਲਾਂ","ਪੂਰੀ","ਸਮਾਂ","ਜ਼ਿਆਦਾ","ਇਸਦੀ","ਸਕਦੀ","ਵਿਚਕਾਰ","ਰਾਜਧਾਨੀ","ਉਸਦਾ","ਜੁਲਾਈ","ਜੂਨ","ਅਧੀਨ","ਸਥਾਪਨਾ","ਸੇਵਾ","ਭਾਵ","ਵਰਗ","ਛੋਟੇ","ਦਿੰਦਾ","ਸਮਾਜਿਕ","ਹੁੰਦੀਆਂ","ਟੀਮ","ਔਰਤਾਂ","ਅਕਸਰ","ਪ੍ਰਕਾਸ਼ਿਤ","ਉਰਦੂ","ਰੰਗ","ਪਾਰਟੀ","ਬਣਾ","ਪ੍ਰਭਾਵ","ਸ਼ੁਰੂਆਤ","ਲਗਭਗ","ਮਈ","ਸਿਰਫ","ਨੇੜੇ","ਜਿਸਨੂੰ","ਹਾਲਾਂਕਿ","ਦੂਰ","ਸਤੰਬਰ","ਕਿਤਾਬ","ਕਦੇ","ਉੱਤਰੀ","ਪ੍ਰਕਾਰ","ਇਸਨੇ","ਪ੍ਰਦੇਸ਼","ਅੱਗੇ","ਸੰਯੁਕਤ","ਪੜ੍ਹਾਈ","ਵਧੇਰੇ","ਨਾਲ਼","ਮਨੁੱਖ","ਬਾਕੀ","ਪ੍ਰਧਾਨ","ਦੂਜੀ","ਕੁੱਲ","ਆਫ਼","ਅਧਿਐਨ","ਰਾਸ਼ਟਰੀ","ਪੁੱਤਰ","ਅੰਤਰਰਾਸ਼ਟਰੀ","ਧਰਤੀ","ਕੇਂਦਰ","ਦੇਸ਼ਾਂ","ਮੱਧ","ਜ਼ਿਲ੍ਹਾ","ਸਾਰੀਆਂ","ਪੱਧਰ","ਹੋਵੇ","ਜੇ","ਭਾਈ","ਰਹਿਣ","ਪੁਰਸਕਾਰ","ਸਭਿਆਚਾਰ","ਪਤਾ","ਪਾਸੇ","ਨਵੇਂ","ਕੰਪਨੀ","ਬਾਹਰ","ਵੇਲੇ","ਸੰਨ","ਪੂਰਬੀ","ਵਿਚਾਰ","ਕਾਰਜ","ਪੀ","ਮਹੱਤਵਪੂਰਨ","ਦੁਨੀਆਂ","ਧਾਰਮਿਕ","ਮਨੁੱਖੀ","ਸਮੂਹ","ਅਜਿਹੇ","ਲਾਲ","ਦੂਜਾ","ਭਰਾ","ਸ੍ਰੀ","ਅੰਤ","ਜਾਂਦੀਆਂ","ਸ਼ਾਹ","ਰਹਿੰਦੇ","ਮਹਾਨ","ਚੀਨ","ਮੀਟਰ","ਵਰਗੇ","ਨਾਲੋਂ","ਹਾਸਲ","ਕਿਸਮ","ਅਜਿਹਾ","ਬਣਿਆ","ਭਰ","ਛੱਡ","ਲੈਣ","ਹਿੱਸੇ","ਟੀ","ਲਿਖੇ","ਮਿਲ","ਮੌਜੂਦ","ਦਿੱਤੇ","ਵਾਸਤੇ","ਵਾਲੀਆਂ","ਵਧੀਆ","ਰੂਸੀ","ਜਾਰੀ","ਸਰਕਾਰੀ","ਡਿਗਰੀ","ਪੱਛਮ","ਲੜਾਈ","ਭਾਸ਼ਾਵਾਂ","ਰਾਜਾ","ਜਲੰਧਰ","ਹਿੰਦੂ","ਔਰਤ","ਜੰਗ","ਬਾਬਾ","ਬੱਚਿਆਂ","ਮੰਤਰੀ","ਪਟਿਆਲਾ","ਵਾਂਗ","ਆਉਣ","ਭਾਵੇਂ","ਕੇਵਲ","ਐਸ","ਪ੍ਰਾਚੀਨ","ਰਹਿੰਦਾ","ਬੋਲੀ","ਅਵਾਰਡ","ਨਗਰ","ਖੇਡਾਂ","ਫਿਲਮਾਂ","ਬੱਚੇ","ਕੌਰ","ਤੋ","ਪ੍ਰਤੀ","ਕੁਆਂਟਮ","ਅਬਾਦੀ","ਪੁਸਤਕ","ਐਮ","ਰਾਮ","ਖੇਤਰਾਂ","ਫਰਵਰੀ","ਕ੍ਰਿਕਟ","ਪੈਂਦਾ","ਇਤਿਹਾਸਕ","ਲੱਗ","ਬ੍ਰਿਟਿਸ਼","ਆਇਆ","ਮਿਲਦਾ"],Qm=["از","با","به","برای","و","باید","شاید","اکنون","اگر","اگرچه","الا","اما","اندر","اینکه","باری","بالعکس","بدون","بر","بلکه","بنابراین","بی","پس","تا","جز","چنانچه","چه","چون","در","را","روی","زیرا","سپس","غیر","که","لیکن","مانند","مثل","مگر","نه","نیز","هرچند","هم","همان","وانگهی","ولی","ولو","همانند","همچو"],Xm=["a","aby","ach","acz","aczkolwiek","aj","albo","ale","ależ","ani","aż","bardziej","bardzo","bo","bowiem","by","byli","bynajmniej","być","był","była","było","były","będzie","będą","cali","cała","cały","ci","cię","ciebie","co","cokolwiek","coś","czasami","czasem","czemu","czy","czyli","daleko","dla","dlaczego","dlatego","do","dobrze","dokąd","dość","dużo","dwa","dwaj","dwie","dwoje","dziś","dzisiaj","gdy","gdyby","gdyż","gdzie","gdziekolwiek","gdzieś","i","ich","ile","im","inna","inne","inny","innych","iż","ja","ją","jak","jakaś","jakby","jaki","jakichś","jakie","jakiś","jakiż","jakkolwiek","jako","jakoś","je","jeden","jedna","jedno","jednak","jednakże","jego","jej","jemu","jest","jestem","jeszcze","jeśli","jeżeli","już","ją","każdy","kiedy","kilka","kimś","kto","ktokolwiek","ktoś","która","które","którego","której","który","których","którym","którzy","ku","lat","lecz","lub","ma","mają","mało","mam","mi","mimo","między","mną","mnie","mogą","moi","moim","moja","moje","może","możliwe","można","mój","mu","musi","my","na","nad","nam","nami","nas","nasi","nasz","nasza","nasze","naszego","naszych","natomiast","natychmiast","nawet","nią","nic","nich","nie","niech","niego","niej","niemu","nigdy","nim","nimi","niż","no","o","obok","od","około","on","ona","one","oni","ono","oraz","oto","owszem","pan","pana","pani","po","pod","podczas","pomimo","ponad","ponieważ","powinien","powinna","powinni","powinno","poza","prawie","przecież","przed","przede","przedtem","przez","przy","roku","również","sam","sama","są","się","skąd","sobie","sobą","sposób","swoje","ta","tak","taka","taki","takie","także","tam","te","tego","tej","temu","ten","teraz","też","to","tobą","tobie","toteż","trzeba","tu","tutaj","twoi","twoim","twoja","twoje","twym","twój","ty","tych","tylko","tym","u","w","wam","wami","was","wasz","zaś","wasza","wasze","we","według","wiele","wielu","więc","więcej","tę","wszyscy","wszystkich","wszystkie","wszystkim","wszystko","wtedy","wy","właśnie","z","za","zapewne","zawsze","ze","zł","znowu","znów","został","żaden","żadna","żadne","żadnych","że","żeby"],eh=["a","à","ao","aos","aquela","aquelas","aquele","aqueles","aquilo","as","às","até","com","como","da","das","de","dela","delas","dele","deles","depois","do","dos","e","ela","elas","ele","eles","em","entre","essa","essas","esse","esses","esta","estas","este","estes","eu","isso","isto","já","lhe","lhes","mais","mas","me","mesmo","meu","meus","minha","minhas","muito","muitos","na","não","nas","nem","no","nos","nós","nossa","nossas","nosso","nossos","num","nuns","numa","numas","o","os","ou","para","pela","pelas","pelo","pelos","por","quais","qual","quando","que","quem","se","sem","seu","seus","só","sua","suas","também","te","teu","teus","tu","tua","tuas","um","uma","umas","você","vocês","vos","vosso","vossos"],th=["a","à","adeus","agora","aí","ainda","além","algo","alguém","algum","alguma","algumas","alguns","ali","ampla","amplas","amplo","amplos","ano","anos","ante","antes","ao","aos","apenas","apoio","após","aquela","aquelas","aquele","aqueles","aqui","aquilo","área","as","às","assim","até","atrás","através","baixo","bastante","bem","boa","boas","bom","bons","breve","cá","cada","catorze","cedo","cento","certamente","certeza","cima","cinco","coisa","coisas","com","como","conselho","contra","contudo","custa","da","dá","dão","daquela","daquelas","daquele","daqueles","dar","das","de","debaixo","dela","delas","dele","deles","demais","dentro","depois","desde","dessa","dessas","desse","desses","desta","destas","deste","destes","deve","devem","devendo","dever","deverá","deverão","deveria","deveriam","devia","deviam","dez","dezenove","dezesseis","dezessete","dezoito","dia","diante","disse","disso","disto","dito","diz","dizem","dizer","do","dois","dos","doze","duas","dúvida","e","é","ela","elas","ele","eles","em","embora","enquanto","entre","era","eram","éramos","és","essa","essas","esse","esses","esta","está","estamos","estão","estar","estas","estás","estava","estavam","estávamos","este","esteja","estejam","estejamos","estes","esteve","estive","estivemos","estiver","estivera","estiveram","estivéramos","estiverem","estivermos","estivesse","estivessem","estivéssemos","estiveste","estivestes","estou","etc","eu","exemplo","faço","falta","favor","faz","fazeis","fazem","fazemos","fazendo","fazer","fazes","feita","feitas","feito","feitos","fez","fim","final","foi","fomos","for","fora","foram","fôramos","forem","forma","formos","fosse","fossem","fôssemos","foste","fostes","fui","geral","grande","grandes","grupo","há","haja","hajam","hajamos","hão","havemos","havia","hei","hoje","hora","horas","houve","houvemos","houver","houvera","houverá","houveram","houvéramos","houverão","houverei","houverem","houveremos","houveria","houveriam","houveríamos","houvermos","houvesse","houvessem","houvéssemos","isso","isto","já","la","lá","lado","lhe","lhes","lo","local","logo","longe","lugar","maior","maioria","mais","mal","mas","máximo","me","meio","menor","menos","mês","meses","mesma","mesmas","mesmo","mesmos","meu","meus","mil","minha","minhas","momento","muita","muitas","muito","muitos","na","nada","não","naquela","naquelas","naquele","naqueles","nas","nem","nenhum","nenhuma","nessa","nessas","nesse","nesses","nesta","nestas","neste","nestes","ninguém","nível","no","noite","nome","nos","nós","nossa","nossas","nosso","nossos","nova","novas","nove","novo","novos","num","numa","número","nunca","o","obra","obrigada","obrigado","oitava","oitavo","oito","onde","ontem","onze","os","ou","outra","outras","outro","outros","para","parece","parte","partir","paucas","pela","pelas","pelo","pelos","pequena","pequenas","pequeno","pequenos","per","perante","perto","pode","pude","pôde","podem","podendo","poder","poderia","poderiam","podia","podiam","põe","põem","pois","ponto","pontos","por","porém","porque","porquê","posição","possível","possivelmente","posso","pouca","poucas","pouco","poucos","primeira","primeiras","primeiro","primeiros","própria","próprias","próprio","próprios","próxima","próximas","próximo","próximos","pude","puderam","quais","quáis","qual","quando","quanto","quantos","quarta","quarto","quatro","que","quê","quem","quer","quereis","querem","queremas","queres","quero","questão","quinta","quinto","quinze","relação","sabe","sabem","são","se","segunda","segundo","sei","seis","seja","sejam","sejamos","sem","sempre","sendo","ser","será","serão","serei","seremos","seria","seriam","seríamos","sete","sétima","sétimo","seu","seus","sexta","sexto","si","sido","sim","sistema","só","sob","sobre","sois","somos","sou","sua","suas","tal","talvez","também","tampouco","tanta","tantas","tanto","tão","tarde","te","tem","tém","têm","temos","tendes","tendo","tenha","tenham","tenhamos","tenho","tens","ter","terá","terão","terceira","terceiro","terei","teremos","teria","teriam","teríamos","teu","teus","teve","ti","tido","tinha","tinham","tínhamos","tive","tivemos","tiver","tivera","tiveram","tivéramos","tiverem","tivermos","tivesse","tivessem","tivéssemos","tiveste","tivestes","toda","todas","todavia","todo","todos","trabalho","três","treze","tu","tua","tuas","tudo","última","últimas","último","últimos","um","uma","umas","uns","vai","vais","vão","vários","vem","vêm","vendo","vens","ver","vez","vezes","viagem","vindo","vinte","vir","você","vocês","vos","vós","vossa","vossas","vosso","vossos","zero"],ah=["acea","aceasta","această","aceea","acei","aceia","acel","acela","acele","acelea","acest","acesta","aceste","acestea","aceşti","aceştia","acolo","acord","acum","ai","aia","aibă","aici","al","ale","alea","altceva","altcineva","am","ar","are","asemenea","asta","astea","astăzi","asupra","au","avea","avem","aveţi","azi","aş","aşadar","aţi","bine","bucur","bună","ca","care","caut","ce","cel","ceva","chiar","cinci","cine","cineva","contra","cu","cum","cumva","curând","curînd","când","cât","câte","câtva","câţi","cînd","cît","cîte","cîtva","cîţi","că","căci","cărei","căror","cărui","către","da","dacă","dar","datorită","dată","dau","de","deci","deja","deoarece","departe","deşi","din","dinaintea","dintr-","dintre","doi","doilea","două","drept","după","dă","ea","ei","el","ele","eram","este","eu","eşti","face","fata","fi","fie","fiecare","fii","fim","fiu","fiţi","frumos","fără","graţie","halbă","iar","ieri","la","le","li","lor","lui","lângă","lîngă","mai","mea","mei","mele","mereu","meu","mi","mie","mine","mult","multă","mulţi","mulţumesc","mâine","mîine","mă","ne","nevoie","nici","nicăieri","nimeni","nimeri","nimic","nişte","noastre","noastră","noi","noroc","nostru","nouă","noştri","nu","opt","ori","oricare","orice","oricine","oricum","oricând","oricât","oricînd","oricît","oriunde","patra","patru","patrulea","pe","pentru","peste","pic","poate","pot","prea","prima","primul","prin","printr-","puţin","puţina","puţină","până","pînă","rog","sa","sale","sau","se","spate","spre","sub","sunt","suntem","sunteţi","sută","sînt","sîntem","sînteţi","să","săi","său","ta","tale","te","timp","tine","toate","toată","tot","totuşi","toţi","trei","treia","treilea","tu","tăi","tău","un","una","unde","undeva","unei","uneia","unele","uneori","unii","unor","unora","unu","unui","unuia","unul","vi","voastre","voastră","voi","vostru","vouă","voştri","vreme","vreo","vreun","vă","zece","zero","zi","zice","îi","îl","îmi","împotriva","în","înainte","înaintea","încotro","încât","încît","între","întrucât","întrucît","îţi","ăla","ălea","ăsta","ăstea","ăştia","şapte","şase","şi","ştiu","ţi","ţie"],nh=["и","в","во","не","что","он","на","я","с","со","как","а","то","все","она","так","его","но","да","ты","к","у","же","вы","за","бы","по","только","ее","мне","было","вот","от","меня","еще","нет","о","из","ему","теперь","когда","даже","ну","ли","если","уже","или","ни","быть","был","него","до","вас","нибудь","уж","вам","сказал","ведь","там","потом","себя","ничего","ей","может","они","тут","где","есть","надо","ней","для","мы","тебя","их","чем","была","сам","чтоб","без","будто","чего","раз","тоже","себе","под","будет","ж","тогда","кто","этот","того","потому","этого","какой","совсем","ним","этом","почти","мой","тем","чтобы","нее","были","куда","всех","никогда","сегодня","можно","при","об","другой","хоть","после","над","больше","тот","через","эти","нас","про","всего","них","какая","много","разве","эту","моя","свою","этой","перед","иногда","лучше","чуть","том","нельзя","такой","им","более","всегда","конечно","всю","между","это","лишь"],rh=["a","aby","aj","ako","aký","ale","alebo","ani","avšak","ba","bez","buï","cez","do","ho","hoci","i","ich","im","ja","jeho","jej","jemu","ju","k","kam","kde","kedže","keï","kto","ktorý","ku","lebo","ma","mi","mne","mnou","mu","my","mòa","môj","na","nad","nami","neho","nej","nemu","nich","nielen","nim","no","nám","nás","náš","ním","o","od","on","ona","oni","ono","ony","po","pod","pre","pred","pri","s","sa","seba","sem","so","svoj","taký","tam","teba","tebe","tebou","tej","ten","ti","tie","to","toho","tomu","tou","tvoj","ty","tá","tým","v","vami","veï","vo","vy","vám","vás","váš","však","z","za","zo","a","èi","èo","èí","òom","òou","òu","že"],sh=["a","ali","april","avgust","b","bi","bil","bila","bile","bili","bilo","biti","blizu","bo","bodo","bojo","bolj","bom","bomo","boste","bova","boš","brez","c","cel","cela","celi","celo","d","da","daleč","dan","danes","datum","december","deset","deseta","deseti","deseto","devet","deveta","deveti","deveto","do","dober","dobra","dobri","dobro","dokler","dol","dolg","dolga","dolgi","dovolj","drug","druga","drugi","drugo","dva","dve","e","eden","en","ena","ene","eni","enkrat","eno","etc.","f","februar","g","g.","ga","ga.","gor","gospa","gospod","h","halo","i","idr.","ii","iii","in","iv","ix","iz","j","januar","jaz","je","ji","jih","jim","jo","julij","junij","jutri","k","kadarkoli","kaj","kajti","kako","kakor","kamor","kamorkoli","kar","karkoli","katerikoli","kdaj","kdo","kdorkoli","ker","ki","kje","kjer","kjerkoli","ko","koder","koderkoli","koga","komu","kot","kratek","kratka","kratke","kratki","l","lahka","lahke","lahki","lahko","le","lep","lepa","lepe","lepi","lepo","leto","m","maj","majhen","majhna","majhni","malce","malo","manj","marec","me","med","medtem","mene","mesec","mi","midva","midve","mnogo","moj","moja","moje","mora","morajo","moram","moramo","morate","moraš","morem","mu","n","na","nad","naj","najina","najino","najmanj","naju","največ","nam","narobe","nas","nato","nazaj","naš","naša","naše","ne","nedavno","nedelja","nek","neka","nekaj","nekatere","nekateri","nekatero","nekdo","neke","nekega","neki","nekje","neko","nekoga","nekoč","ni","nikamor","nikdar","nikjer","nikoli","nič","nje","njega","njegov","njegova","njegovo","njej","njemu","njen","njena","njeno","nji","njih","njihov","njihova","njihovo","njiju","njim","njo","njun","njuna","njuno","no","nocoj","november","npr.","o","ob","oba","obe","oboje","od","odprt","odprta","odprti","okoli","oktober","on","onadva","one","oni","onidve","osem","osma","osmi","osmo","oz.","p","pa","pet","peta","petek","peti","peto","po","pod","pogosto","poleg","poln","polna","polni","polno","ponavadi","ponedeljek","ponovno","potem","povsod","pozdravljen","pozdravljeni","prav","prava","prave","pravi","pravo","prazen","prazna","prazno","prbl.","precej","pred","prej","preko","pri","pribl.","približno","primer","pripravljen","pripravljena","pripravljeni","proti","prva","prvi","prvo","r","ravno","redko","res","reč","s","saj","sam","sama","same","sami","samo","se","sebe","sebi","sedaj","sedem","sedma","sedmi","sedmo","sem","september","seveda","si","sicer","skoraj","skozi","slab","smo","so","sobota","spet","sreda","srednja","srednji","sta","ste","stran","stvar","sva","t","ta","tak","taka","take","taki","tako","takoj","tam","te","tebe","tebi","tega","težak","težka","težki","težko","ti","tista","tiste","tisti","tisto","tj.","tja","to","toda","torek","tretja","tretje","tretji","tri","tu","tudi","tukaj","tvoj","tvoja","tvoje","u","v","vaju","vam","vas","vaš","vaša","vaše","ve","vedno","velik","velika","veliki","veliko","vendar","ves","več","vi","vidva","vii","viii","visok","visoka","visoke","visoki","vsa","vsaj","vsak","vsaka","vsakdo","vsake","vsaki","vsakomur","vse","vsega","vsi","vso","včasih","včeraj","x","z","za","zadaj","zadnji","zakaj","zaprta","zaprti","zaprto","zdaj","zelo","zunaj","č","če","često","četrta","četrtek","četrti","četrto","čez","čigav","š","šest","šesta","šesti","šesto","štiri","ž","že"],ih=["oo","atabo","ay","ku","waxeey","uu","lakin","si","ayuu","soo","waa","ka","kasoo","kale","waxuu","ayee","ayaa","kuu","isku","ugu","jiray","dhan","dambeestii","inuu","in","jirtay","uheestay","aad","uga","hadana","timaado","timaaday"],oh=["a","le","o","ba","ho","oa","ea","ka","hae","tselane","eaba","ke","hore","ha","e","ne","re","bona","me","limo","tsa","haholo","la","empa","ngoanake","se","moo","m'e","bane","mo","tse","sa","li","ena","bina","pina","hape"],lh=["a","un","el","ella","y","sobre","de","la","que","en","los","del","se","las","por","un","para","con","no","una","su","al","lo","como","más","pero","sus","le","ya","o","porque","cuando","muy","sin","sobre","también","me","hasta","donde","quien","desde","nos","durante","uno","ni","contra","ese","eso","mí","qué","otro","él","cual","poco","mi","tú","te","ti","sí"],ch=["na","ya","wa","kwa","ni","za","katika","la","kuwa","kama","kwamba","cha","hiyo","lakini","yake","hata","wakati","hivyo","sasa","wake","au","watu","hii","zaidi","vya","huo","tu","kwenye","si","pia","ili","moja","kila","baada","ambao","ambayo","yao","wao","kuna","hilo","kutoka","kubwa","pamoja","bila","huu","hayo","sana","ndani","mkuu","hizo","kufanya","wengi","hadi","mmoja","hili","juu","kwanza","wetu","kuhusu","baadhi","wote","yetu","hivi","kweli","mara","wengine","nini","ndiyo","zao","kati","hao","hapa","kutokana","muda","habari","ambaye","wenye","nyingine","hakuna","tena","hatua","bado","nafasi","basi","kabisa","hicho","nje","huyo","vile","yote","mkubwa","alikuwa","zote","leo","haya","huko","kutoa","mwa","kiasi","hasa","nyingi","kabla","wale","chini","gani","hapo","lazima","mwingine","bali","huku","zake","ilikuwa","tofauti","kupata","mbalimbali","pale","kusema","badala","wazi","yeye","alisema","hawa","ndio","hizi","tayari","wala","muhimu","ile","mpya","ambazo","dhidi","kwenda","sisi","kwani","jinsi","binafsi","kutumia","mbili","mbali","kuu","mengine","mbele","namna","mengi","upande"],uh=["jag","det","är","du","inte","att","en","och","har","vi","på","i","för","han","vad","med","mig","som","här","om","dig","var","den","så","till","kan","de","ni","ska","ett","men","av","vill","nu","ja","nej","bara","hon","hur","min","där","honom","kom","din","då","när","ha","er","ta","ut","får","man","vara","oss","dem","eller","varför","alla","från","upp","igen","sa","hade","allt","in","sig","ingen","henne","vem","mitt","nåt","blir","än","bli","ju","två","tar","hans","ditt","mina","åt","väl","också","nån","låt","detta","va","dina","dom","blev","inga","sin","just","många","vart","vilken","ur","ens","sitt","e","jo","era","deras","fem","sex","denna","vilket","fyra","vårt","emot","tio","ert","sju","åtta","nånting","ned","ers","nio","mej"],dh=["กล่าว","กว่า","กัน","กับ","การ","ก็","ก่อน","ขณะ","ขอ","ของ","ขึ้น","คง","ครั้ง","ความ","คือ","จะ","จัด","จาก","จึง","ช่วง","ซึ่ง","ดัง","ด้วย","ด้าน","ตั้ง","ตั้งแต่","ตาม","ต่อ","ต่าง","ต่างๆ","ต้อง","ถึง","ถูก","ถ้า","ทั้ง","ทั้งนี้","ทาง","ที่","ที่สุด","ทุก","ทํา","ทําให้","นอกจาก","นัก","นั้น","นี้","น่า","นํา","บาง","ผล","ผ่าน","พบ","พร้อม","มา","มาก","มี","ยัง","รวม","ระหว่าง","รับ","ราย","ร่วม","ลง","วัน","ว่า","สุด","ส่ง","ส่วน","สําหรับ","หนึ่ง","หรือ","หลัง","หลังจาก","หลาย","หาก","อยาก","อยู่","อย่าง","ออก","อะไร","อาจ","อีก","เขา","เข้า","เคย","เฉพาะ","เช่น","เดียว","เดียวกัน","เนื่องจาก","เปิด","เปิดเผย","เป็น","เป็นการ","เพราะ","เพื่อ","เมื่อ","เรา","เริ่ม","เลย","เห็น","เอง","แต่","แบบ","แรก","และ","แล้ว","แห่ง","โดย","ใน","ให้","ได้","ไป","ไม่","ไว้"],mh=["akin","aking","ako","alin","am","amin","aming","ang","ano","anumang","apat","at","atin","ating","ay","bababa","bago","bakit","bawat","bilang","dahil","dalawa","dapat","din","dito","doon","gagawin","gayunman","ginagawa","ginawa","ginawang","gumawa","gusto","habang","hanggang","hindi","huwag","iba","ibaba","ibabaw","ibig","ikaw","ilagay","ilalim","ilan","inyong","isa","isang","itaas","ito","iyo","iyon","iyong","ka","kahit","kailangan","kailanman","kami","kanila","kanilang","kanino","kanya","kanyang","kapag","kapwa","karamihan","katiyakan","katulad","kaya","kaysa","ko","kong","kulang","kumuha","kung","laban","lahat","lamang","likod","lima","maaari","maaaring","maging","mahusay","makita","marami","marapat","masyado","may","mayroon","mga","minsan","mismo","mula","muli","na","nabanggit","naging","nagkaroon","nais","nakita","namin","napaka","narito","nasaan","ng","ngayon","ni","nila","nilang","nito","niya","niyang","noon","o","pa","paano","pababa","paggawa","pagitan","pagkakaroon","pagkatapos","palabas","pamamagitan","panahon","pangalawa","para","paraan","pareho","pataas","pero","pumunta","pumupunta","sa","saan","sabi","sabihin","sarili","sila","sino","siya","tatlo","tayo","tulad","tungkol","una","walang"],hh=["acaba","acep","adeta","altmış","altmış","altı","altı","ama","ancak","arada","artık","aslında","aynen","ayrıca","az","bana","bari","bazen","bazı","bazı","başka","belki","ben","benden","beni","benim","beri","beş","beş","beş","bile","bin","bir","biraz","biri","birkaç","birkez","birçok","birşey","birşeyi","birşey","birşeyi","birşey","biz","bizden","bize","bizi","bizim","bu","buna","bunda","bundan","bunlar","bunları","bunların","bunu","bunun","burada","böyle","böylece","bütün","da","daha","dahi","dahil","daima","dair","dayanarak","de","defa","deđil","değil","diye","diđer","diğer","doksan","dokuz","dolayı","dolayısıyla","dört","edecek","eden","ederek","edilecek","ediliyor","edilmesi","ediyor","elli","en","etmesi","etti","ettiği","ettiğini","eđer","eğer","fakat","gibi","göre","halbuki","halen","hangi","hani","hariç","hatta","hele","hem","henüz","hep","hepsi","her","herhangi","herkes","herkesin","hiç","hiçbir","iken","iki","ila","ile","ilgili","ilk","illa","ise","itibaren","itibariyle","iyi","iyice","için","işte","işte","kadar","kanımca","karşın","katrilyon","kendi","kendilerine","kendini","kendisi","kendisine","kendisini","kere","kez","keşke","ki","kim","kimden","kime","kimi","kimse","kırk","kısaca","kırk","lakin","madem","međer","milyar","milyon","mu","mü","mı","mı","nasıl","nasıl","ne","neden","nedenle","nerde","nere","nerede","nereye","nitekim","niye","niçin","o","olan","olarak","oldu","olduklarını","olduğu","olduğunu","olmadı","olmadığı","olmak","olması","olmayan","olmaz","olsa","olsun","olup","olur","olursa","oluyor","on","ona","ondan","onlar","onlardan","onlari","onların","onları","onların","onu","onun","otuz","oysa","pek","rağmen","sadece","sanki","sekiz","seksen","sen","senden","seni","senin","siz","sizden","sizi","sizin","sonra","tarafından","trilyon","tüm","var","vardı","ve","veya","veyahut","ya","yahut","yani","yapacak","yapmak","yaptı","yaptıkları","yaptığı","yaptığını","yapılan","yapılması","yapıyor","yedi","yerine","yetmiş","yetmiş","yetmiş","yine","yirmi","yoksa","yüz","zaten","çok","çünkü","öyle","üzere","üç","şey","şeyden","şeyi","şeyler","şu","şuna","şunda","şundan","şunu","şey","şeyden","şeyi","şeyler","şu","şuna","şunda","şundan","şunları","şunu","şöyle","şayet","şimdi","şu","şöyle"],fh=["авжеж","адже","але","б","без","був","була","були","було","бути","більш","вам","вас","весь","вздовж","ви","вниз","внизу","вона","вони","воно","все","всередині","всіх","від","він","да","давай","давати","де","дещо","для","до","з","завжди","замість","й","коли","ледве","майже","ми","навколо","навіть","нам","от","отже","отож","поза","про","під","та","так","такий","також","те","ти","тобто","тож","тощо","хоча","це","цей","чи","чого","що","як","який","якої","є","із","інших","їх","її"],ph=["آئی","آئے","آج","آخر","آخرکبر","آدهی","آًب","آٹھ","آیب","اة","اخبزت","اختتبم","ادھر","ارد","اردگرد","ارکبى","اش","اضتعوبل","اضتعوبلات","اضطرذ","اضکب","اضکی","اضکے","اطراف","اغیب","افراد","الگ","اور","اوًچب","اوًچبئی","اوًچی","اوًچے","اى","اً","اًذر","اًہیں","اٹھبًب","اپٌب","اپٌے","اچھب","اچھی","اچھے","اکثر","اکٹھب","اکٹھی","اکٹھے","اکیلا","اکیلی","اکیلے","اگرچہ","اہن","ایطے","ایک","ب","ت","تبزٍ","تت","تر","ترتیت","تریي","تعذاد","تن","تو","توبم","توہی","توہیں","تٌہب","تک","تھب","تھوڑا","تھوڑی","تھوڑے","تھی","تھے","تیي","ثب","ثبئیں","ثبترتیت","ثبری","ثبرے","ثبعث","ثبلا","ثبلترتیت","ثبہر","ثدبئے","ثرآں","ثراں","ثرش","ثعذ","ثغیر","ثلٌذ","ثلٌذوثبلا","ثلکہ","ثي","ثٌب","ثٌبرہب","ثٌبرہی","ثٌبرہے","ثٌبًب","ثٌذ","ثٌذکرو","ثٌذکرًب","ثٌذی","ثڑا","ثڑوں","ثڑی","ثڑے","ثھر","ثھرا","ثھراہوا","ثھرپور","ثھی","ثہت","ثہتر","ثہتری","ثہتریي","ثیچ","ج","خب","خبرہب","خبرہی","خبرہے","خبهوظ","خبًب","خبًتب","خبًتی","خبًتے","خبًٌب","خت","ختن","خجکہ","خص","خططرذ","خلذی","خو","خواى","خوًہی","خوکہ","خٌبة","خگہ","خگہوں","خگہیں","خیطب","خیطبکہ","در","درخبت","درخہ","درخے","درزقیقت","درضت","دش","دفعہ","دلچطپ","دلچطپی","دلچطپیبں","دو","دور","دوراى","دوضرا","دوضروں","دوضری","دوضرے","دوًوں","دکھبئیں","دکھبتب","دکھبتی","دکھبتے","دکھبو","دکھبًب","دکھبیب","دی","دیب","دیتب","دیتی","دیتے","دیر","دیٌب","دیکھو","دیکھٌب","دیکھی","دیکھیں","دے","ر","راضتوں","راضتہ","راضتے","رریعہ","رریعے","رکي","رکھ","رکھب","رکھتب","رکھتبہوں","رکھتی","رکھتے","رکھی","رکھے","رہب","رہی","رہے","ز","زبصل","زبضر","زبل","زبلات","زبلیہ","زصوں","زصہ","زصے","زقبئق","زقیتیں","زقیقت","زکن","زکویہ","زیبدٍ","صبف","صسیر","صفر","صورت","صورتسبل","صورتوں","صورتیں","ض","ضبت","ضبتھ","ضبدٍ","ضبرا","ضبرے","ضبل","ضبلوں","ضت","ضرور","ضرورت","ضروری","ضلطلہ","ضوچ","ضوچب","ضوچتب","ضوچتی","ضوچتے","ضوچو","ضوچٌب","ضوچی","ضوچیں","ضکب","ضکتب","ضکتی","ضکتے","ضکٌب","ضکی","ضکے","ضیذھب","ضیذھی","ضیذھے","ضیکٌڈ","ضے","طرف","طریق","طریقوں","طریقہ","طریقے","طور","طورپر","ظبہر","ع","عذد","عظین","علاقوں","علاقہ","علاقے","علاوٍ","عووهی","غبیذ","غخص","غذ","غروع","غروعبت","غے","فرد","فی","ق","قجل","قجیلہ","قطن","لئے","لا","لازهی","لو","لوجب","لوجی","لوجے","لوسبت","لوسہ","لوگ","لوگوں","لڑکپي","لگتب","لگتی","لگتے","لگٌب","لگی","لگیں","لگے","لی","لیب","لیٌب","لیں","لے","ه","هتعلق","هختلف","هسترم","هسترهہ","هسطوش","هسیذ","هطئلہ","هطئلے","هطبئل","هطتعول","هطلق","هعلوم","هػتول","هلا","هوکي","هوکٌبت","هوکٌہ","هٌبضت","هڑا","هڑًب","هڑے","هکول","هگر","هہرثبى","هیرا","هیری","هیرے","هیں","و","وار","والے","وٍ","ًئی","ًئے","ًب","ًبپطٌذ","ًبگسیر","ًطجت","ًقطہ","ًو","ًوخواى","ًکبلٌب","ًکتہ","ًہ","ًہیں","ًیب","ًے","ٓ آش","ٹھیک","پبئے","پبش","پبًب","پبًچ","پر","پراًب","پطٌذ","پل","پورا","پوچھب","پوچھتب","پوچھتی","پوچھتے","پوچھو","پوچھوں","پوچھٌب","پوچھیں","پچھلا","پھر","پہلا","پہلی","پہلےضی","پہلےضے","پہلےضےہی","پیع","چبر","چبہب","چبہٌب","چبہے","چلا","چلو","چلیں","چلے","چکب","چکی","چکیں","چکے","چھوٹب","چھوٹوں","چھوٹی","چھوٹے","چھہ","چیسیں","ڈھوًڈا","ڈھوًڈلیب","ڈھوًڈو","ڈھوًڈًب","ڈھوًڈی","ڈھوًڈیں","ک","کئی","کئے","کب","کبفی","کبم","کت","کجھی","کرا","کرتب","کرتبہوں","کرتی","کرتے","کرتےہو","کررہب","کررہی","کررہے","کرو","کرًب","کریں","کرے","کطی","کل","کن","کوئی","کوتر","کورا","کوروں","کورٍ","کورے","کوطي","کوى","کوًطب","کوًطی","کوًطے","کھولا","کھولو","کھولٌب","کھولی","کھولیں","کھولے","کہ","کہب","کہتب","کہتی","کہتے","کہو","کہوں","کہٌب","کہی","کہیں","کہے","کی","کیب","کیطب","کیطرف","کیطے","کیلئے","کیوًکہ","کیوں","کیے","کے","کےثعذ","کےرریعے","گئی","گئے","گب","گرد","گروٍ","گروپ","گروہوں","گٌتی","گی","گیب","گے","ہر","ہن","ہو","ہوئی","ہوئے","ہوا","ہوبرا","ہوبری","ہوبرے","ہوتب","ہوتی","ہوتے","ہورہب","ہورہی","ہورہے","ہوضکتب","ہوضکتی","ہوضکتے","ہوًب","ہوًی","ہوًے","ہوچکب","ہوچکی","ہوچکے","ہوگئی","ہوگئے","ہوگیب","ہوں","ہی","ہیں","ہے","ی","یقیٌی","یہ","یہبں"],gh=["bị","bởi","cả","các","cái","cần","càng","chỉ","chiếc","cho","chứ","chưa","chuyện","có","có thể","cứ","của","cùng","cũng","đã","đang","để","đến nỗi","đều","điều","do","đó","được","dưới","gì","khi","không","là","lại","lên","lúc","mà","mỗi","một cách","này","nên","nếu","ngay","nhiều","như","nhưng","những","nơi","nữa","phải","qua","ra","rằng","rất","rồi","sau","sẽ","so","sự","tại","theo","thì","trên","trước","từ","từng","và","vẫn","vào","vậy","vì","việc","với","vừa","vâng","à","ừ","từ"],vh=["ó","ní","ìjàpá","ṣe","rẹ̀","tí","àwọn","sí","ni","náà","anansi","láti","kan","ti","ń","lọ","o","bí","padà","sì","wá","wangari","lè","wà","kí","púpọ̀","odò","mi","wọ́n","pẹ̀lú","a","ṣùgbọ́n","fún","jẹ́","fẹ́","oúnjẹ","rí","igi","kò","ilé","jù","olóńgbò","pé","é","gbogbo","iṣu","inú","bẹ̀rẹ̀","jẹ","fi","dúró","alẹ́","ọjọ́","nítorí","nǹkan","ọ̀rẹ́","àkókò","sínú","ṣ","yìí"],yh=["ukuthi","kodwa","futhi","kakhulu","wakhe","kusho","uma","wathi","umama","kanye","phansi","ngesikhathi","lapho","u","zakhe","khona","ukuba","nje","phezulu","yakhe","kungani","wase","la","mina","wami","ukuze","unonkungu","wabona","wahamba","lakhe","yami","kanjani","kwakukhona","ngelinye"],kh=(t,e=go)=>{if(!Array.isArray(t)||!Array.isArray(e))throw new Error("expected Arrays try: removeStopwords(Array[, Array])");return t.filter(a=>!e.includes(a.toLowerCase()))},bh=Object.freeze(Object.defineProperty({__proto__:null,_123:um,afr:dm,ara:mm,ben:pm,bre:gm,bul:vm,cat:ym,ces:wm,dan:xm,deu:Tm,ell:Am,eng:go,epo:jm,est:Sm,eus:fm,fas:Qm,fin:zm,fra:Em,gle:Mm,glg:Cm,guj:Om,hau:Rm,heb:Lm,hin:Nm,hrv:bm,hun:Im,hye:hm,ind:Fm,ita:Pm,jpn:$m,kor:Vm,kur:Dm,lat:qm,lav:Bm,lgg:Hm,lggNd:Wm,lit:Um,mar:Zm,msa:Km,mya:Gm,nld:_m,nob:Ym,panGu:Jm,pol:Xm,por:eh,porBr:th,removeStopwords:kh,ron:ah,rus:nh,slk:rh,slv:sh,som:ih,sot:oh,spa:lh,swa:ch,swe:uh,tgl:mh,tha:dh,tur:hh,ukr:fh,urd:ph,vie:gh,yor:vh,zho:km,zul:yh},Symbol.toStringTag,{value:"Module"}));var fn;class wh{constructor(){Se(this,fn,{...tl,analyzeResearchEnabled:!0,analyzeGridView:!1,analyzeListView:!0,shouldListDefaultView:!0,analyzeSponsoredProducts:!0,shouldCloseProductSearchModal:!0,analyzeCreatePageEnabled:!0});Je(this,"fetchAsinDocument",async e=>{const a={...e,document:null};try{let n=await Ni(e.link,!1,{method:"GET"},!1,!1,!1);return n.status===200&&(n=await(n==null?void 0:n.text()),a.document=n,n=null),a}catch(n){return console.log("🚀 ~ fetchAsinDocument ~ error:",n),a}})}async login(e){try{const a=al("version");return await ne.runtime.sendMessage({type:nl,payload:{...e,version:a}})}catch(a){return console.log("🚀 ~ EventsService ~ error:",a),{}}}async sendSuccessLoginEvent(){var e,a,n,r;try{const s=await ne.tabs.query({active:!0,currentWindow:!0});(e=s[0])!=null&&e.id&&((r=(n=(a=s[0])==null?void 0:a.url)==null?void 0:n.includes)!=null&&r.call(n,"amazon"))&&await ne.tabs.sendMessage(s[0].id,{type:Li})}catch(s){console.log("🚀 ~ EventsService ~ error:",s);return}}async sendEventToAmazonPages(e,a=!1,n=null,r="amazon"){const s={currentWindow:!0};a||(s.active=!0);try{(await ne.tabs.query(s)).forEach(async o=>{var l,c;o!=null&&o.id&&((c=(l=o==null?void 0:o.url)==null?void 0:l.includes)!=null&&c.call(l,r))&&await ne.tabs.sendMessage(o.id,{type:e,payload:n})})}catch(i){console.log("🚀 ~ Error is occurred while sending event to amazon pages",i);return}}async changeTheme(e){try{(await ne.tabs.query({currentWindow:!0})).forEach(async n=>{var r,s;n!=null&&n.id&&((s=(r=n==null?void 0:n.url)==null?void 0:r.includes)!=null&&s.call(r,"amazon"))&&await ne.tabs.sendMessage(n.id,{type:rl,payload:e})})}catch(a){console.log("🚀 ~ EventsService ~ An error occurred while changing theme:",a)}}async checkValidToken(){var e;try{return((e=await this.getStoreValue(["token"]))==null?void 0:e.token)?await ne.runtime.sendMessage({type:sl,tokenKey:ze()}):(this.removeStoreValue([rs]),{success:!1,message:"Token not found"})}catch(a){return console.log("🚀 ~ EventsService ~ error:",a),{success:!1,message:"Token not found"}}}async fetchConfigs(e,a){try{const n=await ne.runtime.sendMessage({type:e,tokenKey:ze()}),r=ss(Ps(n==null?void 0:n.data));return n!=null&&n.data&&await this.setStoreValue({[a]:n==null?void 0:n.data}),r}catch{return{}}}async processForFieldsConfigs(e,a){var n;try{let r=null;const s=(n=await this.getStoreValue([a]))==null?void 0:n[a];return s&&(r=ss(Ps(s))),r!=null&&r.xpaths||(r=this.fetchConfigs(e,a)),r}catch(r){return console.log("🚀 ~ EventsService ~An error occurred while process for fetch fields configs:",r),{}}}async processASINsRequest(e,a){try{return await ne.runtime.sendMessage({type:il,payload:{asins:e,activeDomain:a}})}catch(n){return console.log("🚀 ~ EventsService ~ error:",n),[]}}async processSingleASINRequest(e){try{return await ne.runtime.sendMessage({type:ol,payload:e})}catch{return[]}}async processDeletedProduct(e){try{const a=ze();return await ne.runtime.sendMessage({type:ll,payload:e,tokenKey:a})}catch{return}}async processForProductHistory(e){try{const a=ze();return await ne.runtime.sendMessage({type:cl,payload:{...e,tokenKey:a}})}catch{return{}}}async processForSaveExcludeBrands(e){try{const a=ze();return await ne.runtime.sendMessage({type:ul,payload:e,tokenKey:a})}catch{return{}}}async processForExcludeBrandsList(e){try{return await ne.runtime.sendMessage({type:dl,payload:e,tokenKey:ze()})}catch{return{}}}async processForUpdateExcludeBrand(e){try{return await ne.runtime.sendMessage({type:ml,payload:e,tokenKey:ze()})}catch{return{}}}async processForDeleteExcludeBrand(e){try{return await ne.runtime.sendMessage({type:hl,payload:e,tokenKey:ze()})}catch{return{}}}async processForSaveFilter(e){try{return await ne.runtime.sendMessage({type:fl,payload:e,tokenKey:ze()})}catch{return{}}}async processForSavedFilterList(e){try{return await ne.runtime.sendMessage({type:pl,payload:e,tokenKey:ze()})}catch{return{}}}async processForUpdateFilter(e,a){try{return await ne.runtime.sendMessage({type:gl,payload:{data:e,id:a},tokenKey:ze()})}catch{return{}}}async processForDeleteFilter(e){try{return await ne.runtime.sendMessage({type:vl,payload:e,tokenKey:ze()})}catch{return{}}}async processForSaveFavoriteGroup(e){try{return await ne.runtime.sendMessage({type:yl,payload:e,tokenKey:ze()})}catch{return{}}}async processForUpdateFavoriteGroup(e,a){try{return await ne.runtime.sendMessage({type:kl,payload:{data:e,id:a},tokenKey:ze()})}catch{return{}}}async processForDeleteFavoriteGroup(e){try{return await ne.runtime.sendMessage({type:bl,payload:e,tokenKey:ze()})}catch{return{}}}async processForSavedFavoriteGroupList(){try{return await ne.runtime.sendMessage({type:wl,tokenKey:ze()})}catch{return{}}}async processForFavoriteItemsList(){try{return await ne.runtime.sendMessage({type:xl,tokenKey:ze()})}catch{return{}}}async handleItemForFavorite(e){try{return await ne.runtime.sendMessage({type:_l,payload:e,tokenKey:ze()})}catch{return{}}}async processSalesEstimation(e,a){try{const n=ze();return await ne.runtime.sendMessage({type:jl,payload:{salesEstimationPayload:a,marketPlace:e,key:n}})}catch(n){return console.log("🚀 ~ error occurred during process for sales estimation:",n),{}}}async saveProductPayload(e){try{return await ne.runtime.sendMessage({type:Sl,payload:e})}catch{return}}async processIndexKeywords(e){try{return await ne.runtime.sendMessage({type:zl,payload:e})}catch{return[]}}async processTrademark(e){const a=ze();try{return await ne.runtime.sendMessage({type:El,payload:e,tokenKey:a})}catch{return{}}}async verifyOtp(e){try{return await ne.runtime.sendMessage({type:Cl,payload:e,tokenKey:ze()})}catch{return{}}}async resendOtp(){try{return await ne.runtime.sendMessage({type:Tl,tokenKey:ze()})}catch{return{}}}async processForHighlightTrademark(e,a){try{return await ne.runtime.sendMessage({type:is,payload:e,shouldUseCredits:a,tokenKey:ze()})}catch{return{}}}async processForGoogleSuggestion(e){try{return await ne.runtime.sendMessage({type:Al,payload:e})}catch{return{}}}async processAmazonKeywordSuggestions(e){try{return await ne.runtime.sendMessage({type:Ol,payload:e})}catch{return{}}}async savePreviousScrapedData(e){try{return await ne.runtime.sendMessage({type:Rl,payload:e})}catch{return{}}}async fetchPreviousScrapedData(){try{return await ne.runtime.sendMessage({type:Ll})}catch(e){return console.log("🚀 ~ EventsService ~ fetchPreviousScrapedData ~ error:",e),"{}"}}async fetchDisplayConfigs(){const e=await this.getStoreValue([...Object.keys(ge(this,fn)).map(n=>`${Nl}.${n}`)])??{},a={...ge(this,fn)};for(const n in e){let r=!0;try{r=JSON.parse(e[n])}catch{}const s=n.split(".").at(-1);a[s]=r===!0}return a}async generateProductSearchUrl(e){try{const a=ze();return await ne.runtime.sendMessage({type:Ml,payload:e,tokenKey:a})}catch{return{}}}async getPlanSummary(){try{const e=ze();return await ne.runtime.sendMessage({type:Il,tokenKey:e})}catch{return{}}}async deductCredits(e,a=!1){try{const n=ze();return await ne.runtime.sendMessage({type:Fl,payload:{module:e,showRemainingCredits:a},tokenKey:n})}catch{return{}}}async setStoreValue(e,a="local"){try{return await ne.storage[a].set(e)}catch(n){return console.log("🚀 ~ EventsService ~ setStoreValue ~ error:",n),{}}}async getStoreValue(e,a="local"){try{return await ne.storage[a].get(e)??{}}catch{return{}}}async removeStoreValue(e,a="local"){try{return await ne.storage[a].remove(e)}catch(n){return console.log("🚀 ~ EventsService ~ removeStoreValue ~ error:",n),{}}}async getStringifyValue(e,a="local"){try{const n={},r=await ne.storage[a].get(e)??{};return Object.keys(r).forEach(s=>{try{n[s]=JSON.parse(r[s])}catch{return{}}}),n}catch{return{}}}async logout(){try{const e=ze(),a=await ne.runtime.sendMessage({type:Pl,tokenKey:e});return a!=null&&a.success?(await ne.storage.local.remove(["token"]),a):{success:!1}}catch(e){return console.log("🚀 ~ EventsService ~ logout ~ error:",e),{success:!1}}}}fn=new WeakMap;const rt=new wh;let Rt=null;const xh=Gl;var Ai;const ii=(Ai=window==null?void 0:window.location)==null?void 0:Ai.origin;var dr,pn,gn,vn,Sa,za,yn,mr,hr,fr,kn,pr,gr,vr,yr,kr,bn,br,wr,xr,_r,wn,jr,Sr,zr;class _h{constructor(){Se(this,dr,{asin:"",title:"",link:"",price:[],brand:"",rating:0,ratings_total:0,reviews_total:0,duplicateProductCount:0,brand_url:"",brand_string:"",main_image:{link:""},images:[],images_count:0,feature_bullets:[],non_common_bullets:[],feature_bullets_count:0,specifications:[],bestsellers_rank:[],salesRankCategory:"",manufacturer:"",description:"",first_available:{raw:"",utc:""},colors:"",fit_type:{},long_keywords:"",focus_keywords:new Map,all_keywords:"",document:null,market_place:"",is_store:!1,product_type:"",amazon_listing_url:null,is_active:1,scraper_source:"extension",sponsored:"",amazon_climate_pledge_friendly:!1,amazon_product_sold_text:"",scraped_at:"",isAmazonChoice:!1,is_mba_product:!1,is_access_category:!1,reverse_lookup:null});Se(this,pn,new Map([]));Se(this,gn,new Map([]));Se(this,vn,new Map([]));Se(this,Sa,{});Se(this,za,{});Se(this,yn,2);Je(this,"fetchPageContent",async e=>{try{let a=await Ni(e.link,!1,{method:"GET"},!1,!1,!1,!1,xh);return a.status===200?(a=await(a==null?void 0:a.text()),new DOMParser().parseFromString(a,"text/html")):void 0}catch(a){console.log("🚀 ~ fetchAsinDocument ~ error:",a);return}});Je(this,"generateMBURl",async e=>{try{const{search:a,marketPlace:n,productCategory:r,sortField:s,newArrivals:i}=e,o={market_place_id:$l[n],product_type:r,sort_by:s,keyword:a==null?void 0:a.trim(),last_30_days_arrivals:i?"yes":"no"};return await rt.generateProductSearchUrl(o)}catch(a){return console.log("🚀 ~ Error while generating mba url ~ error:",a),null}});Je(this,"generateFallbackMBAUrl",e=>`${ii}/s?k=${e.trim()}`);Se(this,mr,(e,a)=>{let n=e==null?void 0:e.querySelectorAll(a==null?void 0:a.option1);n.length||(n=e.querySelectorAll(a==null?void 0:a.option2));const r=[];return n==null||n.forEach(s=>{r.push({link:(s==null?void 0:s.getAttribute("src"))||""})}),r});Se(this,hr,(e,a)=>{const n=[];return e.querySelectorAll(a).forEach(r=>{var i;const s=((i=r.querySelector("img"))==null?void 0:i.getAttribute("alt"))||"";s&&n.push(s)}),n.join(", ")});Se(this,fr,(e,a)=>{const n={menFit:"No",womenFit:"No",youthFit:"No"};return e.querySelectorAll(a).forEach(r=>{var i,o;const s=((o=(i=r==null?void 0:r.textContent)==null?void 0:i.trim())==null?void 0:o.toLowerCase())||"";s&&(qr.MEN[s]?n.menFit="Yes":qr.WOMEN[s]?n.womenFit="Yes":qr.YOUTH[s]&&(n.youthFit="Yes"))}),n});Se(this,kn,(e,a,n)=>{var r;try{let s=e;if(n===Ka.jp){const c=[];e.forEach(d=>{var p,g;if(d=="-")return;const m=(g=(p=d.match(/(\d|,)+/g))==null?void 0:p[0])==null?void 0:g.replace(/,/g,""),u=d.replace(/(\d|,)+/g,"");if(m){c.push(m,"",u);return}c.push(u)}),s=c}let i=null;for(let c=0;c<s.length&&(i=+(((r=(s.shift()||"").match(/\d+/g))==null?void 0:r.join(""))||"0")||null,!i);c++);let o=s.slice(1).join(" ").replace(/ in /g,"").replace(Va.removeParenthesesContent,"").trim()||null;o=(o||"").includes(":")?(o==null?void 0:o.split(":")[1].trim())??"":o;const l=+((a==null?void 0:a.split("/").at(-2))||"")||null;return{rank:i,category:o,node:l}}catch{return{rank:null,category:"",node:null}}});Se(this,pr,(e,a,n,r)=>{var s,i,o,l,c,d,m,u,p,g,_,S;try{const w=n===An.TABLE,j=[],z=[];if(!w){const E=((s=e.querySelector("a"))==null?void 0:s.getAttribute("href"))||"",M=((d=(c=(l=(o=(i=e==null?void 0:e.childNodes)==null?void 0:i[2])==null?void 0:o.nodeValue)==null?void 0:l.replace("(",""))==null?void 0:c.trim())==null?void 0:d.split(" "))||[];let{rank:V,category:O,node:k}=ge(this,kn).call(this,M,E,r);O||(O=((p=(u=(m=e==null?void 0:e.childNodes)==null?void 0:m[3])==null?void 0:u.textContent)==null?void 0:p.trim())||null),j.push({link:E?`${window.location.origin}${E}`:"",rank:V,category:O,node:k}),z.push(O)}const T=e==null?void 0:e.querySelectorAll(a);for(let E=0;E<T.length;E++){const M=T[E],V=((_=(g=M==null?void 0:M.textContent)==null?void 0:g.trim())==null?void 0:_.split(" "))||[],O=((S=M==null?void 0:M.querySelector("a"))==null?void 0:S.getAttribute("href"))||null,{rank:k,category:Z,node:Y}=ge(this,kn).call(this,V,O,r);j.push({link:O?`${window.location.origin}${O}`:"",rank:k,category:Z,node:Y}),z.push(Z)}return{bestsellers_rank:j,sellerRankCategory:z,mainCategory:z[0]||""}}catch{return{bestsellers_rank:[],sellerRankCategory:[],mainCategory:""}}});Je(this,"fetchPriceFields",(e,a)=>{var n;try{const r=((n=e==null?void 0:e.match(/[\d,.]+/g))==null?void 0:n.join(""))||"",s=parseFloat(r==null?void 0:r.replace(",","."))||0,i=Vl[a],o=Dl[a];return{value:s,symbol:i,currency:o,raw:r}}catch{return{value:0,symbol:"",currency:"",raw:""}}});Je(this,"fetchRatingNumber",e=>{if(!e)return 0;const a=e.match(Va.extractNumber),n=[];if(a)a.forEach(r=>{n.push(parseFloat(r.replace(/[^\d.,]/g,"").replace(",",".")))});else return 0;return Math.min(...n)||0});Je(this,"fetchTextContent",(e,a)=>{var n,r,s;return((s=(r=(n=e==null?void 0:e.querySelector)==null?void 0:n.call(e,a))==null?void 0:r.textContent)==null?void 0:s.trim())||""});Je(this,"fetchImageUrl",(e,a)=>{var n,r;return((r=(n=e.querySelector)==null?void 0:n.call(e,a))==null?void 0:r.getAttribute("src"))||""});Se(this,gr,(e,a)=>{var n,r;return((r=(n=e.querySelector)==null?void 0:n.call(e,a))==null?void 0:r.getAttribute("href"))||""});Se(this,vr,e=>{try{if(!e)return null;const n=decodeURIComponent(e).replace("//","").split("/");return n[1]==="dp"?null:n[1]||null}catch{return null}});Se(this,yr,(e,a)=>{var n,r,s,i,o,l,c,d,m,u;try{let p=e==null?void 0:e.querySelector(a==null?void 0:a.option1),g=0,_;return p?(_=this.fetchRatingNumber((l=p==null?void 0:p.querySelector(a==null?void 0:a.ratings_count))==null?void 0:l.getAttribute("title")),g=+(((u=(m=(d=(c=this.fetchTextContent(p,a==null?void 0:a.reviews_count))==null?void 0:c.split(" "))==null?void 0:d[0])==null?void 0:m.match(/\d+/g))==null?void 0:u.join(""))||0),{reviewsCount:g,ratingsCount:_}):(p=e==null?void 0:e.querySelector(a==null?void 0:a.option2),p?(_=this.fetchRatingNumber((r=(n=p==null?void 0:p.textContent)==null?void 0:n.trim)==null?void 0:r.call(n)),g=+((o=(i=(s=p==null?void 0:p.querySelector("a"))==null?void 0:s.textContent)==null?void 0:i.match(/\d+/g))==null?void 0:o.join(""))||0,{reviewsCount:g,ratingsCount:_}):{reviewsCount:g,ratingsCount:0})}catch{return{reviewsCount:0,ratingsCount:0}}});Se(this,kr,(e,a)=>{const n=e.querySelector(a.option1);return(n==null?void 0:n.getAttribute(a.attribute1))||(n==null?void 0:n.getAttribute(a.attribute2))});Je(this,"fetchListingReviews",(e,a)=>{var n,r;try{const s=e.querySelectorAll(a);let i=0,o=0;for(let l=0;l<s.length;l++){const c=s[l];if(!l){i=this.fetchRatingNumber(c.getAttribute("aria-label")||"")||0;continue}o=+(((r=(n=c.getAttribute("aria-label"))==null?void 0:n.match(/\d+/g))==null?void 0:r.join(""))||0);break}return{ratings:i,reviews:o}}catch{return{ratings:0,reviews:0}}});Se(this,bn,(e,a,n,r,s,i=!1,o=!0)=>{let l=i;return e.forEach(c=>{var u;const d=((u=c.textContent)==null?void 0:u.trim().replace(/\n/g," "))||"",m=d.toLowerCase();l||(l=s.some(p=>m.includes(p))),a.push(d),o&&(r.some(g=>m.includes(g.toLowerCase()))||n.push(d))}),l});Se(this,br,(e,a,n,r)=>{try{const s=e.querySelectorAll(a),i=e.querySelectorAll(r),o=ql[n],l=Bl[n],c=[],d=[];let m=ge(this,bn).call(this,i,c,[],[],l,!1,!1);return m=ge(this,bn).call(this,s,c,d,o,l,m),{bulletPoints:c,isStore:m,nonCommonBullets:d}}catch{return{bulletPoints:[],isStore:!1,nonCommonBullets:[]}}});Se(this,wr,(e,a)=>{var s,i,o;const n=(s=e.querySelector(a))==null?void 0:s.nextElementSibling;return(o=(i=n==null?void 0:n.textContent)==null?void 0:i.trim())==null?void 0:o.toLocaleLowerCase()});Je(this,"processIndexKeywords",async(e,a)=>{const n={},r={};return await Promise.all(e.map(async s=>{let i=await rt.processIndexKeywords(s);i==null||i.forEach(o=>{var l;try{((l=new DOMParser().parseFromString(o==null?void 0:o.document,"text/html"))==null?void 0:l.querySelector(`[data-asin="${a}"]`))&&(n[o.keyword]=1),r[o.keyword]=1}catch{r[o.keyword]=1}}),i=[]})),{indexedKeywords:n,allIndexedKeywords:r}});Se(this,xr,(e,a)=>{const n=new RegExp("\\b"+a+"\\b","gi"),r=e.match(n);return r?r.length:0});Se(this,_r,(e,a,n,r,s,i,o)=>{var l,c;try{if(!o)return Ul;if(!Rt){const O=Ft||rm;let k={};const Z=e=="en"?i:e||i,Y=Hl[Z];let N=O[Y];N||(k=bh,N=k[Y]||[]),Y!="english"&&(N=N.concat(O.english)),Rt=N.reduce((L,F)=>(L[F]=1,L),{}),Rt["-"]=1,Rt["/"]=1}const d=r==null?void 0:r.map(O=>On(O)),m=On(a),u=On(n),p=On(s),g=d.join(" "),_=[m,u,p,...d],S={};_.forEach(O=>{if(!O)return;const k=O.split(" "),Y=k.length-1;let N=[];k.forEach((L,F)=>{const X=L.toLowerCase();if(!X||!isNaN(+X))return;const ee=N.length;if(Rt!=null&&Rt[X]){ee>=ge(this,yn)&&(S[N.join(" ")]=1),N.length=0;return}N.push(X),Y===F&&(N.length>=ge(this,yn)&&(S[N.join(" ")]=1),N.length=0)})});const w=Object.keys(S).join(","),j=[...new Set((m+" "+u).split(" ")??[])],z={},T={};for(let O=0;O<j.length;O++){if(!j[O].trim()||!isNaN(+j[O]))continue;const k=j[O].toLocaleLowerCase().trim();if(Rt[k])continue;const Z=`${m} ${u} ${p} ${g}`.trim();let Y=ge(this,xr).call(this,Z,k);z[k]=Y||1}const E=(c=(l=`${p} ${g}`)==null?void 0:l.trim())==null?void 0:c.split(" ");for(let O=0;O<E.length;O++){if(!E[O].trim()||!isNaN(+E[O]))continue;const k=E[O].toLocaleLowerCase().trim();Rt[k]||(T[k]=1)}const M=[...new Set([...Object.keys(z),...Object.keys(T)])].join(",");return{sortedFocusKeywords:Wl(Kl(z),15),allKeywords:M,longTailKeywords:w}}catch(d){return console.log("🚀 ~ Error occurred while process to generate keywords :",d),{sortedFocusKeywords:new Map,allKeywords:"",longTailKeywords:""}}});Se(this,wn,(e,a)=>{if(!e)return"";let n="";const r=e.toLocaleLowerCase();for(const[s,i]of a){const o=s.toLocaleLowerCase();if(new RegExp(`\\b${o}\\b`,"i").test(r)){n=i;break}}return n});Se(this,jr,(e,a)=>{if(!e)return"";let n="";const r=e.toLowerCase();for(const[s,i]of a){const o=s.toLowerCase();if(r.includes(o)){n=i;break}}return n});Se(this,Sr,e=>{var n,r,s,i;return(i=(s=(r=(n=e==null?void 0:e.replace(Va.removeSpaces,""))==null?void 0:n.replace(/[^A-Za-z0-9\u3040-\u30FF\u30A0-\u30FF\u4E00-\u9FFF]+$/g,""))==null?void 0:r.replace(/&nbsp;|&nbsp/g,""))==null?void 0:s.trim())==null?void 0:i.toLowerCase()});Se(this,zr,e=>{if(!e)return"";let a="";return e.split(" ").forEach(n=>{Zl.includes(n.toLowerCase())||(a+=n+" ")}),a.trim()});Je(this,"generateMapping",e=>{const{CATEGORY_IDENTIFIER:a=[],JP_CATEGORY_IDENTIFIER:n=[],KDP_IDENTIFIER:r=[],MONTHS_IDENTIFIER:s={},MARKETPLACE_WISE_CATEGORY_SAVINGS:i={}}=e||{};pa(this,pn,new Map(a)),pa(this,gn,new Map(n)),pa(this,vn,new Map(Object.entries(r))),pa(this,Sa,s),pa(this,za,i)});Je(this,"hasCategorySavings",(e,a)=>{var n;return!!((n=ge(this,za)[e])!=null&&n[a])});Je(this,"processList",(e,a,n="us",r=!0,s)=>{var l,c,d,m,u,p,g,_,S,w,j,z,T,E,M,V,O;const{asin:i}=a,o={...ge(this,dr),...a,link:Mi(ii,n,i,!1)};try{const k=e==null?void 0:e.xPaths,{COUNTRY_BSR:Z,CUSTOMER_REVIEWS:Y,DEPARTMENT:N,DIMENSIONS:L,MANUFACTURER:F,PUBLISHER:X,DATE_FIRST_AVAILABLE:ee}=(e==null?void 0:e.fieldIdentifier)||{};let I=null;if(s?(I=a==null?void 0:a.document,o.title=this.fetchTextContent(I,k==null?void 0:k.product.title),o.main_image={link:""}):a!=null&&a.document&&(I=new DOMParser().parseFromString(a==null?void 0:a.document,"text/html")),!I)return o;if(o.title||(o.title=this.fetchTextContent(I,k==null?void 0:k.product.title)),!((c=(l=o.price)==null?void 0:l[0])!=null&&c.value)){const C=this.fetchTextContent(I,k==null?void 0:k.product.prices),{value:A,symbol:x,currency:$,raw:te}=this.fetchPriceFields(C,n);o.price=[{symbol:x,value:A,currency:$,raw:te}]}if(!o.amazon_product_sold_text){const C=this.fetchTextContent(I,k==null?void 0:k.product.product_bought_text);o.amazon_product_sold_text=C}o.is_mba_product=!!I.querySelector(k==null?void 0:k.product.mba_label),o.market_place=n;const G=ge(this,mr).call(this,I,k==null?void 0:k.product.images);o.images=G,o.images_count=G==null?void 0:G.length;const J=!!I.querySelector(k==null?void 0:k.product.climate_pledge_label);o.amazon_climate_pledge_friendly=J;let U=I.querySelectorAll(k==null?void 0:k.product.specifications.list.option1),ie=An.POINTS;U.length||(U=I.querySelectorAll(k==null?void 0:k.product.specifications.list.option2),ie=An.TABLE);const ue=Fr(I,k==null?void 0:k.product.brand);if(ue){const C=((d=ue==null?void 0:ue.textContent)==null?void 0:d.trim())||"";if(!o.brand){const A=C.includes(":")?C.split(":")[1].trim():C;o.brand=ge(this,zr).call(this,A)}o.brand_string=C,o.brand_url=ue.getAttribute("href")||""}const Ee=this.fetchTextContent(I,k==null?void 0:k.product.description);o.description=Ee;const{bulletPoints:Pe,isStore:ke,nonCommonBullets:Ae}=ge(this,br).call(this,I,k==null?void 0:k.product.features,n,(m=k==null?void 0:k.product)==null?void 0:m.product_details);o.is_store=ke,o.feature_bullets=Pe,o.non_common_bullets=Ae,o.feature_bullets_count=Pe==null?void 0:Pe.length;const be=ge(this,wr).call(this,I,k==null?void 0:k.product.language_identifier)||"",{sortedFocusKeywords:Re,allKeywords:K,longTailKeywords:ye}=ge(this,_r).call(this,be,o.title,o.brand,Ae,Ee,n,r)||{};o.focus_keywords=Re,o.all_keywords=K,o.long_keywords=ye;const _e=(u=this.fetchTextContent(I,k==null?void 0:k.product.amazon_choice))==null?void 0:u.includes("Amazon");o.isAmazonChoice=_e;const Ce=ge(this,vr).call(this,ge(this,gr).call(this,I,k==null?void 0:k.product.reverse_lookup));o.reverse_lookup=Ce;const fe=ge(this,hr).call(this,I,k==null?void 0:k.product.variations.color);o.colors=fe;const we=ge(this,fr).call(this,I,k==null?void 0:k.product.variations.fit);o.fit_type=we;const je=n===Ka.jp;let Me=je?ge(this,jr).call(this,o.title,ge(this,gn)):ge(this,wn).call(this,o.title,ge(this,pn));if(!Me){const C=this.fetchTextContent(I,k==null?void 0:k.product.title);ge(this,wn).call(this,C,ge(this,vn))===Br.KDP&&(Me=Br.KDP,o.is_mba_product=!0)}o.product_type=Me||"";const Ie=[];for(let C=0;C<U.length;C++){const A=U[C];if(!A)continue;const x=ie===An.TABLE?A:A.querySelector(k==null?void 0:k.product.specifications.item),$=x==null?void 0:x.firstElementChild,te=je?ge(this,Sr).call(this,$==null?void 0:$.innerHTML):(w=(S=(_=(g=(p=$==null?void 0:$.innerHTML)==null?void 0:p.replace(Va.removeSpaces,""))==null?void 0:g.replace(Va.removeSpecialCharacterFromEnd,""))==null?void 0:_.replace(/&nbsp;|&nbsp/g,""))==null?void 0:S.trim())==null?void 0:w.toLowerCase();let le=null,me=null;if(L!=null&&L[te]){le=x==null?void 0:x.lastElementChild,me=((j=le==null?void 0:le.textContent)==null?void 0:j.trim())||"",Ie.push({name:"Package Dimensions",value:me});continue}if(ee!=null&&ee[te]){le=x==null?void 0:x.lastElementChild,me=((z=le==null?void 0:le.textContent)==null?void 0:z.trim())||"",o.first_available={raw:me,utc:""},me&&(o.first_available.utc=$s(me.replace(/\.|,/g,","),n,ge(this,Sa))),Ie.push({name:"Date First Available",value:me});continue}if(!o.first_available.raw&&Me===Br.KDP&&(X!=null&&X[te])){le=x==null?void 0:x.lastElementChild,me=((T=le==null?void 0:le.textContent)==null?void 0:T.trim())||"";const $e=/\((.*?)\)/,Ue=me.match($e);me=Ue?Ue==null?void 0:Ue[1]:"",o.first_available={raw:me,utc:""},me&&(o.first_available.utc=$s(me.replace(/\.|,/g,","),n,ge(this,Sa))),Ie.push({name:"Date First Available",value:me});continue}if(N!=null&&N[te]){le=x==null?void 0:x.lastElementChild,me=((E=le==null?void 0:le.textContent)==null?void 0:E.trim())||"",Ie.push({name:"Department",value:me});continue}if(F!=null&&F[te]){le=x==null?void 0:x.lastElementChild,me=((M=le==null?void 0:le.textContent)==null?void 0:M.trim())||"",o.manufacturer=me,Ie.push({name:"Manufacturer",value:me});continue}if(Z!=null&&Z[te]){const{bestsellers_rank:$e,sellerRankCategory:Ue,mainCategory:ot}=ge(this,pr).call(this,x,k==null?void 0:k.product.best_selling_rank,ie,n);o.bsrRank=((V=$e[0])==null?void 0:V.rank)||0,o.bestsellers_rank=$e,o.salesRankCategory=ot,Ie.push({name:"Best Seller Rank",value:Ue});continue}if(!o.reviews_total&&(Y!=null&&Y[te])){const{ratingsCount:$e,reviewsCount:Ue}=ge(this,yr).call(this,x,k==null?void 0:k.product.customer_reviews);o.rating=parseFloat($e.toString())||0,o.reviews_total=Ue,o.ratings_total=Ue;continue}}Ie.push({name:"ASIN",value:i}),o.specifications=Ie,o.scraper_source="extension";const f=ge(this,kr).call(this,I,k==null?void 0:k.product.parent_asin);o.parentAsin=f!==i?f:"";const v=new Date().toISOString().slice(0,19).replace("T"," ");o.scraped_at=v;const y=!!((O=ge(this,za)[n])!=null&&O[Me]);return o.is_access_category=y,delete o.document,I=null,o}catch{return o}})}}dr=new WeakMap,pn=new WeakMap,gn=new WeakMap,vn=new WeakMap,Sa=new WeakMap,za=new WeakMap,yn=new WeakMap,mr=new WeakMap,hr=new WeakMap,fr=new WeakMap,kn=new WeakMap,pr=new WeakMap,gr=new WeakMap,vr=new WeakMap,yr=new WeakMap,kr=new WeakMap,bn=new WeakMap,br=new WeakMap,wr=new WeakMap,xr=new WeakMap,_r=new WeakMap,wn=new WeakMap,jr=new WeakMap,Sr=new WeakMap,zr=new WeakMap;const Mt=new _h,jh=Ma(t=>({scrapDetails:{},setScrapedDetails:e=>t(()=>({scrapDetails:{...e}})),resetScrapedDetails:()=>t(()=>({scrapDetails:{}}))})),Sh=jh,Mr=Ma(t=>({upgradePlanModal:{modalTitle:"",modalDescription:"",isVisible:!1,showViewCredits:!1,showTemplateText:!1},setUpgradePlanModal:e=>t(()=>({upgradePlanModal:e}))})),Ir=Ma((t,e)=>({show:!1,modules:{},setModules:a=>t(n=>({modules:{...n.modules,[a]:1},show:!0})),resetModules:()=>t(()=>({modules:{},show:!1})),hideModules:a=>{const n=e().modules;t(()=>({modules:{},show:!1})),rt.setStoreValue({excludedBannerModules:JSON.stringify({...a,...n})})}})),oi=t=>t.match(/\b[\w-]+\b[^\w\s]?|\S/g)||[],zh=({title:t,text:e,commonWords:a,brandWords:n,wordClickHandler:r})=>{const s=oi(e),i=[];for(let o=0;o<s.length;o++){let l=s[o],c={cursor:"pointer"},d=!1;for(let m in{...n,...a}){const u=oi(m);if(u.length>1&&o+u.length<=s.length){const p=s.slice(o,o+u.length).join(" ");if(p.toLowerCase()===m.toLowerCase()){l=p,c.backgroundColor="#BD2130",c.color="white",c.padding="1px 3px",o+=u.length-1,d=!0;break}}}if(!d){const m=l.toLowerCase().replace(/[^\w\s-]/g,"");n.hasOwnProperty(m)?(c.backgroundColor="#BD2130",c.color="white",c.padding="1px 3px"):a.hasOwnProperty(m)&&(c.backgroundColor="yellow",c.color="#283145",c.padding="1px 3px")}i.push(h.jsx("span",{style:c,onClick:()=>r(l),children:l},o)),i.push(" ")}return h.jsx("div",{id:`highlight-${t}`,className:"highlighted-text-container",style:{whiteSpace:"pre-wrap"},children:i})},Ba=zh,Eh=({asin:t,emitter:e,scrapData:a,trademarkData:n,trademarkPayload:r,setTrademark:s})=>{var p,g,_;const i=Mr(S=>S.setUpgradePlanModal),o=Ir(S=>S.setModules),{trademarkData:{common:l,brand:c},fetching:d,isAlreadyProcessed:m}=n;b.useEffect(()=>{const S=async w=>{var M,V,O,k,Z,Y;if(((M=w.payload)==null?void 0:M.status)===429){i({isVisible:!0,modalTitle:((V=w.payload)==null?void 0:V.title)||"Limit reached",modalDescription:((O=w.payload)==null?void 0:O.description)||""});return}const{allowedCredits:j=0,spentCredits:z=0}=((k=w.payload)==null?void 0:k.credit_details)||{};await Pr(Er.TRADEMARK_SEARCH,j,z,o);const T=Hn(((Z=w.payload)==null?void 0:Z.common)||[]),E=Hn(((Y=w.payload)==null?void 0:Y.brand)||[]);s(N=>({...N,trademarkData:{common:T,brand:E}}))};return e.addEventListener(`${is}-${t}`,S),()=>{e.removeEventListener(`${is}-${t}`,S)}},[]);const u=S=>{if(d)return;const w=S.replace(/[^\w\s-']/g,"");e.updateProductData(Dt.PROCESS_TRADEMARK,{trademarkPayload:r,asin:t,keyword:w})};return h.jsxs("div",{className:"mrdn-research-entry mrdn-research-tmcheck flex-col",children:[h.jsx("span",{children:"Trademark Check:"}),d&&!m?h.jsx(aa,{}):h.jsxs("ul",{className:"mrdn-trademark-check d-flex flex-col gap-y-2",children:[h.jsxs("li",{children:[h.jsx("strong",{className:"label text-main",children:"Brand:"}),h.jsx("span",{className:"value text-primary",children:h.jsx(Ba,{title:"Brand",text:a==null?void 0:a.brand,commonWords:l,brandWords:c,wordClickHandler:u})})]}),h.jsxs("li",{children:[h.jsx("strong",{className:"label text-main",children:"Title:"}),h.jsx("span",{className:"value text-primary",children:h.jsx(Ba,{title:"Title",text:a==null?void 0:a.title,commonWords:l,brandWords:c,wordClickHandler:u})})]}),(p=a==null?void 0:a.non_common_bullets)!=null&&p[0]?h.jsxs("li",{children:[h.jsx("strong",{className:"label text-main",children:"Bullet Points 1:"}),h.jsx("span",{className:"value d-flex flex-col text-primary",children:h.jsx(Za,{text:h.jsx(Ba,{title:"Bullet Points 1",text:a.non_common_bullets[0],commonWords:l,brandWords:c,wordClickHandler:u}),extraClass:"d-flex-important flex-wrap"},"bulletPoint1")})]}):null,(g=a==null?void 0:a.non_common_bullets)!=null&&g[1]?h.jsxs("li",{children:[h.jsx("strong",{className:"label text-main",children:"Bullet Points 2:"}),h.jsx("span",{className:"value d-flex flex-col text-primary",children:(_=a==null?void 0:a.non_common_bullets)!=null&&_[1]?h.jsx(Za,{text:h.jsx(Ba,{title:"Bullet Points 2",text:a.non_common_bullets[1],commonWords:l,brandWords:c,wordClickHandler:u}),extraClass:"d-flex-important flex-wrap"},"bulletPoint2"):null})]}):null,a!=null&&a.description?h.jsxs("li",{children:[h.jsx("strong",{className:"label text-main",children:"Description:"}),h.jsx("span",{className:"value d-flex flex-col text-primary",children:h.jsx(Za,{text:h.jsx(Ba,{title:"Description",text:a.description,commonWords:l,brandWords:c,wordClickHandler:u}),extraClass:"d-flex-important flex-wrap"},"productDescription")})]}):null]})]})};function Ng(t){return q({tag:"svg",attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M14 5l7 7m0 0l-7 7m7-7H3"},child:[]}]})(t)}function Mg(t){return q({tag:"svg",attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636"},child:[]}]})(t)}function Ch(t){return q({tag:"svg",attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"},child:[]}]})(t)}const Th=(t,e)=>`${t} ${e} successfully.`,Ig=(t,e)=>{const a=e?Th(t,e):t;Ze.success(a)},Ah=t=>{const e=t||"Something went wrong, please try again.";Ze.error(e)},Fg=(t,e)=>`An error occurred while trying to ${e} ${t}. Please try again.`,Pg=t=>`${t} ${t>1?"templates":"template"} imported successfully.`,Oh=(t,e,a)=>`You've reached your current plan's ${a} limit of ${t}. Upgrade your plan to ${e} and manage more templates.`,$g=(t,e)=>`${t||0} templates imported. ${Oh(e,"import","template")}`,Vg="Upgrade to higher plan to get more credits and keep enjoying this feature";class Rh extends EventTarget{updateProductData(e,a={},n="payload"){const r=new Event(e);r[n]=a,this.dispatchEvent(r)}}const Dg=new Rh,Lh=()=>{const t=Mr(s=>s.setUpgradePlanModal),e=Ir(s=>s.setModules),[a,n]=b.useState({isInitialLoading:!0,hasCredits:!0,remainingCredits:0});return{checkAndDeductUserCredits:b.useCallback(async(s,i={})=>{var d;const{shouldOnlyFetchCredits:o=!1,onNoCredits:l,defaultDescription:c=""}=i;if(o&&!a.isInitialLoading&&(a.remainingCredits==="no-limit"||a.remainingCredits))return{hasCredits:a.hasCredits};try{const m=await rt.deductCredits(s,o);if(!(m!=null&&m.success))return(m==null?void 0:m.status)===429?t({isVisible:!0,modalTitle:(m==null?void 0:m.title)||"Limit reached",modalDescription:(m==null?void 0:m.description)||c||`Upgrade to a higher plan to get more credits for ${s} and keep enjoying this feature`}):Ah((m==null?void 0:m.message)||"Something went wrong while checking credits. Please try again later."),n({isInitialLoading:!1,hasCredits:!1,remainingCredits:0}),{hasCredits:!1};let u=((m==null?void 0:m.remainingCredits)<0?0:m==null?void 0:m.remainingCredits)||0,p=!!u;if(o&&!u)t({isVisible:!0,modalTitle:(m==null?void 0:m.title)||"Limit reached",modalDescription:(m==null?void 0:m.description)||c||`Upgrade to a higher plan to get more credits for ${s} and keep enjoying this feature`}),l&&l();else if(!o){const{allowedCredits:g=0,spentCredits:_=0}=((d=m==null?void 0:m.result)==null?void 0:d.credit_details)||{},S=g==="Unlimited",w=g&&g===_;let j=0;S?j="no-limit":(j=Math.max((g||0)-(_||0),0),await Pr(s,g,_,e)),p=!!(w||j),u=j}return n({isInitialLoading:!1,hasCredits:p,remainingCredits:u}),{hasCredits:p}}catch{return n({isInitialLoading:!1,hasCredits:!1,remainingCredits:0}),{hasCredits:!1}}},[a])}},Nh=({contentChildren:t,tooltipChildren:e,tooltipId:a,place:n="top",isClickable:r=!0,hasArrow:s=!1,tooltipProps:i={}})=>h.jsxs(h.Fragment,{children:[t,h.jsx(Ss,{id:a,style:{background:"var(--main-bg-color)",color:"var(--text-primary-color)",padding:0,zIndex:1},className:"montserrat-regular",opacity:1,...i,place:n,clickable:r,noArrow:s,children:e})]}),Mh=({options:t,contentChildren:e})=>{const a=wa();return h.jsx("div",{className:"d-flex position-relative download-image-action common-link",children:h.jsx(Nh,{tooltipId:a,place:"bottom",contentChildren:h.jsx(ft,{tooltipTitle:"",extraClass:"bg-merch-dominator color-white i-p-2 h-fit",children:e,tooltipId:a}),tooltipChildren:h.jsx("div",{className:"menuContainer text-sm d-flex flex-col",children:t.map((n,r)=>h.jsxs("div",{className:"menuOption",onClick:n.handler,children:[n.label,n.isProcessing?h.jsx(Qa,{className:"fa-spin mr-2"}):null]},r))})})})},Ih=b.memo(Mh),li=t=>{var e;try{if(!t)return;let n=decodeURIComponent(t).split("I/"),r=n[0]+"I/",s=(e=n[1])==null?void 0:e.split("|");const i=(s==null?void 0:s.length)>=3;let o;return i?o=r+s[2]:o=r+(s==null?void 0:s[0]),o}catch{return t}},Yr=t=>t?decodeURIComponent(t).replace(/_(SX|AC).*?\./,"."):void 0,Fh=({imagesUrl:t,title:e="image",originalImageUrl:a="",isSinglePage:n,productType:r,hasPermission:s})=>{var p,g;const[i,o]=b.useState({original:!1,transparent:!1}),{checkAndDeductUserCredits:l}=Lh(),c=Yl[r]??1,d=((t==null?void 0:t.length)>2?(p=t==null?void 0:t[c])==null?void 0:p.link:(g=t==null?void 0:t[0])==null?void 0:g.link)||"",m=async _=>{if(s)try{if(i.original||i.transparent)return;o(T=>({...T,[_]:!0}));const{hasCredits:S}=await l(Er.IMAGE_DOWNLOAD);if(!S){o(T=>({...T,[_]:!1}));return}const w=(_==="transparent"?d.includes("png")?li(d):li(Yr(d)):n?Yr(d):a)||Yr(a);if(!w){o(T=>({...T,[_]:!1}));return}const z=await(await fetch(w)).blob();Jl.saveAs(z,`${_==="transparent"?`transparent_${e}`:e}.png`),o(T=>({...T,[_]:!1}))}catch{Ze.error("Error occurred while downloading the image. Please try again!")}finally{o(S=>({...S,[_]:!1}))}},u=[{label:"Download Image",handler:()=>{m("original")},isProcessing:i.original},{label:"Transparent PNG Image",handler:()=>{m("transparent")},isProcessing:i.transparent}];return h.jsx(Ih,{contentChildren:h.jsx(Ch,{size:15}),options:u})},Ph=b.memo(Fh),$h=({buttonContent:t=null,isPrefixContent:e=!1,isLoading:a=!1,tooltipTitle:n="",onclickHandler:r,isDisabled:s=!1,buttonIcon:i=null,extraClass:o="",buttonName:l="",tooltipId:c="",buttonProps:d={},tooltipProps:m={}})=>h.jsxs(h.Fragment,{children:[h.jsx("button",{className:`btn d-flex-important merch-common-button bg-merch-dominator text-default gap-1 justify-center align-items-center h-fit cursor-pointer ${o} ${s?"cursor-disabled opacity-80 cursor-not-allowed":""}`,"data-tooltip-id":c,onClick:r,disabled:s,name:l||t,id:c,...d,children:e?h.jsxs(h.Fragment,{children:[t,a?h.jsx(Qa,{className:"fa-spin"}):i]}):h.jsxs(h.Fragment,{children:[a?h.jsx(Qa,{className:"fa-spin"}):i,t]})}),n?h.jsx(Ss,{id:c,content:n,style:{zIndex:1200},...m,place:"top"}):null]}),vo=b.memo($h),Vh=({copyText:t,copyTextTitle:e="Copied to clipboard!",isButton:a=!0,buttonContent:n="Copy",buttonIcon:r=null,iconSize:s=12,hasPermission:i=!0,extraClass:o=""})=>{const{copying:l,copyTextToClipboard:c}=Lr({copyTextTitle:e,toastOptions:{id:n}}),d=async m=>{m==null||m.stopPropagation(),!(!t||!i)&&(l||await c(t))};return a?h.jsx(vo,{buttonContent:n,isLoading:!1,isDisabled:!1,onclickHandler:d,buttonIcon:r||h.jsx(Wt,{size:s}),extraClass:`color-white gap-2 ${o}`}):h.jsx("div",{className:`d-flex justify-center align-items-center restricted-section cursor-pointer  ${o}`,onClick:d,children:r||h.jsx(Wt,{size:s})})},Jr=Vh;function qg(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 294.1L383 167c9.4-9.4 24.6-9.4 33.9 0s9.3 24.6 0 34L273 345c-9.1 9.1-23.7 9.3-33.1.7L95 201.1c-4.7-4.7-7-10.9-7-17s2.3-12.3 7-17c9.4-9.4 24.6-9.4 33.9 0l127.1 127z"},child:[]}]})(t)}function Bg(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M294.1 256L167 129c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.3 34 0L345 239c9.1 9.1 9.3 23.7.7 33.1L201.1 417c-4.7 4.7-10.9 7-17 7s-12.3-2.3-17-7c-9.4-9.4-9.4-24.6 0-33.9l127-127.1z"},child:[]}]})(t)}function Ug(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M331.3 308.7L278.6 256l52.7-52.7c6.2-6.2 6.2-16.4 0-22.6-6.2-6.2-16.4-6.2-22.6 0L256 233.4l-52.7-52.7c-6.2-6.2-15.6-7.1-22.6 0-7.1 7.1-6 16.6 0 22.6l52.7 52.7-52.7 52.7c-6.7 6.7-6.4 16.3 0 22.6 6.4 6.4 16.4 6.2 22.6 0l52.7-52.7 52.7 52.7c6.2 6.2 16.4 6.2 22.6 0 6.3-6.2 6.3-16.4 0-22.6z"},child:[]},{tag:"path",attr:{d:"M256 76c48.1 0 93.3 18.7 127.3 52.7S436 207.9 436 256s-18.7 93.3-52.7 127.3S304.1 436 256 436c-48.1 0-93.3-18.7-127.3-52.7S76 304.1 76 256s18.7-93.3 52.7-127.3S207.9 76 256 76m0-28C141.1 48 48 141.1 48 256s93.1 208 208 208 208-93.1 208-208S370.9 48 256 48z"},child:[]}]})(t)}function Hg(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"circle",attr:{cx:"92",cy:"256",r:"28"},child:[]},{tag:"circle",attr:{cx:"92",cy:"132",r:"28"},child:[]},{tag:"circle",attr:{cx:"92",cy:"380",r:"28"},child:[]},{tag:"path",attr:{d:"M432 240H191.5c-8.8 0-16 7.2-16 16s7.2 16 16 16H432c8.8 0 16-7.2 16-16s-7.2-16-16-16zM432 364H191.5c-8.8 0-16 7.2-16 16s7.2 16 16 16H432c8.8 0 16-7.2 16-16s-7.2-16-16-16zM191.5 148H432c8.8 0 16-7.2 16-16s-7.2-16-16-16H191.5c-8.8 0-16 7.2-16 16s7.2 16 16 16z"},child:[]}]})(t)}function Dh(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M349.6 64c-36.4 0-70.7 16.7-93.6 43.9C233.1 80.7 198.8 64 162.4 64 97.9 64 48 114.2 48 179.1c0 79.5 70.7 143.3 177.8 241.7L256 448l30.2-27.2C393.3 322.4 464 258.6 464 179.1 464 114.2 414.1 64 349.6 64zm-80.8 329.3l-4.2 3.9-8.6 7.8-8.6-7.8-4.2-3.9c-50.4-46.3-94-86.3-122.7-122-28-34.7-40.4-63.1-40.4-92.2 0-22.9 8.4-43.9 23.7-59.3 15.2-15.4 36-23.8 58.6-23.8 26.1 0 52 12.2 69.1 32.5l24.5 29.1 24.5-29.1c17.1-20.4 43-32.5 69.1-32.5 22.6 0 43.4 8.4 58.7 23.8 15.3 15.4 23.7 36.5 23.7 59.3 0 29-12.5 57.5-40.4 92.2-28.8 35.7-72.3 75.7-122.8 122z"},child:[]}]})(t)}function qh(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 448l-30.164-27.211C118.718 322.442 48 258.61 48 179.095 48 114.221 97.918 64 162.4 64c36.399 0 70.717 16.742 93.6 43.947C278.882 80.742 313.199 64 349.6 64 414.082 64 464 114.221 464 179.095c0 79.516-70.719 143.348-177.836 241.694L256 448z"},child:[]}]})(t)}function Wg(t){return q({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M74.6 256c0-38.3 31.1-69.4 69.4-69.4h88V144h-88c-61.8 0-112 50.2-112 112s50.2 112 112 112h88v-42.6h-88c-38.3 0-69.4-31.1-69.4-69.4zm85.4 22h192v-44H160v44zm208-134h-88v42.6h88c38.3 0 69.4 31.1 69.4 69.4s-31.1 69.4-69.4 69.4h-88V368h88c61.8 0 112-50.2 112-112s-50.2-112-112-112z"},child:[]}]})(t)}const Bh=Ma(t=>({favoriteList:{extension:{},web:{}},favorites:{options:[{label:"Select Favorite Group",value:0}],keyValuePairs:{}},favoriteRenderKey:0,isAlreadyProcessed:!1,selectedFavoriteGroup:0,setIsAlreadyProcessed:e=>t(()=>({isAlreadyProcessed:e})),setFavoriteList:e=>t(()=>({favoriteList:e,isAlreadyProcessed:!0})),setFavoriteOptions:(e,a)=>t(()=>({favorites:{options:[{label:"Select Favorite Group",value:0},...e],keyValuePairs:a}})),setSelectedFavoriteGroup:e=>t(()=>({selectedFavoriteGroup:e})),updateFavoriteRenderKey:()=>t(e=>({favoriteRenderKey:e.favoriteRenderKey+1}))})),In=Bh,ci={ADD:"1",REMOVE:"2"},Uh=b.memo(({productData:t,isDataProcessing:e,hasPermission:a,asin:n=""})=>{const r=n||t.asin,s=In(Ut(w=>w.setFavoriteList)),i=In(Ut(w=>w.selectedFavoriteGroup)),o=Mr(w=>w.setUpgradePlanModal),l=Ir(w=>w.setModules),c=In(Ut(w=>w.favoriteList.extension[r])),d=In(Ut(w=>w.favoriteList.web[r])),m=d||c,[u,p]=b.useState({isAdding:!1,isAdded:!1}),[g,_]=b.useState({title:"Mark as Favorite",id:wa()}),S=async()=>{var F,X,ee;if(e||u.isAdding||!a)return;if(m){p(ue=>({...ue,isAdding:!0})),Ze.success("Processing, please wait...",ui);const{asin:I,market_place_id:G,group_id:J}=c||d||{},U={group_id:J,market_place:Ql[G],product_asin:I,flag:ci.REMOVE},ie=await rt.handleItemForFavorite(U);if(ie!=null&&ie.success){const ue=await rt.processForFavoriteItemsList(),Ee=await di(ue==null?void 0:ue.result);s({...Ee}),Ze.success((ie==null?void 0:ie.message)||"Product removed from favorites.")}else(ie==null?void 0:ie.status)===429?o({isVisible:!0,modalTitle:(ie==null?void 0:ie.title)||"Limit reached",modalDescription:(ie==null?void 0:ie.description)||""}):Ze.error((ie==null?void 0:ie.message)||"Something went wrong!");p(ue=>({...ue,isAdding:!1}));return}p(I=>({...I,isAdding:!0}));const{asin:w,brand:j,title:z,market_place:T,product_type:E,main_image:M,bsrRank:V,is_mba_product:O,images:k,is_access_category:Z}=t;if(!E){Ze.error("This product cannot be marked as a favorite. Only Merch By Amazon and KDP products are eligible."),p(I=>({...I,isAdding:!1}));return}if(!Z){Ze.error("This product cannot be marked as a favorite as its category is not supported on our platform for this marketplace."),p(I=>({...I,isAdding:!1}));return}if(!O||!V){Ze.error("This product cannot be marked as a favorite. Only Merch by Amazon products with a best seller rank are eligible.."),p(I=>({...I,isAdding:!1}));return}Ze.success("Processing, please wait...",ui);const Y=(M==null?void 0:M.link)||((k==null?void 0:k.length)>2?(F=k==null?void 0:k[1])==null?void 0:F.link:((X=k==null?void 0:k[0])==null?void 0:X.link)||""),N={group_id:+i||0,market_place:T,product_asin:w,brand:j,image:Y,title:z,product_type:E,flag:ci.ADD},L=await rt.handleItemForFavorite(N);if(L!=null&&L.success){const I=await rt.processForFavoriteItemsList(),G=await di(I==null?void 0:I.result);s({...G});const{allowedCredits:J=0,spentCredits:U=0}=((ee=L==null?void 0:L.result)==null?void 0:ee.credit_details)||{};await Pr(Er.FAVORITES,J,U,l),Ze.success((L==null?void 0:L.message)||"Product added to favorites.")}else(L==null?void 0:L.status)===429?o({isVisible:!0,modalTitle:(L==null?void 0:L.title)||"Limit reached",modalDescription:(L==null?void 0:L.description)||""}):Ze.error((L==null?void 0:L.message)||"Something went wrong!");p(I=>({...I,isAdding:!1}))};return b.useEffect(()=>{_(m?()=>({title:"Remove from Favorites",id:wa()}):()=>({title:"Mark as Favorite",id:wa()}))},[m]),h.jsx(ft,{tooltipTitle:g.title,extraClass:"bg-merch-dominator color-white i-p-2 h-fit",children:m?h.jsx(qh,{size:15,fill:"white"}):h.jsx(Dh,{size:15}),onClickHandler:S,tooltipId:g.id,tooltipPlace:"top-end",isDisabled:!a},g.id)}),Hh=b.memo(Uh),Wh=Ma(t=>({user:{email:"",first_name:"",last_name:"",name:""},plan:"",permissions:{productSearch:!1,metrics:!1,sortingAndPageRedirection:!1,exportDetails:!1,filters:!1,favorites:!1,productDetails:!1,productActions:!1,productHistory:!1,listView:!1},setUser:e=>t(()=>({user:e})),setPermissions:e=>t(()=>({permissions:e})),setPlan:e=>t(()=>({plan:e}))})),Kh=({extraClass:t=""})=>{const e=a=>{a.preventDefault(),Xl("pricing")};return h.jsx("div",{className:`w-full h-full upgrade-plan-button d-none ${t}`,children:h.jsx(vo,{buttonContent:"Upgrade to access all the features",isLoading:!1,isDisabled:!1,onclickHandler:e,buttonIcon:h.jsx(gu,{}),extraClass:"bg-merch-dominator color-white gap-x-1 border-none",buttonProps:{type:"button"}})})},Zh=50,Gh={common:{},brand:{}},Yh=b.memo(({dataFetching:t,scrapedPayload:e,estimatedSalesVisibility:a,uniqueKey:n})=>{const[r,s]=b.useState({loading:!0,value:0});return b.useEffect(()=>{if(!t&&a){const{estimated_sales:i=0}=e;s(o=>({...o,loading:!1,value:i}))}},[t,a]),h.jsx(Vt,{show:a,component:h.jsxs("div",{className:"mrdn-research-entry",children:[h.jsx("span",{className:"text-nowrap",children:"Estimated Sales:"}),h.jsxs("span",{className:`mrdn-entry-added with-button-section montserrat-bold d-flex justify-between align-items-center w-full ${r.loading?"mrdn-skeleton":""}`,children:[r.loading?h.jsx(aa,{}):h.jsx("span",{className:"i-text-14px bg-merch-dominator badge ml-2 badge-secondary bsr-rank-badge",children:r.value?h.jsx(lo,{value:r.value},`estimated-sales-${n}`):"N/A"}),h.jsx(ft,{tooltipTitle:"Estimated Sales per month based on BSR. ",extraClass:"mrdn-research-info i-p-0",isButtonType:!1,children:h.jsx(Bi,{size:14}),tooltipId:`estimated-sales-tooltip-${n}`})]})]})},n)}),Jh=({emitter:t,asin:e="N/A",itemIndex:a=-1,activeDomain:n="default",isSinglePage:r=!1,displayConfigs:s,isListViewDetailsPage:i=!1,handleProductSearchModal:o})=>{var U,ie,ue,Ee,Pe,ke,Ae,be,Re;const l=Mr(K=>K.setUpgradePlanModal),c=Ir(K=>K.setModules),{copying:d,copyTextToClipboard:m}=Lr({copyTextTitle:"Keywords copied to clipboard!"}),[u,p]=b.useState({}),[g,_]=b.useState(!0),[S,w]=b.useState({fetching:!1,isOpen:!1,trademarkData:{...Gh},isAlreadyProcessed:!1}),[j,z]=b.useState(""),[T,E]=b.useState({loading:!1,open:!1,indexedKeywords:{},allIndexedKeywords:""}),M=Sh(Ut(K=>K.scrapDetails[e])),{productDetails:V,productActions:O,productSearch:k,favorites:Z}=Wh(Ut(K=>K.permissions)),Y=!O,N=M==null;b.useEffect(()=>{N||(p(()=>M),_(!1))},[N]);const L=K=>{p(()=>K.payload),_(!1)};b.useEffect(()=>(t.addEventListener(`${Dt.UPDATE_SCRAPE_DATA}-${e}`,L),()=>{t.removeEventListener(`${Dt.UPDATE_SCRAPE_DATA}-${e}`,L)}),[]);const F=async()=>{var f,v,y;if(Y||g)return;w(C=>({...C,fetching:!0,isOpen:!0}));const K=((f=await rt.getStringifyValue(["merch.trademark-filters"]))==null?void 0:f["merch.trademark-filters"])??{},_e={...ec(tc,K),keyword:"",criteria:ac[0].value},Ce=`${u==null?void 0:u.title} ${u==null?void 0:u.brand} ${u==null?void 0:u.description} ${(v=u==null?void 0:u.non_common_bullets)!=null&&v.length?(y=u==null?void 0:u.non_common_bullets)==null?void 0:y.join(" "):""}`;z(Ce);const fe=await sf(_e,{sortBy:"",sortOrder:""},Ce,1,!0);if((fe==null?void 0:fe.status)===429){l({isVisible:!0,modalTitle:(fe==null?void 0:fe.title)||"Limit reached",modalDescription:(fe==null?void 0:fe.description)||""}),w(C=>({...C,fetching:!1}));return}const{allowedCredits:we=0,spentCredits:je=0}=(fe==null?void 0:fe.credit_details)||{};await Pr(Er.TRADEMARK_SEARCH,we,je,c);const Me=Hn((fe==null?void 0:fe.common)||[]),Ie=Hn((fe==null?void 0:fe.brand)||[]);w(C=>({...C,fetching:!1,isOpen:!0,trademarkData:{common:Me,brand:Ie},isAlreadyProcessed:!0}))},X=async()=>{if(Y||g||T.loading||T.allIndexedKeywords)return;E(je=>({...je,loading:!0,open:!0}));const K=[...u==null?void 0:u.all_keywords.split(","),...u==null?void 0:u.long_keywords.split(",")];if(!(u!=null&&u.all_keywords)&&!(u!=null&&u.long_keywords)){E(je=>({...je,loading:!1,open:!0}));return}const ye=window.location.origin,_e=[];K.forEach(je=>{if(!je)return;const Me=`${ye}/s?k=${encodeURIComponent(`${u==null?void 0:u.asin} `)}${je}`;_e.push({keyword:je,link:Me})});const Ce=nc(_e,Zh),{indexedKeywords:fe,allIndexedKeywords:we}=await Mt.processIndexKeywords(Ce,u==null?void 0:u.asin);E(je=>({...je,loading:!1,open:!0,indexedKeywords:fe,allIndexedKeywords:Object.keys(we).join(", ")}))},ee=async K=>{K&&(g||d||await m(K))},I=async K=>{var _e,Ce;if((_e=K==null?void 0:K.preventDefault)==null||_e.call(K),(Ce=K==null?void 0:K.stopPropagation)==null||Ce.call(K),g||d||Y)return;const ye=hf(u);await ee(JSON.stringify(ye))},G=()=>{!V||!k||o(u==null?void 0:u.title)},J=()=>{!V||!k||o(u==null?void 0:u.brand)};return h.jsxs("div",{className:"d-flex flex-col mrdn-research montserrat-medium",style:{gap:"6.4px"},id:`mdProductInformation-${e}-${a}`,children:[i&&h.jsx("div",{className:"w-full pb-2",children:g?h.jsx(aa,{height:300}):h.jsx("img",{style:{borderRadius:"4px",objectFit:"contain",width:"100%",maxWidth:"100%",height:"300px"},src:(U=u==null?void 0:u.main_image)==null?void 0:U.link,alt:"product-image"})}),h.jsx(Qe,{title:"Best Seller Rank (BSR)",uniqueKey:`bsrRank-${e}-${a}`,loading:g,mainContainerClass:"d-flex flex-wrap align-items-center ",valueClass:`d-flex align-items-center color-white i-text-14px ${g?"":"bg-merch-dominator badge ml-2 badge-secondary bsr-rank-badge"}`,children:h.jsxs(h.Fragment,{children:[h.jsx("span",{children:"#"})," ",u!=null&&u.bsrRank?h.jsx(lo,{value:u.bsrRank}):"N/A"]})}),h.jsx(Yh,{dataFetching:g,scrapedPayload:u,estimatedSalesVisibility:s==null?void 0:s.analyzeEstimatedSales,uniqueKey:`analyzeEstimatedSales-${e}-${a}`}),h.jsx(Vt,{show:s==null?void 0:s.analyzeSalesRankCategory,component:h.jsxs("div",{className:"mrdn-research-entry flex-col",children:[h.jsx("span",{children:"Best Seller Rank (BSR) Categories:"}),g?h.jsx(aa,{}):h.jsx("span",{className:`mrdn-entry-bsrsubcategories montserrat-bold  ${g?"mrdn-skeleton":""}`,children:(ie=u==null?void 0:u.bestsellers_rank)!=null&&ie.length?h.jsx("ul",{children:(ue=u==null?void 0:u.bestsellers_rank)==null?void 0:ue.map((K,ye)=>h.jsxs("li",{children:[h.jsxs("strong",{className:"text-main",children:["#"," ",Ya(K==null?void 0:K.rank)||"N/A"]}),h.jsxs("span",{children:[" ",(K==null?void 0:K.category)||""]})]},`bsr-${ye}`))}):"N/A"})]})},`analyzeSalesRankCategory-${e}-${a}`),h.jsx(Qe,{title:"Reviews",uniqueKey:`analyzeReviews-${e}-${a}`,shouldShow:s==null?void 0:s.analyzeReviews,loading:g,children:`${(u==null?void 0:u.rating)||"N/A"} ${u!=null&&u.reviews_total?`(${Ya(u==null?void 0:u.reviews_total)||""})`:""}`}),h.jsx(Qe,{title:"ASIN",uniqueKey:`analyzeAsin-${e}-${a}`,shouldShow:s==null?void 0:s.analyzeAsin,loading:g,valueClass:"d-flex gap-x-1 gap-y-1 flex-wrap",children:h.jsxs(h.Fragment,{children:[u==null?void 0:u.asin,u!=null&&u.parentAsin?h.jsxs("span",{className:"text-muted",children:[" (",u==null?void 0:u.parentAsin,")"]}):null,h.jsx(Jr,{copyText:u==null?void 0:u.asin,isButton:!1,iconSize:10})]})}),h.jsx(Qe,{title:"Title",uniqueKey:`analyzeTitle-${e}-${a}`,loading:g,shouldShow:s==null?void 0:s.analyzeTitle,valueClass:"d-flex gap-2",children:h.jsxs(h.Fragment,{children:[u!=null&&u.title?h.jsx("div",{onClick:G,className:"cursor-pointer",children:u==null?void 0:u.title}):"N/A",h.jsx(Jr,{copyText:u==null?void 0:u.title,isButton:!1,iconSize:10})]})}),h.jsx(Qe,{title:"Brand",uniqueKey:`analyzeBrand-${e}-${a}`,loading:g,shouldShow:s==null?void 0:s.analyzeBrand,valueClass:"d-flex gap-2",children:h.jsxs(h.Fragment,{children:[u!=null&&u.brand?h.jsx("div",{onClick:J,className:"cursor-pointer",children:u==null?void 0:u.brand}):"N/A",h.jsx(Jr,{copyText:u==null?void 0:u.brand,isButton:!1,iconSize:10})]})}),h.jsx(Qe,{title:"Date Published",uniqueKey:`analyzeDatePublished-${e}-${a}`,shouldShow:s==null?void 0:s.analyzeDatePublished,loading:g,children:((Ee=u==null?void 0:u.first_available)==null?void 0:Ee.raw)||"N/A"}),h.jsx(Qe,{title:"Amazon Choice",uniqueKey:`analyzeAmazonChoice-${e}-${a}`,shouldShow:s==null?void 0:s.analyzeAmazonChoice,loading:g,children:u!=null&&u.isAmazonChoice?"Yes":"No"}),h.jsx(Qe,{title:"Bought In Past Month",uniqueKey:`analyzeBoughtInPastMonth-${e}-${a}`,loading:g,shouldShow:s==null?void 0:s.analyzeBoughtInPastMonth,valueClass:"text-main",children:((ke=(Pe=u.amazon_product_sold_text)==null?void 0:Pe.split(" "))==null?void 0:ke[0])||"N/A"}),h.jsx(Qe,{title:"Sponsored",uniqueKey:`analyzeSponsored-${e}-${a}`,loading:g,shouldShow:s==null?void 0:s.analyzeSponsored,valueClass:"d-flex",children:+(u==null?void 0:u.sponsored)?h.jsxs("div",{className:"d-flex gap-2 align-items-center",children:["Yes",h.jsx("span",{className:"d-flex font-semibold w-fit badge-success text-sm border-r-1 px-2 py-1",children:"AD"})]}):"No"}),h.jsx(Qe,{title:"Merchant Type",uniqueKey:`analyzeMerchantType-${e}-${a}`,loading:g,shouldShow:s==null?void 0:s.analyzeMerchantType,children:"Coming soon"}),h.jsx(Qe,{title:"Your Product",uniqueKey:`analyzeOwnProduct-${e}-${a}`,loading:g,shouldShow:s==null?void 0:s.analyzeOwnProduct,children:"Coming soon"}),h.jsx(Qe,{title:"Your Sold",uniqueKey:`analyzeOwnSold-${e}-${a}`,loading:g,shouldShow:s==null?void 0:s.analyzeOwnSold,children:"Coming soon"}),h.jsx(Qe,{title:"Your Last Sold",uniqueKey:`analyzeOwnLastSold-${e}-${a}`,loading:g,shouldShow:s==null?void 0:s.analyzeOwnLastSold,children:"Coming soon"}),h.jsx(Qe,{title:"Your Royalties",uniqueKey:`analyzeOwnRoyalties-${e}-${a}`,loading:g,shouldShow:s==null?void 0:s.analyzeOwnRoyalties,children:"Coming soon"}),h.jsxs("div",{className:`d-flex flex-col position-relative ${V?"":"product-details-restricted"}`,style:{gap:"6.4px"},children:[V?null:h.jsx(Kh,{extraClass:"p-10px"}),h.jsx(Vt,{show:s==null?void 0:s.analyzeFocusKeywords,component:h.jsx(Mn,{title:"Focus Keywords",isFetching:g,keywords:u==null?void 0:u.focus_keywords,infoTitle:"This shows all the focused keywords",handleCopyKeywords:ee,uniqueKey:`analyzeFocusKeywords-${e}-${a}`,hasPermission:V,hasProductSearchPermission:k})},`analyzeFocusKeywords-${e}-${a}`),h.jsx(Vt,{show:s==null?void 0:s.analyzeLongTailKeywords,component:h.jsx(Mn,{title:"Long-Tail Keywords",uniqueKey:`analyzeLongTailKeywords-${e}-${a}`,isFetching:g,infoTitle:"This shows all the detected long-tail keywords",directKeywords:u==null?void 0:u.long_keywords,handleCopyKeywords:ee,hasPermission:V,hasProductSearchPermission:k})},`analyzeLongTailKeywords-${e}-${a}`),h.jsx(Vt,{show:s==null?void 0:s.analyzeAllKeywords,component:h.jsx(Mn,{title:"All Keywords",uniqueKey:`analyzeAllKeywords-${e}-${a}`,isFetching:g,infoTitle:"This shows all the keywords",directKeywords:u==null?void 0:u.all_keywords,handleCopyKeywords:ee,hasPermission:V,hasProductSearchPermission:k})},`analyzeAllKeywords-${e}-${a}`)]}),O&&h.jsx(Vt,{show:T.open,component:h.jsx(h.Fragment,{children:h.jsx(Mn,{title:"Indexed Keywords",uniqueKey:`indexKeywords-${e}-${a}`,isFetching:T.loading,infoTitle:"Merch Dominator searches for this product with given keywords. If product is found with specified keywords, it shown green, otherwise red.",extraClass:"indexed-keywords",handleCopyKeywords:ee,hasPermission:O,directKeywords:T.allIndexedKeywords,hasProductSearchPermission:k,directChildren:T.loading?h.jsx(aa,{}):h.jsx(Za,{maxHeight:"6em",text:h.jsx("ul",{className:"mrdn-entry-indexedkeywords d-flex flex-wrap gap-1",children:T.allIndexedKeywords&&((be=(Ae=T.allIndexedKeywords)==null?void 0:Ae.split(", "))==null?void 0:be.map((K,ye)=>{const _e=`badge ${T.indexedKeywords[K]?"badge-success":"badge-danger"}`;return h.jsx("li",{className:_e,children:K},ye)}))})},"indexed-keywords")})})},`indexKeywords-${e}-${a}`),O&&S.isOpen?h.jsx(Eh,{asin:u==null?void 0:u.asin,emitter:t,scrapData:u,trademarkData:S,trademarkPayload:j,setTrademark:w}):null,h.jsx(Vt,{show:s==null?void 0:s.analyzeButtons,component:h.jsxs("div",{className:`mrdn-research-entry mrdn-research-entry-buttons align-items-center btn-toolbar gap-3 mt-4 ${O?"":"product-actions-restricted"}`,children:[r?null:h.jsx(Hh,{productData:u,isDataProcessing:g,asin:e,hasPermission:Z}),h.jsx(ft,{tooltipTitle:"Copy product details to Clipboard",extraClass:"bg-merch-dominator color-white i-p-2 h-fit",children:h.jsx(Wt,{size:15}),isDisabled:Y||d,onClickHandler:I,tooltipId:`copy-product-details-${e}-${a}`},`copy-product-details-${e}-${a}`),h.jsx(Ph,{imagesUrl:u==null?void 0:u.images,title:u==null?void 0:u.title,originalImageUrl:(Re=u==null?void 0:u.main_image)==null?void 0:Re.link,isSinglePage:r,productType:u==null?void 0:u.product_type,hasPermission:O}),h.jsx(ft,{tooltipTitle:"Analyze Amazon indexed keywords of specified product",extraClass:"bg-merch-dominator color-white i-p-2 h-fit",children:T.loading?h.jsx(Qa,{className:"fa-spin",size:15}):h.jsx(pu,{size:15}),onClickHandler:X,isDisabled:Y||T.loading,tooltipId:`index-keywords-${e}-${a}`}),h.jsx(ft,{tooltipTitle:"Report Infringement",extraClass:"bg-merch-dominator i-p-2 color-white h-fit",children:Y?h.jsx(Hs,{size:15}):h.jsx("a",{href:"/report/infringement/",target:"_blank",className:"d-flex align-items-center color-white",children:h.jsx(Hs,{size:15})}),tooltipId:`infringement-${e}-${a}`,isDisabled:Y}),h.jsx(ft,{tooltipTitle:"Check for trademarks",extraClass:"bg-merch-dominator mrdn-btn-tm-check color-white i-p-2 h-fit",onClickHandler:F,children:h.jsxs(h.Fragment,{children:[S.fetching?h.jsx(Qa,{className:"fa-spin",size:15}):h.jsx(yu,{size:15}),h.jsx("span",{children:r?" Check Trademarks":""})]}),isDisabled:Y||S.fetching,tooltipId:`tm-check-$${e}-${a}`})]})},`analyzeButtons-${e}-${a}`)]})};function Kg(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"},child:[]},{tag:"path",attr:{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"},child:[]}]})(t)}function Zg(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"},child:[]},{tag:"polyline",attr:{points:"15 3 21 3 21 9"},child:[]},{tag:"line",attr:{x1:"10",y1:"14",x2:"21",y2:"3"},child:[]}]})(t)}function Gg(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"},child:[]},{tag:"circle",attr:{cx:"12",cy:"12",r:"3"},child:[]}]})(t)}function Yg(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polygon",attr:{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"},child:[]}]})(t)}function Qh(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"4",y1:"9",x2:"20",y2:"9"},child:[]},{tag:"line",attr:{x1:"4",y1:"15",x2:"20",y2:"15"},child:[]},{tag:"line",attr:{x1:"10",y1:"3",x2:"8",y2:"21"},child:[]},{tag:"line",attr:{x1:"16",y1:"3",x2:"14",y2:"21"},child:[]}]})(t)}function Jg(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"10"},child:[]},{tag:"line",attr:{x1:"12",y1:"8",x2:"12",y2:"16"},child:[]},{tag:"line",attr:{x1:"8",y1:"12",x2:"16",y2:"12"},child:[]}]})(t)}function Qg(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"3 6 5 6 21 6"},child:[]},{tag:"path",attr:{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"},child:[]},{tag:"line",attr:{x1:"10",y1:"11",x2:"10",y2:"17"},child:[]},{tag:"line",attr:{x1:"14",y1:"11",x2:"14",y2:"17"},child:[]}]})(t)}function Xg(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"23 18 13.5 8.5 8.5 13.5 1 6"},child:[]},{tag:"polyline",attr:{points:"17 18 23 18 23 12"},child:[]}]})(t)}function ev(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"23 6 13.5 15.5 8.5 10.5 1 18"},child:[]},{tag:"polyline",attr:{points:"17 6 23 6 23 12"},child:[]}]})(t)}function Xh(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M7.502 6h7.128A3.375 3.375 0 0118 9.375v9.375a3 3 0 003-3V6.108c0-1.505-1.125-2.811-2.664-2.94a48.972 48.972 0 00-.673-.05A3 3 0 0015 1.5h-1.5a3 3 0 00-2.663 1.618c-.225.015-.45.032-.673.05C8.662 3.295 7.554 4.542 7.502 6zM13.5 3A1.5 1.5 0 0012 4.5h4.5A1.5 1.5 0 0015 3h-1.5z",clipRule:"evenodd"},child:[]},{tag:"path",attr:{fillRule:"evenodd",d:"M3 9.375C3 8.339 3.84 7.5 4.875 7.5h9.75c1.036 0 1.875.84 1.875 1.875v11.25c0 1.035-.84 1.875-1.875 1.875h-9.75A1.875 1.875 0 013 20.625V9.375zM6 12a.75.75 0 01.75-.75h.008a.75.75 0 01.75.75v.008a.75.75 0 01-.75.75H6.75a.75.75 0 01-.75-.75V12zm2.25 0a.75.75 0 01.75-.75h3.75a.75.75 0 010 1.5H9a.75.75 0 01-.75-.75zM6 15a.75.75 0 01.75-.75h.008a.75.75 0 01.75.75v.008a.75.75 0 01-.75.75H6.75a.75.75 0 01-.75-.75V15zm2.25 0a.75.75 0 01.75-.75h3.75a.75.75 0 010 1.5H9a.75.75 0 01-.75-.75zM6 18a.75.75 0 01.75-.75h.008a.75.75 0 01.75.75v.008a.75.75 0 01-.75.75H6.75a.75.75 0 01-.75-.75V18zm2.25 0a.75.75 0 01.75-.75h3.75a.75.75 0 010 1.5H9a.75.75 0 01-.75-.75z",clipRule:"evenodd"},child:[]}]})(t)}const ef=({label:t="",checked:e,onChange:a,extraClass:n="",inputProps:r={}})=>h.jsx("div",{className:`merch-checkbox-container ${n}`,children:h.jsxs("label",{className:"font-medium",children:[h.jsx("input",{type:"checkbox",className:"cursor-pointer",checked:e,onChange:a,...r}),h.jsx("span",{className:"checkbox"}),t]})}),tf=({position:t=1,page:e=1,asin:a,emitter:n})=>{const[r,s]=b.useState(!0),[i,o]=b.useState(!1),l=b.useCallback(m=>{if(m.payload.type===rc.ALL){s(!0);return}s(!1)},[]),c=b.useCallback(m=>{o(m.payload.show),m.payload.show&&s(!0)},[]);b.useEffect(()=>(n.addEventListener(Dt.PRODUCT_SELECTION,l),n.addEventListener(Dt.TOGGLE_SELECTION_VISIBILITY,c),()=>{n.removeEventListener(Dt.PRODUCT_SELECTION,l),n.removeEventListener(Dt.TOGGLE_SELECTION_VISIBILITY,c)}),[]);const d=b.useCallback(m=>{const u=m.target.checked;s(u),n.updateProductData(Dt.SELECT_SINGLE_PRODUCT,{asin:a,status:u})},[]);return h.jsx("div",{className:"merch-dominator-style-container",style:{position:"absolute",zIndex:1,marginRight:.25,right:0},children:h.jsxs("div",{className:"bg-merch-dominator font-medium page-badge badge color-white d-flex-important",style:{borderTopLeftRadius:0,borderBottomRightRadius:0,borderTopRightRadius:0,fontSize:"85%",borderImage:"none"},children:[i?h.jsx(ef,{checked:r,onChange:d}):null,h.jsx(ft,{isButtonType:!1,tooltipPlace:"left",extraClass:"i-p-0 i-line-h-unset",tooltipId:`position-in-search-result-${t}`,tooltipTitle:"Position in search result",children:h.jsxs("div",{className:"flex-fill p-1 d-flex gap-x-1 align-items-center","data-title":"Position in search result",children:[h.jsx(Qh,{})," ",t]})}),h.jsx(ft,{isButtonType:!1,tooltipPlace:"right",extraClass:"i-p-0 i-line-h-unset",tooltipId:`page-of-search-result-${t}`,tooltipTitle:"Page of search results",children:h.jsxs("div",{className:"flex-fill p-1 d-flex gap-x-1 align-items-center","data-title":"Page of search results",children:[h.jsx(Xh,{})," ",e]})})]})})},af=b.memo(tf),ui={icon:h.jsx(jc,{fill:"var(--text-main-color)",style:{border:"1px solid var(--text-main-color)",borderRadius:"8px"}}),style:{color:"var(--text-main-color)"}},nf=()=>{var r,s,i;const e=(((r=window==null?void 0:window.location)==null?void 0:r.href)||"").split("/"),a=e.indexOf("dp"),n=a!==-1?a+1:a;return(i=(s=e[n]||"")==null?void 0:s.match(/^[^?]+/))==null?void 0:i[0]},rf=()=>{const t=window.location.hostname,e=(t==null?void 0:t.split(".").pop())||Ka.com;return Ka[e]||Ka.com},tv=()=>{const t=rf(),e=document.querySelector("title");if(sc[t].some(r=>{var s;return(s=e==null?void 0:e.textContent)==null?void 0:s.toLowerCase().includes(r.toLowerCase())})){const r=nf();if(r){const s=[{asin:r,market_place:t,deleted_at:new Date().toISOString().slice(0,19).replace("T"," ")}];rt.processDeletedProduct(s).then(i=>{});return}}},av=(t="merch-dominator-montserrat-font")=>{if(!document.getElementById(t)){const a=document.createElement("link");a.id=t,a.rel="stylesheet",a.type="text/css",a.href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap",document.head.appendChild(a)}},nv=async(t,e,a,n,r,s,i,o,l=!0,c)=>{var E,M,V,O,k,Z,Y,N,L,F,X,ee,I,G;const d=(E=window==null?void 0:window.location)==null?void 0:E.origin;let m=!0;l||r.addEventListener("stop",()=>{m=!1});const{page:u,pageDoc:p}=s,g=p.querySelectorAll((M=t==null?void 0:t.listing)==null?void 0:M.main),_=g.length,S={};let w=c||0;const j={};let z="";const T=!i.analyzeSponsoredProducts&&!l;for(let J=0;J<_;J++){if(!m)return{productListingInfo:null,pageProcessedItems:0};try{const U=g[J];if(z=(U==null?void 0:U.getAttribute("data-asin"))||"",!z||!l&&(j[z]||n[z]))continue;const ie=Fr(U,(V=t==null?void 0:t.listing)==null?void 0:V.scraped_info_section);if(!ie)continue;if(l){if(U.querySelector(".mrdn-research-details"))continue;const f=document.createElement("div");f.className="mrdn-research-details merch-dominator-style-container",f.id=z,f.setAttribute("data-research-asin",z),ie.appendChild(f);const v=document.createElement("div");ie.insertBefore(v,ie.firstChild),Ja(v).render(h.jsx(af,{position:J+1,page:u,asin:z,emitter:o})),Ja(f).render(h.jsx(Jh,{emitter:o,asin:z,activeDomain:e,displayConfigs:i,handleProductSearchModal:a,itemIndex:J}))}const ue={},Ee=((N=(Y=(Z=(k=U.querySelector((O=t==null?void 0:t.listing)==null?void 0:O.sponsored_label))==null?void 0:k.textContent)==null?void 0:Z.trim())==null?void 0:Y.split(" "))==null?void 0:N.length)==1?"1":"0";if(ue.sponsored=Ee,T&&+Ee)continue;const Pe=Mi(d,e,z);ue.link=Pe;const ke=Mt.fetchTextContent(U,(L=t==null?void 0:t.listing)==null?void 0:L.price_list),{value:Ae,symbol:be,currency:Re,raw:K}=Mt.fetchPriceFields(ke,e);ue.price=[{symbol:be,value:Ae,currency:Re,raw:K}],ue.asin=z;const ye=Mt.fetchImageUrl(U,(F=t==null?void 0:t.listing)==null?void 0:F.main_image);ue.main_image={link:ye};const _e=Mt.fetchTextContent(U,(X=t==null?void 0:t.listing)==null?void 0:X.product_bought_text);ue.amazon_product_sold_text=_e;const Ce=Mt.fetchTextContent(U,(ee=t.listing)==null?void 0:ee.title);ue.title=Ce;const{ratings:fe,reviews:we}=Mt.fetchListingReviews(U,(I=t==null?void 0:t.listing)==null?void 0:I.rating_details);ue.rating=fe,ue.reviews_total=we,ue.ratings_total=we;const je=Mt.fetchTextContent(U,(G=t==null?void 0:t.listing)==null?void 0:G.brand);ue.brand=je,l&&(U.setAttribute("data-rank",w.toString()),U.setAttribute("data-index",w.toString()),U.setAttribute("data-sponsored",Ee));const Me=l&&S[z]?S[z].duplicateProductCount+1:0;S[z]={...ue,duplicateProductCount:Me}}catch(U){console.log(`🚀 ~ Error Occurred in product ${J+1}:`,U)}l||(j[z]=1),w++}return{productListingInfo:S,pageProcessedItems:w}},rv=async(t,e,a,n={})=>{const r={};let s=0;const o=Array.from({length:a-e+1},(l,c)=>e+c).map(async l=>{if(n[l]){r[l]=n[l],s+=n[l];return}const c=await of(l),d=c==null?void 0:c.querySelectorAll(t);r[l]=(d==null?void 0:d.length)||0,s+=(d==null?void 0:d.length)||0});return await Promise.all(o),{rangeWiseProductsCount:r,rangeTotalProductsCount:s}},sf=async(t,e,a,n,r=!1,s=!1)=>{const{sortBy:i,sortOrder:o}=e,{tmOffice:l,niceClass:c,trademarkType:d,status:m,application_date:u,registration_date:p,wordCount:g,criteria:_,keyword:S}=t,w={type:"search",keywords:S||a,criteria:_,offices:l.join(","),class:c.join(","),tm_type:d.join(","),status:m.join(","),page:n};if(!r){const z=g.length&&g.length<2?g.join(","):"",T=u!=null&&u.includes("to")?u.replace(" to ",".."):u?`${u}..${u}`:"",E=p!=null&&p.includes("to")?p.replace(" to ",".."):p?`${p}..${p}`:"";w.word_count=z,w.application_date=T,w.registration_date=E,w.sort_by=i,w.sort_order=o}return r?await rt.processForHighlightTrademark(w,s)??{}:await rt.processTrademark(w)??{}},sv=(t,e,a)=>{let n="";const r=t;switch(e.toLowerCase()){case"wo":n=`https://www3.wipo.int/madrid/monitor/en/showData.jsp?ID=ROM.${r}`;break;case"em":n=`https://euipo.europa.eu/eSearch/#details/trademarks/${r}`;break;case"us":n=`https://tsdr.uspto.gov/#caseNumber=${r}&caseType=SERIAL_NO&searchType=statusSearch`;break;case"gb":n=`https://trademarks.ipo.gov.uk/ipo-tmcase/page/Results/1/${r}`;break;case"de":n=`https://register.dpma.de/DPMAregister/marke/register/${r}/DE?lang=en`;break;case"fr":n=`https://data.inpi.fr/marques/FR${r}`;break;case"es":n=`http://consultas2.oepm.es/ceo/jsp/busqueda/consultaExterna.xhtml?numExp=${r}`;break;default:n=`https://www.tmdn.org/tmview/#/tmview/detail/${a}`;break}return n},iv=t=>{if(!t.querySelector(".back-to-top")){const a=t.createElement("div");a.className="merch-dominator-style-container montserrat-medium",a.style.position="fixed",a.style.bottom="20px",a.style.right="20px",a.style.zIndex="200",t.body.appendChild(a),Ja(a).render(h.jsx(vu,{}))}},ov=t=>{const e={...t},{date_published:a,duration_range:n,duration:r}=e,s=a==="between";if(s&&(n!=null&&n.length)){const[i,o]=n,l=Ur(i,"yyyy-MM-dd"),c=Ur(o,"yyyy-MM-dd");e.duration_range=[l,c]}else if(!s&&r){const i=Ur(r,"yyyy-MM-dd");e.duration=i}return e},lv=t=>{const e={...t},{duration_range:a,duration:n,date_published:r}=e,s=r==="between";return s&&(a!=null&&a.length)?e.duration_range=[new Date(a[0]),new Date(a[1])]:!s&&n&&(e.duration=new Date(n)),e},cv=(t,e,a=!1)=>{var X,ee,I;let n=!0;const{uploadDate:r="0",bsr:s="0",price:i="0",rating:o="0",reviews:l="0",listing:c,mbaOnly:d,isSponsored:m=!1,brand:u}=t??{},{date_published:p,duration:g,duration_range:_,bsr:{checked:S,range:w},price:{checked:j,range:z},rating:{checked:T,range:E},reviews:{checked:M,range:V},excludeKeywords:O,excludeBrands:k,soldStatus:Z,merchProductsOnly:Y,sponsoredOnly:N}=e;if(k!=null&&k.length){const G=(X=u==null?void 0:u.trim())==null?void 0:X.toLowerCase();if(k.some(U=>U.toLowerCase()===G))return!1}if(O!=null&&O.length){const G=(ee=c==null?void 0:c.trim())==null?void 0:ee.toLowerCase();if((I=O==null?void 0:O.some)!=null&&I.call(O,J=>G.includes(J.toLowerCase())))return!1}let L=null;const F=p==="between";if(!F)L=Hr(g);else{const[G,J]=_;(G||J)&&(L=[Hr(G),Hr(J)])}if(L){if(F){const[G,J]=L;n=G&&J?Da(+r,[+G,+J]):r==G}else p=="after"?n=r>=L:n=r<=L;if(!n)return n}return Z&&(Z=="sold"?n=!!+s:Z=="not-sold"&&(n=!+s),!n)||a&&N&&(N=="sponsored"?n=m:N=="not-sponsored"&&(n=!m),!n)||(n=Y?d:n,!n)||S&&(n=Da(+s,w),!n)||j&&(n=Da(+i,z),!n)||T&&(n=Da(+o,E),!n)||M&&(n=Da(+l,V),!n),n},uv=(t,e)=>{var o,l,c,d;const a=Fr(t,e),[n,...r]=((o=a==null?void 0:a.textContent)==null?void 0:o.split(" "))||[],s=t.getElementsByTagName("script");let i=0;for(let m=0;m<s.length;m++)try{const u=s[m];if(!((l=u==null?void 0:u.attributes)!=null&&l.length)){const p=u==null?void 0:u.innerHTML,g=p==null?void 0:p.match(/"totalResultCount":\d+/);if(g){const _="{"+g[0]+"}";i=JSON.parse(_).totalResultCount;break}}}catch(u){console.log("🚀 ~ Error occurred while fetch total product count ~ error:",u)}return i||(i=+(((d=(c=r==null?void 0:r.join(""))==null?void 0:c.match(/\d+/g))==null?void 0:d.join(""))||"0")),{totalCount:i}},dv=(t,e,a)=>{var n;try{const r=Fr(t,e);if(!r)return 0;const s=(n=r==null?void 0:r.querySelectorAll)==null?void 0:n.call(r,a),i=s[s.length-1];let o=i?+((i==null?void 0:i.textContent)??0):0;if(isNaN(o)){const l=s[s.length-2];o=l&&+((l==null?void 0:l.textContent)??0)||0}return o}catch(r){return console.log("🚀 ~ fetchTotalPageCount ~ error:",r),0}},of=async t=>{var s;const e=((s=window==null?void 0:window.location)==null?void 0:s.href)??"";if(!e)return;const a=new URL(e);a.searchParams.set("page",`${t}`),a.searchParams.set("ref",`sr_pg_${t}`);const r={link:a.toString()};return await Mt.fetchPageContent(r)},mv=(t,e)=>({filter_title:e,filter_data:{...t,searchWord:"",searchType:""},filter_type:"1",module:"Best Sellers Ext"}),lf=({messageType:t})=>{switch(b.useEffect(()=>{ne.runtime.onMessage.addListener((e,a,n)=>{var r,s,i;try{switch(e.type){case Li:return new Promise(o=>setTimeout(o,1e3)).then(()=>{window.location.reload()}),!0;default:break}}catch{const l=(i=(s=(r=ne)==null?void 0:r.runtime)==null?void 0:s.lastError)==null?void 0:i.message;console.log("Error in banner section: ",l),n({success:!1})}}),window.scrollTo(0,0)},[]),t){case lc:return h.jsxs("div",{className:"banner-section bg-merch-dominator color-white p-2 text-center",children:["To access extension features, logging in is required. If you don't have an account yet, please sign up by",h.jsx("a",{href:"https://merchdominator.com/pricing",target:"_blank",referrerPolicy:"no-referrer",children:" Clicking here."})]});case oc:return h.jsx("div",{className:"banner-section bg-merch-dominator color-white p-2 text-center",children:"To access extension features, logging in is required. Please complete your login process"});case ic:return h.jsxs("div",{className:"banner-section bg-merch-dominator color-white p-2 text-center",children:["To access extension features, Higher subscription plan is Required. Please Upgrade your plan by",h.jsx("a",{href:"https://merchdominator.com/pricing",target:"_blank",referrerPolicy:"no-referrer",children:" Clicking here."})]});default:return null}},hv=t=>{if(document.querySelector("#banner-container"))return;const e=document.createElement("div");e.id="banner-container",e.className="merch-dominator-style-container",document.body.prepend(e),Ja(e).render(h.jsx(lf,{messageType:t}))},fv=(t,e)=>{switch(t){case 0:case 2:return cc.includes(e);case 3:return!0;case 4:return!0;default:return!1}},pv=t=>t?t<=1e5?"veryLow":t<=3e5?"low":t<=6e5?"medium":t<=1e6?"high":"veryHigh":"notAvailable",gv=(t,e,a,n,r,s)=>{const i=e<10?10-e+.1:1,o=a/10,l=n<10?10-n+1/10:1,c=n/10,d=e/10,m=a/10;let u=0,p=0;for(const[w,j]of Object.entries(r))s[w],u+=j;const g=25*u*(1+o)/(i*l),_=Math.max(1,g*.01);p=Math.round(_);let S=0;if(t<=p)S=t?1:0;else{const w=+(c+d-m).toFixed(2),j=Math.max(1,w),z=Math.max(1,ys(5,t/p*2)),T=1;S=Math.ceil(j*z*T*t/10)}return{estimatedSales:S,minimumDesignsForOneSale:p}},Fn={lowerRange:1.2,mediumRange:.9,mediumLowerRange:.7,highRange:.25},vv=(t,e)=>{if(!e)return{demandScore:0};const a=t.veryLow+t.low,n=t.medium+t.high,r=t.veryHigh,s=t.notAvailable,i={lowerRange:a*100/e,mediumRange:n*100/e,mediumLowerRange:r*100/e,highRange:s*100/e};let o=Fn.lowerRange*i.lowerRange+Fn.mediumRange*i.mediumRange+Fn.mediumLowerRange*i.mediumLowerRange+Fn.highRange*i.highRange;return{demandScore:ys(10,Math.max(1,Math.round(o/10)))}},cf={100:10,500:9,1e3:8,1500:7,2500:6,4e3:5,6e3:4,8e3:3,1e4:2},yv=t=>{for(const[e,a]of Object.entries(cf))if(t<=+e)return a;return 1},uf=(t,e,a)=>{const n=t/10,r=e/10;return 10*(Wr.w1*n+Wr.w2*r+Wr.w3*(1/(1+a)))},df=(t,e,a)=>Math.max(e,Math.min(t,a)),mf=(t,e,a)=>(t-e)/(a-e)*10,kv=(t,e,a)=>{const n=uf(t,e,a),r=df(n,Rn.minScore,Rn.maxScore);return ys(10,Math.ceil(mf(r,Rn.minScore,Rn.maxScore)))},bv=t=>t<=3?"red":t<=7?"darkyellow":"darkgreen",wv=t=>{const e=t.split(","),a=e.length;let n=t.slice(0,34);const r=n.split(","),s=e.indexOf((r==null?void 0:r.at(-1))||"");let i=r.length;s===-1&&(i=i-1);const o=a>i?a-i:0;return{firstSlicedPart:n,remainingWordLength:o}},xv=(t,e)=>t.map(a=>{var Y;const{salesRankCategory:n,title:r,link:s,non_common_bullets:i,description:o,brand:l,first_available:c,price:d,bsrRank:m,reviews_total:u,rating:p,focus_keywords:g,all_keywords:_,long_keywords:S,estimated_sales:w=0,fit_type:{menFit:j,womenFit:z,youthFit:T}={},colors:E,isAmazonChoice:M,main_image:{link:V},parentAsin:O,asin:k,amazon_product_sold_text:Z}=a||{};return{ASIN:k,"Product URL":s,"Best Seller Rank (BSR) ":Ya(m),"Best Seller Rank (BSR)  Category":n,Brand:l,Title:r,"Bullet Points 1":(i==null?void 0:i[0])||"-","Bullet Points 2":(i==null?void 0:i[1])||"-",Description:o,price:(Y=d==null?void 0:d[0])==null?void 0:Y.value,Reviews:Ya(u),Ratings:p,Published:c==null?void 0:c.utc,"Estimated Sales":w,"Own Product":"Coming soon","Men Fit":j,"Women Fit":z,"Youth Fit":T,"Configured Colors":E,"Amazon Choice":M?"Yes":"No","Bought In past Month":Z||"N/A","Merchant Type":"Coming soon","Product Image":V,"Parent ASIN":O,Advertising:"No","Focus Keywords":[...g==null?void 0:g.keys()].join(",")||"","Long Tail Keywords":S,"All Keywords":_}}),hf=t=>{var z;const{title:e,non_common_bullets:a,description:n,brand:r,first_available:s,price:i,reviews_total:o,rating:l,bsrRank:c,focus_keywords:d,all_keywords:m,long_keywords:u,isAmazonChoice:p,amazon_product_sold_text:g,estimated_sales:_,asin:S,product_type:w}=t||{};return{ASIN:S,"Best Seller Rank":c,"Estimated Sales":_||"N/A",category:w||"N/A",Reviews:o,Ratings:l,Brand:r,"Date Published":s==null?void 0:s.utc,Price:(z=i==null?void 0:i[0])==null?void 0:z.value,"Amazon Choice":p?"Yes":"No","Bought In past Month":g||"N/A","Focus Keywords":[...d==null?void 0:d.keys()].join(","),"Long Tail Keywords":u,"All Keywords":m,Title:e,Description:n,"Bullet Points 1":(a==null?void 0:a[0])||"","Bullet Points 2":(a==null?void 0:a[1])||""}},di=async t=>{const{extension_favorite:e=[],web_favorite:a=[]}=t||{},n={extension:{},web:{}};return await Promise.all([e.forEach(r=>{n.extension[r.asin]=r}),a.forEach(r=>{n.web[r.asin]=r})]),n},_v=(t,e,a)=>t?e?t-e:a?1:-1:a?-1:1,jv=(t,e,a)=>t?e?t.localeCompare(e):a?1:-1:a?-1:1,Sv=(t,e)=>{const a=new Set(t.map(n=>n.toLowerCase()));return[...t,...e.filter(n=>!a.has(n.toLowerCase()))]},zv=()=>{var j,z,T,E,M,V,O,k,Z,Y,N;const t=document.querySelector("#list-view-modal-container .list-view-float-modal");if(!t)return{imageLeftPosition:265,actionsLeftPosition:140};const e=(j=t==null?void 0:t.style)==null?void 0:j.transform;let a=0;e&&(a=+(((T=(z=e==null?void 0:e.match)==null?void 0:z.call(e,/translate\((\d+)(?:px)?/))==null?void 0:T[1])||0));const n=document.querySelector('[col-id="images_count"]'),r=document.querySelector('[col-id="is_mba_product"]'),s=document.querySelector('[col-id="images_count"].large-image-size')?40:0,i=+((V=(M=(E=n==null?void 0:n.style)==null?void 0:E.width)==null?void 0:M.replace)==null?void 0:V.call(M,"px",""))||0,o=+((Z=(k=(O=r==null?void 0:r.style)==null?void 0:O.width)==null?void 0:k.replace)==null?void 0:Z.call(k,"px",""))||0,l=i/2+10,c=o/2-10,d=(Y=r==null?void 0:r.getBoundingClientRect)==null?void 0:Y.call(r),m=(N=n==null?void 0:n.getBoundingClientRect)==null?void 0:N.call(n),u=(m==null?void 0:m.left)||265,g=((d==null?void 0:d.left)||140)-a,_=u+s-a,S=Math.ceil(_+l),w=Math.ceil(g+c);return{imageLeftPosition:S,actionsLeftPosition:w}},Ev=()=>{window.onerror=function(t,e,a,n,r){const s={type:"CONTENT_SCRIPT_ERROR",message:t,source:e,lineno:a,colno:n,stack:r?r.stack:null};return console.log("🚀 ~ Error Data:",s),!0},window.addEventListener("error",function(t){console.log("🚀 ~ Caught Error:",t.message)}),window.addEventListener("unhandledrejection",function(t){try{const e={type:"UNHANDLED_REJECTION",message:t.reason?t.reason.message:"Unhandled rejection",stack:t.reason?t.reason.stack:null};console.log("🚀 ~ Unhandled Rejection:",e)}catch(e){console.log("🚀 ~ Error while handling unhandled rejection:",e)}t.preventDefault()})},Fr=(t,e)=>{for(const a in e){const n=t==null?void 0:t.querySelector(e[a]);if(n)return n}return null},Cv=(t,e)=>{for(const a in e){const n=t==null?void 0:t.getElementById(e[a]);if(n)return n}return null},Tv=(t,e)=>t?e:[],Av=(t,e,a,n=!1)=>{const r=t.querySelector(e);if(!r)return{targetContainer:null,wrapperContainer:null};let s=document.getElementById(a==null?void 0:a.id);return n&&(s==null||s.remove(),s=null),s||(s=document.createElement("div"),Object.assign(s,a)),{targetContainer:r,wrapperContainer:s}},Ov=(t,e=300,a=5e3)=>new Promise((n,r)=>{let s=0;const i=()=>{try{const o=document.querySelector(t);o?n(o):s>=a?n(null):(s+=e,setTimeout(i,e))}catch{n(null)}};i()}),Rv=(t,e,a,n=300,r=5e3)=>new Promise(s=>{let i=0;const o=()=>{try{const l=(a||document).querySelector(t);if(l){let c=document.getElementById(e==null?void 0:e.id);c||(c=document.createElement("div"),Object.assign(c,e)),l.insertAdjacentElement("afterend",c),s(c)}else i>=r?s(null):(i+=n,setTimeout(o,n))}catch{s(null)}};o()}),Lv=(t,e,a=!0)=>{try{const n=a?JSON.stringify(e):e;localStorage.setItem(t,n)}catch(n){n instanceof DOMException&&n.name==="QuotaExceededError"&&Ze.error("Please remove unused data from local storage to save new data.")}},Nv=(t,e=!0,a={})=>{try{const n=localStorage.getItem(t);return n===null?void 0:e?ss(n,a):n}catch{return}},Mv=(t,e)=>{var a;t.current||(t.current=document.querySelector(e)),(a=t.current)==null||a.scrollTo(0,0)},Iv=t=>{const e=document.querySelector(t);e&&e.scrollIntoView({behavior:"smooth",block:"center"})},Fv=(t,e=document)=>{try{return t?e.querySelector(t):null}catch{return null}},Pv=(t,e,a=!0)=>{const n=document.querySelector(t);n==null||n.classList.toggle(e,a)},Pr=async(t,e,a=0,n)=>{if(!e||e==="Unlimited"||Math.min(Math.round(a/+e*100),100)<85)return;const{[rs]:s={}}=await rt.getStringifyValue([rs])||{};s[t]||n==null||n(t)};var ve;(function(t){t.assertEqual=r=>r;function e(r){}t.assertIs=e;function a(r){throw new Error}t.assertNever=a,t.arrayToEnum=r=>{const s={};for(const i of r)s[i]=i;return s},t.getValidEnumValues=r=>{const s=t.objectKeys(r).filter(o=>typeof r[r[o]]!="number"),i={};for(const o of s)i[o]=r[o];return t.objectValues(i)},t.objectValues=r=>t.objectKeys(r).map(function(s){return r[s]}),t.objectKeys=typeof Object.keys=="function"?r=>Object.keys(r):r=>{const s=[];for(const i in r)Object.prototype.hasOwnProperty.call(r,i)&&s.push(i);return s},t.find=(r,s)=>{for(const i of r)if(s(i))return i},t.isInteger=typeof Number.isInteger=="function"?r=>Number.isInteger(r):r=>typeof r=="number"&&isFinite(r)&&Math.floor(r)===r;function n(r,s=" | "){return r.map(i=>typeof i=="string"?`'${i}'`:i).join(s)}t.joinValues=n,t.jsonStringifyReplacer=(r,s)=>typeof s=="bigint"?s.toString():s})(ve||(ve={}));var fs;(function(t){t.mergeShapes=(e,a)=>({...e,...a})})(fs||(fs={}));const H=ve.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Bt=t=>{switch(typeof t){case"undefined":return H.undefined;case"string":return H.string;case"number":return isNaN(t)?H.nan:H.number;case"boolean":return H.boolean;case"function":return H.function;case"bigint":return H.bigint;case"symbol":return H.symbol;case"object":return Array.isArray(t)?H.array:t===null?H.null:t.then&&typeof t.then=="function"&&t.catch&&typeof t.catch=="function"?H.promise:typeof Map<"u"&&t instanceof Map?H.map:typeof Set<"u"&&t instanceof Set?H.set:typeof Date<"u"&&t instanceof Date?H.date:H.object;default:return H.unknown}},R=ve.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),ff=t=>JSON.stringify(t,null,2).replace(/"([^"]+)":/g,"$1:");class it extends Error{constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const a=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,a):this.__proto__=a,this.name="ZodError",this.issues=e}get errors(){return this.issues}format(e){const a=e||function(s){return s.message},n={_errors:[]},r=s=>{for(const i of s.issues)if(i.code==="invalid_union")i.unionErrors.map(r);else if(i.code==="invalid_return_type")r(i.returnTypeError);else if(i.code==="invalid_arguments")r(i.argumentsError);else if(i.path.length===0)n._errors.push(a(i));else{let o=n,l=0;for(;l<i.path.length;){const c=i.path[l];l===i.path.length-1?(o[c]=o[c]||{_errors:[]},o[c]._errors.push(a(i))):o[c]=o[c]||{_errors:[]},o=o[c],l++}}};return r(this),n}static assert(e){if(!(e instanceof it))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,ve.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=a=>a.message){const a={},n=[];for(const r of this.issues)r.path.length>0?(a[r.path[0]]=a[r.path[0]]||[],a[r.path[0]].push(e(r))):n.push(e(r));return{formErrors:n,fieldErrors:a}}get formErrors(){return this.flatten()}}it.create=t=>new it(t);const Oa=(t,e)=>{let a;switch(t.code){case R.invalid_type:t.received===H.undefined?a="Required":a=`Expected ${t.expected}, received ${t.received}`;break;case R.invalid_literal:a=`Invalid literal value, expected ${JSON.stringify(t.expected,ve.jsonStringifyReplacer)}`;break;case R.unrecognized_keys:a=`Unrecognized key(s) in object: ${ve.joinValues(t.keys,", ")}`;break;case R.invalid_union:a="Invalid input";break;case R.invalid_union_discriminator:a=`Invalid discriminator value. Expected ${ve.joinValues(t.options)}`;break;case R.invalid_enum_value:a=`Invalid enum value. Expected ${ve.joinValues(t.options)}, received '${t.received}'`;break;case R.invalid_arguments:a="Invalid function arguments";break;case R.invalid_return_type:a="Invalid function return type";break;case R.invalid_date:a="Invalid date";break;case R.invalid_string:typeof t.validation=="object"?"includes"in t.validation?(a=`Invalid input: must include "${t.validation.includes}"`,typeof t.validation.position=="number"&&(a=`${a} at one or more positions greater than or equal to ${t.validation.position}`)):"startsWith"in t.validation?a=`Invalid input: must start with "${t.validation.startsWith}"`:"endsWith"in t.validation?a=`Invalid input: must end with "${t.validation.endsWith}"`:ve.assertNever(t.validation):t.validation!=="regex"?a=`Invalid ${t.validation}`:a="Invalid";break;case R.too_small:t.type==="array"?a=`Array must contain ${t.exact?"exactly":t.inclusive?"at least":"more than"} ${t.minimum} element(s)`:t.type==="string"?a=`String must contain ${t.exact?"exactly":t.inclusive?"at least":"over"} ${t.minimum} character(s)`:t.type==="number"?a=`Number must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${t.minimum}`:t.type==="date"?a=`Date must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(t.minimum))}`:a="Invalid input";break;case R.too_big:t.type==="array"?a=`Array must contain ${t.exact?"exactly":t.inclusive?"at most":"less than"} ${t.maximum} element(s)`:t.type==="string"?a=`String must contain ${t.exact?"exactly":t.inclusive?"at most":"under"} ${t.maximum} character(s)`:t.type==="number"?a=`Number must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="bigint"?a=`BigInt must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="date"?a=`Date must be ${t.exact?"exactly":t.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(t.maximum))}`:a="Invalid input";break;case R.custom:a="Invalid input";break;case R.invalid_intersection_types:a="Intersection results could not be merged";break;case R.not_multiple_of:a=`Number must be a multiple of ${t.multipleOf}`;break;case R.not_finite:a="Number must be finite";break;default:a=e.defaultError,ve.assertNever(t)}return{message:a}};let yo=Oa;function pf(t){yo=t}function Qn(){return yo}const Xn=t=>{const{data:e,path:a,errorMaps:n,issueData:r}=t,s=[...a,...r.path||[]],i={...r,path:s};if(r.message!==void 0)return{...r,path:s,message:r.message};let o="";const l=n.filter(c=>!!c).slice().reverse();for(const c of l)o=c(i,{data:e,defaultError:o}).message;return{...r,path:s,message:o}},gf=[];function D(t,e){const a=Qn(),n=Xn({issueData:e,data:t.data,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,a,a===Oa?void 0:Oa].filter(r=>!!r)});t.common.issues.push(n)}class We{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,a){const n=[];for(const r of a){if(r.status==="aborted")return se;r.status==="dirty"&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,a){const n=[];for(const r of a){const s=await r.key,i=await r.value;n.push({key:s,value:i})}return We.mergeObjectSync(e,n)}static mergeObjectSync(e,a){const n={};for(const r of a){const{key:s,value:i}=r;if(s.status==="aborted"||i.status==="aborted")return se;s.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),s.value!=="__proto__"&&(typeof i.value<"u"||r.alwaysSet)&&(n[s.value]=i.value)}return{status:e.value,value:n}}}const se=Object.freeze({status:"aborted"}),ka=t=>({status:"dirty",value:t}),Ye=t=>({status:"valid",value:t}),ps=t=>t.status==="aborted",gs=t=>t.status==="dirty",en=t=>t.status==="valid",er=t=>typeof Promise<"u"&&t instanceof Promise;function tr(t,e,a,n){if(a==="a"&&!n)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?t!==e||!n:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return a==="m"?n:a==="a"?n.call(t):n?n.value:e.get(t)}function ko(t,e,a,n,r){if(n==="m")throw new TypeError("Private method is not writable");if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?t!==e||!r:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return n==="a"?r.call(t,a):r?r.value=a:e.set(t,a),a}var Q;(function(t){t.errToObj=e=>typeof e=="string"?{message:e}:e||{},t.toString=e=>typeof e=="string"?e:e==null?void 0:e.message})(Q||(Q={}));var Ha,Wa;class zt{constructor(e,a,n,r){this._cachedPath=[],this.parent=e,this.data=a,this._path=n,this._key=r}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const mi=(t,e)=>{if(en(e))return{success:!0,data:e.value};if(!t.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const a=new it(t.common.issues);return this._error=a,this._error}}};function de(t){if(!t)return{};const{errorMap:e,invalid_type_error:a,required_error:n,description:r}=t;if(e&&(a||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:r}:{errorMap:(i,o)=>{var l,c;const{message:d}=t;return i.code==="invalid_enum_value"?{message:d??o.defaultError}:typeof o.data>"u"?{message:(l=d??n)!==null&&l!==void 0?l:o.defaultError}:i.code!=="invalid_type"?{message:o.defaultError}:{message:(c=d??a)!==null&&c!==void 0?c:o.defaultError}},description:r}}class he{constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this)}get description(){return this._def.description}_getType(e){return Bt(e.data)}_getOrReturnCtx(e,a){return a||{common:e.parent.common,data:e.data,parsedType:Bt(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new We,ctx:{common:e.parent.common,data:e.data,parsedType:Bt(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const a=this._parse(e);if(er(a))throw new Error("Synchronous parse encountered promise.");return a}_parseAsync(e){const a=this._parse(e);return Promise.resolve(a)}parse(e,a){const n=this.safeParse(e,a);if(n.success)return n.data;throw n.error}safeParse(e,a){var n;const r={common:{issues:[],async:(n=a==null?void 0:a.async)!==null&&n!==void 0?n:!1,contextualErrorMap:a==null?void 0:a.errorMap},path:(a==null?void 0:a.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Bt(e)},s=this._parseSync({data:e,path:r.path,parent:r});return mi(r,s)}async parseAsync(e,a){const n=await this.safeParseAsync(e,a);if(n.success)return n.data;throw n.error}async safeParseAsync(e,a){const n={common:{issues:[],contextualErrorMap:a==null?void 0:a.errorMap,async:!0},path:(a==null?void 0:a.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Bt(e)},r=this._parse({data:e,path:n.path,parent:n}),s=await(er(r)?r:Promise.resolve(r));return mi(n,s)}refine(e,a){const n=r=>typeof a=="string"||typeof a>"u"?{message:a}:typeof a=="function"?a(r):a;return this._refinement((r,s)=>{const i=e(r),o=()=>s.addIssue({code:R.custom,...n(r)});return typeof Promise<"u"&&i instanceof Promise?i.then(l=>l?!0:(o(),!1)):i?!0:(o(),!1)})}refinement(e,a){return this._refinement((n,r)=>e(n)?!0:(r.addIssue(typeof a=="function"?a(n,r):a),!1))}_refinement(e){return new kt({schema:this,typeName:re.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}optional(){return jt.create(this,this._def)}nullable(){return Jt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return gt.create(this,this._def)}promise(){return La.create(this,this._def)}or(e){return rn.create([this,e],this._def)}and(e){return sn.create(this,e,this._def)}transform(e){return new kt({...de(this._def),schema:this,typeName:re.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const a=typeof e=="function"?e:()=>e;return new dn({...de(this._def),innerType:this,defaultValue:a,typeName:re.ZodDefault})}brand(){return new zs({typeName:re.ZodBranded,type:this,...de(this._def)})}catch(e){const a=typeof e=="function"?e:()=>e;return new mn({...de(this._def),innerType:this,catchValue:a,typeName:re.ZodCatch})}describe(e){const a=this.constructor;return new a({...this._def,description:e})}pipe(e){return zn.create(this,e)}readonly(){return hn.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const vf=/^c[^\s-]{8,}$/i,yf=/^[0-9a-z]+$/,kf=/^[0-9A-HJKMNP-TV-Z]{26}$/,bf=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,wf=/^[a-z0-9_-]{21}$/i,xf=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,_f=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,jf="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let Qr;const Sf=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,zf=/^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,Ef=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,bo="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",Cf=new RegExp(`^${bo}$`);function wo(t){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return t.precision?e=`${e}\\.\\d{${t.precision}}`:t.precision==null&&(e=`${e}(\\.\\d+)?`),e}function Tf(t){return new RegExp(`^${wo(t)}$`)}function xo(t){let e=`${bo}T${wo(t)}`;const a=[];return a.push(t.local?"Z?":"Z"),t.offset&&a.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${a.join("|")})`,new RegExp(`^${e}$`)}function Af(t,e){return!!((e==="v4"||!e)&&Sf.test(t)||(e==="v6"||!e)&&zf.test(t))}class pt extends he{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==H.string){const s=this._getOrReturnCtx(e);return D(s,{code:R.invalid_type,expected:H.string,received:s.parsedType}),se}const n=new We;let r;for(const s of this._def.checks)if(s.kind==="min")e.data.length<s.value&&(r=this._getOrReturnCtx(e,r),D(r,{code:R.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),n.dirty());else if(s.kind==="max")e.data.length>s.value&&(r=this._getOrReturnCtx(e,r),D(r,{code:R.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),n.dirty());else if(s.kind==="length"){const i=e.data.length>s.value,o=e.data.length<s.value;(i||o)&&(r=this._getOrReturnCtx(e,r),i?D(r,{code:R.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}):o&&D(r,{code:R.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}),n.dirty())}else if(s.kind==="email")_f.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"email",code:R.invalid_string,message:s.message}),n.dirty());else if(s.kind==="emoji")Qr||(Qr=new RegExp(jf,"u")),Qr.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"emoji",code:R.invalid_string,message:s.message}),n.dirty());else if(s.kind==="uuid")bf.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"uuid",code:R.invalid_string,message:s.message}),n.dirty());else if(s.kind==="nanoid")wf.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"nanoid",code:R.invalid_string,message:s.message}),n.dirty());else if(s.kind==="cuid")vf.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"cuid",code:R.invalid_string,message:s.message}),n.dirty());else if(s.kind==="cuid2")yf.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"cuid2",code:R.invalid_string,message:s.message}),n.dirty());else if(s.kind==="ulid")kf.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"ulid",code:R.invalid_string,message:s.message}),n.dirty());else if(s.kind==="url")try{new URL(e.data)}catch{r=this._getOrReturnCtx(e,r),D(r,{validation:"url",code:R.invalid_string,message:s.message}),n.dirty()}else s.kind==="regex"?(s.regex.lastIndex=0,s.regex.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"regex",code:R.invalid_string,message:s.message}),n.dirty())):s.kind==="trim"?e.data=e.data.trim():s.kind==="includes"?e.data.includes(s.value,s.position)||(r=this._getOrReturnCtx(e,r),D(r,{code:R.invalid_string,validation:{includes:s.value,position:s.position},message:s.message}),n.dirty()):s.kind==="toLowerCase"?e.data=e.data.toLowerCase():s.kind==="toUpperCase"?e.data=e.data.toUpperCase():s.kind==="startsWith"?e.data.startsWith(s.value)||(r=this._getOrReturnCtx(e,r),D(r,{code:R.invalid_string,validation:{startsWith:s.value},message:s.message}),n.dirty()):s.kind==="endsWith"?e.data.endsWith(s.value)||(r=this._getOrReturnCtx(e,r),D(r,{code:R.invalid_string,validation:{endsWith:s.value},message:s.message}),n.dirty()):s.kind==="datetime"?xo(s).test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{code:R.invalid_string,validation:"datetime",message:s.message}),n.dirty()):s.kind==="date"?Cf.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{code:R.invalid_string,validation:"date",message:s.message}),n.dirty()):s.kind==="time"?Tf(s).test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{code:R.invalid_string,validation:"time",message:s.message}),n.dirty()):s.kind==="duration"?xf.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"duration",code:R.invalid_string,message:s.message}),n.dirty()):s.kind==="ip"?Af(e.data,s.version)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"ip",code:R.invalid_string,message:s.message}),n.dirty()):s.kind==="base64"?Ef.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"base64",code:R.invalid_string,message:s.message}),n.dirty()):ve.assertNever(s);return{status:n.value,value:e.data}}_regex(e,a,n){return this.refinement(r=>e.test(r),{validation:a,code:R.invalid_string,...Q.errToObj(n)})}_addCheck(e){return new pt({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...Q.errToObj(e)})}url(e){return this._addCheck({kind:"url",...Q.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...Q.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...Q.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...Q.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...Q.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...Q.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...Q.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...Q.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...Q.errToObj(e)})}datetime(e){var a,n;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,offset:(a=e==null?void 0:e.offset)!==null&&a!==void 0?a:!1,local:(n=e==null?void 0:e.local)!==null&&n!==void 0?n:!1,...Q.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,...Q.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...Q.errToObj(e)})}regex(e,a){return this._addCheck({kind:"regex",regex:e,...Q.errToObj(a)})}includes(e,a){return this._addCheck({kind:"includes",value:e,position:a==null?void 0:a.position,...Q.errToObj(a==null?void 0:a.message)})}startsWith(e,a){return this._addCheck({kind:"startsWith",value:e,...Q.errToObj(a)})}endsWith(e,a){return this._addCheck({kind:"endsWith",value:e,...Q.errToObj(a)})}min(e,a){return this._addCheck({kind:"min",value:e,...Q.errToObj(a)})}max(e,a){return this._addCheck({kind:"max",value:e,...Q.errToObj(a)})}length(e,a){return this._addCheck({kind:"length",value:e,...Q.errToObj(a)})}nonempty(e){return this.min(1,Q.errToObj(e))}trim(){return new pt({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new pt({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new pt({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get minLength(){let e=null;for(const a of this._def.checks)a.kind==="min"&&(e===null||a.value>e)&&(e=a.value);return e}get maxLength(){let e=null;for(const a of this._def.checks)a.kind==="max"&&(e===null||a.value<e)&&(e=a.value);return e}}pt.create=t=>{var e;return new pt({checks:[],typeName:re.ZodString,coerce:(e=t==null?void 0:t.coerce)!==null&&e!==void 0?e:!1,...de(t)})};function Of(t,e){const a=(t.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,r=a>n?a:n,s=parseInt(t.toFixed(r).replace(".","")),i=parseInt(e.toFixed(r).replace(".",""));return s%i/Math.pow(10,r)}class Zt extends he{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==H.number){const s=this._getOrReturnCtx(e);return D(s,{code:R.invalid_type,expected:H.number,received:s.parsedType}),se}let n;const r=new We;for(const s of this._def.checks)s.kind==="int"?ve.isInteger(e.data)||(n=this._getOrReturnCtx(e,n),D(n,{code:R.invalid_type,expected:"integer",received:"float",message:s.message}),r.dirty()):s.kind==="min"?(s.inclusive?e.data<s.value:e.data<=s.value)&&(n=this._getOrReturnCtx(e,n),D(n,{code:R.too_small,minimum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),r.dirty()):s.kind==="max"?(s.inclusive?e.data>s.value:e.data>=s.value)&&(n=this._getOrReturnCtx(e,n),D(n,{code:R.too_big,maximum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),r.dirty()):s.kind==="multipleOf"?Of(e.data,s.value)!==0&&(n=this._getOrReturnCtx(e,n),D(n,{code:R.not_multiple_of,multipleOf:s.value,message:s.message}),r.dirty()):s.kind==="finite"?Number.isFinite(e.data)||(n=this._getOrReturnCtx(e,n),D(n,{code:R.not_finite,message:s.message}),r.dirty()):ve.assertNever(s);return{status:r.value,value:e.data}}gte(e,a){return this.setLimit("min",e,!0,Q.toString(a))}gt(e,a){return this.setLimit("min",e,!1,Q.toString(a))}lte(e,a){return this.setLimit("max",e,!0,Q.toString(a))}lt(e,a){return this.setLimit("max",e,!1,Q.toString(a))}setLimit(e,a,n,r){return new Zt({...this._def,checks:[...this._def.checks,{kind:e,value:a,inclusive:n,message:Q.toString(r)}]})}_addCheck(e){return new Zt({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:Q.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:Q.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:Q.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:Q.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:Q.toString(e)})}multipleOf(e,a){return this._addCheck({kind:"multipleOf",value:e,message:Q.toString(a)})}finite(e){return this._addCheck({kind:"finite",message:Q.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:Q.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:Q.toString(e)})}get minValue(){let e=null;for(const a of this._def.checks)a.kind==="min"&&(e===null||a.value>e)&&(e=a.value);return e}get maxValue(){let e=null;for(const a of this._def.checks)a.kind==="max"&&(e===null||a.value<e)&&(e=a.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&ve.isInteger(e.value))}get isFinite(){let e=null,a=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(a===null||n.value>a)&&(a=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(a)&&Number.isFinite(e)}}Zt.create=t=>new Zt({checks:[],typeName:re.ZodNumber,coerce:(t==null?void 0:t.coerce)||!1,...de(t)});class Gt extends he{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce&&(e.data=BigInt(e.data)),this._getType(e)!==H.bigint){const s=this._getOrReturnCtx(e);return D(s,{code:R.invalid_type,expected:H.bigint,received:s.parsedType}),se}let n;const r=new We;for(const s of this._def.checks)s.kind==="min"?(s.inclusive?e.data<s.value:e.data<=s.value)&&(n=this._getOrReturnCtx(e,n),D(n,{code:R.too_small,type:"bigint",minimum:s.value,inclusive:s.inclusive,message:s.message}),r.dirty()):s.kind==="max"?(s.inclusive?e.data>s.value:e.data>=s.value)&&(n=this._getOrReturnCtx(e,n),D(n,{code:R.too_big,type:"bigint",maximum:s.value,inclusive:s.inclusive,message:s.message}),r.dirty()):s.kind==="multipleOf"?e.data%s.value!==BigInt(0)&&(n=this._getOrReturnCtx(e,n),D(n,{code:R.not_multiple_of,multipleOf:s.value,message:s.message}),r.dirty()):ve.assertNever(s);return{status:r.value,value:e.data}}gte(e,a){return this.setLimit("min",e,!0,Q.toString(a))}gt(e,a){return this.setLimit("min",e,!1,Q.toString(a))}lte(e,a){return this.setLimit("max",e,!0,Q.toString(a))}lt(e,a){return this.setLimit("max",e,!1,Q.toString(a))}setLimit(e,a,n,r){return new Gt({...this._def,checks:[...this._def.checks,{kind:e,value:a,inclusive:n,message:Q.toString(r)}]})}_addCheck(e){return new Gt({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:Q.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:Q.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:Q.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:Q.toString(e)})}multipleOf(e,a){return this._addCheck({kind:"multipleOf",value:e,message:Q.toString(a)})}get minValue(){let e=null;for(const a of this._def.checks)a.kind==="min"&&(e===null||a.value>e)&&(e=a.value);return e}get maxValue(){let e=null;for(const a of this._def.checks)a.kind==="max"&&(e===null||a.value<e)&&(e=a.value);return e}}Gt.create=t=>{var e;return new Gt({checks:[],typeName:re.ZodBigInt,coerce:(e=t==null?void 0:t.coerce)!==null&&e!==void 0?e:!1,...de(t)})};class tn extends he{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==H.boolean){const n=this._getOrReturnCtx(e);return D(n,{code:R.invalid_type,expected:H.boolean,received:n.parsedType}),se}return Ye(e.data)}}tn.create=t=>new tn({typeName:re.ZodBoolean,coerce:(t==null?void 0:t.coerce)||!1,...de(t)});class oa extends he{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==H.date){const s=this._getOrReturnCtx(e);return D(s,{code:R.invalid_type,expected:H.date,received:s.parsedType}),se}if(isNaN(e.data.getTime())){const s=this._getOrReturnCtx(e);return D(s,{code:R.invalid_date}),se}const n=new We;let r;for(const s of this._def.checks)s.kind==="min"?e.data.getTime()<s.value&&(r=this._getOrReturnCtx(e,r),D(r,{code:R.too_small,message:s.message,inclusive:!0,exact:!1,minimum:s.value,type:"date"}),n.dirty()):s.kind==="max"?e.data.getTime()>s.value&&(r=this._getOrReturnCtx(e,r),D(r,{code:R.too_big,message:s.message,inclusive:!0,exact:!1,maximum:s.value,type:"date"}),n.dirty()):ve.assertNever(s);return{status:n.value,value:new Date(e.data.getTime())}}_addCheck(e){return new oa({...this._def,checks:[...this._def.checks,e]})}min(e,a){return this._addCheck({kind:"min",value:e.getTime(),message:Q.toString(a)})}max(e,a){return this._addCheck({kind:"max",value:e.getTime(),message:Q.toString(a)})}get minDate(){let e=null;for(const a of this._def.checks)a.kind==="min"&&(e===null||a.value>e)&&(e=a.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const a of this._def.checks)a.kind==="max"&&(e===null||a.value<e)&&(e=a.value);return e!=null?new Date(e):null}}oa.create=t=>new oa({checks:[],coerce:(t==null?void 0:t.coerce)||!1,typeName:re.ZodDate,...de(t)});class ar extends he{_parse(e){if(this._getType(e)!==H.symbol){const n=this._getOrReturnCtx(e);return D(n,{code:R.invalid_type,expected:H.symbol,received:n.parsedType}),se}return Ye(e.data)}}ar.create=t=>new ar({typeName:re.ZodSymbol,...de(t)});class an extends he{_parse(e){if(this._getType(e)!==H.undefined){const n=this._getOrReturnCtx(e);return D(n,{code:R.invalid_type,expected:H.undefined,received:n.parsedType}),se}return Ye(e.data)}}an.create=t=>new an({typeName:re.ZodUndefined,...de(t)});class nn extends he{_parse(e){if(this._getType(e)!==H.null){const n=this._getOrReturnCtx(e);return D(n,{code:R.invalid_type,expected:H.null,received:n.parsedType}),se}return Ye(e.data)}}nn.create=t=>new nn({typeName:re.ZodNull,...de(t)});class Ra extends he{constructor(){super(...arguments),this._any=!0}_parse(e){return Ye(e.data)}}Ra.create=t=>new Ra({typeName:re.ZodAny,...de(t)});class ra extends he{constructor(){super(...arguments),this._unknown=!0}_parse(e){return Ye(e.data)}}ra.create=t=>new ra({typeName:re.ZodUnknown,...de(t)});class Pt extends he{_parse(e){const a=this._getOrReturnCtx(e);return D(a,{code:R.invalid_type,expected:H.never,received:a.parsedType}),se}}Pt.create=t=>new Pt({typeName:re.ZodNever,...de(t)});class nr extends he{_parse(e){if(this._getType(e)!==H.undefined){const n=this._getOrReturnCtx(e);return D(n,{code:R.invalid_type,expected:H.void,received:n.parsedType}),se}return Ye(e.data)}}nr.create=t=>new nr({typeName:re.ZodVoid,...de(t)});class gt extends he{_parse(e){const{ctx:a,status:n}=this._processInputParams(e),r=this._def;if(a.parsedType!==H.array)return D(a,{code:R.invalid_type,expected:H.array,received:a.parsedType}),se;if(r.exactLength!==null){const i=a.data.length>r.exactLength.value,o=a.data.length<r.exactLength.value;(i||o)&&(D(a,{code:i?R.too_big:R.too_small,minimum:o?r.exactLength.value:void 0,maximum:i?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(r.minLength!==null&&a.data.length<r.minLength.value&&(D(a,{code:R.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),r.maxLength!==null&&a.data.length>r.maxLength.value&&(D(a,{code:R.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),a.common.async)return Promise.all([...a.data].map((i,o)=>r.type._parseAsync(new zt(a,i,a.path,o)))).then(i=>We.mergeArray(n,i));const s=[...a.data].map((i,o)=>r.type._parseSync(new zt(a,i,a.path,o)));return We.mergeArray(n,s)}get element(){return this._def.type}min(e,a){return new gt({...this._def,minLength:{value:e,message:Q.toString(a)}})}max(e,a){return new gt({...this._def,maxLength:{value:e,message:Q.toString(a)}})}length(e,a){return new gt({...this._def,exactLength:{value:e,message:Q.toString(a)}})}nonempty(e){return this.min(1,e)}}gt.create=(t,e)=>new gt({type:t,minLength:null,maxLength:null,exactLength:null,typeName:re.ZodArray,...de(e)});function ya(t){if(t instanceof Ne){const e={};for(const a in t.shape){const n=t.shape[a];e[a]=jt.create(ya(n))}return new Ne({...t._def,shape:()=>e})}else return t instanceof gt?new gt({...t._def,type:ya(t.element)}):t instanceof jt?jt.create(ya(t.unwrap())):t instanceof Jt?Jt.create(ya(t.unwrap())):t instanceof Et?Et.create(t.items.map(e=>ya(e))):t}class Ne extends he{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),a=ve.objectKeys(e);return this._cached={shape:e,keys:a}}_parse(e){if(this._getType(e)!==H.object){const c=this._getOrReturnCtx(e);return D(c,{code:R.invalid_type,expected:H.object,received:c.parsedType}),se}const{status:n,ctx:r}=this._processInputParams(e),{shape:s,keys:i}=this._getCached(),o=[];if(!(this._def.catchall instanceof Pt&&this._def.unknownKeys==="strip"))for(const c in r.data)i.includes(c)||o.push(c);const l=[];for(const c of i){const d=s[c],m=r.data[c];l.push({key:{status:"valid",value:c},value:d._parse(new zt(r,m,r.path,c)),alwaysSet:c in r.data})}if(this._def.catchall instanceof Pt){const c=this._def.unknownKeys;if(c==="passthrough")for(const d of o)l.push({key:{status:"valid",value:d},value:{status:"valid",value:r.data[d]}});else if(c==="strict")o.length>0&&(D(r,{code:R.unrecognized_keys,keys:o}),n.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const d of o){const m=r.data[d];l.push({key:{status:"valid",value:d},value:c._parse(new zt(r,m,r.path,d)),alwaysSet:d in r.data})}}return r.common.async?Promise.resolve().then(async()=>{const c=[];for(const d of l){const m=await d.key,u=await d.value;c.push({key:m,value:u,alwaysSet:d.alwaysSet})}return c}).then(c=>We.mergeObjectSync(n,c)):We.mergeObjectSync(n,l)}get shape(){return this._def.shape()}strict(e){return Q.errToObj,new Ne({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(a,n)=>{var r,s,i,o;const l=(i=(s=(r=this._def).errorMap)===null||s===void 0?void 0:s.call(r,a,n).message)!==null&&i!==void 0?i:n.defaultError;return a.code==="unrecognized_keys"?{message:(o=Q.errToObj(e).message)!==null&&o!==void 0?o:l}:{message:l}}}:{}})}strip(){return new Ne({...this._def,unknownKeys:"strip"})}passthrough(){return new Ne({...this._def,unknownKeys:"passthrough"})}extend(e){return new Ne({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new Ne({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:re.ZodObject})}setKey(e,a){return this.augment({[e]:a})}catchall(e){return new Ne({...this._def,catchall:e})}pick(e){const a={};return ve.objectKeys(e).forEach(n=>{e[n]&&this.shape[n]&&(a[n]=this.shape[n])}),new Ne({...this._def,shape:()=>a})}omit(e){const a={};return ve.objectKeys(this.shape).forEach(n=>{e[n]||(a[n]=this.shape[n])}),new Ne({...this._def,shape:()=>a})}deepPartial(){return ya(this)}partial(e){const a={};return ve.objectKeys(this.shape).forEach(n=>{const r=this.shape[n];e&&!e[n]?a[n]=r:a[n]=r.optional()}),new Ne({...this._def,shape:()=>a})}required(e){const a={};return ve.objectKeys(this.shape).forEach(n=>{if(e&&!e[n])a[n]=this.shape[n];else{let s=this.shape[n];for(;s instanceof jt;)s=s._def.innerType;a[n]=s}}),new Ne({...this._def,shape:()=>a})}keyof(){return _o(ve.objectKeys(this.shape))}}Ne.create=(t,e)=>new Ne({shape:()=>t,unknownKeys:"strip",catchall:Pt.create(),typeName:re.ZodObject,...de(e)});Ne.strictCreate=(t,e)=>new Ne({shape:()=>t,unknownKeys:"strict",catchall:Pt.create(),typeName:re.ZodObject,...de(e)});Ne.lazycreate=(t,e)=>new Ne({shape:t,unknownKeys:"strip",catchall:Pt.create(),typeName:re.ZodObject,...de(e)});class rn extends he{_parse(e){const{ctx:a}=this._processInputParams(e),n=this._def.options;function r(s){for(const o of s)if(o.result.status==="valid")return o.result;for(const o of s)if(o.result.status==="dirty")return a.common.issues.push(...o.ctx.common.issues),o.result;const i=s.map(o=>new it(o.ctx.common.issues));return D(a,{code:R.invalid_union,unionErrors:i}),se}if(a.common.async)return Promise.all(n.map(async s=>{const i={...a,common:{...a.common,issues:[]},parent:null};return{result:await s._parseAsync({data:a.data,path:a.path,parent:i}),ctx:i}})).then(r);{let s;const i=[];for(const l of n){const c={...a,common:{...a.common,issues:[]},parent:null},d=l._parseSync({data:a.data,path:a.path,parent:c});if(d.status==="valid")return d;d.status==="dirty"&&!s&&(s={result:d,ctx:c}),c.common.issues.length&&i.push(c.common.issues)}if(s)return a.common.issues.push(...s.ctx.common.issues),s.result;const o=i.map(l=>new it(l));return D(a,{code:R.invalid_union,unionErrors:o}),se}}get options(){return this._def.options}}rn.create=(t,e)=>new rn({options:t,typeName:re.ZodUnion,...de(e)});const Nt=t=>t instanceof ln?Nt(t.schema):t instanceof kt?Nt(t.innerType()):t instanceof cn?[t.value]:t instanceof Yt?t.options:t instanceof un?ve.objectValues(t.enum):t instanceof dn?Nt(t._def.innerType):t instanceof an?[void 0]:t instanceof nn?[null]:t instanceof jt?[void 0,...Nt(t.unwrap())]:t instanceof Jt?[null,...Nt(t.unwrap())]:t instanceof zs||t instanceof hn?Nt(t.unwrap()):t instanceof mn?Nt(t._def.innerType):[];class $r extends he{_parse(e){const{ctx:a}=this._processInputParams(e);if(a.parsedType!==H.object)return D(a,{code:R.invalid_type,expected:H.object,received:a.parsedType}),se;const n=this.discriminator,r=a.data[n],s=this.optionsMap.get(r);return s?a.common.async?s._parseAsync({data:a.data,path:a.path,parent:a}):s._parseSync({data:a.data,path:a.path,parent:a}):(D(a,{code:R.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),se)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,a,n){const r=new Map;for(const s of a){const i=Nt(s.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const o of i){if(r.has(o))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(o)}`);r.set(o,s)}}return new $r({typeName:re.ZodDiscriminatedUnion,discriminator:e,options:a,optionsMap:r,...de(n)})}}function vs(t,e){const a=Bt(t),n=Bt(e);if(t===e)return{valid:!0,data:t};if(a===H.object&&n===H.object){const r=ve.objectKeys(e),s=ve.objectKeys(t).filter(o=>r.indexOf(o)!==-1),i={...t,...e};for(const o of s){const l=vs(t[o],e[o]);if(!l.valid)return{valid:!1};i[o]=l.data}return{valid:!0,data:i}}else if(a===H.array&&n===H.array){if(t.length!==e.length)return{valid:!1};const r=[];for(let s=0;s<t.length;s++){const i=t[s],o=e[s],l=vs(i,o);if(!l.valid)return{valid:!1};r.push(l.data)}return{valid:!0,data:r}}else return a===H.date&&n===H.date&&+t==+e?{valid:!0,data:t}:{valid:!1}}class sn extends he{_parse(e){const{status:a,ctx:n}=this._processInputParams(e),r=(s,i)=>{if(ps(s)||ps(i))return se;const o=vs(s.value,i.value);return o.valid?((gs(s)||gs(i))&&a.dirty(),{status:a.value,value:o.data}):(D(n,{code:R.invalid_intersection_types}),se)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([s,i])=>r(s,i)):r(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}sn.create=(t,e,a)=>new sn({left:t,right:e,typeName:re.ZodIntersection,...de(a)});class Et extends he{_parse(e){const{status:a,ctx:n}=this._processInputParams(e);if(n.parsedType!==H.array)return D(n,{code:R.invalid_type,expected:H.array,received:n.parsedType}),se;if(n.data.length<this._def.items.length)return D(n,{code:R.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),se;!this._def.rest&&n.data.length>this._def.items.length&&(D(n,{code:R.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),a.dirty());const s=[...n.data].map((i,o)=>{const l=this._def.items[o]||this._def.rest;return l?l._parse(new zt(n,i,n.path,o)):null}).filter(i=>!!i);return n.common.async?Promise.all(s).then(i=>We.mergeArray(a,i)):We.mergeArray(a,s)}get items(){return this._def.items}rest(e){return new Et({...this._def,rest:e})}}Et.create=(t,e)=>{if(!Array.isArray(t))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Et({items:t,typeName:re.ZodTuple,rest:null,...de(e)})};class on extends he{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:a,ctx:n}=this._processInputParams(e);if(n.parsedType!==H.object)return D(n,{code:R.invalid_type,expected:H.object,received:n.parsedType}),se;const r=[],s=this._def.keyType,i=this._def.valueType;for(const o in n.data)r.push({key:s._parse(new zt(n,o,n.path,o)),value:i._parse(new zt(n,n.data[o],n.path,o)),alwaysSet:o in n.data});return n.common.async?We.mergeObjectAsync(a,r):We.mergeObjectSync(a,r)}get element(){return this._def.valueType}static create(e,a,n){return a instanceof he?new on({keyType:e,valueType:a,typeName:re.ZodRecord,...de(n)}):new on({keyType:pt.create(),valueType:e,typeName:re.ZodRecord,...de(a)})}}class rr extends he{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:a,ctx:n}=this._processInputParams(e);if(n.parsedType!==H.map)return D(n,{code:R.invalid_type,expected:H.map,received:n.parsedType}),se;const r=this._def.keyType,s=this._def.valueType,i=[...n.data.entries()].map(([o,l],c)=>({key:r._parse(new zt(n,o,n.path,[c,"key"])),value:s._parse(new zt(n,l,n.path,[c,"value"]))}));if(n.common.async){const o=new Map;return Promise.resolve().then(async()=>{for(const l of i){const c=await l.key,d=await l.value;if(c.status==="aborted"||d.status==="aborted")return se;(c.status==="dirty"||d.status==="dirty")&&a.dirty(),o.set(c.value,d.value)}return{status:a.value,value:o}})}else{const o=new Map;for(const l of i){const c=l.key,d=l.value;if(c.status==="aborted"||d.status==="aborted")return se;(c.status==="dirty"||d.status==="dirty")&&a.dirty(),o.set(c.value,d.value)}return{status:a.value,value:o}}}}rr.create=(t,e,a)=>new rr({valueType:e,keyType:t,typeName:re.ZodMap,...de(a)});class la extends he{_parse(e){const{status:a,ctx:n}=this._processInputParams(e);if(n.parsedType!==H.set)return D(n,{code:R.invalid_type,expected:H.set,received:n.parsedType}),se;const r=this._def;r.minSize!==null&&n.data.size<r.minSize.value&&(D(n,{code:R.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),a.dirty()),r.maxSize!==null&&n.data.size>r.maxSize.value&&(D(n,{code:R.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),a.dirty());const s=this._def.valueType;function i(l){const c=new Set;for(const d of l){if(d.status==="aborted")return se;d.status==="dirty"&&a.dirty(),c.add(d.value)}return{status:a.value,value:c}}const o=[...n.data.values()].map((l,c)=>s._parse(new zt(n,l,n.path,c)));return n.common.async?Promise.all(o).then(l=>i(l)):i(o)}min(e,a){return new la({...this._def,minSize:{value:e,message:Q.toString(a)}})}max(e,a){return new la({...this._def,maxSize:{value:e,message:Q.toString(a)}})}size(e,a){return this.min(e,a).max(e,a)}nonempty(e){return this.min(1,e)}}la.create=(t,e)=>new la({valueType:t,minSize:null,maxSize:null,typeName:re.ZodSet,...de(e)});class _a extends he{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:a}=this._processInputParams(e);if(a.parsedType!==H.function)return D(a,{code:R.invalid_type,expected:H.function,received:a.parsedType}),se;function n(o,l){return Xn({data:o,path:a.path,errorMaps:[a.common.contextualErrorMap,a.schemaErrorMap,Qn(),Oa].filter(c=>!!c),issueData:{code:R.invalid_arguments,argumentsError:l}})}function r(o,l){return Xn({data:o,path:a.path,errorMaps:[a.common.contextualErrorMap,a.schemaErrorMap,Qn(),Oa].filter(c=>!!c),issueData:{code:R.invalid_return_type,returnTypeError:l}})}const s={errorMap:a.common.contextualErrorMap},i=a.data;if(this._def.returns instanceof La){const o=this;return Ye(async function(...l){const c=new it([]),d=await o._def.args.parseAsync(l,s).catch(p=>{throw c.addIssue(n(l,p)),c}),m=await Reflect.apply(i,this,d);return await o._def.returns._def.type.parseAsync(m,s).catch(p=>{throw c.addIssue(r(m,p)),c})})}else{const o=this;return Ye(function(...l){const c=o._def.args.safeParse(l,s);if(!c.success)throw new it([n(l,c.error)]);const d=Reflect.apply(i,this,c.data),m=o._def.returns.safeParse(d,s);if(!m.success)throw new it([r(d,m.error)]);return m.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new _a({...this._def,args:Et.create(e).rest(ra.create())})}returns(e){return new _a({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,a,n){return new _a({args:e||Et.create([]).rest(ra.create()),returns:a||ra.create(),typeName:re.ZodFunction,...de(n)})}}class ln extends he{get schema(){return this._def.getter()}_parse(e){const{ctx:a}=this._processInputParams(e);return this._def.getter()._parse({data:a.data,path:a.path,parent:a})}}ln.create=(t,e)=>new ln({getter:t,typeName:re.ZodLazy,...de(e)});class cn extends he{_parse(e){if(e.data!==this._def.value){const a=this._getOrReturnCtx(e);return D(a,{received:a.data,code:R.invalid_literal,expected:this._def.value}),se}return{status:"valid",value:e.data}}get value(){return this._def.value}}cn.create=(t,e)=>new cn({value:t,typeName:re.ZodLiteral,...de(e)});function _o(t,e){return new Yt({values:t,typeName:re.ZodEnum,...de(e)})}class Yt extends he{constructor(){super(...arguments),Ha.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const a=this._getOrReturnCtx(e),n=this._def.values;return D(a,{expected:ve.joinValues(n),received:a.parsedType,code:R.invalid_type}),se}if(tr(this,Ha,"f")||ko(this,Ha,new Set(this._def.values),"f"),!tr(this,Ha,"f").has(e.data)){const a=this._getOrReturnCtx(e),n=this._def.values;return D(a,{received:a.data,code:R.invalid_enum_value,options:n}),se}return Ye(e.data)}get options(){return this._def.values}get enum(){const e={};for(const a of this._def.values)e[a]=a;return e}get Values(){const e={};for(const a of this._def.values)e[a]=a;return e}get Enum(){const e={};for(const a of this._def.values)e[a]=a;return e}extract(e,a=this._def){return Yt.create(e,{...this._def,...a})}exclude(e,a=this._def){return Yt.create(this.options.filter(n=>!e.includes(n)),{...this._def,...a})}}Ha=new WeakMap;Yt.create=_o;class un extends he{constructor(){super(...arguments),Wa.set(this,void 0)}_parse(e){const a=ve.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==H.string&&n.parsedType!==H.number){const r=ve.objectValues(a);return D(n,{expected:ve.joinValues(r),received:n.parsedType,code:R.invalid_type}),se}if(tr(this,Wa,"f")||ko(this,Wa,new Set(ve.getValidEnumValues(this._def.values)),"f"),!tr(this,Wa,"f").has(e.data)){const r=ve.objectValues(a);return D(n,{received:n.data,code:R.invalid_enum_value,options:r}),se}return Ye(e.data)}get enum(){return this._def.values}}Wa=new WeakMap;un.create=(t,e)=>new un({values:t,typeName:re.ZodNativeEnum,...de(e)});class La extends he{unwrap(){return this._def.type}_parse(e){const{ctx:a}=this._processInputParams(e);if(a.parsedType!==H.promise&&a.common.async===!1)return D(a,{code:R.invalid_type,expected:H.promise,received:a.parsedType}),se;const n=a.parsedType===H.promise?a.data:Promise.resolve(a.data);return Ye(n.then(r=>this._def.type.parseAsync(r,{path:a.path,errorMap:a.common.contextualErrorMap})))}}La.create=(t,e)=>new La({type:t,typeName:re.ZodPromise,...de(e)});class kt extends he{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===re.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:a,ctx:n}=this._processInputParams(e),r=this._def.effect||null,s={addIssue:i=>{D(n,i),i.fatal?a.abort():a.dirty()},get path(){return n.path}};if(s.addIssue=s.addIssue.bind(s),r.type==="preprocess"){const i=r.transform(n.data,s);if(n.common.async)return Promise.resolve(i).then(async o=>{if(a.value==="aborted")return se;const l=await this._def.schema._parseAsync({data:o,path:n.path,parent:n});return l.status==="aborted"?se:l.status==="dirty"||a.value==="dirty"?ka(l.value):l});{if(a.value==="aborted")return se;const o=this._def.schema._parseSync({data:i,path:n.path,parent:n});return o.status==="aborted"?se:o.status==="dirty"||a.value==="dirty"?ka(o.value):o}}if(r.type==="refinement"){const i=o=>{const l=r.refinement(o,s);if(n.common.async)return Promise.resolve(l);if(l instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(n.common.async===!1){const o=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return o.status==="aborted"?se:(o.status==="dirty"&&a.dirty(),i(o.value),{status:a.value,value:o.value})}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(o=>o.status==="aborted"?se:(o.status==="dirty"&&a.dirty(),i(o.value).then(()=>({status:a.value,value:o.value}))))}if(r.type==="transform")if(n.common.async===!1){const i=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!en(i))return i;const o=r.transform(i.value,s);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:a.value,value:o}}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(i=>en(i)?Promise.resolve(r.transform(i.value,s)).then(o=>({status:a.value,value:o})):i);ve.assertNever(r)}}kt.create=(t,e,a)=>new kt({schema:t,typeName:re.ZodEffects,effect:e,...de(a)});kt.createWithPreprocess=(t,e,a)=>new kt({schema:e,effect:{type:"preprocess",transform:t},typeName:re.ZodEffects,...de(a)});class jt extends he{_parse(e){return this._getType(e)===H.undefined?Ye(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}jt.create=(t,e)=>new jt({innerType:t,typeName:re.ZodOptional,...de(e)});class Jt extends he{_parse(e){return this._getType(e)===H.null?Ye(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Jt.create=(t,e)=>new Jt({innerType:t,typeName:re.ZodNullable,...de(e)});class dn extends he{_parse(e){const{ctx:a}=this._processInputParams(e);let n=a.data;return a.parsedType===H.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:a.path,parent:a})}removeDefault(){return this._def.innerType}}dn.create=(t,e)=>new dn({innerType:t,typeName:re.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...de(e)});class mn extends he{_parse(e){const{ctx:a}=this._processInputParams(e),n={...a,common:{...a.common,issues:[]}},r=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return er(r)?r.then(s=>({status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new it(n.common.issues)},input:n.data})})):{status:"valid",value:r.status==="valid"?r.value:this._def.catchValue({get error(){return new it(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}mn.create=(t,e)=>new mn({innerType:t,typeName:re.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...de(e)});class sr extends he{_parse(e){if(this._getType(e)!==H.nan){const n=this._getOrReturnCtx(e);return D(n,{code:R.invalid_type,expected:H.nan,received:n.parsedType}),se}return{status:"valid",value:e.data}}}sr.create=t=>new sr({typeName:re.ZodNaN,...de(t)});const Rf=Symbol("zod_brand");class zs extends he{_parse(e){const{ctx:a}=this._processInputParams(e),n=a.data;return this._def.type._parse({data:n,path:a.path,parent:a})}unwrap(){return this._def.type}}class zn extends he{_parse(e){const{status:a,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const s=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return s.status==="aborted"?se:s.status==="dirty"?(a.dirty(),ka(s.value)):this._def.out._parseAsync({data:s.value,path:n.path,parent:n})})();{const r=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?se:r.status==="dirty"?(a.dirty(),{status:"dirty",value:r.value}):this._def.out._parseSync({data:r.value,path:n.path,parent:n})}}static create(e,a){return new zn({in:e,out:a,typeName:re.ZodPipeline})}}class hn extends he{_parse(e){const a=this._def.innerType._parse(e);return en(a)&&(a.value=Object.freeze(a.value)),a}unwrap(){return this._def.innerType}}hn.create=(t,e)=>new hn({innerType:t,typeName:re.ZodReadonly,...de(e)});function jo(t,e={},a){return t?Ra.create().superRefine((n,r)=>{var s,i;if(!t(n)){const o=typeof e=="function"?e(n):typeof e=="string"?{message:e}:e,l=(i=(s=o.fatal)!==null&&s!==void 0?s:a)!==null&&i!==void 0?i:!0,c=typeof o=="string"?{message:o}:o;r.addIssue({code:"custom",...c,fatal:l})}}):Ra.create()}const Lf={object:Ne.lazycreate};var re;(function(t){t.ZodString="ZodString",t.ZodNumber="ZodNumber",t.ZodNaN="ZodNaN",t.ZodBigInt="ZodBigInt",t.ZodBoolean="ZodBoolean",t.ZodDate="ZodDate",t.ZodSymbol="ZodSymbol",t.ZodUndefined="ZodUndefined",t.ZodNull="ZodNull",t.ZodAny="ZodAny",t.ZodUnknown="ZodUnknown",t.ZodNever="ZodNever",t.ZodVoid="ZodVoid",t.ZodArray="ZodArray",t.ZodObject="ZodObject",t.ZodUnion="ZodUnion",t.ZodDiscriminatedUnion="ZodDiscriminatedUnion",t.ZodIntersection="ZodIntersection",t.ZodTuple="ZodTuple",t.ZodRecord="ZodRecord",t.ZodMap="ZodMap",t.ZodSet="ZodSet",t.ZodFunction="ZodFunction",t.ZodLazy="ZodLazy",t.ZodLiteral="ZodLiteral",t.ZodEnum="ZodEnum",t.ZodEffects="ZodEffects",t.ZodNativeEnum="ZodNativeEnum",t.ZodOptional="ZodOptional",t.ZodNullable="ZodNullable",t.ZodDefault="ZodDefault",t.ZodCatch="ZodCatch",t.ZodPromise="ZodPromise",t.ZodBranded="ZodBranded",t.ZodPipeline="ZodPipeline",t.ZodReadonly="ZodReadonly"})(re||(re={}));const Nf=(t,e={message:`Input not instance of ${t.name}`})=>jo(a=>a instanceof t,e),So=pt.create,zo=Zt.create,Mf=sr.create,If=Gt.create,Eo=tn.create,Ff=oa.create,Pf=ar.create,$f=an.create,Vf=nn.create,Df=Ra.create,qf=ra.create,Bf=Pt.create,Uf=nr.create,Hf=gt.create,Wf=Ne.create,Kf=Ne.strictCreate,Zf=rn.create,Gf=$r.create,Yf=sn.create,Jf=Et.create,Qf=on.create,Xf=rr.create,ep=la.create,tp=_a.create,ap=ln.create,np=cn.create,rp=Yt.create,sp=un.create,ip=La.create,hi=kt.create,op=jt.create,lp=Jt.create,cp=kt.createWithPreprocess,up=zn.create,dp=()=>So().optional(),mp=()=>zo().optional(),hp=()=>Eo().optional(),fp={string:t=>pt.create({...t,coerce:!0}),number:t=>Zt.create({...t,coerce:!0}),boolean:t=>tn.create({...t,coerce:!0}),bigint:t=>Gt.create({...t,coerce:!0}),date:t=>oa.create({...t,coerce:!0})},pp=se;var $v=Object.freeze({__proto__:null,defaultErrorMap:Oa,setErrorMap:pf,getErrorMap:Qn,makeIssue:Xn,EMPTY_PATH:gf,addIssueToContext:D,ParseStatus:We,INVALID:se,DIRTY:ka,OK:Ye,isAborted:ps,isDirty:gs,isValid:en,isAsync:er,get util(){return ve},get objectUtil(){return fs},ZodParsedType:H,getParsedType:Bt,ZodType:he,datetimeRegex:xo,ZodString:pt,ZodNumber:Zt,ZodBigInt:Gt,ZodBoolean:tn,ZodDate:oa,ZodSymbol:ar,ZodUndefined:an,ZodNull:nn,ZodAny:Ra,ZodUnknown:ra,ZodNever:Pt,ZodVoid:nr,ZodArray:gt,ZodObject:Ne,ZodUnion:rn,ZodDiscriminatedUnion:$r,ZodIntersection:sn,ZodTuple:Et,ZodRecord:on,ZodMap:rr,ZodSet:la,ZodFunction:_a,ZodLazy:ln,ZodLiteral:cn,ZodEnum:Yt,ZodNativeEnum:un,ZodPromise:La,ZodEffects:kt,ZodTransformer:kt,ZodOptional:jt,ZodNullable:Jt,ZodDefault:dn,ZodCatch:mn,ZodNaN:sr,BRAND:Rf,ZodBranded:zs,ZodPipeline:zn,ZodReadonly:hn,custom:jo,Schema:he,ZodSchema:he,late:Lf,get ZodFirstPartyTypeKind(){return re},coerce:fp,any:Df,array:Hf,bigint:If,boolean:Eo,date:Ff,discriminatedUnion:Gf,effect:hi,enum:rp,function:tp,instanceof:Nf,intersection:Yf,lazy:ap,literal:np,map:Xf,nan:Mf,nativeEnum:sp,never:Bf,null:Vf,nullable:lp,number:zo,object:Wf,oboolean:hp,onumber:mp,optional:op,ostring:dp,pipeline:up,preprocess:cp,promise:ip,record:Qf,set:ep,strictObject:Kf,string:So,symbol:Pf,transformer:hi,tuple:Jf,undefined:$f,union:Zf,unknown:qf,void:Uf,NEVER:pp,ZodIssueCode:R,quotelessJson:ff,ZodError:it}),En=t=>t.type==="checkbox",ba=t=>t instanceof Date,Ge=t=>t==null;const Co=t=>typeof t=="object";var Ve=t=>!Ge(t)&&!Array.isArray(t)&&Co(t)&&!ba(t),To=t=>Ve(t)&&t.target?En(t.target)?t.target.checked:t.target.value:t,gp=t=>t.substring(0,t.search(/\.\d+(\.|$)/))||t,Ao=(t,e)=>t.has(gp(e)),vp=t=>{const e=t.constructor&&t.constructor.prototype;return Ve(e)&&e.hasOwnProperty("isPrototypeOf")},Es=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function Xe(t){let e;const a=Array.isArray(t);if(t instanceof Date)e=new Date(t);else if(t instanceof Set)e=new Set(t);else if(!(Es&&(t instanceof Blob||t instanceof FileList))&&(a||Ve(t)))if(e=a?[]:{},!a&&!vp(t))e=t;else for(const n in t)t.hasOwnProperty(n)&&(e[n]=Xe(t[n]));else return t;return e}var Cn=t=>Array.isArray(t)?t.filter(Boolean):[],Fe=t=>t===void 0,P=(t,e,a)=>{if(!e||!Ve(t))return a;const n=Cn(e.split(/[,[\].]+?/)).reduce((r,s)=>Ge(r)?r:r[s],t);return Fe(n)||n===t?Fe(t[e])?a:t[e]:n},mt=t=>typeof t=="boolean";const ir={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},ht={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},Lt={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},Oo=W.createContext(null),Cs=()=>W.useContext(Oo),Vv=t=>{const{children:e,...a}=t;return W.createElement(Oo.Provider,{value:a},e)};var Ro=(t,e,a,n=!0)=>{const r={defaultValues:e._defaultValues};for(const s in t)Object.defineProperty(r,s,{get:()=>{const i=s;return e._proxyFormState[i]!==ht.all&&(e._proxyFormState[i]=!n||ht.all),a&&(a[i]=!0),t[i]}});return r},ut=t=>Ve(t)&&!Object.keys(t).length,Lo=(t,e,a,n)=>{a(t);const{name:r,...s}=t;return ut(s)||Object.keys(s).length>=Object.keys(e).length||Object.keys(s).find(i=>e[i]===(!n||ht.all))},qn=t=>Array.isArray(t)?t:[t],No=(t,e,a)=>!t||!e||t===e||qn(t).some(n=>n&&(a?n===e:n.startsWith(e)||e.startsWith(n)));function Ts(t){const e=W.useRef(t);e.current=t,W.useEffect(()=>{const a=!t.disabled&&e.current.subject&&e.current.subject.subscribe({next:e.current.next});return()=>{a&&a.unsubscribe()}},[t.disabled])}function yp(t){const e=Cs(),{control:a=e.control,disabled:n,name:r,exact:s}=t||{},[i,o]=W.useState(a._formState),l=W.useRef(!0),c=W.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1}),d=W.useRef(r);return d.current=r,Ts({disabled:n,next:m=>l.current&&No(d.current,m.name,s)&&Lo(m,c.current,a._updateFormState)&&o({...a._formState,...m}),subject:a._subjects.state}),W.useEffect(()=>(l.current=!0,c.current.isValid&&a._updateValid(!0),()=>{l.current=!1}),[a]),Ro(i,a,c.current,!1)}var xt=t=>typeof t=="string",Mo=(t,e,a,n,r)=>xt(t)?(n&&e.watch.add(t),P(a,t,r)):Array.isArray(t)?t.map(s=>(n&&e.watch.add(s),P(a,s))):(n&&(e.watchAll=!0),a);function kp(t){const e=Cs(),{control:a=e.control,name:n,defaultValue:r,disabled:s,exact:i}=t||{},o=W.useRef(n);o.current=n,Ts({disabled:s,subject:a._subjects.values,next:d=>{No(o.current,d.name,i)&&c(Xe(Mo(o.current,a._names,d.values||a._formValues,!1,r)))}});const[l,c]=W.useState(a._getWatch(n,r));return W.useEffect(()=>a._removeUnmounted()),l}var As=t=>/^\w*$/.test(t),Io=t=>Cn(t.replace(/["|']|\]/g,"").split(/\.|\[/)),xe=(t,e,a)=>{let n=-1;const r=As(e)?[e]:Io(e),s=r.length,i=s-1;for(;++n<s;){const o=r[n];let l=a;if(n!==i){const c=t[o];l=Ve(c)||Array.isArray(c)?c:isNaN(+r[n+1])?{}:[]}t[o]=l,t=t[o]}return t};function bp(t){const e=Cs(),{name:a,disabled:n,control:r=e.control,shouldUnregister:s}=t,i=Ao(r._names.array,a),o=kp({control:r,name:a,defaultValue:P(r._formValues,a,P(r._defaultValues,a,t.defaultValue)),exact:!0}),l=yp({control:r,name:a}),c=W.useRef(r.register(a,{...t.rules,value:o,...mt(t.disabled)?{disabled:t.disabled}:{}}));return W.useEffect(()=>{const d=r._options.shouldUnregister||s,m=(u,p)=>{const g=P(r._fields,u);g&&(g._f.mount=p)};if(m(a,!0),d){const u=Xe(P(r._options.defaultValues,a));xe(r._defaultValues,a,u),Fe(P(r._formValues,a))&&xe(r._formValues,a,u)}return()=>{(i?d&&!r._state.action:d)?r.unregister(a):m(a,!1)}},[a,r,i,s]),W.useEffect(()=>{P(r._fields,a)&&r._updateDisabledField({disabled:n,fields:r._fields,name:a,value:P(r._fields,a)._f.value})},[n,a,r]),{field:{name:a,value:o,...mt(n)||l.disabled?{disabled:l.disabled||n}:{},onChange:W.useCallback(d=>c.current.onChange({target:{value:To(d),name:a},type:ir.CHANGE}),[a]),onBlur:W.useCallback(()=>c.current.onBlur({target:{value:P(r._formValues,a),name:a},type:ir.BLUR}),[a,r]),ref:d=>{const m=P(r._fields,a);m&&d&&(m._f.ref={focus:()=>d.focus(),select:()=>d.select(),setCustomValidity:u=>d.setCustomValidity(u),reportValidity:()=>d.reportValidity()})}},formState:l,fieldState:Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!P(l.errors,a)},isDirty:{enumerable:!0,get:()=>!!P(l.dirtyFields,a)},isTouched:{enumerable:!0,get:()=>!!P(l.touchedFields,a)},isValidating:{enumerable:!0,get:()=>!!P(l.validatingFields,a)},error:{enumerable:!0,get:()=>P(l.errors,a)}})}}const Dv=t=>t.render(bp(t));var Fo=(t,e,a,n,r)=>e?{...a[t],types:{...a[t]&&a[t].types?a[t].types:{},[n]:r||!0}}:{},fi=t=>({isOnSubmit:!t||t===ht.onSubmit,isOnBlur:t===ht.onBlur,isOnChange:t===ht.onChange,isOnAll:t===ht.all,isOnTouch:t===ht.onTouched}),pi=(t,e,a)=>!a&&(e.watchAll||e.watch.has(t)||[...e.watch].some(n=>t.startsWith(n)&&/^\.\w+/.test(t.slice(n.length))));const Ga=(t,e,a,n)=>{for(const r of a||Object.keys(t)){const s=P(t,r);if(s){const{_f:i,...o}=s;if(i){if(i.refs&&i.refs[0]&&e(i.refs[0],r)&&!n)break;if(i.ref&&e(i.ref,i.name)&&!n)break;Ga(o,e)}else Ve(o)&&Ga(o,e)}}};var wp=(t,e,a)=>{const n=Cn(P(t,a));return xe(n,"root",e[a]),xe(t,a,n),t},Os=t=>t.type==="file",Ht=t=>typeof t=="function",or=t=>{if(!Es)return!1;const e=t?t.ownerDocument:0;return t instanceof(e&&e.defaultView?e.defaultView.HTMLElement:HTMLElement)},Bn=t=>xt(t),Rs=t=>t.type==="radio",lr=t=>t instanceof RegExp;const gi={value:!1,isValid:!1},vi={value:!0,isValid:!0};var Po=t=>{if(Array.isArray(t)){if(t.length>1){const e=t.filter(a=>a&&a.checked&&!a.disabled).map(a=>a.value);return{value:e,isValid:!!e.length}}return t[0].checked&&!t[0].disabled?t[0].attributes&&!Fe(t[0].attributes.value)?Fe(t[0].value)||t[0].value===""?vi:{value:t[0].value,isValid:!0}:vi:gi}return gi};const yi={isValid:!1,value:null};var $o=t=>Array.isArray(t)?t.reduce((e,a)=>a&&a.checked&&!a.disabled?{isValid:!0,value:a.value}:e,yi):yi;function ki(t,e,a="validate"){if(Bn(t)||Array.isArray(t)&&t.every(Bn)||mt(t)&&!t)return{type:a,message:Bn(t)?t:"",ref:e}}var va=t=>Ve(t)&&!lr(t)?t:{value:t,message:""},bi=async(t,e,a,n,r)=>{const{ref:s,refs:i,required:o,maxLength:l,minLength:c,min:d,max:m,pattern:u,validate:p,name:g,valueAsNumber:_,mount:S,disabled:w}=t._f,j=P(e,g);if(!S||w)return{};const z=i?i[0]:s,T=N=>{n&&z.reportValidity&&(z.setCustomValidity(mt(N)?"":N||""),z.reportValidity())},E={},M=Rs(s),V=En(s),O=M||V,k=(_||Os(s))&&Fe(s.value)&&Fe(j)||or(s)&&s.value===""||j===""||Array.isArray(j)&&!j.length,Z=Fo.bind(null,g,a,E),Y=(N,L,F,X=Lt.maxLength,ee=Lt.minLength)=>{const I=N?L:F;E[g]={type:N?X:ee,message:I,ref:s,...Z(N?X:ee,I)}};if(r?!Array.isArray(j)||!j.length:o&&(!O&&(k||Ge(j))||mt(j)&&!j||V&&!Po(i).isValid||M&&!$o(i).isValid)){const{value:N,message:L}=Bn(o)?{value:!!o,message:o}:va(o);if(N&&(E[g]={type:Lt.required,message:L,ref:z,...Z(Lt.required,L)},!a))return T(L),E}if(!k&&(!Ge(d)||!Ge(m))){let N,L;const F=va(m),X=va(d);if(!Ge(j)&&!isNaN(j)){const ee=s.valueAsNumber||j&&+j;Ge(F.value)||(N=ee>F.value),Ge(X.value)||(L=ee<X.value)}else{const ee=s.valueAsDate||new Date(j),I=U=>new Date(new Date().toDateString()+" "+U),G=s.type=="time",J=s.type=="week";xt(F.value)&&j&&(N=G?I(j)>I(F.value):J?j>F.value:ee>new Date(F.value)),xt(X.value)&&j&&(L=G?I(j)<I(X.value):J?j<X.value:ee<new Date(X.value))}if((N||L)&&(Y(!!N,F.message,X.message,Lt.max,Lt.min),!a))return T(E[g].message),E}if((l||c)&&!k&&(xt(j)||r&&Array.isArray(j))){const N=va(l),L=va(c),F=!Ge(N.value)&&j.length>+N.value,X=!Ge(L.value)&&j.length<+L.value;if((F||X)&&(Y(F,N.message,L.message),!a))return T(E[g].message),E}if(u&&!k&&xt(j)){const{value:N,message:L}=va(u);if(lr(N)&&!j.match(N)&&(E[g]={type:Lt.pattern,message:L,ref:s,...Z(Lt.pattern,L)},!a))return T(L),E}if(p){if(Ht(p)){const N=await p(j,e),L=ki(N,z);if(L&&(E[g]={...L,...Z(Lt.validate,L.message)},!a))return T(L.message),E}else if(Ve(p)){let N={};for(const L in p){if(!ut(N)&&!a)break;const F=ki(await p[L](j,e),z,L);F&&(N={...F,...Z(L,F.message)},T(F.message),a&&(E[g]=N))}if(!ut(N)&&(E[g]={ref:z,...N},!a))return E}}return T(!0),E};function xp(t,e){const a=e.slice(0,-1).length;let n=0;for(;n<a;)t=Fe(t)?n++:t[e[n++]];return t}function _p(t){for(const e in t)if(t.hasOwnProperty(e)&&!Fe(t[e]))return!1;return!0}function Be(t,e){const a=Array.isArray(e)?e:As(e)?[e]:Io(e),n=a.length===1?t:xp(t,a),r=a.length-1,s=a[r];return n&&delete n[s],r!==0&&(Ve(n)&&ut(n)||Array.isArray(n)&&_p(n))&&Be(t,a.slice(0,-1)),t}var Xr=()=>{let t=[];return{get observers(){return t},next:r=>{for(const s of t)s.next&&s.next(r)},subscribe:r=>(t.push(r),{unsubscribe:()=>{t=t.filter(s=>s!==r)}}),unsubscribe:()=>{t=[]}}},cr=t=>Ge(t)||!Co(t);function ea(t,e){if(cr(t)||cr(e))return t===e;if(ba(t)&&ba(e))return t.getTime()===e.getTime();const a=Object.keys(t),n=Object.keys(e);if(a.length!==n.length)return!1;for(const r of a){const s=t[r];if(!n.includes(r))return!1;if(r!=="ref"){const i=e[r];if(ba(s)&&ba(i)||Ve(s)&&Ve(i)||Array.isArray(s)&&Array.isArray(i)?!ea(s,i):s!==i)return!1}}return!0}var Vo=t=>t.type==="select-multiple",jp=t=>Rs(t)||En(t),es=t=>or(t)&&t.isConnected,Do=t=>{for(const e in t)if(Ht(t[e]))return!0;return!1};function ur(t,e={}){const a=Array.isArray(t);if(Ve(t)||a)for(const n in t)Array.isArray(t[n])||Ve(t[n])&&!Do(t[n])?(e[n]=Array.isArray(t[n])?[]:{},ur(t[n],e[n])):Ge(t[n])||(e[n]=!0);return e}function qo(t,e,a){const n=Array.isArray(t);if(Ve(t)||n)for(const r in t)Array.isArray(t[r])||Ve(t[r])&&!Do(t[r])?Fe(e)||cr(a[r])?a[r]=Array.isArray(t[r])?ur(t[r],[]):{...ur(t[r])}:qo(t[r],Ge(e)?{}:e[r],a[r]):a[r]=!ea(t[r],e[r]);return a}var Pn=(t,e)=>qo(t,e,ur(e)),Bo=(t,{valueAsNumber:e,valueAsDate:a,setValueAs:n})=>Fe(t)?t:e?t===""?NaN:t&&+t:a&&xt(t)?new Date(t):n?n(t):t;function ts(t){const e=t.ref;if(!(t.refs?t.refs.every(a=>a.disabled):e.disabled))return Os(e)?e.files:Rs(e)?$o(t.refs).value:Vo(e)?[...e.selectedOptions].map(({value:a})=>a):En(e)?Po(t.refs).value:Bo(Fe(e.value)?t.ref.value:e.value,t)}var Sp=(t,e,a,n)=>{const r={};for(const s of t){const i=P(e,s);i&&xe(r,s,i._f)}return{criteriaMode:a,names:[...t],fields:r,shouldUseNativeValidation:n}},Ua=t=>Fe(t)?t:lr(t)?t.source:Ve(t)?lr(t.value)?t.value.source:t.value:t,zp=t=>t.mount&&(t.required||t.min||t.max||t.maxLength||t.minLength||t.pattern||t.validate);function wi(t,e,a){const n=P(t,a);if(n||As(a))return{error:n,name:a};const r=a.split(".");for(;r.length;){const s=r.join("."),i=P(e,s),o=P(t,s);if(i&&!Array.isArray(i)&&a!==s)return{name:a};if(o&&o.type)return{name:s,error:o};r.pop()}return{name:a}}var Ep=(t,e,a,n,r)=>r.isOnAll?!1:!a&&r.isOnTouch?!(e||t):(a?n.isOnBlur:r.isOnBlur)?!t:(a?n.isOnChange:r.isOnChange)?t:!0,Cp=(t,e)=>!Cn(P(t,e)).length&&Be(t,e);const Tp={mode:ht.onSubmit,reValidateMode:ht.onChange,shouldFocusError:!0};function Ap(t={}){let e={...Tp,...t},a={submitCount:0,isDirty:!1,isLoading:Ht(e.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1},n={},r=Ve(e.defaultValues)||Ve(e.values)?Xe(e.defaultValues||e.values)||{}:{},s=e.shouldUnregister?{}:Xe(r),i={action:!1,mount:!1,watch:!1},o={mount:new Set,unMount:new Set,array:new Set,watch:new Set},l,c=0;const d={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},m={values:Xr(),array:Xr(),state:Xr()},u=fi(e.mode),p=fi(e.reValidateMode),g=e.criteriaMode===ht.all,_=f=>v=>{clearTimeout(c),c=setTimeout(f,v)},S=async f=>{if(d.isValid||f){const v=e.resolver?ut((await O()).errors):await Z(n,!0);v!==a.isValid&&m.state.next({isValid:v})}},w=(f,v)=>{(d.isValidating||d.validatingFields)&&((f||Array.from(o.mount)).forEach(y=>y&&xe(a.validatingFields,y,!!v)),a.isValidating=Object.values(a.validatingFields).some(y=>y),m.state.next({validatingFields:a.validatingFields,isValidating:a.isValidating}))},j=(f,v=[],y,C,A=!0,x=!0)=>{if(C&&y){if(i.action=!0,x&&Array.isArray(P(n,f))){const $=y(P(n,f),C.argA,C.argB);A&&xe(n,f,$)}if(x&&Array.isArray(P(a.errors,f))){const $=y(P(a.errors,f),C.argA,C.argB);A&&xe(a.errors,f,$),Cp(a.errors,f)}if(d.touchedFields&&x&&Array.isArray(P(a.touchedFields,f))){const $=y(P(a.touchedFields,f),C.argA,C.argB);A&&xe(a.touchedFields,f,$)}d.dirtyFields&&(a.dirtyFields=Pn(r,s)),m.state.next({name:f,isDirty:N(f,v),dirtyFields:a.dirtyFields,errors:a.errors,isValid:a.isValid})}else xe(s,f,v)},z=(f,v)=>{xe(a.errors,f,v),m.state.next({errors:a.errors})},T=f=>{a.errors=f,m.state.next({errors:a.errors,isValid:!1})},E=(f,v,y,C)=>{const A=P(n,f);if(A){const x=P(s,f,Fe(y)?P(r,f):y);Fe(x)||C&&C.defaultChecked||v?xe(s,f,v?x:ts(A._f)):X(f,x),i.mount&&S()}},M=(f,v,y,C,A)=>{let x=!1,$=!1;const te={name:f},le=!!(P(n,f)&&P(n,f)._f.disabled);if(!y||C){d.isDirty&&($=a.isDirty,a.isDirty=te.isDirty=N(),x=$!==te.isDirty);const me=le||ea(P(r,f),v);$=!!(!le&&P(a.dirtyFields,f)),me||le?Be(a.dirtyFields,f):xe(a.dirtyFields,f,!0),te.dirtyFields=a.dirtyFields,x=x||d.dirtyFields&&$!==!me}if(y){const me=P(a.touchedFields,f);me||(xe(a.touchedFields,f,y),te.touchedFields=a.touchedFields,x=x||d.touchedFields&&me!==y)}return x&&A&&m.state.next(te),x?te:{}},V=(f,v,y,C)=>{const A=P(a.errors,f),x=d.isValid&&mt(v)&&a.isValid!==v;if(t.delayError&&y?(l=_(()=>z(f,y)),l(t.delayError)):(clearTimeout(c),l=null,y?xe(a.errors,f,y):Be(a.errors,f)),(y?!ea(A,y):A)||!ut(C)||x){const $={...C,...x&&mt(v)?{isValid:v}:{},errors:a.errors,name:f};a={...a,...$},m.state.next($)}},O=async f=>{w(f,!0);const v=await e.resolver(s,e.context,Sp(f||o.mount,n,e.criteriaMode,e.shouldUseNativeValidation));return w(f),v},k=async f=>{const{errors:v}=await O(f);if(f)for(const y of f){const C=P(v,y);C?xe(a.errors,y,C):Be(a.errors,y)}else a.errors=v;return v},Z=async(f,v,y={valid:!0})=>{for(const C in f){const A=f[C];if(A){const{_f:x,...$}=A;if(x){const te=o.array.has(x.name);w([C],!0);const le=await bi(A,s,g,e.shouldUseNativeValidation&&!v,te);if(w([C]),le[x.name]&&(y.valid=!1,v))break;!v&&(P(le,x.name)?te?wp(a.errors,le,x.name):xe(a.errors,x.name,le[x.name]):Be(a.errors,x.name))}$&&await Z($,v,y)}}return y.valid},Y=()=>{for(const f of o.unMount){const v=P(n,f);v&&(v._f.refs?v._f.refs.every(y=>!es(y)):!es(v._f.ref))&&Ae(f)}o.unMount=new Set},N=(f,v)=>(f&&v&&xe(s,f,v),!ea(ie(),r)),L=(f,v,y)=>Mo(f,o,{...i.mount?s:Fe(v)?r:xt(f)?{[f]:v}:v},y,v),F=f=>Cn(P(i.mount?s:r,f,t.shouldUnregister?P(r,f,[]):[])),X=(f,v,y={})=>{const C=P(n,f);let A=v;if(C){const x=C._f;x&&(!x.disabled&&xe(s,f,Bo(v,x)),A=or(x.ref)&&Ge(v)?"":v,Vo(x.ref)?[...x.ref.options].forEach($=>$.selected=A.includes($.value)):x.refs?En(x.ref)?x.refs.length>1?x.refs.forEach($=>(!$.defaultChecked||!$.disabled)&&($.checked=Array.isArray(A)?!!A.find(te=>te===$.value):A===$.value)):x.refs[0]&&(x.refs[0].checked=!!A):x.refs.forEach($=>$.checked=$.value===A):Os(x.ref)?x.ref.value="":(x.ref.value=A,x.ref.type||m.values.next({name:f,values:{...s}})))}(y.shouldDirty||y.shouldTouch)&&M(f,A,y.shouldTouch,y.shouldDirty,!0),y.shouldValidate&&U(f)},ee=(f,v,y)=>{for(const C in v){const A=v[C],x=`${f}.${C}`,$=P(n,x);(o.array.has(f)||!cr(A)||$&&!$._f)&&!ba(A)?ee(x,A,y):X(x,A,y)}},I=(f,v,y={})=>{const C=P(n,f),A=o.array.has(f),x=Xe(v);xe(s,f,x),A?(m.array.next({name:f,values:{...s}}),(d.isDirty||d.dirtyFields)&&y.shouldDirty&&m.state.next({name:f,dirtyFields:Pn(r,s),isDirty:N(f,x)})):C&&!C._f&&!Ge(x)?ee(f,x,y):X(f,x,y),pi(f,o)&&m.state.next({...a}),m.values.next({name:i.mount?f:void 0,values:{...s}})},G=async f=>{const v=f.target;let y=v.name,C=!0;const A=P(n,y),x=()=>v.type?ts(A._f):To(f),$=te=>{C=Number.isNaN(te)||te===P(s,y,te)};if(A){let te,le;const me=x(),$e=f.type===ir.BLUR||f.type===ir.FOCUS_OUT,Ue=!zp(A._f)&&!e.resolver&&!P(a.errors,y)&&!A._f.deps||Ep($e,P(a.touchedFields,y),a.isSubmitted,p,u),ot=pi(y,o,$e);xe(s,y,me),$e?(A._f.onBlur&&A._f.onBlur(f),l&&l(0)):A._f.onChange&&A._f.onChange(f);const Tt=M(y,me,$e,!1),tt=!ut(Tt)||ot;if(!$e&&m.values.next({name:y,type:f.type,values:{...s}}),Ue)return d.isValid&&S(),tt&&m.state.next({name:y,...ot?{}:Tt});if(!$e&&ot&&m.state.next({...a}),e.resolver){const{errors:Le}=await O([y]);if($(me),C){const ua=wi(a.errors,n,y),$t=wi(Le,n,ua.name||y);te=$t.error,y=$t.name,le=ut(Le)}}else w([y],!0),te=(await bi(A,s,g,e.shouldUseNativeValidation))[y],w([y]),$(me),C&&(te?le=!1:d.isValid&&(le=await Z(n,!0)));C&&(A._f.deps&&U(A._f.deps),V(y,le,te,Tt))}},J=(f,v)=>{if(P(a.errors,v)&&f.focus)return f.focus(),1},U=async(f,v={})=>{let y,C;const A=qn(f);if(e.resolver){const x=await k(Fe(f)?f:A);y=ut(x),C=f?!A.some($=>P(x,$)):y}else f?(C=(await Promise.all(A.map(async x=>{const $=P(n,x);return await Z($&&$._f?{[x]:$}:$)}))).every(Boolean),!(!C&&!a.isValid)&&S()):C=y=await Z(n);return m.state.next({...!xt(f)||d.isValid&&y!==a.isValid?{}:{name:f},...e.resolver||!f?{isValid:y}:{},errors:a.errors}),v.shouldFocus&&!C&&Ga(n,J,f?A:o.mount),C},ie=f=>{const v={...r,...i.mount?s:{}};return Fe(f)?v:xt(f)?P(v,f):f.map(y=>P(v,y))},ue=(f,v)=>({invalid:!!P((v||a).errors,f),isDirty:!!P((v||a).dirtyFields,f),isTouched:!!P((v||a).touchedFields,f),isValidating:!!P((v||a).validatingFields,f),error:P((v||a).errors,f)}),Ee=f=>{f&&qn(f).forEach(v=>Be(a.errors,v)),m.state.next({errors:f?a.errors:{}})},Pe=(f,v,y)=>{const C=(P(n,f,{_f:{}})._f||{}).ref;xe(a.errors,f,{...v,ref:C}),m.state.next({name:f,errors:a.errors,isValid:!1}),y&&y.shouldFocus&&C&&C.focus&&C.focus()},ke=(f,v)=>Ht(f)?m.values.subscribe({next:y=>f(L(void 0,v),y)}):L(f,v,!0),Ae=(f,v={})=>{for(const y of f?qn(f):o.mount)o.mount.delete(y),o.array.delete(y),v.keepValue||(Be(n,y),Be(s,y)),!v.keepError&&Be(a.errors,y),!v.keepDirty&&Be(a.dirtyFields,y),!v.keepTouched&&Be(a.touchedFields,y),!v.keepIsValidating&&Be(a.validatingFields,y),!e.shouldUnregister&&!v.keepDefaultValue&&Be(r,y);m.values.next({values:{...s}}),m.state.next({...a,...v.keepDirty?{isDirty:N()}:{}}),!v.keepIsValid&&S()},be=({disabled:f,name:v,field:y,fields:C,value:A})=>{if(mt(f)){const x=f?void 0:Fe(A)?ts(y?y._f:P(C,v)._f):A;xe(s,v,x),M(v,x,!1,!1,!0)}},Re=(f,v={})=>{let y=P(n,f);const C=mt(v.disabled);return xe(n,f,{...y||{},_f:{...y&&y._f?y._f:{ref:{name:f}},name:f,mount:!0,...v}}),o.mount.add(f),y?be({field:y,disabled:v.disabled,name:f,value:v.value}):E(f,!0,v.value),{...C?{disabled:v.disabled}:{},...e.progressive?{required:!!v.required,min:Ua(v.min),max:Ua(v.max),minLength:Ua(v.minLength),maxLength:Ua(v.maxLength),pattern:Ua(v.pattern)}:{},name:f,onChange:G,onBlur:G,ref:A=>{if(A){Re(f,v),y=P(n,f);const x=Fe(A.value)&&A.querySelectorAll&&A.querySelectorAll("input,select,textarea")[0]||A,$=jp(x),te=y._f.refs||[];if($?te.find(le=>le===x):x===y._f.ref)return;xe(n,f,{_f:{...y._f,...$?{refs:[...te.filter(es),x,...Array.isArray(P(r,f))?[{}]:[]],ref:{type:x.type,name:f}}:{ref:x}}}),E(f,!1,void 0,x)}else y=P(n,f,{}),y._f&&(y._f.mount=!1),(e.shouldUnregister||v.shouldUnregister)&&!(Ao(o.array,f)&&i.action)&&o.unMount.add(f)}}},K=()=>e.shouldFocusError&&Ga(n,J,o.mount),ye=f=>{mt(f)&&(m.state.next({disabled:f}),Ga(n,(v,y)=>{let C=f;const A=P(n,y);A&&mt(A._f.disabled)&&(C||(C=A._f.disabled)),v.disabled=C},0,!1))},_e=(f,v)=>async y=>{let C;y&&(y.preventDefault&&y.preventDefault(),y.persist&&y.persist());let A=Xe(s);if(m.state.next({isSubmitting:!0}),e.resolver){const{errors:x,values:$}=await O();a.errors=x,A=$}else await Z(n);if(Be(a.errors,"root"),ut(a.errors)){m.state.next({errors:{}});try{await f(A,y)}catch(x){C=x}}else v&&await v({...a.errors},y),K(),setTimeout(K);if(m.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:ut(a.errors)&&!C,submitCount:a.submitCount+1,errors:a.errors}),C)throw C},Ce=(f,v={})=>{P(n,f)&&(Fe(v.defaultValue)?I(f,Xe(P(r,f))):(I(f,v.defaultValue),xe(r,f,Xe(v.defaultValue))),v.keepTouched||Be(a.touchedFields,f),v.keepDirty||(Be(a.dirtyFields,f),a.isDirty=v.defaultValue?N(f,Xe(P(r,f))):N()),v.keepError||(Be(a.errors,f),d.isValid&&S()),m.state.next({...a}))},fe=(f,v={})=>{const y=f?Xe(f):r,C=Xe(y),A=ut(f),x=A?r:C;if(v.keepDefaultValues||(r=y),!v.keepValues){if(v.keepDirtyValues)for(const $ of o.mount)P(a.dirtyFields,$)?xe(x,$,P(s,$)):I($,P(x,$));else{if(Es&&Fe(f))for(const $ of o.mount){const te=P(n,$);if(te&&te._f){const le=Array.isArray(te._f.refs)?te._f.refs[0]:te._f.ref;if(or(le)){const me=le.closest("form");if(me){me.reset();break}}}}n={}}s=t.shouldUnregister?v.keepDefaultValues?Xe(r):{}:Xe(x),m.array.next({values:{...x}}),m.values.next({values:{...x}})}o={mount:v.keepDirtyValues?o.mount:new Set,unMount:new Set,array:new Set,watch:new Set,watchAll:!1,focus:""},i.mount=!d.isValid||!!v.keepIsValid||!!v.keepDirtyValues,i.watch=!!t.shouldUnregister,m.state.next({submitCount:v.keepSubmitCount?a.submitCount:0,isDirty:A?!1:v.keepDirty?a.isDirty:!!(v.keepDefaultValues&&!ea(f,r)),isSubmitted:v.keepIsSubmitted?a.isSubmitted:!1,dirtyFields:A?[]:v.keepDirtyValues?v.keepDefaultValues&&s?Pn(r,s):a.dirtyFields:v.keepDefaultValues&&f?Pn(r,f):{},touchedFields:v.keepTouched?a.touchedFields:{},errors:v.keepErrors?a.errors:{},isSubmitSuccessful:v.keepIsSubmitSuccessful?a.isSubmitSuccessful:!1,isSubmitting:!1})},we=(f,v)=>fe(Ht(f)?f(s):f,v);return{control:{register:Re,unregister:Ae,getFieldState:ue,handleSubmit:_e,setError:Pe,_executeSchema:O,_getWatch:L,_getDirty:N,_updateValid:S,_removeUnmounted:Y,_updateFieldArray:j,_updateDisabledField:be,_getFieldArray:F,_reset:fe,_resetDefaultValues:()=>Ht(e.defaultValues)&&e.defaultValues().then(f=>{we(f,e.resetOptions),m.state.next({isLoading:!1})}),_updateFormState:f=>{a={...a,...f}},_disableForm:ye,_subjects:m,_proxyFormState:d,_setErrors:T,get _fields(){return n},get _formValues(){return s},get _state(){return i},set _state(f){i=f},get _defaultValues(){return r},get _names(){return o},set _names(f){o=f},get _formState(){return a},set _formState(f){a=f},get _options(){return e},set _options(f){e={...e,...f}}},trigger:U,register:Re,handleSubmit:_e,watch:ke,setValue:I,getValues:ie,reset:we,resetField:Ce,clearErrors:Ee,unregister:Ae,setError:Pe,setFocus:(f,v={})=>{const y=P(n,f),C=y&&y._f;if(C){const A=C.refs?C.refs[0]:C.ref;A.focus&&(A.focus(),v.shouldSelect&&A.select())}},getFieldState:ue}}function qv(t={}){const e=W.useRef(),a=W.useRef(),[n,r]=W.useState({isDirty:!1,isValidating:!1,isLoading:Ht(t.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1,defaultValues:Ht(t.defaultValues)?void 0:t.defaultValues});e.current||(e.current={...Ap(t),formState:n});const s=e.current.control;return s._options=t,Ts({subject:s._subjects.state,next:i=>{Lo(i,s._proxyFormState,s._updateFormState,!0)&&r({...s._formState})}}),W.useEffect(()=>s._disableForm(t.disabled),[s,t.disabled]),W.useEffect(()=>{if(s._proxyFormState.isDirty){const i=s._getDirty();i!==n.isDirty&&s._subjects.state.next({isDirty:i})}},[s,n.isDirty]),W.useEffect(()=>{t.values&&!ea(t.values,a.current)?(s._reset(t.values,s._options.resetOptions),a.current=t.values,r(i=>({...i}))):s._resetDefaultValues()},[t.values,s]),W.useEffect(()=>{t.errors&&s._setErrors(t.errors)},[t.errors,s]),W.useEffect(()=>{s._state.mount||(s._updateValid(),s._state.mount=!0),s._state.watch&&(s._state.watch=!1,s._subjects.state.next({...s._formState})),s._removeUnmounted()}),W.useEffect(()=>{t.shouldUnregister&&s._subjects.values.next({values:s._getWatch()})},[t.shouldUnregister,s]),e.current.formState=Ro(n,s),e.current}var xi=function(t,e,a){if(t&&"reportValidity"in t){var n=P(a,e);t.setCustomValidity(n&&n.message||""),t.reportValidity()}},Uo=function(t,e){var a=function(r){var s=e.fields[r];s&&s.ref&&"reportValidity"in s.ref?xi(s.ref,r,t):s.refs&&s.refs.forEach(function(i){return xi(i,r,t)})};for(var n in e.fields)a(n)},Op=function(t,e){e.shouldUseNativeValidation&&Uo(t,e);var a={};for(var n in t){var r=P(e.fields,n),s=Object.assign(t[n]||{},{ref:r&&r.ref});if(Rp(e.names||Object.keys(t),n)){var i=Object.assign({},P(a,n));xe(i,"root",s),xe(a,n,i)}else xe(a,n,s)}return a},Rp=function(t,e){return t.some(function(a){return a.startsWith(e+".")})},Lp=function(t,e){for(var a={};t.length;){var n=t[0],r=n.code,s=n.message,i=n.path.join(".");if(!a[i])if("unionErrors"in n){var o=n.unionErrors[0].errors[0];a[i]={message:o.message,type:o.code}}else a[i]={message:s,type:r};if("unionErrors"in n&&n.unionErrors.forEach(function(d){return d.errors.forEach(function(m){return t.push(m)})}),e){var l=a[i].types,c=l&&l[n.code];a[i]=Fo(i,e,a,r,c?[].concat(c,n.message):n.message)}t.shift()}return a},Bv=function(t,e,a){return a===void 0&&(a={}),function(n,r,s){try{return Promise.resolve(function(i,o){try{var l=Promise.resolve(t[a.mode==="sync"?"parse":"parseAsync"](n,e)).then(function(c){return s.shouldUseNativeValidation&&Uo({},s),{errors:{},values:a.raw?n:c}})}catch(c){return o(c)}return l&&l.then?l.then(void 0,o):l}(0,function(i){if(function(o){return o.errors!=null}(i))return{values:{},errors:Op(Lp(i.errors,!s.shouldUseNativeValidation&&s.criteriaMode==="all"),s)};throw i}))}catch(i){return Promise.reject(i)}}};const Np=({width:t="",open:e,modalTitle:a,modalTitleChildren:n,handleClose:r,handleBlur:s,headerSection:i=null,headerExtraClass:o="",bodySection:l,footerSection:c,uniqueId:d,isCloseButtonDisabled:m=!1,extraClass:u=""})=>{const p=b.useRef(null),g=_=>{p.current&&!p.current.contains(_==null?void 0:_.target)&&(s==null||s(_))};return b.useEffect(()=>{if(s)return e?document.addEventListener("mousedown",g):document.removeEventListener("mousedown",g),()=>{document.removeEventListener("mousedown",g)}},[e]),e?h.jsx("div",{id:`modal-${d}`,className:`modal fade ${u}`,tabIndex:-1,role:"dialog",children:h.jsx("div",{className:"mrdn-modal-dialog border-r-1 text-primary",role:"document",style:t?{width:t}:{},children:h.jsxs("div",{className:"mrdn-modal-content bg-main",ref:p,children:[h.jsx("div",{className:`mrdn-modal-header ${o}`,children:h.jsxs("div",{className:"d-flex flex-col gap-y-4 w-full",children:[h.jsxs("header",{className:"d-flex flex justify-between align-items-center modal-header-border-bottom px-4 py-2",children:[n?h.jsxs("div",{className:"d-flex gap-x-2 align-items-center",children:[h.jsx("h3",{className:"mrdn-modal-title",children:a}),n]}):h.jsx("h3",{className:"mrdn-modal-title",children:a}),h.jsx("button",{type:"button",className:"modal-close text-primary d-flex",onClick:r,"aria-label":"Close",disabled:m,children:h.jsx(ku,{})})]}),i]})}),h.jsx("div",{className:"mrdn-modal-body scrollbar-style",children:l}),c?h.jsx("div",{className:"modal-footer justify-content-between",children:c}):null]})})}):null},Uv=b.memo(Np);function _i(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),a.push.apply(a,n)}return a}function ta(t){for(var e=1;e<arguments.length;e++){var a=arguments[e]!=null?arguments[e]:{};e%2?_i(Object(a),!0).forEach(function(n){dt(t,n,a[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):_i(Object(a)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(a,n))})}return t}function Mp(t){return!!(t.addonBefore||t.addonAfter)}function Ip(t){return!!(t.prefix||t.suffix||t.allowClear)}function ji(t,e,a){var n=e.cloneNode(!0),r=Object.create(t,{target:{value:n},currentTarget:{value:n}});return n.value=a,typeof e.selectionStart=="number"&&typeof e.selectionEnd=="number"&&(n.selectionStart=e.selectionStart,n.selectionEnd=e.selectionEnd),r}function Si(t,e,a,n){if(a){var r=e;if(e.type==="click"){r=ji(e,t,""),a(r);return}if(t.type!=="file"&&n!==void 0){r=ji(e,t,n),a(r);return}a(r)}}function Fp(t,e){if(t){t.focus(e);var a=e||{},n=a.cursor;if(n){var r=t.value.length;switch(n){case"start":t.setSelectionRange(0,0);break;case"end":t.setSelectionRange(r,r);break;default:t.setSelectionRange(0,r)}}}}var Pp=function(e){var a,n,r=e.inputElement,s=e.children,i=e.prefixCls,o=e.prefix,l=e.suffix,c=e.addonBefore,d=e.addonAfter,m=e.className,u=e.style,p=e.disabled,g=e.readOnly,_=e.focused,S=e.triggerFocus,w=e.allowClear,j=e.value,z=e.handleReset,T=e.hidden,E=e.classes,M=e.classNames,V=e.dataAttrs,O=e.styles,k=e.components,Z=s??r,Y=(k==null?void 0:k.affixWrapper)||"span",N=(k==null?void 0:k.groupWrapper)||"span",L=(k==null?void 0:k.wrapper)||"span",F=(k==null?void 0:k.groupAddon)||"span",X=b.useRef(null),ee=function(we){var je;(je=X.current)!==null&&je!==void 0&&je.contains(we.target)&&(S==null||S())},I=Ip(e),G=b.cloneElement(Z,{value:j,className:et(Z.props.className,!I&&(M==null?void 0:M.variant))||null});if(I){var J,U=null;if(w){var ie,ue=!p&&!g&&j,Ee="".concat(i,"-clear-icon"),Pe=Oi(w)==="object"&&w!==null&&w!==void 0&&w.clearIcon?w.clearIcon:"✖";U=W.createElement("span",{onClick:z,onMouseDown:function(we){return we.preventDefault()},className:et(Ee,(ie={},dt(ie,"".concat(Ee,"-hidden"),!ue),dt(ie,"".concat(Ee,"-has-suffix"),!!l),ie)),role:"button",tabIndex:-1},Pe)}var ke="".concat(i,"-affix-wrapper"),Ae=et(ke,(J={},dt(J,"".concat(i,"-disabled"),p),dt(J,"".concat(ke,"-disabled"),p),dt(J,"".concat(ke,"-focused"),_),dt(J,"".concat(ke,"-readonly"),g),dt(J,"".concat(ke,"-input-with-clear-btn"),l&&w&&j),J),E==null?void 0:E.affixWrapper,M==null?void 0:M.affixWrapper,M==null?void 0:M.variant),be=(l||w)&&W.createElement("span",{className:et("".concat(i,"-suffix"),M==null?void 0:M.suffix),style:O==null?void 0:O.suffix},U,l);G=W.createElement(Y,ns({className:Ae,style:O==null?void 0:O.affixWrapper,onClick:ee},V==null?void 0:V.affixWrapper,{ref:X}),o&&W.createElement("span",{className:et("".concat(i,"-prefix"),M==null?void 0:M.prefix),style:O==null?void 0:O.prefix},o),G,be)}if(Mp(e)){var Re="".concat(i,"-group"),K="".concat(Re,"-addon"),ye="".concat(Re,"-wrapper"),_e=et("".concat(i,"-wrapper"),Re,E==null?void 0:E.wrapper,M==null?void 0:M.wrapper),Ce=et(ye,dt({},"".concat(ye,"-disabled"),p),E==null?void 0:E.group,M==null?void 0:M.groupWrapper);G=W.createElement(N,{className:Ce},W.createElement(L,{className:_e},c&&W.createElement(F,{className:K},c),G,d&&W.createElement(F,{className:K},d)))}return W.cloneElement(G,{className:et((a=G.props)===null||a===void 0?void 0:a.className,m)||null,style:ta(ta({},(n=G.props)===null||n===void 0?void 0:n.style),u),hidden:T})};function $p(t){if(Array.isArray(t))return Qo(t)}function Vp(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Dp(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function qp(t){return $p(t)||Vp(t)||Ri(t)||Dp()}function Bp(t){if(Array.isArray(t))return t}function Up(t,e){var a=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(a!=null){var n,r,s,i,o=[],l=!0,c=!1;try{if(s=(a=a.call(t)).next,e===0){if(Object(a)!==a)return;l=!1}else for(;!(l=(n=s.call(a)).done)&&(o.push(n.value),o.length!==e);l=!0);}catch(d){c=!0,r=d}finally{try{if(!l&&a.return!=null&&(i=a.return(),Object(i)!==i))return}finally{if(c)throw r}}return o}}function Hp(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ja(t,e){return Bp(t)||Up(t,e)||Ri(t,e)||Hp()}function Ho(t,e){if(t==null)return{};var a,n,r=Xo(t,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);for(n=0;n<s.length;n++)a=s[n],e.indexOf(a)===-1&&{}.propertyIsEnumerable.call(t,a)&&(r[a]=t[a])}return r}function zi(t){var e=b.useRef();e.current=t;var a=b.useCallback(function(){for(var n,r=arguments.length,s=new Array(r),i=0;i<r;i++)s[i]=arguments[i];return(n=e.current)===null||n===void 0?void 0:n.call.apply(n,[e].concat(s))},[]);return a}function Wp(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}var Ei=Wp()?b.useLayoutEffect:b.useEffect,Kp=function(e,a){var n=b.useRef(!0);Ei(function(){return e(n.current)},a),Ei(function(){return n.current=!1,function(){n.current=!0}},[])},Ci=function(e,a){Kp(function(n){if(!n)return e()},a)};function Ti(t){var e=b.useRef(!1),a=b.useState(t),n=ja(a,2),r=n[0],s=n[1];b.useEffect(function(){return e.current=!1,function(){e.current=!0}},[]);function i(o,l){l&&e.current||s(o)}return[r,i]}function as(t){return t!==void 0}function Zp(t,e){var a=e||{},n=a.defaultValue,r=a.value,s=a.onChange,i=a.postState,o=Ti(function(){return as(r)?r:as(n)?typeof n=="function"?n():n:typeof t=="function"?t():t}),l=ja(o,2),c=l[0],d=l[1],m=r!==void 0?r:c,u=i?i(m):m,p=zi(s),g=Ti([m]),_=ja(g,2),S=_[0],w=_[1];Ci(function(){var z=S[0];c!==z&&p(c,z)},[S]),Ci(function(){as(r)||d(r)},[r]);var j=zi(function(z,T){d(z,T),w([m],T)});return[u,j]}function Gp(t,e){var a=Object.assign({},t);return Array.isArray(e)&&e.forEach(function(n){delete a[n]}),a}var Yp=["show"];function Jp(t,e){return b.useMemo(function(){var a={};e&&(a.show=Oi(e)==="object"&&e.formatter?e.formatter:!!e),a=ta(ta({},a),t);var n=a,r=n.show,s=Ho(n,Yp);return ta(ta({},s),{},{show:!!r,showFormatter:typeof r=="function"?r:void 0,strategy:s.strategy||function(i){return i.length}})},[t,e])}var Qp=["autoComplete","onChange","onFocus","onBlur","onPressEnter","onKeyDown","prefixCls","disabled","htmlSize","className","maxLength","suffix","showCount","count","type","classes","classNames","styles","onCompositionStart","onCompositionEnd"],Xp=b.forwardRef(function(t,e){var a=t.autoComplete,n=t.onChange,r=t.onFocus,s=t.onBlur,i=t.onPressEnter,o=t.onKeyDown,l=t.prefixCls,c=l===void 0?"rc-input":l,d=t.disabled,m=t.htmlSize,u=t.className,p=t.maxLength,g=t.suffix,_=t.showCount,S=t.count,w=t.type,j=w===void 0?"text":w,z=t.classes,T=t.classNames,E=t.styles,M=t.onCompositionStart,V=t.onCompositionEnd,O=Ho(t,Qp),k=b.useState(!1),Z=ja(k,2),Y=Z[0],N=Z[1],L=b.useRef(!1),F=b.useRef(null),X=function(y){F.current&&Fp(F.current,y)},ee=Zp(t.defaultValue,{value:t.value}),I=ja(ee,2),G=I[0],J=I[1],U=G==null?"":String(G),ie=b.useState(null),ue=ja(ie,2),Ee=ue[0],Pe=ue[1],ke=Jp(S,_),Ae=ke.max||p,be=ke.strategy(U),Re=!!Ae&&be>Ae;b.useImperativeHandle(e,function(){return{focus:X,blur:function(){var y;(y=F.current)===null||y===void 0||y.blur()},setSelectionRange:function(y,C,A){var x;(x=F.current)===null||x===void 0||x.setSelectionRange(y,C,A)},select:function(){var y;(y=F.current)===null||y===void 0||y.select()},input:F.current}}),b.useEffect(function(){N(function(v){return v&&d?!1:v})},[d]);var K=function(y,C,A){var x=C;if(!L.current&&ke.exceedFormatter&&ke.max&&ke.strategy(C)>ke.max){if(x=ke.exceedFormatter(C,{max:ke.max}),C!==x){var $,te;Pe([(($=F.current)===null||$===void 0?void 0:$.selectionStart)||0,((te=F.current)===null||te===void 0?void 0:te.selectionEnd)||0])}}else if(A.source==="compositionEnd")return;J(x),F.current&&Si(F.current,y,n,x)};b.useEffect(function(){if(Ee){var v;(v=F.current)===null||v===void 0||v.setSelectionRange.apply(v,qp(Ee))}},[Ee]);var ye=function(y){K(y,y.target.value,{source:"change"})},_e=function(y){L.current=!1,K(y,y.currentTarget.value,{source:"compositionEnd"}),V==null||V(y)},Ce=function(y){i&&y.key==="Enter"&&i(y),o==null||o(y)},fe=function(y){N(!0),r==null||r(y)},we=function(y){N(!1),s==null||s(y)},je=function(y){J(""),X(),F.current&&Si(F.current,y,n)},Me=Re&&"".concat(c,"-out-of-range"),Ie=function(){var y=Gp(t,["prefixCls","onPressEnter","addonBefore","addonAfter","prefix","suffix","allowClear","defaultValue","showCount","count","classes","htmlSize","styles","classNames"]);return W.createElement("input",ns({autoComplete:a},y,{onChange:ye,onFocus:fe,onBlur:we,onKeyDown:Ce,className:et(c,dt({},"".concat(c,"-disabled"),d),T==null?void 0:T.input),style:E==null?void 0:E.input,ref:F,size:m,type:j,onCompositionStart:function(A){L.current=!0,M==null||M(A)},onCompositionEnd:_e}))},f=function(){var y=Number(Ae)>0;if(g||ke.show){var C=ke.showFormatter?ke.showFormatter({value:U,count:be,maxLength:Ae}):"".concat(be).concat(y?" / ".concat(Ae):"");return W.createElement(W.Fragment,null,ke.show&&W.createElement("span",{className:et("".concat(c,"-show-count-suffix"),dt({},"".concat(c,"-show-count-has-suffix"),!!g),T==null?void 0:T.count),style:ta({},E==null?void 0:E.count)},C),g)}return null};return W.createElement(Pp,ns({},O,{prefixCls:c,className:et(u,Me),handleReset:je,value:U,focused:Y,triggerFocus:X,suffix:f(),disabled:d,classes:z,classNames:T,styles:E}),Ie())});const Hv=({type:t="text",value:e,label:a="",uniqueId:n,onChange:r,isDisabled:s=!1,extraClass:i="",placeHolder:o="",inputClassName:l="",labelClass:c="",helperText:d="",hasPermission:m=!0,...u})=>{const p=`h-38px mrdn-input h-[38px] ${l} bg-main ${s?"cursor-disabled opacity-80":""} w-full d-flex-important`,g=s||!m,_=S=>{g||r(S)};return h.jsxs("div",{className:`d-flex flex-col ${i}`,children:[a?h.jsx("label",{className:`d-flex align-items-center gap-x-1 text-primary montserrat-medium color-gray-200 ${c}`,children:a}):null,h.jsx(Xp,{type:t,id:n,value:e,onChange:_,disabled:g,className:p,autoComplete:"off",style:{color:"var(--text-primary-color)"},placeholder:o,...u}),d&&h.jsx("p",{className:"text-red-500 error-message color-danger ml-2 mt-1",children:d})]})};function Wv(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{fill:"none",d:"M24 24H0V0h24v24z"},child:[]},{tag:"path",attr:{d:"M21 15h2v2h-2v-2zm0-4h2v2h-2v-2zm2 8h-2v2c1 0 2-1 2-2zM13 3h2v2h-2V3zm8 4h2v2h-2V7zm0-4v2h2c0-1-1-2-2-2zM1 7h2v2H1V7zm16-4h2v2h-2V3zm0 16h2v2h-2v-2zM3 3C2 3 1 4 1 5h2V3zm6 0h2v2H9V3zM5 3h2v2H5V3zm-4 8v8c0 1.1.9 2 2 2h12V11H1zm2 8 2.5-3.21 1.79 2.15 2.5-3.22L13 19H3z"},child:[]}]})(t)}function Kv(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{fill:"none",d:"M0 0h24v24H0V0zm24 24H0V0h24v24z"},child:[]},{tag:"path",attr:{d:"M23 15h-2v2h2v-2zm0-4h-2v2h2v-2zm0 8h-2v2c1 0 2-1 2-2zM15 3h-2v2h2V3zm8 4h-2v2h2V7zm-2-4v2h2c0-1-1-2-2-2zM3 21h8v-6H1v4c0 1.1.9 2 2 2zM3 7H1v2h2V7zm12 12h-2v2h2v-2zm4-16h-2v2h2V3zm0 16h-2v2h2v-2zM3 3C2 3 1 4 1 5h2V3zm0 8H1v2h2v-2zm8-8H9v2h2V3zM7 3H5v2h2V3z"},child:[]}]})(t)}function Zv(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{fill:"none",d:"M0 0h24v24H0z"},child:[]},{tag:"path",attr:{d:"M15.41 7.41 14 6l-6 6 6 6 1.41-1.41L10.83 12z"},child:[]}]})(t)}function Gv(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{fill:"none",d:"M0 0h24v24H0z"},child:[]},{tag:"path",attr:{d:"M10 6 8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"},child:[]}]})(t)}function Yv(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{fill:"none",d:"M0 0h24v24H0V0z"},child:[]},{tag:"path",attr:{d:"m12.87 15.07-2.54-2.51.03-.03A17.52 17.52 0 0 0 14.07 6H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7 1.62-4.33L19.12 17h-3.24z"},child:[]}]})(t)}function Jv(t){return q({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{fill:"none",d:"M0 0h24v24H0z"},child:[]},{tag:"path",attr:{d:"M3 21h3.75L17.81 9.94l-3.75-3.75L3 17.25V21zm2-2.92 9.06-9.06.92.92L5.92 19H5v-.92zM18.37 3.29a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83a.996.996 0 0 0 0-1.41l-2.34-2.34z"},child:[]}]})(t)}const Qv=({message:t,extraClass:e=""})=>t&&h.jsx("p",{className:`color-danger error-message mt-1 ml-2 ${e}`,children:t});function Xv(t){return q({tag:"svg",attr:{viewBox:"0 0 1024 1024"},child:[{tag:"path",attr:{d:"M485 467.5c-11.6 4.9-20.9 12.2-27.8 22-6.9 9.8-10.4 21.6-10.4 35.5 0 17.8 7.5 31.5 22.4 41.2 14.1 9.1 28.9 11.4 44.4 6.8 17.9-5.2 30-17.9 36.4-38.1 3-9.3 4.5-19.7 4.5-31.3v-50.2c-12.6.4-24.4 1.6-35.5 3.7-11.1 2.1-22.4 5.6-34 10.4zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm35.8 262.7c-7.2-10.9-20.1-16.4-38.7-16.4-1.3 0-3 .1-5.3.3-2.2.2-6.6 1.5-12.9 3.7a79.4 79.4 0 0 0-17.9 9.1c-5.5 3.8-11.5 10-18 18.4-6.4 8.5-11.5 18.4-15.3 29.8l-94-8.4c0-12.4 2.4-24.7 7-36.9 4.7-12.2 11.8-23.9 21.4-35 9.6-11.2 21.1-21 34.5-29.4 13.4-8.5 29.6-15.2 48.4-20.3 18.9-5.1 39.1-7.6 60.9-7.6 21.3 0 40.6 2.6 57.8 7.7 17.2 5.2 31.1 11.5 41.4 19.1a117 117 0 0 1 25.9 25.7c6.9 9.6 11.7 18.5 14.4 26.7 2.7 8.2 4 15.7 4 22.8v182.5c0 6.4 1.4 13 4.3 19.8 2.9 6.8 6.3 12.8 10.2 18 3.9 5.2 7.9 9.9 12 14.3 4.1 4.3 7.6 7.7 10.6 9.9l4.1 3.4-72.5 69.4c-8.5-7.7-16.9-15.4-25.2-23.4-8.3-8-14.5-14-18.5-18.1l-6.1-6.2c-2.4-2.3-5-5.7-8-10.2-8.1 12.2-18.5 22.8-31.1 31.8-12.7 9-26.3 15.6-40.7 19.7-14.5 4.1-29.4 6.5-44.7 7.1-15.3.6-30-1.5-43.9-6.5-13.9-5-26.5-11.7-37.6-20.3-11.1-8.6-19.9-20.2-26.5-35-6.6-14.8-9.9-31.5-9.9-50.4 0-17.4 3-33.3 8.9-47.7 6-14.5 13.6-26.5 23-36.1 9.4-9.6 20.7-18.2 34-25.7s26.4-13.4 39.2-17.7c12.8-4.2 26.6-7.8 41.5-10.7 14.9-2.9 27.6-4.8 38.2-5.7 10.6-.9 21.2-1.6 31.8-2v-39.4c0-13.5-2.3-23.5-6.7-30.1zm180.5 379.6c-2.8 3.3-7.5 7.8-14.1 13.5s-16.8 12.7-30.5 21.1c-13.7 8.4-28.8 16-45 22.9-16.3 6.9-36.3 12.9-60.1 18-23.7 5.1-48.2 7.6-73.3 7.6-25.4 0-50.7-3.2-76.1-9.6-25.4-6.4-47.6-14.3-66.8-23.7-19.1-9.4-37.6-20.2-55.1-32.2-17.6-12.1-31.7-22.9-42.4-32.5-10.6-9.6-19.6-18.7-26.8-27.1-1.7-1.9-2.8-3.6-3.2-5.1-.4-1.5-.3-2.8.3-3.7.6-.9 1.5-1.6 2.6-2.2a7.42 7.42 0 0 1 7.4.8c40.9 24.2 72.9 41.3 95.9 51.4 82.9 36.4 168 45.7 255.3 27.9 40.5-8.3 82.1-22.2 124.9-41.8 3.2-1.2 6-1.5 8.3-.9 2.3.6 3.5 2.4 3.5 5.4 0 2.8-1.6 6.3-4.8 10.2zm59.9-29c-1.8 11.1-4.9 21.6-9.1 31.8-7.2 17.1-16.3 30-27.1 38.4-3.6 2.9-6.4 3.8-8.3 2.8-1.9-1-1.9-3.5 0-7.4 4.5-9.3 9.2-21.8 14.2-37.7 5-15.8 5.7-26 2.1-30.5-1.1-1.5-2.7-2.6-5-3.6-2.2-.9-5.1-1.5-8.6-1.9s-6.7-.6-9.4-.8c-2.8-.2-6.5-.2-11.2 0-4.7.2-8 .4-10.1.6a874.4 874.4 0 0 1-17.1 1.5c-1.3.2-2.7.4-4.1.5-1.5.1-2.7.2-3.5.3l-2.7.3c-1 .1-1.7.2-2.2.2h-3.2l-1-.2-.6-.5-.5-.9c-1.3-3.3 3.7-7.4 15-12.4s22.3-8.1 32.9-9.3c9.8-1.5 21.3-1.5 34.5-.3s21.3 3.7 24.3 7.4c2.3 3.5 2.5 10.7.7 21.7z"},child:[]}]})(t)}function e1(t){return q({tag:"svg",attr:{viewBox:"0 0 1024 1024"},child:[{tag:"path",attr:{d:"M251.2 387H320v68.8c0 1.8 1.8 3.2 4 3.2h48c2.2 0 4-1.4 4-3.3V387h68.8c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H376v-68.8c0-1.8-1.8-3.2-4-3.2h-48c-2.2 0-4 1.4-4 3.2V331h-68.8c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm328 0h193.6c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H579.2c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm0 265h193.6c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H579.2c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm0 104h193.6c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H579.2c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm-195.7-81l61.2-74.9c4.3-5.2.7-13.1-5.9-13.1H388c-2.3 0-4.5 1-5.9 2.9l-34 41.6-34-41.6a7.85 7.85 0 0 0-5.9-2.9h-50.9c-6.6 0-10.2 7.9-5.9 13.1l61.2 74.9-62.7 76.8c-4.4 5.2-.8 13.1 5.8 13.1h50.8c2.3 0 4.5-1 5.9-2.9l35.5-43.5 35.5 43.5c1.5 1.8 3.7 2.9 5.9 2.9h50.8c6.6 0 10.2-7.9 5.9-13.1L383.5 675zM880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-36 732H180V180h664v664z"},child:[]}]})(t)}function t1(t){return q({tag:"svg",attr:{viewBox:"0 0 1024 1024",fill:"currentColor",fillRule:"evenodd"},child:[{tag:"path",attr:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h360c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H184V184h656v320c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V144c0-17.7-14.3-32-32-32ZM770.87 824.869l-52.2 52.2c-4.7 4.7-1.9 12.8 4.7 13.6l179.4 21c5.1.6 9.5-3.7 8.9-8.9l-21-179.4c-.8-6.6-8.9-9.4-13.6-4.7l-52.4 52.4-256.2-256.2c-3.1-3.1-8.2-3.1-11.3 0l-42.4 42.4c-3.1 3.1-3.1 8.2 0 11.3l256.1 256.3Z",transform:"matrix(1 0 0 -1 0 1024)"},child:[]}]})(t)}function a1(t){return q({tag:"svg",attr:{viewBox:"0 0 1024 1024"},child:[{tag:"path",attr:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"},child:[]},{tag:"path",attr:{d:"M464 336a48 48 0 1 0 96 0 48 48 0 1 0-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z"},child:[]}]})(t)}function n1(t){return q({tag:"svg",attr:{viewBox:"0 0 1024 1024"},child:[{tag:"path",attr:{d:"M855.7 210.8l-42.4-42.4a8.03 8.03 0 0 0-11.3 0L168.3 801.9a8.03 8.03 0 0 0 0 11.3l42.4 42.4c3.1 3.1 8.2 3.1 11.3 0L855.6 222c3.2-3 3.2-8.1.1-11.2zM304 448c79.4 0 144-64.6 144-144s-64.6-144-144-144-144 64.6-144 144 64.6 144 144 144zm0-216c39.7 0 72 32.3 72 72s-32.3 72-72 72-72-32.3-72-72 32.3-72 72-72zm416 344c-79.4 0-144 64.6-144 144s64.6 144 144 144 144-64.6 144-144-64.6-144-144-144zm0 216c-39.7 0-72-32.3-72-72s32.3-72 72-72 72 32.3 72 72-32.3 72-72 72z"},child:[]}]})(t)}const r1=({switchTitle:t="",name:e,id:a,checked:n,onChangeHandler:r,extraClass:s="",children:i=null,hasPermission:o=!0,...l})=>{const c=m=>{o&&r(m)},d=`slider round restricted-section ${o?"":"cursor-disabled opacity-80 !cursor-not-allowed"}`;return h.jsxs("div",{className:`d-flex gap-2 align-items-center ${s}`,children:[h.jsxs("label",{className:"switch",children:[h.jsx("input",{type:"checkbox",checked:n,onChange:c,name:e,disabled:!o,...l,id:a??e}),h.jsx("span",{className:d,children:i})]}),t?h.jsx("label",{className:"font-medium switch-title",children:t}):null]})};export{kv as $,Xv as A,Bg as B,Dv as C,Xg as D,ev as E,Vv as F,yg as G,Ss as H,ku as I,$v as J,Bv as K,Wt as L,Uv as M,lo as N,ft as O,Jh as P,Bi as Q,e1 as R,vg as S,Hv as T,Kh as U,gv as V,r1 as W,pv as X,vv as Y,yv as Z,Ze as _,Mr as a,Fv as a$,Jr as a0,bv as a1,qg as a2,q as a3,Wp as a4,ja as a5,qp as a6,Kp as a7,ta as a8,zi as a9,jv as aA,_v as aB,wv as aC,Ud as aD,zv as aE,cv as aF,Ch as aG,Nh as aH,Kv as aI,Wv as aJ,of as aK,rv as aL,ov as aM,uv as aN,dv as aO,nv as aP,xv as aQ,Zv as aR,Gv as aS,iv as aT,Ah as aU,ig as aV,Ev as aW,av as aX,hv as aY,tv as aZ,fv as a_,Ti as aa,Ho as ab,Xp as ac,In as ad,Jv as ae,Qg as af,cg as ag,di as ah,Th as ai,Fg as aj,Sv as ak,bg as al,Qv as am,Yg as an,xg as ao,lv as ap,mv as aq,ng as ar,Qe as as,Wg as at,hf as au,Sg as av,Hh as aw,Ph as ax,Hs as ay,aa as az,Lh as b,Ig as b0,Ov as b1,Ft as b2,rm as b3,bh as b4,Gg as b5,Zg as b6,Mv as b7,Pg as b8,$g as b9,jg as bA,Rv as bB,Pv as bC,ad as bD,sv as bE,hg as bF,mg as bG,fg as bH,Kd as bI,og as bJ,ug as bK,rg as bL,sg as bM,Qa as bN,pg as bO,lg as bP,zg as bQ,t1 as bR,Oh as ba,_g as bb,n1 as bc,Iv as bd,Tg as be,Av as bf,Ug as bg,Ba as bh,Ng as bi,gg as bj,Nv as bk,Lv as bl,wg as bm,kg as bn,Hg as bo,Yv as bp,Cg as bq,a1 as br,sf as bs,Og as bt,Rg as bu,Ag as bv,Kg as bw,Lg as bx,Mg as by,Jg as bz,Ma as c,Vg as d,rt as e,Lr as f,Ir as g,oo as h,Wh as i,h as j,qv as k,Tv as l,dg as m,ef as n,vo as o,Eg as p,Mt as q,Sh as r,rf as s,Pr as t,Ut as u,Fr as v,nf as w,Cv as x,Ja as y,Dg as z};
