var ke=Object.defineProperty;var Ae=(s,t,e)=>t in s?ke(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e;var c=(s,t,e)=>(Ae(s,typeof t!="symbol"?t+"":t,e),e),De=(s,t,e)=>{if(!t.has(s))throw TypeError("Cannot "+e)};var o=(s,t,e)=>(De(s,t,"read from private field"),e?e.call(s):t.get(s)),E=(s,t,e)=>{if(t.has(s))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(s):t.set(s,e)};import{c6 as Fe,c7 as Ce,u as m,c8 as u,ax as Ne,aH as Ie,O as xe,w as Ke,c9 as be,ca as Le,cb as L,cc as v,cd as U,ce as B,cf as G,aL as ve,aJ as F,aK as Ue,aI as $,aN as M,aG as J,aS as V,aR as H,aM as j,aP as z,aQ as R,aT as Y,aO as W,aV as X,aU as Be,aW as K,cg as q,ch as Z,ci as Q,cj as ee,ck as te,cl as re,cm as ae,cn as se,co as oe,cp as ne,cq as ce,cr as ie,cs as ue,ct as he,bm as le,cu as de,cv as Se,cw as fe,cx as ye,cy as pe,bT as ge,aj as Ee,cz as me,cA as Te,cB as Oe,cC as Ge,cD as $e,cE as Me,cF as Je,cG as Ve}from"./chunk-4fe4df4f.js";import"./chunk-58ddc683.js";const He={us:{for:"for",and:"and",with:"with"},uk:{for:"for",and:"and",with:"with"},de:{for:"für",and:"und",with:"mit"},fr:{for:"pour",and:"et",with:"avec"},it:{for:"per",and:"e",with:"con"},es:{for:"para",and:"y",with:"con"},jp:{for:"のために",and:"そして",with:"と"},default:{for:"for",and:"and",with:"with"}},je={com:"133-0620691-3568558",us:"133-0620691-3568558",uk:"262-1799453-2816910",de:"262-5901404-9516021",fr:"258-7575197-0153331",it:"257-6087701-7436632",es:"261-6122023-1458313",jp:"355-7307220-4451711"},ze=["*://www.amazon.com/s*","*://*/*/dp*","*://*/dp/*","*://www.amazon.co.uk/s*","*://www.amazon.de/s*","*://www.amazon.fr/s*","*://www.amazon.it/s*","*://www.amazon.es/s*","*://www.amazon.co.jp/s*"],_e={merchDominatorSearch:{id:"merch-dominator-search",title:"Merch Dominator",contexts:["selection"],documentUrlPatterns:ze},productSearch:{id:"product-search",parentId:"merch-dominator-search",title:"Open in Product Search",contexts:["selection"]},checkTrademark:{id:"check-trademark",parentId:"merch-dominator-search",title:"Check Trademark",contexts:["selection"]}};var T,d,C,N,k,f,O,w,I,A,D,x,P;class Re{constructor(){E(this,T,t=>`${Fe}${t}`);E(this,d,t=>`${Ce}${t}`);E(this,C,t=>`https://suggestqueries.google.com/complete/search${t}`);E(this,N,(t,e)=>`https://completion.${t}/api/2017/suggestions?site-variant=desktop&client-info=amazon-search-ui&suggestion-type=KEYWORD&${e}`);E(this,k,{us:"ATVPDKIKX0DER",uk:"A1F83G8C2ARO7P",de:"A1PA6795UKMFR9",fr:"A13V1IB3VIYZZH",it:"APJ6JRA9NG5V4",es:"A1RKKUPIHCS9HS",jp:"A1VC38T7YXB528",default:"ATVPDKIKX0DER"});E(this,f,{Accept:"application/json","Content-Type":"application/json","Access-Control-Allow-Origin":"*"});E(this,O,async t=>{try{return await m.storage.local.get(t)??{}}catch(e){return console.log("🚀 ~ BackgroundService ~ getStoreValue ~ error:",e),{}}});E(this,w,async t=>{const e={...t,document:null};try{let r=await u(t.link,!1,{method:"GET"},!1,!1,!1);return r.status===200&&(r=await(r==null?void 0:r.text()),e.document=r,r=null),e}catch(r){return console.log("🚀 ~ fetchAsinDocument ~ error:",r),e}});c(this,"processForFieldsConfigurations",async t=>{var n;const e=(n=await o(this,O).call(this,["token"]))==null?void 0:n.token;return await u(o(this,T).call(this,`extension-css-selector?auth_token=${e}&token=${t}`),!1,{method:"GET",headers:o(this,f)},!1,!0)});c(this,"processProductHistory",async t=>{var y;const{asin:e,market_place:r,category:n,tokenKey:l=""}=t,S=(y=await o(this,O).call(this,["token"]))==null?void 0:y.token;return await u(o(this,T).call(this,`get-merch-product-history?asin=${e}&market_place=${r}&category=${n}&token=${l}&auth_token=${S}`),!1,{},!1,!0,!1,!1)});c(this,"processAsinsInBatch",async t=>{const e=t.map(n=>o(this,w).call(this,n));return await Promise.all(e)});c(this,"processSingleAsin",async t=>await o(this,w).call(this,t));c(this,"processForSalesEstimation",async(t,e,r)=>{var S;const n=(S=await o(this,O).call(this,["token"]))==null?void 0:S.token,l=await u(o(this,T).call(this,`get-merch-sales-estimation?auth_token=${n}&market_place=${t}&token=${r}`),!1,{method:"POST",headers:{...o(this,f)},body:JSON.stringify(e)},!1,!0);return l!=null&&l.status?l==null?void 0:l.data:l});c(this,"processDeletedProducts",async(t,e)=>{var l;const r=(l=await o(this,O).call(this,["token"]))==null?void 0:l.token,n=await u(o(this,T).call(this,`extension-delete-product?auth_token=${r}&token=${e}`),!1,{method:"POST",headers:{...o(this,f)},body:JSON.stringify(t)},!1,!0);return n!=null&&n.status?n==null?void 0:n.data:n});c(this,"login",async t=>await u(o(this,d).call(this,"login"),!1,{method:"POST",headers:{...o(this,f)},body:JSON.stringify(t)},!0,!1,!0,!1));c(this,"logout",async t=>await u(o(this,d).call(this,"logout"),!1,{method:"GET",headers:{...o(this,f),token:t}},!0,!1,!0,!1));c(this,"verifyOtp",async(t,e)=>await u(o(this,d).call(this,"check-otp"),!1,{method:"POST",headers:{...o(this,f),token:e},body:JSON.stringify(t)},!0,!1,!0,!1));c(this,"resendOtp",async t=>await u(o(this,d).call(this,"resend"),!1,{method:"GET",headers:{...o(this,f),token:t}},!0,!1,!0,!1));c(this,"checkValidToken",async t=>await u(o(this,d).call(this,"check-token"),!1,{method:"POST",headers:{...o(this,f),token:t}},!0,!1,!0,!1));c(this,"processTrademark",async(t,e)=>await u(o(this,d).call(this,"fetch-tm"),!1,{method:"POST",headers:{...o(this,f),token:e},body:JSON.stringify(t)},!0));c(this,"processForHighlightTrademark",async(t,e,r)=>{const n=await u(o(this,d).call(this,"fetch-tm-listing-words"),!1,{method:"POST",headers:{...o(this,f),token:e},body:JSON.stringify(t)},!0);return(n==null?void 0:n.status)==="success"?n==null?void 0:n.result:n});c(this,"processForSaveExcludeBrands",async(t,e)=>await u(o(this,d).call(this,"save-brand"),!1,{method:"POST",headers:{...o(this,f),token:e},body:JSON.stringify(t)},!0));c(this,"processForExcludeBrandsList",async(t,e)=>await u(o(this,d).call(this,"fetch-brands-list"),!1,{method:"POST",headers:{...o(this,f),token:e},body:JSON.stringify(t)},!0));c(this,"processForUpdatedExcludeBrand",async(t,e)=>{const{market_place:r,brand_name:n,id:l}=t;return await u(o(this,d).call(this,`update-brand/${l}`),!1,{method:"POST",headers:{...o(this,f),token:e},body:JSON.stringify({market_place:r,brand_name:n})},!0)});c(this,"processForDeleteExcludeBrand",async(t,e)=>await u(o(this,d).call(this,`delete-brand/${t}`),!1,{method:"DELETE",headers:{...o(this,f),token:e}},!0));c(this,"processForSaveFilter",async(t,e)=>await u(o(this,d).call(this,"save-filter"),!1,{method:"POST",headers:{...o(this,f),token:e},body:JSON.stringify(t)},!0));c(this,"processForSavedFilterList",async(t,e)=>await u(o(this,d).call(this,`fetch-filter?filter_type=${t}`),!1,{method:"GET",headers:{...o(this,f),token:e}},!0));c(this,"processForUpdateFilter",async(t,e)=>{const{data:r,id:n}=t??{};return await u(o(this,d).call(this,`update-filter/${n}`),!1,{method:"POST",headers:{...o(this,f),token:e},body:JSON.stringify(r)},!0)});c(this,"processForDeleteFilter",async(t,e)=>await u(o(this,d).call(this,"delete-filter"),!1,{method:"POST",headers:{...o(this,f),token:e},body:JSON.stringify({id:t})},!0));c(this,"processForSaveFavoriteGroup",async(t,e)=>await u(o(this,d).call(this,"save-favorite-group"),!1,{method:"POST",headers:{...o(this,f),token:e},body:JSON.stringify(t)},!0));c(this,"processForUpdateFavoriteGroup",async(t,e)=>{const{data:r,id:n}=t??{};return await u(o(this,d).call(this,`update-favorite-group/${n}`),!1,{method:"POST",headers:{...o(this,f),token:e},body:JSON.stringify(r)},!0)});c(this,"processForDeleteFavoriteGroup",async(t,e)=>await u(o(this,d).call(this,"delete-favorite-group"),!1,{method:"POST",headers:{...o(this,f),token:e},body:JSON.stringify({id:t})},!0));c(this,"processForSavedFavoriteGroupList",async t=>await u(o(this,d).call(this,"fetch-favorite-group"),!1,{method:"GET",headers:{...o(this,f),token:t}},!0));c(this,"processForSavedFavoriteList",async t=>await u(o(this,d).call(this,"fetch-favorite-list"),!1,{method:"GET",headers:{...o(this,f),token:t}},!0));c(this,"processForAddToFavoriteList",async(t,e)=>await u(o(this,d).call(this,"mark-favorite"),!1,{method:"POST",headers:{...o(this,f),token:e},body:JSON.stringify(t)},!0));c(this,"processSaveScrapedData",async t=>{const e=await u(o(this,T).call(this,"extension-save-product"),!1,{method:"POST",headers:{...o(this,f),"auth-token":"0Ujqh6Nb6ye0r4jzM7aJe2j2DkDwIiKex1KLaKOCUitvA9wuPt"},body:JSON.stringify(t)});return e!=null&&e.status?{success:!0}:{success:!1}});c(this,"generateProductSearchUrl",async(t,e)=>await u(o(this,d).call(this,"fetch-mba-url"),!1,{method:"POST",headers:{token:e},body:JSON.stringify(t)},!0));c(this,"processIndexKeywords",async t=>{const e=t.map(n=>o(this,w).call(this,n));return await Promise.all(e)});E(this,I,(t,e,r)=>{const n=He[e]??{},l={0:{prefix:t,postfix:""},1:{prefix:" ",postfix:t},2:{prefix:t+" ",postfix:""},3:{prefix:" ",postfix:t+" "},4:{prefix:t+" ",postfix:`${n.for} `},5:{prefix:t+" ",postfix:`${n.and} `},6:{prefix:t+" ",postfix:`${n.with} `}};if(r!=null&&r.length)for(const S of r)delete l[S];return Object.values(l)});E(this,A,async(t,e,r,n,l="aps")=>{var y;const S=r.split(".").at(-1)||"us",p=await u(o(this,N).call(this,r,`mid=${n}&session-id=${je[S]}&prefix=${t}&suffix=${e}&alias=${l}`),!1,{},!0,!1,!1);return((y=p==null?void 0:p.suggestions)==null?void 0:y.map(g=>g==null?void 0:g.value).join(","))||""});E(this,D,async(t,e,r,n,l="aps",S)=>{const p=o(this,I).call(this,t,e,S),y=[],g=p.length;for(let a=0;a<g;a++)y.push(o(this,A).call(this,p[a].prefix,p[a].postfix,r,n,l));return Promise.all(y)});E(this,x,async(t,e)=>{var n;const r=await u(o(this,C).call(this,`?q=${t}&client=chrome&_=${Date.now()}&hl=${e}&gl=${e}`),!1,{},!0,!1,!1);return((n=r==null?void 0:r[1])==null?void 0:n.join(","))||""});E(this,P,t=>t.replace(/,+/g,",").replace(/^,|,$/g,""));c(this,"processSuggestions",async(t,e,r)=>{try{const n=t.length>10?10:t.length,l=o(this,k)[e],S=await o(this,D).call(this,t[0],e,r,l),p=t.slice(1,n).map(a=>o(this,A).call(this,a,"",r,l)),y=t.slice(0,n).map(a=>o(this,x).call(this,a,e)),g=await Promise.all(p),_=await Promise.all(y);return{googleSuggestions:o(this,P).call(this,_.join(",")),suggestedKeywords:o(this,P).call(this,g.join(",")),searchSuggestions:o(this,P).call(this,[...new Set(S.join(",").split(","))].join(","))}}catch(n){return console.log("🚀 Error occurred during process search suggestions:",n),{googleSuggestions:"",suggestedKeywords:"",searchSuggestions:""}}});c(this,"processAmazonKeywordSuggestions",async t=>{const{keyword:e,domain:r,host:n,alias:l}=t;try{const S=o(this,k)[r],[p="",y="",...g]=await o(this,D).call(this,e,r,n,S,l,[2,3]);return{keywordPrefixSuggestions:p,keywordPostfixSuggestions:y,restKeywordsSuggestions:[...new Set(g.join(",").replace(/(^,|,+(?=,)|,$)/g,"").split(","))].join(",")}}catch(S){return console.log("🚀 Error occurred during process amazon keyword suggestions in product search:",S),{keywordPrefixSuggestions:"",keywordPostfixSuggestions:"",restKeywordsSuggestions:""}}});c(this,"fetchMerchPageConfigs",async t=>{var n;const e=(n=await o(this,O).call(this,["token"]))==null?void 0:n.token;return await u(o(this,T).call(this,`merch-page-selector?auth_token=${e}&token=${t}`),!1,{method:"GET",headers:o(this,f)},!1,!0)});c(this,"fetchRedbubbleKeywords",async(t,e)=>await u(o(this,d).call(this,"get-redbubble-trend-record"),!1,{method:"POST",headers:{...o(this,f),token:e},body:JSON.stringify(t)},!0));c(this,"base64ToFile",async(t,e,r="image")=>{try{const n=await fetch(t);if(!n.ok)return Ne(n),null;const l=await n.blob();return new File([l],r,{type:e})}catch{return null}});c(this,"generateAiListing",async(t,e)=>{var y,g;const{image:r}=t;let n="ai-listing-from-keyword";const l={...o(this,f),token:e};let S="";if(r!=null&&r[0]){l==null||delete l["Content-Type"],S=new FormData;const[_,a,We,Pe]=r,b=await this.base64ToFile(_,a,Pe);b&&S.append("image",b),S.append("title",t.title),S.append("market_place",t.market_place.toString()),S.append("product_type",t.product_type),(y=t.normalTags)!=null&&y.length&&S.append("normalTags",JSON.stringify(t.normalTags)),(g=t.primaryTags)!=null&&g.length&&S.append("primaryTags",JSON.stringify(t.primaryTags)),t.previous_response&&S.append("previous_response",JSON.stringify(t.previous_response)),n="ai-listing-from-design"}else S=JSON.stringify(t);return await u(o(this,d).call(this,n),!1,{method:"POST",headers:l,body:S},!0,!1,!0,!0,1,!1)});c(this,"translateRawText",async(t,e)=>await u(o(this,T).call(this,`translate-text?language=${t.lang}&auth_token=0Ujqh6Nb6ye0r4jzM7aJe2j2DkDwIiKex1KLaKOCUitvA9wuPt`),!1,{method:"POST",headers:{...o(this,f)},body:JSON.stringify(t.text)},!1,!0,!1,!1));c(this,"fetchBannedKeywords",async t=>await u(o(this,d).call(this,"fetch-banned-keywords"),!1,{method:"GET",headers:{token:t}},!0));c(this,"upsertBannedKeywords",async(t,e)=>await u(o(this,d).call(this,"add-banned-keywords"),!1,{method:"POST",headers:{token:e},body:JSON.stringify(t)},!0));c(this,"deleteBannedKeywords",async(t,e)=>await u(o(this,d).call(this,"delete-banned-keywords"),!1,{method:"DELETE",headers:{token:e},body:JSON.stringify(t)},!0));c(this,"disableBannedKeywords",async(t,e)=>await u(o(this,d).call(this,"disable-banned-tag"),!1,{method:"POST",headers:{token:e},body:JSON.stringify(t)},!0));c(this,"updateSingleBannedKeyword",async(t,e)=>await u(o(this,d).call(this,"edit-banned-keyword"),!1,{method:"POST",headers:{token:e},body:JSON.stringify(t)},!0));c(this,"deleteSingleBannedKeyword",async(t,e)=>await u(o(this,d).call(this,"delete-single-banned-keyword"),!1,{method:"DELETE",headers:{token:e},body:JSON.stringify(t)},!0));c(this,"fetchBannedKeywordsByMarketPlace",async(t,e)=>await u(o(this,d).call(this,`get-banned-keyword?marketplace=${t.marketplace}`),!1,{method:"GET",headers:{token:e}},!0));c(this,"fetchPlanSummary",async t=>await u(o(this,d).call(this,"credit-summary"),!1,{method:"GET",headers:{token:t}},!0));c(this,"deductCredits",async(t,e)=>{const r=t.showRemainingCredits?`deduct-credit?module=${t.module}&useCredit=1`:`deduct-credit?module=${t.module}`;return await u(o(this,d).call(this,r),!1,{method:"GET",headers:{token:e}},!0)});c(this,"fetchTemplates",async(t,e)=>await u(o(this,d).call(this,`user-extension-templates${t!=null&&t.category?`?category=${t.category}`:""}`),!1,{method:"GET",headers:{token:e}},!0));c(this,"saveTemplate",async(t,e)=>await u(o(this,d).call(this,"user-extension-templates"),!1,{method:"POST",headers:{token:e},body:JSON.stringify(t)},!0));c(this,"updateTemplate",async(t,e)=>await u(o(this,d).call(this,`user-extension-templates/${t.id}?_method=PUT`),!1,{method:"POST",headers:{token:e},body:JSON.stringify(t.data)},!0));c(this,"deleteTemplate",async(t,e)=>await u(o(this,d).call(this,`user-extension-templates/${t}`),!1,{method:"DELETE",headers:{token:e}},!0));c(this,"fetchTemplateById",async(t,e)=>await u(o(this,d).call(this,`user-extension-templates/fetch-single/${t}`),!1,{method:"GET",headers:{token:e}},!0));c(this,"processImportTemplates",async(t,e)=>await u(o(this,d).call(this,"user-extension-templates/import"),!1,{method:"POST",headers:{token:e},body:JSON.stringify(t)},!0))}}T=new WeakMap,d=new WeakMap,C=new WeakMap,N=new WeakMap,k=new WeakMap,f=new WeakMap,O=new WeakMap,w=new WeakMap,I=new WeakMap,A=new WeakMap,D=new WeakMap,x=new WeakMap,P=new WeakMap;const i=new Re,we=!1;let Ye="{}";m.runtime.onInstalled.addListener(function(s){var t,e;try{m.contextMenus.removeAll(()=>{Object.keys(_e).forEach(n=>{m.contextMenus.create(_e[n],()=>{})})})}catch{console.log("🚀 ~ error occurred while creating context menus:",(e=(t=m)==null?void 0:t.runtime)==null?void 0:e.lastError)}s.reason==="install"&&m.storage.local.clear(function(){})});m.storage.onChanged.addListener((s,t)=>{for(let[e,{oldValue:r,newValue:n}]of Object.entries(s))e==="token"&&n&&i.checkValidToken(Ie()).then(l=>{var S;((S=l==null?void 0:l.result)==null?void 0:S.otp_status)!==!1&&m.storage.local.remove(["token"])})});m.contextMenus.onClicked.addListener((s,t)=>{try{s.menuItemId==="product-search"&&(t!=null&&t.id)?m.tabs.sendMessage(t.id,{type:xe,payload:s.selectionText}):s.menuItemId==="check-trademark"&&(t!=null&&t.id)&&m.tabs.sendMessage(t.id,{type:Ke,payload:s.selectionText})}catch{}});const h=(s,t,e)=>{var r,n,l;t({success:!1,message:((l=(n=(r=m)==null?void 0:r.runtime)==null?void 0:n.lastError)==null?void 0:l.message)||(s==null?void 0:s.message)})};m.runtime.onMessage.addListener((s,t,e)=>{try{const r=(s==null?void 0:s.tokenKey)||"";switch(s.type){case"LOG_DATA":console.log("Entered",s.payload),e({success:!0});break;case Ve:return i.login((s==null?void 0:s.payload)||{}).then(e),!0;case Je:return i.logout(r).then(e),!0;case Me:return i.checkValidToken(r).then(e),!0;case $e:return i.verifyOtp((s==null?void 0:s.payload)||{},r).then(e),!0;case Ge:return i.resendOtp(r).then(e),!0;case Oe:const{keywords:n,domain:l,host:S}=(s==null?void 0:s.payload)||{};return i.processSuggestions(n,l,S).then(e).catch(a=>h(a,e,Oe)),!0;case Te:return i.processAmazonKeywordSuggestions((s==null?void 0:s.payload)||{}).then(e).catch(a=>h(a,e,Te)),!0;case me:const{marketPlace:p,salesEstimationPayload:y,key:g}=(s==null?void 0:s.payload)||{};return i.processForSalesEstimation(p,y,g).then(e).catch(a=>h(a,e,me)),!0;case Ee:return i.processForFieldsConfigurations(r).then(e).catch(a=>h(a,e,Ee)),!0;case ge:return i.fetchMerchPageConfigs(r).then(e).catch(a=>h(a,e,ge)),!0;case pe:return i.processSaveScrapedData(s.payload).then(e).catch(a=>h(a,e,pe)),!0;case ye:return i.generateProductSearchUrl((s==null?void 0:s.payload)||{},r).then(e).catch(a=>h(a,e,ye)),!0;case fe:return i.processProductHistory((s==null?void 0:s.payload)||{}).then(e).catch(a=>{h(a,e,fe)}),!0;case Se:return i.processIndexKeywords((s==null?void 0:s.payload)||[]).then(e).catch(a=>h(a,e,Se)),!0;case de:return i.processTrademark(s.payload,r).then(e).catch(a=>h(a,e,de)),!0;case le:return i.processForHighlightTrademark(s.payload,r,s.shouldUseCredits).then(a=>{e(a)}).catch(a=>h(a,e,le)),!0;case he:return i.processForSaveExcludeBrands(s.payload,r).then(e).catch(a=>h(a,e,he)),!0;case ue:return i.processForExcludeBrandsList(s.payload,r).then(e).catch(a=>h(a,e,ue)),!0;case ie:return i.processForUpdatedExcludeBrand(s.payload,r).then(e).catch(a=>h(a,e,ie)),!0;case ce:return i.processForDeleteExcludeBrand(s.payload,r).then(e).catch(a=>h(a,e,ce)),!0;case ne:return i.processForSaveFilter(s.payload,r).then(e).catch(a=>h(a,e,ne)),!0;case oe:return i.processForSavedFilterList(s.payload,r).then(e).catch(a=>h(a,e,oe)),!0;case se:return i.processForUpdateFilter(s.payload,r).then(e).catch(a=>h(a,e,se)),!0;case ae:return i.processForDeleteFilter(s.payload,r).then(e).catch(a=>h(a,e,ae)),!0;case re:return i.processForSavedFavoriteList(r).then(e).catch(a=>h(a,e,re)),!0;case te:return i.processForAddToFavoriteList(s.payload,r).then(e).catch(a=>h(a,e,te)),!0;case ee:return i.processForSavedFavoriteGroupList(r).then(e).catch(a=>h(a,e,ee)),!0;case Q:return i.processForSaveFavoriteGroup(s.payload,r).then(e).catch(a=>h(a,e,Q)),!0;case Z:return i.processForUpdateFavoriteGroup(s.payload,r).then(e).catch(a=>h(a,e,Z)),!0;case q:return i.processForDeleteFavoriteGroup(s.payload,r).then(e).catch(a=>h(a,e,q)),!0;case K:return i.fetchRedbubbleKeywords(s.payload,r).then(e).catch(a=>h(a,e,K)),!0;case Be:return i.generateAiListing(s.payload,r).then(e).catch(a=>h(a,e,K)),!0;case X:return i.translateRawText(s.payload,r).then(e).catch(a=>h(a,e,X)),!0;case W:return i.fetchBannedKeywords(r).then(e).catch(a=>h(a,e,W)),!0;case Y:return i.fetchBannedKeywordsByMarketPlace(s.payload,r).then(e).catch(a=>h(a,e,Y)),!0;case R:return i.deleteBannedKeywords(s.payload,r).then(e).catch(a=>h(a,e,R)),!0;case z:return i.upsertBannedKeywords(s.payload,r).then(e).catch(a=>h(a,e,z)),!0;case j:return i.disableBannedKeywords(s.payload,r).then(e).catch(a=>h(a,e,j)),!0;case H:return i.updateSingleBannedKeyword(s.payload,r).then(e).catch(a=>h(a,e,H)),!0;case V:return i.deleteSingleBannedKeyword(s.payload,r).then(e).catch(a=>h(a,e,V)),!0;case J:return i.fetchTemplates(s.payload,r).then(e).catch(a=>h(a,e,J)),!0;case M:return i.fetchTemplateById(s.payload,r).then(e).catch(a=>h(a,e,M)),!0;case $:return i.processImportTemplates(s.payload,r).then(e).catch(a=>h(a,e,$)),!0;case F:return i.saveTemplate(s.payload,r).then(e).catch(a=>h(a,e,F)),!0;case Ue:return i.updateTemplate(s.payload,r).then(e).catch(a=>h(a,e,F)),!0;case ve:return i.deleteTemplate(s.payload,r).then(e).catch(a=>h(a,e,F)),!0;case G:return i.fetchPlanSummary(r).then(e).catch(a=>h(a,e,G)),!0;case B:return i.deductCredits(s.payload,r).then(e).catch(a=>h(a,e,B)),!0;case U:return i.processDeletedProducts(s==null?void 0:s.payload,r).then(e).catch(a=>h(a,e,U)),!0;case v:const{asins:_}=(s==null?void 0:s.payload)||{};return i.processAsinsInBatch(_).then(e).catch(a=>h(a,e,v)),!0;case L:return i.processSingleAsin((s==null?void 0:s.payload)||{}).then(e).catch(a=>h(a,e,L)),!0;case Le:e({success:!0});break;case be:e(Ye);break;default:break}}catch(r){h(r,e)}});
