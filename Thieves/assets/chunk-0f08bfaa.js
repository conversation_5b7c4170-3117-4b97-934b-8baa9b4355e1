import{c as Pa,a$ as ye,aU as se,b0 as De,b1 as Ze,b2 as jc,b3 as Sc,b4 as Ec,e as at,J as ct,j as s,az as _c,o as fe,b5 as Ac,b6 as kc,af as sn,ae as Tn,B as on,n as Jn,C as ke,a as Tt,u as Ji,k as gt,K as Lt,b7 as jn,b8 as ln,b9 as cn,T as qe,I as $t,m as Gt,M as pt,F as bt,am as At,ba as dn,aH as Ut,x as Pc,bb as Nc,O as vr,Q as ws,ac as Ic,bc as Oc,bd as Kt,a3 as el,_ as tl,be as Mc,W as wr,bf as Ot,bg as Cs,bh as Dc,L as Ts,a0 as jo,bi as So,ak as Lc,bj as $c,g as Fc,l as Bc,bk as Jr,bl as es,bm as ts,bn as Eo,bo as _o,bp as Hc,bq as Rc,br as js,z as nl,t as Ao,bs as Uc,bt as Vc,bu as zc,bv as Kc,bw as qc,bx as Wc,by as Gc,bz as Yc,b as al,bA as ko,aG as Xc,i as Qc,bB as Po,aV as Zc,aX as Jc,bC as Ss,aY as ed,y as td,a_ as nd}from"./chunk-d1fd3088.js";import{r as b,a as ft,o as ad,q as rd,s as sd,t as od,u as id,v as ld,w as cd,x as dd,y as ud,z as pd,A as fd,B as hd,R as P,P as xe}from"./chunk-58ddc683.js";import{o as md,p as No,r as gd,s as bd,t as xd,v as Pt,w as un,D as Yt,y as yd,z as Es,A as vd,S as na,q as wd,f as Cd,E as Td,d as rl,T as jd,H as _s,I as Sd,J as Ed,K as sl,L as Io,P as _d,U as Ad,m as kd,n as Pd,C as Nd,M as ns}from"./chunk-3f92e3e3.js";import{ap as Id,K as Ge,aq as oa,ar as ol,as,at as Me,au as Cr,av as nt,aw as tt,ax as Et,ay as Od,az as il,L as rs,aA as Ta,aB as Tr,aC as wn,aD as Md,aE as As,aF as Ha,M as Oo,N as Mo,u as rt,aG as Dd,aH as dt,aI as Ld,aJ as $d,aK as Fd,aL as Bd,aM as Hd,aN as Rd,aO as Ud,aP as Vd,aQ as zd,aR as Kd,aS as qd,aT as Wd,aU as Gd,aV as Yd,aW as Xd,m as Qd,aX as ks,aY as et,aZ as Ra,a_ as Sn,a$ as Ua,b0 as Va,b1 as ll,b2 as qt,b3 as cl,b4 as Zd,b5 as za,b6 as Jd,b7 as ss,b8 as eu,b9 as tu,ba as nu,bb as wt,bc as au,bd as ja,be as Bt,bf as ru,bg as Ka,bh as $r,bi as dl,bj as su,bk as Do,D as Lo,U as ea,bl as ou,bm as $o,bn as ul,bo as iu,bp as Mn,bq as os,br as is,bs as or,bt as lu,bu as cu,bv as qa,bw as Ps,bx as Fr,C as sa,q as pl,by as du,bz as uu,bA as pu,bB as fu,bC as ya,G as hu,bD as mu,bE as Br,bF as fa,bG as gu,bH as bu,bI as ls,bJ as xu,bK as cs,bL as lr,bM as yu,bN as vu,a7 as wu,bO as Cu,bP as Tu,bQ as ju,bR as Ns,x as Su,bS as Eu,ah as _u,bT as Au,ak as ku,bU as Pu,am as Nu,an as Iu,ao as Ou}from"./chunk-4fe4df4f.js";var cr=globalThis&&globalThis.__assign||function(){return cr=Object.assign||function(e){for(var t,a=1,n=arguments.length;a<n;a++){t=arguments[a];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},cr.apply(this,arguments)},Mu=globalThis&&globalThis.__rest||function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]]);return a},Du=md("CircleLoader","0% {transform: rotate(0deg)} 50% {transform: rotate(180deg)} 100% {transform: rotate(360deg)}","circle");function Lu(e){var t=e.loading,a=t===void 0?!0:t,n=e.color,r=n===void 0?"#000000":n,o=e.speedMultiplier,l=o===void 0?1:o,i=e.cssOverride,d=i===void 0?{}:i,f=e.size,c=f===void 0?50:f,p=Mu(e,["loading","color","speedMultiplier","cssOverride","size"]),u=cr({display:"inherit",position:"relative",width:No(c),height:No(c)},d),h=function(g){var v=gd(c),C=v.value,j=v.unit;return{position:"absolute",height:"".concat(C*(1-g/10)).concat(j),width:"".concat(C*(1-g/10)).concat(j),borderTop:"1px solid ".concat(r),borderBottom:"none",borderLeft:"1px solid ".concat(r),borderRight:"none",borderRadius:"100%",transition:"2s",top:"".concat(g*.7*2.5,"%"),left:"".concat(g*.35*2.5,"%"),animation:"".concat(Du," ").concat(1/l,"s ").concat(g*.2/l,"s infinite linear")}};return a?b.createElement("span",cr({style:u},p),b.createElement("span",{style:h(0)}),b.createElement("span",{style:h(1)}),b.createElement("span",{style:h(2)}),b.createElement("span",{style:h(3)}),b.createElement("span",{style:h(4)})):null}const $u=(e,t)=>{const[a,n]=b.useState(e);return b.useEffect(()=>{const r=e==null?void 0:e.toString().trim();if(!r){n(r);return}const o=setTimeout(()=>{n(r)},t);return()=>{clearTimeout(o)}},[e,t]),a},pn=$u;const jr=Pa(e=>({merchXpaths:{},constants:{},setMerchFieldsConfigs:t=>e(()=>({merchXpaths:t.xPaths,constants:t.constants}))})),Fu={us:!0,de:!1,fr:!1,it:!1,es:!1,jp:!1},Bu={us:!0,de:!0,fr:!0,it:!0,es:!0,jp:!0},fl=Pa(e=>({accordionConfigs:{...Fu},visibilityConfigs:{...Bu},isLanguageExpanded:!0,updateIsLanguageExpanded:t=>e(()=>({isLanguageExpanded:t})),updateVisibilityConfigs:t=>{e(a=>{const n={...a.accordionConfigs},r={...a.visibilityConfigs},o=Object.keys(a.visibilityConfigs).reduce((i,d)=>{const f=t[d];return i[d]=!!f,f&&!r[d]?n[d]=!0:f||(n[d]=!1),i},{}),l=Object.values(n).some(Boolean);return{visibilityConfigs:o,accordionConfigs:n,isLanguageExpanded:l}})},updateExpandConfigs:(t,a)=>{e(n=>{const r={...n.accordionConfigs,[a]:!t},o=Object.values(r).some(Boolean);return{accordionConfigs:r,isLanguageExpanded:o}})},toggleExpandConfigs:(t,a)=>{e(n=>{const r=Object.keys(n.accordionConfigs).reduce((l,i)=>{const f=typeof a[i]=="boolean";return l[i]=f?!t:!1,l},{}),o=Object.values(r).some(Boolean);return{accordionConfigs:r,isLanguageExpanded:o}})}}));var Hu=Ku,jt=[],Ru=typeof Uint8Array<"u"?Uint8Array:Array,Fo="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(var Wa=0,Uu=Fo.length;Wa<Uu;++Wa)jt[Fo.charCodeAt(Wa)]=Wa;jt["-".charCodeAt(0)]=62;jt["_".charCodeAt(0)]=63;function Vu(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var a=e.indexOf("=");a===-1&&(a=t);var n=a===t?0:4-a%4;return[a,n]}function zu(e,t,a){return(t+a)*3/4-a}function Ku(e){var t,a=Vu(e),n=a[0],r=a[1],o=new Ru(zu(e,n,r)),l=0,i=r>0?n-4:n,d;for(d=0;d<i;d+=4)t=jt[e.charCodeAt(d)]<<18|jt[e.charCodeAt(d+1)]<<12|jt[e.charCodeAt(d+2)]<<6|jt[e.charCodeAt(d+3)],o[l++]=t>>16&255,o[l++]=t>>8&255,o[l++]=t&255;return r===2&&(t=jt[e.charCodeAt(d)]<<2|jt[e.charCodeAt(d+1)]>>4,o[l++]=t&255),r===1&&(t=jt[e.charCodeAt(d)]<<10|jt[e.charCodeAt(d+1)]<<4|jt[e.charCodeAt(d+2)]>>2,o[l++]=t>>8&255,o[l++]=t&255),o}const Bo={},aa=(e,t="input")=>{try{e.dispatchEvent(new KeyboardEvent(t,{bubbles:!0,cancelable:!0}))}catch{}},Ho=(e,t="input")=>{try{e.dispatchEvent(new Event(t,{bubbles:!0,cancelable:!0}))}catch{}},Is=e=>{let t="";return e==null||e.classList.forEach(a=>{a.startsWith("checkbox-")&&(t=a.slice(9))}),t},Sr=e=>{let t="";return e==null||e.classList.forEach(a=>{a.endsWith("-container")&&(t=a.slice(0,-10))}),t},Mt=e=>{const t=document.querySelector(e);return Sr(t)},Os=(e,t)=>{document.querySelectorAll(e).forEach(a=>{var r;const n=Sr(a);(r=document.querySelector(`.product-card .${n}${t}`))==null||r.click()})},Ms=e=>{const t=[];return document.querySelectorAll(e).forEach(a=>{t.push(a.id.replace("-card",""))}),t},qu=e=>{const t=[];return document.querySelectorAll(e).forEach(a=>{const n=ye("input",a);n!=null&&n.checked&&t.push(a.classList[0])}),t},hl=(e,t,a)=>{const n=new Set(e);document.querySelectorAll(t).forEach(r=>{var d;const o=r.classList[0],l=r.querySelector("i"),i=(d=l==null?void 0:l.classList)==null?void 0:d.contains(a);l&&o&&(n.has(o)&&!i||!n.has(o)&&i)&&l.click()})},Ds=e=>{const t=[];return document.querySelectorAll(e).forEach(a=>{var n;if(a&&(n=a.querySelector("input"))!=null&&n.checked){const r=Is(a);t.push(r)}}),t},ml=e=>{const t=[],a=ye(e);if(a){const n=a.style.backgroundColor;n&&t.push(n)}return t},_a=e=>!!Id[e],Wu=(e,t)=>t.length?t.reduce((a,n)=>(e.get(n)??1/0)<(e.get(a)??1/0)?n:a,t[0]):null,ds=(e,t,a)=>{const n=new Set(e??[]);let r=!1;if(document.querySelectorAll(t).forEach(o=>{var f;const l=(f=o.querySelector("input"))==null?void 0:f.checked,i=Is(o);if(!i)return;const d=!l&&n.has(i);d||l&&!n.has(i)?(!r&&d&&(r=!0),o==null||o.click()):l&&(r=!0)}),!r&&a){let o=document.querySelector(`${t}.checkbox-${a}`);o||(o=document.querySelector(t)),o==null||o.click()}},gl=e=>{const t=[];try{return document.querySelectorAll(e).forEach(a=>{const n=a.querySelector("input");n&&n.checked&&t.push(a.classList[0].replace("-checkbox",""))}),t}catch{return[]}},Gu=async(e,t,a,n,r)=>{var i,d,f;await mt(t,1e3);const o={};let l=(i=ye(e))==null?void 0:i.getAttribute("aria-valuenow");return(!l||!ye(r))&&a&&((d=ye(n))==null||d.click(),await mt(t,500,500,1e5,!0),l=(f=ye(e))==null?void 0:f.getAttribute("aria-valuenow")),l&&(o.percent=Math.floor(+l||100)),o},Yu=(e,t)=>{var l;const a=(l=ye(e))==null?void 0:l.classList.contains("active"),n=()=>!!ye(t),r=a?tt.back:tt.front;return(n()?r:r===tt.back?tt.front:tt.back).toLowerCase()},bl=(e,t)=>{const a=new Set(e);document.querySelectorAll(t).forEach(n=>{var i;const r=n.querySelector("input"),o=r==null?void 0:r.checked,l=(i=n.classList[0])==null?void 0:i.replace("-checkbox","");l&&(!o&&a.has(l)||o&&!a.has(l))&&(n==null||n.click())})},xl=async(e,t,a)=>{try{const n=ye(e);if(n){n.click();const r=await Ze(t,200,2e3);r.value=a.replace("#",""),r.dispatchEvent(new KeyboardEvent("keyup",{bubbles:!0,cancelable:!0,key:"Enter"})),await Me(200),n.click()}}catch{}},Ro=(e,t,a,n,r,o)=>{if(!n||n.textContent!==t){const l=document.createElement("span");return l.className=e,l.setAttribute("data-tooltip-id",o),l.textContent=t,l.onclick=()=>a(r),l}return null},ra=(e,t)=>{var a,n,r;return(((r=(n=(a=e==null?void 0:e.closest(t))==null?void 0:a.parentElement)==null?void 0:n.querySelector("strong"))==null?void 0:r.textContent)||oa.us).trim()},yn=(e,t,a,n,r)=>{var h,g,v,C;const o=Cr[r??".com"].toUpperCase(),l=o==="JP",i=l?100:1;let d=parseFloat(t||"0")+a*i;const f=((g=(h=e[n])==null?void 0:h.min)==null?void 0:g[o])??0,c=((C=(v=e[n])==null?void 0:v.max)==null?void 0:C[o])??0;let p=!1,u=!1;return d>=c?(d=c,u=!0):d<=f&&(d=f,p=!0),{price:d.toFixed(l?0:2),isMinLimitReached:p,isMaxLimitReached:u}},Xu=(e,t,a,n)=>{var g,v,C;const r=Cr[a??".com"].toUpperCase(),o=r==="JP",l=e[n],i=((g=l==null?void 0:l.increment)==null?void 0:g[r])??1,d=((v=l==null?void 0:l.min_royalty)==null?void 0:v[r])??0,f=Math.floor((t-d)/i),p=(((C=l==null?void 0:l.min)==null?void 0:C[r])??0)*(o?.01:1)+f,u=t-(d+f*i);return((p+u/i)*(o?100:1)).toFixed(o?0:2)},Uo=async(e,t,a,n,r,o)=>{var m,T,E;const l=ra(n,a),i=Od[l],d=l===oa.jp,f=+(o*(d?100:1)).toFixed(d?0:2),p=((m=t[r])==null?void 0:m[i])||(d?1:.01),u=Xu(e,+o,l,r),{price:h,isMaxLimitReached:g,isMinLimitReached:v}=yn(e,u,0,r,l);if(n.value=h,Ho(n),g||v)return;await Me(1e3);const C=`.${r}-${i}-royalty`;if(!await(async(O=7)=>{var A,_;for(let D=0;D<O;D++){const x=(_=(A=ye(C))==null?void 0:A.textContent)==null?void 0:_.trim();if((x==null?void 0:x.split(" ")[1])!==void 0)return!0;await Me(500)}return!1})())return;const N=()=>{var A,_,D;return+(((D=(_=(A=ye(C))==null?void 0:A.textContent)==null?void 0:_.trim())==null?void 0:D.split(" ")[1])||"0")};let M=N();if(f===M)return;let I=0,F=+h,S=0,R=0;const w=d?30:25;for(;S<w&&(M=N(),M!==f);){if(M===I){if(R++,R<=2){await Me(1e3);continue}}else R=0,I=M;const O=M<f?p:-p;F=Math.round((F+O)*100)/100;const{price:A,isMaxLimitReached:_,isMinLimitReached:D}=yn(e,F.toString(),0,r,l);if(_||D)break;const x=(E=(T=ye(C))==null?void 0:T.closest(".form-row"))==null?void 0:E.querySelector("input");if(!x){F-=O;continue}x.value=A,Ho(x),await Me(1500),S++}},Qu=(e,t)=>{const a={};return document==null||document.querySelectorAll(e).forEach(n=>{const r=ra(n,t),o=Cr[r];a[o]=n.value}),a},yl=(e,t,a,n,r,o=!1)=>{const l=o?{}:t.reduce((i,d,f)=>{const c=il[f+1];return!c||!d||(i[c]=d),i},{});document==null||document.querySelectorAll(n).forEach((i,d)=>{const f=ra(i,r),c=Cr[f];if(o&&(l[c]=t[d]),!l[c])return;const{price:p}=yn(e,l[c],0,a,f);i.value=p,aa(i)})},Zu=e=>{const t=[];try{return document.querySelectorAll(e).forEach(a=>{t.push(a.value)}),t}catch{return[]}},Ju=e=>{const t={};for(const a of e){const[n,r]=a.split("-");!n||!r||(t[n]||(t[n]=new Set),t[n].add(r.toLowerCase().replace(/gb/g,"uk")))}return Object.fromEntries(Object.entries(t).map(([a,n])=>[a,Array.from(n)]))},Ls=(e,t)=>{const a=new DataTransfer,n=new File([t],"final_design.png",{type:"image/png"});a.items.add(n),e.files=a.files,aa(e,"change")},vl=e=>{var o,l;if(!(e!=null&&e.startsWith("data:image/")))return null;const[t,a]=e.split(","),n=(l=(o=t==null?void 0:t.match)==null?void 0:o.call(t,/:(.*?);/))==null?void 0:l[1],r=Hu(a);return new Blob([r],{type:n})},us=(e,t)=>{const a={};return document.querySelectorAll(e).forEach(n=>{var l;const r=n.querySelector(t),o=(l=r==null?void 0:r.getAttribute("id"))==null?void 0:l.split("-")[0];if(o){const i=ol[o];a[i]=!0}n.style.display="none"}),a},wl=async(e,t)=>{var r;const a={};let n=document.querySelectorAll(e);for(const o of n){const l=o.querySelector(t),i=(r=l==null?void 0:l.getAttribute("id"))==null?void 0:r.split("-")[0];if(i){const d=ol[i],f={};for(const c in as){const p=as[c],u=await Ze(`#${i} #${p}`,500,1e4);if(!u)continue;const h=u.value;f[c]=h}a[d]=f}}return n=null,a},ep=(e,t)=>{const a=new RegExp("\\b"+t+"\\b","gi"),n=e.match(a);return n?n.length:0},tp=(e,t,a,n,r,o)=>{try{if(!o)return{topFocusKeywords:new Map([["Beauty",20],["Fashion",15],["Shoes",12],["Deals",8],["Jewelry",6]]),topLongKeywords:new Map([["Beauty",20],["Fashion",15],["Shoes",12],["Deals",8],["Jewelry",6]])};let l=Bo[e];if(!l){const N=jc||Sc;let M={};const I=Md[As[e]];let F=N[I]??[];F.length||(M=Ec,F=M[I]||[]),I!=="english"&&(F=F.concat(N.english));const S=new Set(F);S.add("-"),S.add("/"),Bo[e]=S,l=S}const i=n==null?void 0:n.map(N=>Ha(N)),d=Ha(t),f=Ha(a),c=Ha(r),p=i.join(" "),u=[d,f,c,...i],h={};u.forEach(N=>{if(!N)return;const M=N.split(" "),F=M.length-1;let S=[];M.forEach((R,w)=>{const m=R.toLowerCase();if(!m||!isNaN(+m))return;const T=S.length;if(l!=null&&l.has(m)){T>=2&&(h[S.join(" ")]=1),S.length=0;return}S.push(m),F===w&&(S.length>=2&&(h[S.join(" ")]=1),S.length=0)})});const g=[...new Set((d+" "+f).split(" ")??[])],v={};for(let N=0;N<g.length;N++){if(!g[N].trim()||!isNaN(+g[N]))continue;const M=g[N].toLocaleLowerCase().trim();if(l.has(M))continue;const I=`${d} ${f} ${c} ${p}`.trim();let F=ep(I,M);v[M]=F||1}const C=Oo(Mo(v),50),j=Oo(Mo(h),50);return{topFocusKeywords:C,topLongKeywords:j}}catch{return{topFocusKeywords:new Map,topLongKeywords:new Map}}},Vo=(e,t="asc")=>[...e].sort((a,n)=>{const r=a.charCodeAt(0)>=65&&a.charCodeAt(0)<=90,o=n.charCodeAt(0)>=65&&n.charCodeAt(0)<=90;return r!==o?r?-1:1:t==="asc"?a.localeCompare(n,void 0,{sensitivity:"base"}):n.localeCompare(a,void 0,{sensitivity:"base"})}),np=e=>{try{return e.map(({market_place:t,market_place_id:a,banned_keywords:n,recommended_keywords:r,unchecked_tags:o,id:l})=>({id:l,marketPlace:t,marketPlaceId:a,uncheckedTags:o||[],bannedKeywords:n?Vo(n.split(",")):[],recommendedBannedKeywords:r?Vo(r.split(",")):[]}))}catch{return[]}},ap=async(e,t)=>{var a;try{if(typeof e=="string"&&e.startsWith("data:image")){const[o,l]=e.split(","),i=(a=o==null?void 0:o.match(/:(.*?);/))==null?void 0:a[1];return[e,i,!0,t]}const n=e instanceof Blob,r=n?e:await new Response(e).blob();return await new Promise((o,l)=>{const i=new FileReader;i.onload=()=>{o([i.result,r.type,n,t])},i.onerror=()=>{l(new Error("Error reading file as Base64"))},i.readAsDataURL(r)})}catch{return[e,"image/png",!0,t]}},Sa=e=>Array.isArray(e)?e.filter(t=>typeof t=="string"):[],rp=(e,t)=>{const a={percent:rs(+(t==null?void 0:t.percent)||0,100)};return t.printType?a.printType=Ta.some(n=>n.value===(t==null?void 0:t.printType))?t.printType:Ta[0].value:t.bgColor&&(a.bgColor=t.bgColor),e===Tr.TUMBLER&&(a.tumblerScale=rs(+(t==null?void 0:t.tumblerScale)||0,100),a.tumblerTab=wn.some(n=>n.value===(t==null?void 0:t.tumblerTab))?t==null?void 0:t.tumblerTab:wn[0].value,a.usePattern=(t==null?void 0:t.usePattern)||!1),a},sp=e=>Array.isArray(e)?e.filter(t=>t==null?void 0:t.product_id).map(({product_id:t,fit_types:a,colors:n,prices:r,scale:o})=>{const l={product_id:t,fit_types:Sa(a),colors:Sa(n),prices:Sa(r)};return o!=null&&o.percent&&(l.scale=rp(t,o)),l}):[],op=e=>Array.isArray(e)?e.filter(t=>oa[t.language]).map(({language:t,fields:a})=>({language:t,fields:nt.map((n,r)=>{var o;return{id:n,value:((o=a==null?void 0:a[r])==null?void 0:o.value)||""}})})):[],ip=(e,t)=>{const a=[],n=new Set;for(const r of e){const{name:o,template_metadata:l,category:i}=r;if(i!==t||!(o!=null&&o.trim())||n.has(o.trim()))continue;const{product_list:d,product_metadata:f,listing:c,translation_option:p}=l||{};n.add(o.trim()),a.push({name:o.trim(),category:i,template_metadata:{product_list:Sa(d),product_metadata:sp(f),listing:op(c),translation_option:p}})}return{formattedTemplates:a}},lp=(e,t)=>{if(!e||!e.length){se("No templates found! Please create or import templates before exporting.");return}try{const a=JSON.stringify(e,null,2),n=new Blob([a],{type:"application/json"}),r=URL.createObjectURL(n),o=document.createElement("a");o.href=r,o.download=t,document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(r),De("Export successful!")}catch{se("An error occurred while exporting. Please try again.")}},$s=(e,t)=>{const a=[],n=new Set;for(const{name:r,template_metadata:o,category:l}of e){if(l!==t)continue;const i=(o==null?void 0:o.values)||[],d=r==null?void 0:r.trim();if(!d||n.has(d)||!(i!=null&&i.length))continue;const f=Sa(i);f.length&&(n.add(d),a.push({name:d,category:l,template_metadata:{values:f}}))}return{uniqueTemplates:a,uniqueItems:a.length}},ps=e=>{var a,n;const t=document.querySelector(e);if(t){((n=(a=t.textContent)==null?void 0:a.trim())==null?void 0:n.toLowerCase())==="expand all languages"&&t.click();const o=ye(e);o&&(o.style.display="none")}},mt=async(e,t=200,a=100,n=6e4,r=!1)=>{const o=Date.now();r||await Me(t);const l=()=>!!document.querySelector(e);for(;l()&&!(Date.now()-o>n);)await Me(t);await Me(a)},cp=async(e,t,a=200,n=100,r=6e4)=>{const o=Date.now();await Me(a);const l=()=>{var f;const d=document.querySelector(e);return((f=d==null?void 0:d.textContent)==null?void 0:f.trim())||""},i=()=>l().includes(t);if(i()){for(;i()&&!(Date.now()-o>r);)await Me(a);await Me(n)}},Ga=async e=>{const t=document.createElement("canvas");t.width=e.naturalWidth,t.height=e.naturalHeight;const a=t.getContext("2d");if(!a)return t;a.drawImage(e,0,0);const{data:n,width:r,height:o}=a.getImageData(0,0,t.width,t.height);let l=null,i=null,d=null,f=null;for(let g=0;g<o;g++)for(let v=0;v<r;v++)n[(g*r+v)*4+3]!==0&&(l===null&&(l=g),i=g,(d===null||v<d)&&(d=v),(f===null||v>f)&&(f=v));if(l===null||l===null||i===null||d===null||f===null)return t;const c=f-d+1,p=i-l+1,u=document.createElement("canvas");u.width=c,u.height=p;const h=u.getContext("2d");return h==null||h.drawImage(t,d,l,c,p,0,0,c,p),u},zo=e=>{const t=document.getElementById(e);t&&t.click()},Cl=(e,t)=>{zo(e),zo(t)},Hr=async(e,t,a)=>{const n=ye("label[for='TUMBLER-DESIGN-wizzy']");if(!n){se("Unable to find the upload area for the design.");return}const r=n.getAttribute("for");if(!r){se("Unable to process the design upload.");return}const o=document.getElementById(r);if(!o){se("Unable to find the upload input for the design.");return}Ls(o,a),await mt(t,1500)},Rr=async(e,t=0)=>{const a=ye(e);a&&(a.click(),await Me(t))},Ur=(e,t)=>{const a=document.createElement("canvas");a.width=e,a.height=t;const n=a.getContext("2d");if(!n){se("Unable to create canvas for design processing.");return}return{canvas:a,ctx:n}},Ht=async(e,t="",a,n=!0)=>{e==null||e.stopPropagation(),e==null||e.preventDefault();try{n||await mt(t);let r=Math.round(Number(a||100));const o=ye("mat-slider");if(o){let l=Math.round(parseFloat(o.getAttribute("aria-valuenow")||"0"));const i=l<r?1:-1,d=Math.abs(r-l);for(let f=0;f<d;f++){const c=new KeyboardEvent("keydown",{bubbles:!0,cancelable:!0,key:i>0?"ArrowRight":"ArrowLeft",code:i>0?"ArrowRight":"ArrowLeft",keyCode:i>0?39:37,which:i>0?39:37});o.dispatchEvent(c),await Me(5)}}}catch{}},fs=(e,t)=>{const a=URL.createObjectURL(e),n=document.createElement("a");n.href=a,n.download=t,n.style.display="none",document.body.appendChild(n),requestAnimationFrame(()=>{n.click(),document.body.removeChild(n),URL.revokeObjectURL(a)})},dp=async(e,t=.15)=>{const a=document.createElement("canvas");a.width=e.naturalWidth,a.height=e.naturalHeight;const n=a.getContext("2d");if(!n)return a;n.drawImage(e,0,0);const{data:r,width:o,height:l}=n.getImageData(0,0,a.width,a.height);let i=null,d=null,f=null,c=null;for(let m=0;m<l;m++)for(let T=0;T<o;T++)r[(m*o+T)*4+3]!==0&&(i===null&&(i=m),d=m,(f===null||T<f)&&(f=T),(c===null||T>c)&&(c=T));if(i===null||d===null||f===null||c===null)return a;const p=f,u=o-1-c,h=i,g=l-1-d,v=Math.floor(p*t),C=Math.floor(u*t),j=Math.floor(h*t),N=Math.floor(g*t),M=Math.max(0,f-v),I=Math.max(0,i-j),F=Math.min(o-M,c-f+1+v+C),S=Math.min(l-I,d-i+1+j+N),R=document.createElement("canvas");R.width=F,R.height=S;const w=R.getContext("2d");return w&&w.drawImage(e,M,I,F,S,0,0,F,S),R},up=async(e,t,a)=>{var n;try{const o=((n=document.querySelector(a.back_btn))==null?void 0:n.classList.contains("active"))?tt.back:tt.front,l=tt[e],i=l===tt.front?tt.back:tt.front,d=async p=>{const u=ye(`${a.design_view_prefix}${p}`);u&&(u.click(),await Me(500),await mt(a.single_product_uploading_container,1e3))},f=()=>!!ye(a.delete_btn),c=async p=>(await d(p),f());if(o===l&&f())Et(`Design is on the target view for ${t} product`);else if(await c(i)){const p=ye(a.delete_btn);p&&(p.click(),await Me(500)),await d(l);const u=ye(a.global_uploaded_img);let h=vl(u.src);const g=`label[for='${t}-${l}-wizzy']`,v=await Ze(g),C=v==null?void 0:v.getAttribute("for"),j=document.getElementById(C);Ls(j,h),h=null,await Me(500),await mt(a.single_product_uploading_container,1e3),await Ze(a.canvas,500,6e4)}else await c(l)&&Et(`Design is on not the target view so changing view for ${t} product`)}catch{}},pp=e=>e.map(t=>{var l;const{name:a,template_metadata:n,category:r}=t,o=Ge(n);return(l=o==null?void 0:o.product_metadata)!=null&&l.length&&o.product_metadata.forEach(i=>{Array.isArray(i.scale)&&(i.scale={})}),{category:r,name:a,template_metadata:o}}),fp=(e,t=!1)=>Object.values(e).reduce((a,n)=>(n.forEach(r=>{r&&(a[r]=t)}),a),{}),En=Pa(e=>({scale:100,tumblerScale:100,tumblerTab:wn[0].value,isPattern:!1,updateTumblerScale:t=>{e({tumblerScale:t}),at.setStoreValue({"merch.tumblerScale":t})},updateScale:t=>{e({scale:t}),at.setStoreValue({"merch.scale":t})},tumblerImages:{front:null,back:null},updateTumblerImage:(t,a)=>e(n=>({tumblerImages:{...n.tumblerImages,[t]:a}})),errors:{front:null,back:null},updateErrors:(t,a)=>e(n=>({errors:{...n.errors,[t]:a}})),updateTumblerTab:(t,a=!1)=>{e({tumblerTab:t}),!a&&at.setStoreValue({"merch.tumblerTab":t})},updateIsPattern:(t,a=!1)=>{e({isPattern:t}),!a&&at.setStoreValue({"merch.isPattern":t})}})),ia=ct.object({templateTitle:ct.string().min(1,"Please enter a template name").refine(e=>e.trim(),{message:"Please enter a template name"}),searchText:ct.string()}),hp=ct.object({templateTitle:ct.string().min(1,"Please enter a template name").refine(e=>e.trim(),{message:"Please enter a template name"}),designTitle:ct.string().min(3,"The title must be at least 3 characters long. Please enter a valid title.").refine(e=>e.trim()&&e.trim().length>=3,{message:"The title must be at least 3 characters long. Please enter a valid title."}),designBrand:ct.string().min(3,"The brand name must be at least 3 characters long. Please enter a valid brand name.").refine(e=>e.trim()&&e.trim().length>=3,{message:"The brand name must be at least 3 characters long. Please enter a valid brand name."})}),mp=ct.string().min(3,"The title must be at least 3 characters long").max(60,"The title must be no more than 60 characters long").refine(e=>e.trim()&&e.trim().length>=3&&e.trim().length<=60,{message:"The title must be between 3 and 60 characters long"}),gp=ct.string().min(3,"The brand name must be at least 3 characters long").max(50,"The brand name must be no more than 50 characters long").refine(e=>e.trim()&&e.trim().length>=3&&e.trim().length<=50,{message:"The brand name must be between 3 and 50 characters long"}),On=ct.object({designTitle:mp,designBrand:gp}),bp=ct.object({listing:ct.object({us:On,de:On,fr:On,it:On,es:On,jp:On})}),Tl=ct.object({title:ct.string().min(1,"Please enter a template name").refine(e=>e.trim(),{message:"Please enter a template name"})}),xp=ct.object({globalImageResizeScale:ct.number().min(50,{message:"Value must be between 50 and 100"}).max(100,{message:"Value must be between 50 and 100"})}),Je=({open:e,children:t})=>e&&ft.createPortal(s.jsx("div",{className:"merch-dominator-style-container mrdn-merch-container montserrat-medium text-primary",children:t}),document.body),yp=({tabOptions:e,activeTab:t,handleActiveTab:a})=>s.jsx("div",{className:"tab-btn-group w-full",children:e.map((n,r)=>{const o=n.value===t;return s.jsx("button",{onClick:l=>{l.stopPropagation(),l.preventDefault(),a(n.value)},className:`tab-action-btn-${r} mrdn-tab px-4 py-2 font-semibold ${o?"bg-merch-dominator color-white":"bg-main text-primary"}`,style:{fontSize:"14px"},children:n.label},n.value)})}),Wt=b.memo(yp),Ko=async e=>{try{return await rt.runtime.sendMessage({type:Xd,payload:e,tokenKey:dt()})}catch{}},vp=async e=>{try{return await rt.runtime.sendMessage({type:Gd,payload:e,tokenKey:dt()})}catch{}},wp=async(e,t)=>{try{return await rt.runtime.sendMessage({type:Yd,payload:{text:e,lang:t},tokenKey:dt()})}catch{}},Cp=async()=>{try{return await rt.runtime.sendMessage({type:Ud,tokenKey:dt()})}catch{}},Tp=async e=>{try{return await rt.runtime.sendMessage({type:Vd,payload:e,tokenKey:dt()})}catch{}},jp=async e=>{try{return await rt.runtime.sendMessage({type:zd,payload:{id:e},tokenKey:dt()})}catch{}},Sp=async e=>{try{return await rt.runtime.sendMessage({type:Kd,payload:e,tokenKey:dt()})}catch{}},Ep=async e=>{try{return await rt.runtime.sendMessage({type:qd,payload:e,tokenKey:dt()})}catch{}},jl=async e=>{try{return await rt.runtime.sendMessage({type:Hd,payload:e,tokenKey:dt()})}catch{}},qo=async e=>{try{return await rt.runtime.sendMessage({type:Wd,payload:{marketplace:e},tokenKey:dt()})}catch{}},Xt=async(e="")=>{try{return await rt.runtime.sendMessage({type:Dd,payload:{category:e},tokenKey:dt()})}catch{}},Qt=async e=>{try{return await rt.runtime.sendMessage({type:$d,payload:e,tokenKey:dt()})}catch{}},_n=async(e,t)=>{try{return await rt.runtime.sendMessage({type:Fd,payload:{id:e,data:t},tokenKey:dt()})}catch{}},fn=async e=>{try{return await rt.runtime.sendMessage({type:Bd,payload:e,tokenKey:dt()})}catch{}},_p=async e=>{try{return await rt.runtime.sendMessage({type:Rd,payload:e,tokenKey:dt()})}catch{}},hn=async e=>{try{return await rt.runtime.sendMessage({type:Ld,payload:e,tokenKey:dt()})}catch{}},mn=({count:e=5,height:t=44,extraClass:a=""})=>s.jsx("div",{className:`d-flex gap-4 flex-col w-full ${a}`,children:Array.from({length:e}).map((n,r)=>s.jsx(_c,{height:t},r))}),Fs=e=>{const[t,a]=e.split("-");return`${t.replace(/_/g," ").toLowerCase()}-${(a==null?void 0:a.replace(/-GB/g,"-UK"))||""}`},Ap=({template:e,onDelete:t,isProcessing:a,onApply:n,onOpenInNewTab:r,onViewTemplate:o})=>{var d;const l=f=>{f.preventDefault(),f.stopPropagation()},i=b.useMemo(()=>Ge(e.template_metadata),[e.template_metadata]);return s.jsx("li",{className:"d-flex w-full py-2 flex-col justify-between gap-3 secondary-border actions-bg border-r-1 px-3",children:s.jsxs("div",{className:"d-flex justify-between gap-2",children:[s.jsxs("div",{className:"d-flex flex-col w-full gap-1",children:[s.jsx("div",{className:"d-flex align-items-center text-base w-full cursor-pointer text-break-word",onClick:f=>{l(f),n(i)},children:e.name}),s.jsx("div",{className:"d-flex flex-wrap gap-1 text-capitalize cursor-default",children:(d=i==null?void 0:i.product_list)==null?void 0:d.map(f=>s.jsx("div",{className:"d-flex keyword-section cursor-default",children:Fs(f)},f))})]}),s.jsxs("div",{className:"action-buttons d-flex h-fit gap-3",children:[s.jsx(fe,{onclickHandler:f=>{l(f),o(i,e.name)},buttonIcon:s.jsx(Ac,{}),tooltipId:`${e.id}-view-template-action`,tooltipTitle:"View Template",extraClass:"border-none"}),s.jsx(fe,{onclickHandler:f=>{l(f),r(e.id)},buttonIcon:s.jsx(kc,{}),tooltipId:`${e.id}-new-tab-apply-action`,tooltipTitle:"Apply Template in New Tab",extraClass:"border-none"}),s.jsx("button",{onClick:f=>{l(f),t(f,e.id)},disabled:a,style:{background:"var(--color-danger-light)"},className:"border-none",children:s.jsx(sn,{size:12})})]})]})})},kp=b.memo(Ap),Pp=({templates:e,onDelete:t,isProcessing:a=!1,handleApply:n,applyTemplateInNewTab:r,handleViewTemplate:o})=>s.jsx("div",{className:"item-list-container position-relative mrdn-master-templates-container",children:e.length?s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"d-flex justify-end text-main font-semibold",children:["Total Templates: ",e.length]}),s.jsx("ul",{className:"d-flex flex-col gap-2 text-primary i-pb-4 i-pt-3",children:e.map(l=>{const i=`${l.name}-${l.id}`;return s.jsx(kp,{template:l,onDelete:t,onApply:n,isProcessing:a,onOpenInNewTab:r,onViewTemplate:o},i)})})]}):s.jsx("div",{className:"no-content-text text-center mt-4",children:a?s.jsx(mn,{}):"No Templates Found"})}),Np=b.memo(Pp),Ip=({template:e,onEdit:t,onDelete:a,isProcessing:n,handleApply:r})=>{var u;const[o,l]=b.useState(!1),i=b.useRef(null),d=b.useMemo(()=>Ge(e.template_metadata,{values:[]}),[e.template_metadata]),f=(h,g)=>{h.stopPropagation(),h.preventDefault(),g?t(e):a(h,e)},c=h=>{h.stopPropagation(),h.preventDefault(),r(e)},p=h=>{h.stopPropagation(),h.preventDefault(),l(g=>!g)};return b.useEffect(()=>{var h;i.current&&(o?(i.current.style.height=`${i.current.scrollHeight}px`,(h=i.current)==null||h.scrollIntoView({behavior:"smooth",block:"nearest"})):i.current.style.height="0")},[o]),s.jsxs("li",{className:"d-flex w-full flex-col justify-between secondary-border-bottom",children:[s.jsxs("div",{className:"d-flex justify-between gap-2 action-container",children:[s.jsx("div",{className:"text-base w-full item-title cursor-pointer text-break-word",onClick:c,children:e.name}),s.jsxs("div",{className:"action-buttons d-flex h-fit gap-3",children:[s.jsx("button",{onClick:h=>{f(h,"edit")},disabled:n,children:s.jsx(Tn,{size:12})}),s.jsx("button",{onClick:f,disabled:n,style:{background:"var(--color-danger-light)"},children:s.jsx(sn,{size:12})}),s.jsx("button",{onClick:p,disabled:n,className:"i-text-main i-main-border bg-main expand-action",children:s.jsx("div",{className:"d-flex",style:o?{transform:"rotate(-90deg)",transition:"0.3s ease-in-out"}:{transition:"0.3s ease-in-out"},children:s.jsx(on,{size:12})})})]})]}),s.jsx("div",{className:`expandable-section d-flex text-13px flex-wrap gap-2 ${o?"expanded i-mb-1":""}`,ref:i,children:o&&((u=d.values)==null?void 0:u.map(h=>s.jsx("div",{className:"d-flex keyword-section cursor-default",children:Fs(h)},h)))})]})},Op=({templates:e,onEdit:t,onDelete:a,isProcessing:n=!1,handleApply:r})=>s.jsx("div",{className:"item-list-container position-relative ",children:e.length?s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"d-flex justify-end text-main font-semibold",children:["Total Templates: ",e.length]}),s.jsx("ul",{className:"d-flex flex-col text-primary i-pb-4 i-pt-3",children:e.map(o=>{const l=`${o.name}-${o.id}`;return s.jsx(Ip,{template:o,onEdit:t,onDelete:a,handleApply:r,isProcessing:n},l)})})]}):s.jsx("div",{className:"no-content-text text-center",children:n?s.jsx(mn,{}):"No Templates Found"})}),Mp=b.memo(Op),Dp=({selectionMethods:e,productInventory:t})=>{const{control:a,setValue:n,watch:r,getValues:o,clearErrors:l}=e,[i,d]=b.useState(!1),f=r(),c=b.useMemo(()=>Object.entries(t),[t]),p=b.useCallback(j=>t[j].every(N=>!N||f[N]),[f]),u=b.useCallback(j=>c.every(([N,M])=>{const I=M[j];return!I||f[I]}),[f]),h=b.useCallback(()=>{const j=c.every(([N,M])=>M.every(I=>!I||o(I)));d(j)},[o]),g=b.useCallback(()=>{const j=!i;d(j),c.forEach(([N,M])=>{M.forEach(I=>{I&&n(I,j)})}),j&&l("productSelection")},[i,n,l]),v=b.useCallback(j=>{const N=p(j);t[j].forEach(M=>{M&&n(M,!N)}),h(),l("productSelection")},[p,n,h,l]),C=b.useCallback(j=>{const N=u(j);c.forEach(([M,I])=>{const F=I[j];F&&n(F,!N)}),h(),l("productSelection")},[u,n,h,l]);return b.useEffect(()=>{h()},[f,h]),s.jsxs("table",{className:"product-selection-table table Table",children:[s.jsx("thead",{style:{background:"var(--text-main-color)"},className:"color-white",children:s.jsxs("tr",{children:[s.jsx("th",{}),s.jsx("th",{className:"text-left",children:"Select All"}),s.jsx("th",{children:".com"}),s.jsx("th",{children:".co.uk"}),s.jsx("th",{children:".de"}),s.jsx("th",{children:".fr"}),s.jsx("th",{children:".it"}),s.jsx("th",{children:".es"}),s.jsx("th",{children:".co.jp"})]})}),s.jsxs("tbody",{children:[s.jsxs("tr",{style:{background:"var(--table-first-item-bg)"},children:[s.jsx("td",{children:s.jsx(Jn,{checked:i,onChange:g})}),s.jsx("td",{children:"Select All Products"}),Qd.map((j,N)=>s.jsx("td",{children:s.jsx(Jn,{checked:u(N),onChange:()=>C(N)})},N))]}),c.map(([j,N])=>s.jsxs("tr",{children:[s.jsx("td",{style:{background:"var(--table-first-item-bg)"},children:s.jsx(Jn,{checked:p(j),onChange:()=>v(j)})}),s.jsx("td",{className:"mrdn-product-name",children:j}),N.map((M,I)=>s.jsx("td",{children:M&&s.jsx(ke,{name:M,control:a,render:({field:F})=>s.jsx(Jn,{checked:F.value,onChange:S=>{F.onChange(S.target.checked),l("productSelection"),S.target.checked||h()},inputProps:{"data-id":M}})})},I))]},j))]})]})},Lp=({collectionKey:e,fileName:t,processTemplateImport:a,templateLimit:n=0,extraClass:r})=>{const[o,l]=b.useState(!1),i=b.useRef(null),d=Tt(u=>u.setUpgradePlanModal),f=()=>{var u;o||(u=i.current)==null||u.click()},c=u=>{var v;l(!0);const h=(v=u.target.files)==null?void 0:v[0];if(!h){l(!1);return}if(h.type!=="application/json"&&!h.name.endsWith(".json")){se("Unsupported file format! Please upload a valid JSON file."),u.target.value="",l(!1);return}const g=new FileReader;g.onload=async C=>{var j;try{if(!((j=C.target)!=null&&j.result)){se("Invalid file! Please select a valid JSON file.");return}const N=JSON.parse(C.target.result);await a(N)}catch{se("Failed to import your file, Please try again.")}finally{u.target.value="",l(!1)}},g.readAsText(h)},p=b.useCallback(async()=>{if(!n){d({isVisible:!0,modalTitle:"Upgrade to Export Templates",modalDescription:"Your current plan does not include access to export templates. Please upgrade to a higher plan to enable this feature."});return}if(!o){l(!0);try{const u=await Xt(e);if(u!=null&&u.templates){lp(pp(u.templates),t);return}se((u==null?void 0:u.message)||"Failed to fetch templates for export.")}finally{l(!1)}}},[o,n]);return s.jsxs("div",{className:`d-flex gap-2 ${r}`,children:[s.jsx(fe,{buttonContent:"Import Templates",onclickHandler:f,buttonIcon:s.jsx(bd,{}),isDisabled:o,tooltipId:"import-global-template-action"}),s.jsx(fe,{buttonContent:"Export Templates",onclickHandler:p,buttonIcon:s.jsx(xd,{}),isDisabled:o,tooltipId:"export-global-template-action"}),s.jsx("input",{ref:i,type:"file",id:"mrdn-import-templates-action",className:"i-d-none",onChange:c})]})},gn=b.memo(Lp),Ya={name:"",category:"",template_metadata:"",id:0},$p=({setOpen:e,applyTemplate:t,productCheckboxSelector:a})=>{var ce;const n=b.useRef(null),{PRODUCT_INVENTORY:r,PRODUCT_SELECTION_TYPES:o}=jr(Ji(k=>({PRODUCT_INVENTORY:k.constants.PRODUCT_INVENTORY,PRODUCT_SELECTION_TYPES:k.constants.PRODUCT_SELECTION_TYPES})))||{},l=b.useMemo(()=>({...fp(r,!1),...ks}),[r]),i=gt({defaultValues:l,mode:"onChange",resolver:Lt(ia)}),{control:d,formState:{errors:f},watch:c,setValue:p,clearErrors:u,setError:h,reset:g,getValues:v}=i,{maxTemplateCount:C,hasUnlimitedAccess:j,importTemplateCount:N,exportTemplateCount:M}=Pt(k=>k),I=Tt(k=>k.setUpgradePlanModal),[F,S]=b.useState(et.ADD),[R,w]=b.useState([]),[m,T]=b.useState([]),[E,O]=b.useState(!1),[A,_]=b.useState(!1),[D,x]=b.useState(!0),[y,G]=b.useState({...Ya}),[$,K]=b.useState({modalOpen:!1,template:null,processing:!1}),J=c("searchText"),Y=pn(J,500),ee=F===et.ADD,Z=b.useCallback(async()=>{const k=await Xt(Ra);k!=null&&k.templates&&w(k==null?void 0:k.templates),x(!1)},[]);b.useEffect(()=>{if(a){const k=qu(a).reduce((L,B)=>(L[B]=!0,L),{});g({...v(),...k})}Z()},[]);const q=b.useCallback((k,L)=>{if(!k)return T(L);T(L.filter(B=>B.name.toLowerCase().includes(k.toLowerCase())))},[]);b.useEffect(()=>{D||q(Y,R)},[Y,D,R,q]);const Q=b.useCallback(k=>{S(k),jn(n,"#modal-product-selection-templates .mrdn-modal-body")},[]),oe=async k=>{if(!E){O(!0);try{const{templateTitle:L,searchText:B,productSelection:W,...z}=v(),te=new Set;for(const _e in z)z[_e]&&te.add(_e);const X=Array.from(te);if(!X.length){h("productSelection",{type:"manual",message:"At least one product must be selected"});return}const ae=L.trim(),me=y.id,ge={name:ae,category:Ra,template_metadata:{values:X}},he=me?await _n(me,ge):await Qt(ge);if(he!=null&&he.name){De("Template",me?"updated":"saved"),me&&G(Ya),await Z();return}if((he==null?void 0:he.status)===429){I({isVisible:!0,showTemplateText:!0,modalTitle:(he==null?void 0:he.title)||"Template Limit Reached",modalDescription:(he==null?void 0:he.description)||dn(C,"save","template")});return}await Z(),se(he==null?void 0:he.message)}catch(L){se(L==null?void 0:L.message)}finally{O(!1)}}},de=k=>{G(W=>({...W,...k}));const L=Ge(k.template_metadata),B=new Set((L==null?void 0:L.values)||[]);Object.entries(r).forEach(([W,z])=>{z.forEach(te=>{te&&p(te,B.has(te))})}),p("templateTitle",k.name),Q(et.ADD),u("productSelection")},pe=b.useCallback(()=>{p("templateTitle",""),G(Ya),u("productSelection")},[]),Ce=b.useCallback((k,L)=>{$.processing||K(B=>({...B,modalOpen:!B.modalOpen,template:(L==null?void 0:L.id)||null}))},[$.processing]),re=async()=>{if(!$.processing){K(k=>({...k,processing:!0}));try{const{template:k}=$,L=await fn(k);if(L!=null&&L.success){De("Template","deleted"),await Z();return}await Z(),se(L==null?void 0:L.message)}catch{se("An error occurred while deleting the template")}finally{$.template===y.id&&G(Ya),K(k=>({...k,modalOpen:!1,template:null,processing:!1}))}}},U=k=>{if(A||E)return;_(!0);const L=Ge(k.template_metadata);t(L.values||[]),_(!1),e(!1)},ne=b.useCallback(async k=>{const L=new Set(o),B=[],W=new Set;for(const te of k){const{name:X,template_metadata:ae,category:me}=te;if(me!==Ra)continue;const ge=X==null?void 0:X.trim(),he=(ae==null?void 0:ae.values)||[];if(!ge||W.has(ge)||!(he!=null&&he.length))continue;const _e=Array.isArray(he)?he.filter(Oe=>typeof Oe=="string"&&L.has(Oe)):[];_e.length&&(W.add(ge),B.push({name:ge,category:me,template_metadata:{values:_e}}))}if(!B.length){De("Import complete. No valid templates were available to import.");return}const z=await hn(B);if(z!=null&&z.success){De((z==null?void 0:z.message)||ln((z==null?void 0:z.count)||0)),await Z();return}if((z==null?void 0:z.status)===429){const te=(z==null?void 0:z.description)||cn(z==null?void 0:z.count,N);I({isVisible:!0,showTemplateText:!0,modalTitle:(z==null?void 0:z.title)||"Template Limit Reached",modalDescription:te}),(+te.split(" ")[0]||0)&&await Z();return}se(z==null?void 0:z.message)},[N]),H=b.useMemo(()=>s.jsx("div",{className:"w-full position-relative",children:s.jsx(ke,{name:"searchText",control:d,render:({field:{onChange:k,value:L}})=>s.jsx(qe,{onChange:k,value:L,uniqueId:"selection-search_input",labelClass:"flex !font-medium",inputClassName:"merch-input",placeHolder:"Search by title",isDisabled:!1,allowClear:{clearIcon:s.jsx($t,{size:16})},addonBefore:s.jsx("div",{className:"d-flex h-full pl-2 align-items-center product-search-box text-primary",children:s.jsx(Gt,{})}),hasPermission:!0})})}),[d]),V=b.useCallback(k=>{k.stopPropagation(),e(!1)},[e]);return s.jsxs(s.Fragment,{children:[s.jsx(Je,{open:!0,children:s.jsx(pt,{open:!0,width:"60vw",handleClose:V,uniqueId:"product-selection-templates",modalTitle:"Product Selection Templates",extraClass:"merch-dominator-style-container",modalTitleChildren:j?null:s.jsx(un,{showTemplateLimit:!0,templateLimit:C,buttonId:"products-selection-templates-upgrade-button"}),bodySection:s.jsxs("div",{className:"d-flex flex-col gap-y-3",children:[s.jsxs("div",{className:"d-flex justify-between",children:[s.jsx(Wt,{tabOptions:Sn,activeTab:F,handleActiveTab:Q}),ee?s.jsx(gn,{collectionKey:Ra,fileName:"merch-dominator-products-templates.json",processTemplateImport:ne,extraClass:"w-full justify-end align-items-center",templateLimit:M}):H]}),ee?s.jsx(bt,{...i,children:s.jsxs("form",{onSubmit:i.handleSubmit(oe),className:"d-flex flex-col gap-y-3",id:"selection-template-form",children:[s.jsxs("div",{className:"w-full d-flex flex-col gap-y-4",children:[s.jsx(ke,{control:d,name:"templateTitle",render:({field:{onChange:k,value:L}})=>{var B;return s.jsx(qe,{uniqueId:"selection-template-title-input",type:"text",placeholder:"Enter template name",value:L,onChange:k,label:"Add New Template*",extraClass:"w-full",helperText:(B=f==null?void 0:f.templateTitle)==null?void 0:B.message})}}),s.jsxs("div",{className:"d-flex w-full justify-end gap-x-2 pr-2 template-actions",children:[s.jsx(fe,{buttonContent:"Save",isLoading:E,isDisabled:D||E,onclickHandler:()=>{},extraClass:"color-white border-none",buttonProps:{type:"submit"},tooltipId:"save-template-selection"}),y.name&&s.jsx(fe,{buttonContent:"Cancel",onclickHandler:pe,isDisabled:E,tooltipId:"cancel-product-selection",extraClass:"outlined-btn",buttonProps:{type:"button"}})]})]}),s.jsxs("div",{className:"merch-dominator-selection-container",id:"mrdn-product-selection-container",children:[s.jsx(At,{message:(ce=f==null?void 0:f.productSelection)==null?void 0:ce.message}),s.jsx(Dp,{selectionMethods:i,productInventory:r})]})]})}):s.jsx(Mp,{templates:m,onEdit:de,onDelete:Ce,isProcessing:D||E,handleApply:U})]})})}),s.jsx(Je,{open:$.modalOpen,children:s.jsx(Yt,{deleteModalState:$,handleModalVisibility:Ce,handleDeleteAction:re,modalId:"delete-selection-template",deleteHeaderTitle:"Confirm template deletion",confirmationMessage:"Are you sure you want to delete this template?"})})]})},Fp=$p,Bp=({selectors:e})=>{const[t,a]=b.useState(!1),n=()=>{var i,d;(d=(i=document.querySelector(e==null?void 0:e.all_product_selection_btn))==null?void 0:i.click)==null||d.call(i)},r=()=>{var i,d;(d=(i=document.querySelector(e==null?void 0:e.none_product_selection_btn))==null?void 0:i.click)==null||d.call(i)},o=()=>{document.querySelectorAll(e==null?void 0:e.checkbox).forEach(i=>{const d=i.querySelector("i");d&&d.click()})},l=i=>{hl(i,e==null?void 0:e.checkbox,e==null?void 0:e.selected_checkbox)};return s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"d-flex justify-between py-2 px-4",id:"mrdn-product-selection",children:[s.jsxs("div",{className:"d-flex gap-x-2",children:[s.jsx(fe,{buttonContent:"All",onclickHandler:n,tooltipId:"all-product-selection",extraClass:"outlined-btn"}),s.jsx(fe,{buttonContent:"None",onclickHandler:r,tooltipId:"remove-product-selection",extraClass:"outlined-btn"}),s.jsx(fe,{buttonContent:"Alter",onclickHandler:o,tooltipId:"alter-product-selection",extraClass:"outlined-btn"})]}),s.jsx(fe,{buttonContent:"Templates",onclickHandler:()=>a(!0),tooltipId:"product-selection-templates"})]}),t&&s.jsx(Fp,{applyTemplate:l,setOpen:a,productCheckboxSelector:e==null?void 0:e.checkbox})]})},Hp=({template:e,onEdit:t,onDelete:a,isProcessing:n,handleApply:r})=>{var u;const[o,l]=b.useState(!1),i=b.useRef(null),d=b.useMemo(()=>Ge(e.template_metadata,{values:[]}),[e.template_metadata]),f=(h,g)=>{h.stopPropagation(),h.preventDefault(),g?t(e):a(h,e)},c=h=>{h.stopPropagation(),h.preventDefault(),r(e)},p=h=>{h.stopPropagation(),h.preventDefault(),l(g=>!g)};return b.useEffect(()=>{var h;o&&((h=i.current)==null||h.scrollIntoView({behavior:"smooth",block:"nearest"}))},[o]),s.jsxs("li",{className:`d-flex w-full flex-col justify-between secondary-border-bottom ${o?"gap-1 pb-2":""}`,children:[s.jsxs("div",{className:"d-flex justify-between gap-2 action-container",children:[s.jsx("div",{className:"text-base w-full item-title cursor-pointer text-break-word",onClick:c,children:e.name}),s.jsxs("div",{className:"action-buttons d-flex h-fit gap-3",children:[s.jsx("button",{onClick:h=>{f(h,"edit")},disabled:n,children:s.jsx(Tn,{size:12})}),s.jsx("button",{onClick:f,disabled:n,style:{background:"var(--color-danger-light)"},children:s.jsx(sn,{size:12})}),s.jsx("button",{onClick:p,disabled:n,className:"i-text-main i-main-border bg-main",children:s.jsx("div",{className:"d-flex",style:o?{transform:"rotate(-90deg)",transition:"0.3s ease-in-out"}:{transition:"0.3s ease-in-out"},children:s.jsx(on,{size:12})})})]})]}),o&&s.jsx("div",{className:"d-flex gap-x-2",ref:i,children:(u=d==null?void 0:d.values)==null?void 0:u.map(h=>s.jsx("div",{className:"d-flex keyword-section cursor-default",children:h},h))})]})},Rp=({templates:e,onEdit:t,onDelete:a,isProcessing:n=!1,handleApply:r})=>s.jsx("div",{className:"item-list-container position-relative",children:e.length?s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"d-flex justify-end text-main font-semibold",children:["Total Templates: ",e.length]}),s.jsx("ul",{className:"d-flex flex-col text-primary i-pb-4 i-pt-3",children:e.map(o=>{const l=`${o.name}-${o.id}`;return s.jsx(Hp,{template:o,onEdit:t,onDelete:a,handleApply:r,isProcessing:n},l)})})]}):s.jsx("div",{className:"no-content-text text-center",children:n?s.jsx(mn,{}):"No Templates Found"})}),Up=b.memo(Rp),Xa={name:"",category:"",template_metadata:"",id:0},Vp=({setOpen:e,applyTemplate:t,fitsCheckboxSelector:a})=>{var re;const n=jr(U=>U.constants.FIT_TYPES)||{},r=gt({defaultValues:{...n,...ks},mode:"onChange",resolver:Lt(ia)}),{maxTemplateCount:o,hasUnlimitedAccess:l,importTemplateCount:i,exportTemplateCount:d}=Pt(U=>U),f=Tt(U=>U.setUpgradePlanModal),{control:c,getValues:p,setValue:u,setError:h,watch:g,formState:{errors:v},clearErrors:C}=r,j=b.useRef(null),[N,M]=b.useState(et.ADD),[I,F]=b.useState([]),[S,R]=b.useState([]),[w,m]=b.useState(!0),[T,E]=b.useState(!1),[O,A]=b.useState(!1),[_,D]=b.useState({...Xa}),[x,y]=b.useState({modalOpen:!1,template:null,processing:!1}),G=g("searchText"),$=pn(G,500),K=N===et.ADD,J=b.useCallback(async()=>{const U=await Xt(Ua);U!=null&&U.templates&&F(U==null?void 0:U.templates),m(!1)},[]),Y=b.useCallback((U,ne)=>{if(!U)return R(ne);R(ne.filter(H=>H.name.toLowerCase().includes(U.toLowerCase())))},[]);b.useEffect(()=>{a&&gl(a).forEach(U=>{u(U,!0)}),J()},[]),b.useEffect(()=>{w||Y($,I)},[$,w,I,Y]);const ee=b.useCallback(U=>{M(U),jn(j,"#modal-fit-types-templates .mrdn-modal-body")},[]),Z=async U=>{if(!T){E(!0);try{const{templateTitle:ne,searchText:H,...V}=p(),ce=Object.entries(V).filter(([z,te])=>te).map(([z])=>z);if(!ce.length){h("selection",{type:"manual",message:"At least one fit type must be selected"});return}const k=ne.trim(),L=_.id,B={name:k,category:Ua,template_metadata:{values:ce}},W=L?await _n(L,B):await Qt(B);if(W!=null&&W.name){De("Template",L?"updated":"saved"),L&&D(Xa),await J();return}if((W==null?void 0:W.status)===429){f({isVisible:!0,showTemplateText:!0,modalTitle:(W==null?void 0:W.title)||"Template Limit Reached",modalDescription:(W==null?void 0:W.description)||dn(o,"save","template")});return}await J(),se(W==null?void 0:W.message)}catch{se("An error occurred while managing the template")}finally{E(!1)}}},q=b.useCallback(U=>{const ne=Ge(U.template_metadata);D(H=>({...H,...U})),Object.keys(n).forEach(H=>{u(H,ne.values.includes(H))}),u("templateTitle",U.name),ee(et.ADD),C("selection")},[u,ee,C,Y]),Q=b.useCallback(()=>{r.setValue("templateTitle",""),D(U=>({...U,...Xa})),C("selection")},[r,C]),oe=U=>{if(O||T)return;A(!0);const ne=Ge(U.template_metadata);t(ne.values||[]),A(!1),e(!1)},de=b.useCallback((U,ne)=>{x.processing||y(H=>({...H,modalOpen:!H.modalOpen,template:(ne==null?void 0:ne.id)||null}))},[x.processing]),pe=async()=>{if(!x.processing){y(U=>({...U,processing:!0}));try{const{template:U}=x,ne=await fn(U);if(ne!=null&&ne.success){De("Template","deleted"),await J();return}await J(),se(ne==null?void 0:ne.message)}catch{se("An error occurred while deleting the template")}finally{x.template===_.id&&D(Xa),y(U=>({...U,modalOpen:!1,template:null,processing:!1}))}}},Ce=b.useCallback(async U=>{const{uniqueTemplates:ne,uniqueItems:H}=$s(U,Ua);if(!H){De("Import complete. No valid templates were available to import.");return}const V=await hn(ne);if(V!=null&&V.success){De((V==null?void 0:V.message)||ln((V==null?void 0:V.count)||0)),await J();return}if((V==null?void 0:V.status)===429){const ce=(V==null?void 0:V.description)||cn(V==null?void 0:V.count,i);f({isVisible:!0,showTemplateText:!0,modalTitle:(V==null?void 0:V.title)||"Template Limit Reached",modalDescription:ce}),(+ce.split(" ")[0]||0)&&await J();return}se(V==null?void 0:V.message)},[i,f]);return s.jsxs(s.Fragment,{children:[s.jsx(Je,{open:!0,children:s.jsx(pt,{open:!0,width:"50vw",handleClose:U=>{U.stopPropagation(),e(!1)},uniqueId:"product-fit-types-templates",modalTitle:"Fit Types Templates",modalTitleChildren:l?null:s.jsx(un,{showTemplateLimit:!0,templateLimit:o,buttonId:"fit-types-templates-upgrade-button"}),bodySection:s.jsxs("div",{className:"d-flex flex-col gap-y-3 h-full",children:[s.jsxs("div",{className:"d-flex justify-between",children:[s.jsx(Wt,{tabOptions:Sn,activeTab:N,handleActiveTab:ee}),K?s.jsx(gn,{collectionKey:Ua,fileName:"merch-dominator-fit-types-templates.json",processTemplateImport:Ce,extraClass:"w-full justify-end align-items-center",templateLimit:d}):s.jsx("div",{className:"w-full position-relative",children:s.jsx(ke,{name:"searchText",control:c,render:({field:{onChange:U,value:ne}})=>s.jsx(qe,{onChange:U,value:ne,uniqueId:"fit-types-search_input",labelClass:"flex !font-medium",inputClassName:"merch-input",placeHolder:"Search by title",isDisabled:!1,allowClear:{clearIcon:s.jsx($t,{size:16})},addonBefore:s.jsx("div",{className:"d-flex h-full pl-2 align-items-center product-search-box text-primary",children:s.jsx(Gt,{})}),hasPermission:!0})})})]}),K?s.jsx(bt,{...r,children:s.jsxs("form",{onSubmit:r.handleSubmit(Z),className:"d-flex flex-col gap-y-3 h-full",id:"fit-types-template-form",children:[s.jsx("div",{className:"w-full d-flex flex-col gap-y-4",children:s.jsx(ke,{control:c,name:"templateTitle",render:({field:{onChange:U,value:ne}})=>{var H;return s.jsx(qe,{uniqueId:"fit-types-template-title-input",type:"text",placeholder:"Enter template name",value:ne,onChange:U,label:"Add New Template*",extraClass:"w-full",helperText:(H=v==null?void 0:v.templateTitle)==null?void 0:H.message})}})}),s.jsxs("div",{className:"merch-dominator-selection-container highlighted-selection-container",id:"mrdn-fit-types-selection-container",children:[s.jsx(At,{message:(re=v==null?void 0:v.selection)==null?void 0:re.message}),s.jsxs("div",{className:"d-flex flex-col gap-y-2",children:[s.jsx("span",{children:"Select Fit Types"}),s.jsx("div",{className:"d-flex flex-wrap gap-4 text-capitalize",children:Object.keys(n).map(U=>s.jsx(ke,{name:U,control:c,render:({field:{onChange:ne,value:H}})=>s.jsx(Jn,{checked:H,label:U,onChange:V=>{ne(V.target.checked),C("selection")}})},U))})]})]}),s.jsxs("div",{className:"d-flex w-full justify-end gap-x-2 pr-2 template-actions mt-auto",children:[s.jsx(fe,{buttonContent:"Save",isLoading:T,isDisabled:w||T,onclickHandler:()=>{},extraClass:"color-white border-none",buttonProps:{type:"submit"},tooltipId:"save-fit-types-selection"}),_.name&&s.jsx(fe,{buttonContent:"Cancel",onclickHandler:Q,isDisabled:T,tooltipId:"cancel-fit-types-selection",extraClass:"outlined-btn",buttonProps:{type:"button"}})]})]})}):s.jsx(Up,{templates:S,onEdit:q,onDelete:de,isProcessing:w||T,handleApply:oe})]})})}),s.jsx(Je,{open:x.modalOpen,children:s.jsx(Yt,{deleteModalState:x,handleModalVisibility:de,handleDeleteAction:pe,modalId:"delete-fit-types-selection-template",deleteHeaderTitle:"Confirm template deletion",confirmationMessage:"Are you sure you want to delete this template?"})})]})},zp=({selectors:e})=>{const[t,a]=b.useState(!1),n=r=>{bl(r,e==null?void 0:e.checkbox)};return s.jsxs(s.Fragment,{children:[s.jsx(fe,{buttonContent:"Templates",onclickHandler:()=>a(!0),tooltipId:"product-fit-types-templates"}),t&&s.jsx(Vp,{setOpen:a,applyTemplate:n,fitsCheckboxSelector:e==null?void 0:e.checkbox})]})},Na=Pa(e=>({isLoading:!1,title:"",updateLoadingState:(t,a)=>e({isLoading:t,title:a||""})})),Kp=({checked:e,onChange:t,id:a,extraClass:n="",inputProps:r={}})=>s.jsx("div",{"data-tooltip-id":a,className:`merch-color-checkbox-container position-relative ${n}`,children:s.jsxs("label",{className:"mb-0 h-full w-full",children:[s.jsx("input",{type:"checkbox",className:"cursor-pointer",checked:e,onChange:t,...r}),s.jsx("span",{className:"checkbox"})]})}),qp=({template:e,onEdit:t,onDelete:a,isProcessing:n,handleApply:r})=>{var u;const[o,l]=b.useState(!1),i=b.useRef(null),d=b.useMemo(()=>Ge(e.template_metadata,{values:[]}),[e.template_metadata]),f=(h,g)=>{h.stopPropagation(),h.preventDefault(),g?t(e):a(h,e)},c=h=>{h.stopPropagation(),h.preventDefault(),r(e)},p=h=>{h.stopPropagation(),h.preventDefault(),l(g=>!g)};return b.useEffect(()=>{var h;i.current&&(o?((h=i.current)==null||h.scrollIntoView({behavior:"smooth",block:"nearest"}),i.current.style.height=`${i.current.scrollHeight}px`):i.current.style.height="0")},[o]),s.jsxs("li",{className:`d-flex w-full flex-col justify-between secondary-border-bottom ${o?"gap-1 pb-2":""}`,children:[s.jsxs("div",{className:"d-flex justify-between gap-2 action-container",children:[s.jsx("div",{className:"text-base w-full item-title cursor-pointer text-break-word",onClick:c,children:e.name}),s.jsxs("div",{className:"action-buttons d-flex h-fit gap-3",children:[s.jsx("button",{onClick:h=>{f(h,"edit")},disabled:n,children:s.jsx(Tn,{size:12})}),s.jsx("button",{onClick:f,disabled:n,style:{background:"var(--color-danger-light)"},children:s.jsx(sn,{size:12})}),s.jsx("button",{onClick:p,disabled:n,className:"i-text-main i-main-border bg-main expand-action",children:s.jsx("div",{className:"d-flex",style:o?{transform:"rotate(-90deg)",transition:"0.3s ease-in-out"}:{transition:"0.3s ease-in-out"},children:s.jsx(on,{size:12})})})]})]}),s.jsx("div",{className:`d-flex flex-wrap gap-4 text-capitalize expandable-section ${o?"expanded":""}`,ref:i,children:o&&((u=d==null?void 0:d.values)==null?void 0:u.map(h=>s.jsx("div",{className:`merch-color-checkbox-container mrdn-color-${h}`},h)))})]})},Wp=({templates:e,onEdit:t,onDelete:a,isProcessing:n=!1,handleApply:r})=>s.jsx("div",{className:"item-list-container position-relative",children:e.length?s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"d-flex justify-end text-main font-semibold",children:["Total Templates: ",e.length]}),s.jsx("ul",{className:"d-flex flex-col text-primary i-pb-4 i-pt-3",children:e.map(o=>{const l=`${o.name}-${o.id}`;return s.jsx(qp,{template:o,onEdit:t,onDelete:a,handleApply:r,isProcessing:n},l)})})]}):s.jsx("div",{className:"no-content-text text-center",children:n?s.jsx(mn,{}):"No Templates Found"})}),Gp=b.memo(Wp),Qa={name:"",category:"",template_metadata:"",id:0},Yp=({setOpen:e,applyTemplate:t,colorCheckboxSelector:a,productColors:n})=>{var ne;const r=jr(Ji(H=>H.constants.COLOR_DEFAULT_VALUES))||{},o=gt({defaultValues:{colors:{...r},...ks},mode:"onChange",resolver:Lt(ia)}),{maxTemplateCount:l,hasUnlimitedAccess:i,importTemplateCount:d,exportTemplateCount:f}=Pt(H=>H),c=Tt(H=>H.setUpgradePlanModal),{control:p,getValues:u,setValue:h,setError:g,watch:v,formState:{errors:C},clearErrors:j}=o,N=b.useRef(null),[M,I]=b.useState(et.ADD),[F,S]=b.useState([]),[R,w]=b.useState([]),[m,T]=b.useState(!0),[E,O]=b.useState(!1),[A,_]=b.useState(!1),[D,x]=b.useState({...Qa}),[y,G]=b.useState({modalOpen:!1,template:null,processing:!1}),$=v("searchText"),K=pn($,500),J=M===et.ADD,Y=b.useCallback(async()=>{const H=await Xt(Va);H!=null&&H.templates&&S(H==null?void 0:H.templates),T(!1)},[]),ee=b.useCallback((H,V)=>{if(!H)return w(V);w(V.filter(ce=>ce.name.toLowerCase().includes(H.toLowerCase())))},[]);b.useEffect(()=>{a&&Ds(a).forEach(H=>h(`colors.${H}`,!0)),Y()},[]),b.useEffect(()=>{m||ee(K,F)},[K,m,F]);const Z=b.useCallback(H=>{I(H),jn(N,"#modal-product-colors-templates .mrdn-modal-body")},[]),q=b.useCallback((H,V)=>{const ce=u(),k=Object.entries(ce.colors),L={};switch(V){case"selectAll":k.forEach(([B,W])=>{L[B]=!0});break;case"deselectAll":k.forEach(([B,W])=>{L[B]=!1}),g("selection",{type:"manual",message:"At least one color must be selected"});break;case"selectAlter":k.forEach(([B,W])=>{L[B]=!W});break;case"selectLite":k.forEach(([B,W])=>{var z;L[B]=!((z=n[B])!=null&&z.is_dark)});break;case"selectDark":k.forEach(([B,W])=>{var z;L[B]=(z=n[B])==null?void 0:z.is_dark});break}h("colors",L)},[u]),Q=async H=>{if(!E){O(!0);try{const{templateTitle:V,colors:ce}=u(),k=Object.entries(ce).filter(([te,X])=>X).map(([te])=>te);if(!k.length){g("selection",{type:"manual",message:"At least one color must be selected"});return}const L=V.trim(),B=D.id,W={name:L,category:Va,template_metadata:{values:k}},z=B?await _n(B,W):await Qt(W);if(z!=null&&z.name){De("Template",B?"updated":"saved"),B&&x(Qa),await Y();return}if((z==null?void 0:z.status)===429){c({isVisible:!0,showTemplateText:!0,modalTitle:(z==null?void 0:z.title)||"Template Limit Reached",modalDescription:(z==null?void 0:z.description)||dn(l,"save","template")});return}await Y(),se(z==null?void 0:z.message)}catch{se("An error occurred while saving the template")}finally{O(!1)}}},oe=H=>{const V=Ge(H.template_metadata);x(L=>({...L,...H}));const ce=new Set((V==null?void 0:V.values)||[]),k=Object.keys(n).reduce((L,B)=>(L[B]=ce.has(B),L),{});h("colors",k),h("templateTitle",H.name),Z(et.ADD),j("selection")},de=b.useCallback(()=>{o.setValue("templateTitle",""),x(H=>({...H,...Qa})),j("selection")},[]),pe=b.useCallback((H,V)=>{y.processing||G(ce=>({...ce,modalOpen:!ce.modalOpen,template:(V==null?void 0:V.id)||null}))},[y.processing]),Ce=async()=>{if(!y.processing){G(H=>({...H,processing:!0}));try{const{template:H}=y,V=await fn(H);if(V!=null&&V.success){De("Template","deleted"),await Y();return}await Y(),se(V==null?void 0:V.message)}catch{se("An error occurred while deleting the template")}finally{y.template===D.id&&x(Qa),G(H=>({...H,modalOpen:!1,template:null,processing:!1}))}}},re=H=>{if(A||E)return;_(!0);const V=Ge(H.template_metadata);t(V.values||[]),_(!1),e(!1)},U=b.useCallback(async H=>{const{uniqueTemplates:V,uniqueItems:ce}=$s(H,Va);if(!ce){De("Import complete. No valid templates were available to import.");return}const k=await hn(V);if(k!=null&&k.success){De((k==null?void 0:k.message)||ln((k==null?void 0:k.count)||0)),await Y();return}if((k==null?void 0:k.status)===429){const L=(k==null?void 0:k.description)||cn(k==null?void 0:k.count,d);c({isVisible:!0,showTemplateText:!0,modalTitle:(k==null?void 0:k.title)||"Template Limit Reached",modalDescription:L}),(+L.split(" ")[0]||0)&&await Y();return}se(k==null?void 0:k.message)},[d]);return s.jsxs(s.Fragment,{children:[s.jsx(Je,{open:!0,children:s.jsx(pt,{open:!0,width:"50vw",handleClose:H=>{H.stopPropagation(),e(!1)},uniqueId:"product-colors-templates",modalTitle:"Color Templates",modalTitleChildren:i?null:s.jsx(un,{showTemplateLimit:!0,templateLimit:l,buttonId:"color-templates-upgrade-button"}),bodySection:s.jsxs("div",{className:"d-flex flex-col gap-y-3 h-full",children:[s.jsxs("div",{className:"d-flex justify-between",children:[s.jsx(Wt,{tabOptions:Sn,activeTab:M,handleActiveTab:Z}),J?s.jsx(gn,{collectionKey:Va,fileName:"merch-dominator-color-templates.json",processTemplateImport:U,extraClass:"w-full justify-end align-items-center",templateLimit:f}):s.jsx("div",{className:"w-full position-relative",children:s.jsx(ke,{name:"searchText",control:p,render:({field:{onChange:H,value:V}})=>s.jsx(qe,{onChange:H,value:V,uniqueId:"colors-search_input",labelClass:"flex !font-medium",inputClassName:"merch-input",placeHolder:"Search by title",isDisabled:!1,allowClear:{clearIcon:s.jsx($t,{size:16})},addonBefore:s.jsx("div",{className:"d-flex h-full pl-2 align-items-center product-search-box text-primary",children:s.jsx(Gt,{})}),hasPermission:!0})})})]}),J?s.jsx(bt,{...o,children:s.jsxs("form",{onSubmit:o.handleSubmit(Q),className:"d-flex flex-col gap-y-3 h-full",id:"colors-template-form",children:[s.jsx("div",{className:"w-full d-flex flex-col gap-y-4",children:s.jsx(ke,{control:p,name:"templateTitle",render:({field:{onChange:H,value:V}})=>{var ce;return s.jsx(qe,{uniqueId:"colors-template-title-input",type:"text",placeholder:"Enter template name",value:V,onChange:H,label:"Add New Template*",extraClass:"w-full",helperText:(ce=C==null?void 0:C.templateTitle)==null?void 0:ce.message})}})}),s.jsxs("div",{className:"merch-dominator-selection-container highlighted-selection-container",id:"mrdn-colors-selection-container",children:[s.jsx(At,{message:((ne=C==null?void 0:C.selection)==null?void 0:ne.message)||""}),s.jsxs("div",{className:"d-flex flex-col gap-y-2",children:[s.jsx("span",{children:"Select Colors"}),s.jsx("div",{className:"d-flex justify-end flex-wrap gap-2 mb-2",children:ll.map(({content:H,action:V,tooltipId:ce,tooltipTitle:k})=>s.jsx(Ut,{tooltipId:ce,tooltipChildren:k,contentChildren:s.jsx(fe,{buttonContent:H,onclickHandler:L=>q(L,V),tooltipId:ce,extraClass:"outlined-btn",buttonProps:{type:"button"}}),isClickable:!1},V))}),s.jsx("div",{className:"d-flex flex-wrap gap-4 text-capitalize",children:Object.entries(r).map(([H,V])=>s.jsx(ke,{name:`colors.${H}`,control:p,render:({field:{onChange:ce,value:k}})=>s.jsx(Ut,{tooltipId:`mrdn-color-${H}-tooltip`,tooltipChildren:H.replaceAll("_"," "),contentChildren:s.jsx(Kp,{id:`mrdn-color-${H}-tooltip`,checked:k,onChange:L=>{ce(L.target.checked),j("selection")},extraClass:`mrdn-color-${H}`}),isClickable:!1})},H))})]})]}),s.jsxs("div",{className:"d-flex w-full justify-end gap-x-2 pr-2 template-actions mt-auto",children:[s.jsx(fe,{buttonContent:"Save",isLoading:E,isDisabled:m||E,onclickHandler:()=>{},extraClass:"color-white border-none",buttonProps:{type:"submit"},tooltipId:"save-colors-selection"}),D.name&&s.jsx(fe,{buttonContent:"Cancel",onclickHandler:de,isDisabled:E,tooltipId:"cancel-colors-selection",extraClass:"outlined-btn",buttonProps:{type:"button"}})]})]})}):s.jsx(Gp,{templates:R,onEdit:oe,onDelete:pe,isProcessing:m||E,handleApply:re})]})})}),s.jsx(Je,{open:y.modalOpen,children:s.jsx(Yt,{deleteModalState:y,handleModalVisibility:pe,handleDeleteAction:Ce,modalId:"delete-color-selection-template",deleteHeaderTitle:"Confirm template deletion",confirmationMessage:"Are you sure you want to delete this template?"})})]})},Xp=({selectors:e,productColors:t})=>{const a=Na(g=>g.updateLoadingState),{colorsContainer:n,checkbox:r,activeProductCard:o,editBtnPostfix:l}=e,[i,d]=b.useState(!1),[f,c]=b.useState(!1),p=b.useCallback((g,v,C)=>{if(g.stopPropagation(),C)return;c(!0);const N=g.target.closest(n);if(!N)return;const M=N.querySelectorAll(r);switch(v){case"selectAll":M.forEach(I=>{var F;(F=I.querySelector("i"))!=null&&F.classList.contains("sci-check")||I.click()});break;case"deselectAll":M.forEach(I=>{const F=I.querySelector("i");F&&F.classList.contains("sci-check")&&I.click()});break;case"selectAlter":M.forEach(I=>{I.click()});break;case"selectLite":M.forEach(I=>{var R;const F=(R=I.querySelector("i"))==null?void 0:R.classList.contains("sci-check");let S="";I.classList.forEach(w=>{w.startsWith("checkbox-")&&(S=w.slice(9))}),(t[S]&&!F&&!t[S].is_dark||F&&t[S].is_dark)&&I.click()});break;case"selectDark":M.forEach(I=>{var R;const F=(R=I.querySelector("i"))==null?void 0:R.classList.contains("sci-check");let S="";I.classList.forEach(w=>{w.startsWith("checkbox-")&&(S=w.slice(9))}),(t[S]&&!F&&t[S].is_dark||F&&!t[S].is_dark)&&I.click()});break}c(!1)},[]),u=async g=>{var N,M,I,F;if(g.stopPropagation(),f)return;if(c(!0),!+((N=Pc(document,{id:"mrdn-selected-color-count"}))==null?void 0:N.textContent)){se("Please select at least one color"),c(!1);return}const C=Ds(`${n} ${r}`);a(!0,"Applying color selection...");const j=Ms(o);for(const S of j)(M=qt[S])!=null&&M.colors&&((I=document.querySelector(`.product-card .${S}${l}`))==null||I.click(),await Me(500),ds(C,`${n} ${r}`,(F=qt[S])==null?void 0:F.default_color));a(!1),c(!1)},h=g=>{ds(g,`${e.colorsContainer} ${e.checkbox}`)};return s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"d-flex flex-col gap-2 mrdn-colors-editor-container",children:[s.jsx("div",{className:"d-flex justify-between py-2 px-4 font-semibold secondary-border-bottom",style:{backgroundColor:"var(--actions-bg)"},children:"Colors editor:"}),s.jsxs("div",{className:"d-flex flex-col gap-2 pb-2 px-4 secondary-border-bottom",children:[s.jsx("span",{id:"mrdn-color-selection-title",children:"Which color might Amazon use?"}),s.jsxs("div",{className:"d-flex gap-2 mrdn-color-suggestion-container",children:[s.jsx("div",{className:"border-r-1 d-flex mrdn-color-black secondary-border",style:{height:"32px",width:"32px"}}),s.jsx("span",{className:"mrdn-color-suggestion-title my-auto h-fit text-capitalize",children:"Black"})]})]}),s.jsxs("div",{className:"d-flex justify-between py-2 px-4",id:"mrdn-color-selection",children:[s.jsxs("div",{className:"d-flex flex-wrap gap-2",children:[ll.map(({content:g,action:v,tooltipId:C,tooltipTitle:j})=>s.jsx(Ut,{tooltipId:`selection-${C}`,tooltipChildren:j,contentChildren:s.jsx(fe,{buttonContent:g,onclickHandler:N=>p(N,v,f),isDisabled:f,tooltipId:`selection-${C}`,extraClass:"outlined-btn"}),isClickable:!1},v)),s.jsx(Ut,{tooltipId:"apply-current-selection",tooltipChildren:"Apply the selected colors of the current product to all products",contentChildren:s.jsx(fe,{buttonContent:"To All Products",onclickHandler:u,tooltipId:"apply-current-selection",extraClass:"outlined-btn"}),isClickable:!1})]}),s.jsx(fe,{buttonContent:"Templates",onclickHandler:()=>d(!0),tooltipId:"product-selection-templates"})]})]}),i&&s.jsx(Yp,{setOpen:d,applyTemplate:h,colorCheckboxSelector:`${n} ${r}`,productColors:t})]})};let Za=null;const Qp=({selectors:{colorCheckbox:e,assetContainer:t},colorPriority:a})=>{const[n,r]=b.useState(0),o=b.useCallback(()=>{Za&&clearTimeout(Za),Za=setTimeout(()=>{var p,u,h,g;let l=0,i=[];document.querySelectorAll(`${e} i.sci-check`).forEach(v=>{const C=Is(v==null?void 0:v.parentElement);C&&i.push(C),l+=1});let d=Wu(a,i);if(!d){const v=Sr(document.querySelector(t));d=((p=qt[v])==null?void 0:p.default_color)||"black"}const f=`mrdn-color-${d}`,c=document.querySelector(".mrdn-color-suggestion-container");for(const v of((u=c==null?void 0:c.firstElementChild)==null?void 0:u.classList)||[]){if(!(c!=null&&c.firstElementChild)||v===f)break;v.startsWith("mrdn-color-")?(h=c==null?void 0:c.firstElementChild)==null||h.classList.replace(v,f):(g=c==null?void 0:c.firstElementChild)==null||g.classList.add(f),c.lastElementChild&&(c.lastElementChild.textContent=d==null?void 0:d.replaceAll("_"," "))}r(l)},300)},[Za]);return b.useEffect(()=>{o();const l=document.querySelectorAll(`${e}`);return l.forEach(i=>{i.addEventListener("click",o)}),()=>{l.forEach(i=>{i.removeEventListener("click",o)})}},[]),s.jsxs("div",{className:"selected-color-count d-flex gap-1 align-items-center",style:{fontWeight:"700"},id:"mrdn-selected-color-count",children:[s.jsx(Nc,{className:"text-main",size:16})," ",n]})},Zp=Pa(e=>({price:"1",updatePrice:t=>e({price:t}),royalty:"1",updateRoyalty:t=>e({royalty:t})})),Jp=({template:e,onEdit:t,onDelete:a,isProcessing:n,handleApply:r})=>{var u;const[o,l]=b.useState(!1),i=b.useRef(null),d=b.useMemo(()=>Ge(e.template_metadata,{values:[]}),[e.template_metadata]),f=(h,g)=>{h.stopPropagation(),h.preventDefault(),g?t(e):a(h,e)},c=h=>{h.stopPropagation(),h.preventDefault(),r(e)},p=h=>{h.stopPropagation(),h.preventDefault(),l(g=>!g)};return b.useEffect(()=>{var h;i.current&&(o?((h=i.current)==null||h.scrollIntoView({behavior:"smooth",block:"nearest"}),i.current.style.height=`${i.current.scrollHeight}px`):i.current.style.height="0")},[o]),s.jsxs("li",{className:`d-flex w-full flex-col justify-between secondary-border-bottom ${o?"gap-1 pb-2":""}`,children:[s.jsxs("div",{className:"d-flex justify-between gap-2 action-container",children:[s.jsx("div",{className:"text-base w-full item-title cursor-pointer text-break-word",onClick:c,children:e.name}),s.jsxs("div",{className:"action-buttons d-flex h-fit gap-3",children:[s.jsx("button",{onClick:h=>{f(h,"edit")},disabled:n,children:s.jsx(Tn,{size:12})}),s.jsx("button",{onClick:f,disabled:n,style:{background:"var(--color-danger-light)"},children:s.jsx(sn,{size:12})}),s.jsx("button",{onClick:p,disabled:n,className:"i-text-main i-main-border bg-main expand-action",children:s.jsx("div",{className:"d-flex",style:o?{transform:"rotate(-90deg)",transition:"0.3s ease-in-out"}:{transition:"0.3s ease-in-out"},children:s.jsx(on,{size:12})})})]})]}),s.jsx("div",{className:`gap-4 expandable-section ${o?"expanded":""}`,style:{display:"grid",gridTemplateColumns:"repeat(4, 1fr)"},ref:i,children:o&&((u=d==null?void 0:d.values)==null?void 0:u.map((h,g)=>{if(!h)return null;const v=il[g+1];return s.jsxs("div",{className:"d-flex flex-col gap-2",children:[s.jsxs("span",{className:"text-uppercase",children:[v,s.jsxs("span",{className:"text-lowercase text-13px text-faded",children:[" (",oa[v],")"]})]}),s.jsxs("div",{className:"d-flex align-items-center gap-1",children:[s.jsx("span",{style:{fontWeight:600,fontSize:"17px"},children:cl[v]})," ",h]})]},g)}))})]})},ef=({templates:e,onEdit:t,onDelete:a,isProcessing:n=!1,handleApply:r})=>s.jsx("div",{className:"item-list-container position-relative",children:e.length?s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"d-flex justify-end text-main font-semibold",children:["Total Templates: ",e.length]}),s.jsx("ul",{className:"d-flex flex-col text-primary i-pb-4 i-pt-3",children:e.map(o=>{const l=`${o.name}-${o.id}`;return s.jsx(Jp,{template:o,onEdit:t,onDelete:a,handleApply:r,isProcessing:n},l)})})]}):s.jsx("div",{className:"no-content-text text-center",children:n?s.jsx(mn,{}):"No Templates Found"})}),tf=b.memo(ef),Ja={name:"",category:"",template_metadata:"",id:0},nf=({setOpen:e,applyTemplate:t,priceInputSelector:a,priceInputContainerSelector:n})=>{var re;const r=gt({defaultValues:Zd,mode:"onChange",resolver:Lt(ia)}),{control:o,getValues:l,setValue:i,setError:d,watch:f,formState:{errors:c},clearErrors:p}=r,{maxTemplateCount:u,hasUnlimitedAccess:h,importTemplateCount:g,exportTemplateCount:v}=Pt(U=>U),C=Tt(U=>U.setUpgradePlanModal),j=b.useRef(null),[N,M]=b.useState(et.ADD),[I,F]=b.useState([]),[S,R]=b.useState([]),[w,m]=b.useState(!0),[T,E]=b.useState(!1),[O,A]=b.useState(!1),[_,D]=b.useState({...Ja}),[x,y]=b.useState({modalOpen:!1,template:null,processing:!1}),G=f("searchText"),$=pn(G,500),K=N===et.ADD,J=b.useCallback(async()=>{const U=await Xt(za);U!=null&&U.templates&&F(U==null?void 0:U.templates),m(!1)},[]);b.useEffect(()=>{a&&n&&Object.entries(Qu(a,n)).forEach(([U,ne])=>{i(`prices.${U}`,ne)}),J()},[]);const Y=b.useCallback((U,ne)=>{if(!U)return R(ne);R(ne.filter(H=>H.name.toLowerCase().includes(U.toLowerCase())))},[]);b.useEffect(()=>{w||Y($,I)},[$,w,I]);const ee=b.useCallback(U=>{M(U),jn(j,"#modal-product-prices-templates .mrdn-modal-body")},[]),Z=async U=>{if(!T){E(!0);try{const{templateTitle:ne,prices:H}=l(),V=[];let ce=!1;for(const z of Object.values(H)){const te=z.trim();te&&(ce=!0),V.push(te)}if(!ce){d("selection",{type:"manual",message:"Please enter a price for at least one marketplace."});return}const k=ne.trim(),L=_.id,B={name:k,category:za,template_metadata:{values:V}},W=L?await _n(L,B):await Qt(B);if(W!=null&&W.name){De("Template",L?"updated":"saved"),L&&D(z=>({...z,...Ja})),await J();return}if((W==null?void 0:W.status)===429){C({isVisible:!0,showTemplateText:!0,modalTitle:(W==null?void 0:W.title)||"Template Limit Reached",modalDescription:(W==null?void 0:W.description)||dn(u,"save","template")});return}await J(),se(W==null?void 0:W.message)}catch{se("An error occurred while managing the template.")}finally{E(!1)}}},q=U=>{const ne=Ge(U.template_metadata);D(V=>({...V,...U}));const H=Object.keys(eu).reduce((V,ce,k)=>(V[ce]=ne.values[k]||"",V),{});i("prices",H),i("templateTitle",U.name),ee(et.ADD),p("selection")},Q=b.useCallback(()=>{r.setValue("templateTitle",""),D(U=>({...U,...Ja})),p("selection")},[]),oe=b.useCallback((U,ne)=>{x.processing||y(H=>({...H,modalOpen:!H.modalOpen,template:(ne==null?void 0:ne.id)||null}))},[x.processing]),de=async()=>{if(!x.processing){y(U=>({...U,processing:!0}));try{const{template:U}=x,ne=await fn(U);if(ne!=null&&ne.success){De("Template","deleted"),await J();return}await J(),se(ne==null?void 0:ne.message)}catch{se("An error occurred while deleting the template")}finally{x.template===_.id&&D(U=>({...U,...Ja})),y(U=>({...U,modalOpen:!1,template:null,processing:!1}))}}},pe=U=>{if(O||T)return;A(!0);const ne=Ge(U.template_metadata);t(ne.values||[]),A(!1),e(!1)},Ce=b.useCallback(async U=>{const{uniqueTemplates:ne,uniqueItems:H}=$s(U,za);if(!H){De("Import complete. No valid templates were available to import.");return}const V=await hn(ne);if(V!=null&&V.success){De((V==null?void 0:V.message)||ln((V==null?void 0:V.count)||0)),await J();return}if((V==null?void 0:V.status)===429){const ce=(V==null?void 0:V.description)||cn(V==null?void 0:V.count,g);C({isVisible:!0,showTemplateText:!0,modalTitle:(V==null?void 0:V.title)||"Template Limit Reached",modalDescription:ce}),(+ce.split(" ")[0]||0)&&await J();return}se(V==null?void 0:V.message)},[g]);return s.jsxs(s.Fragment,{children:[s.jsx(Je,{open:!0,children:s.jsx(pt,{open:!0,width:"50vw",handleClose:U=>{U.stopPropagation(),e(!1)},uniqueId:"product-prices-templates",modalTitle:"Price Templates",modalTitleChildren:h?null:s.jsx(un,{showTemplateLimit:!0,templateLimit:u,buttonId:"color-templates-upgrade-button"}),bodySection:s.jsxs("div",{className:"d-flex flex-col gap-y-3 h-full",children:[s.jsxs("div",{className:"d-flex justify-between",children:[s.jsx(Wt,{tabOptions:Sn,activeTab:N,handleActiveTab:ee}),K?s.jsx(gn,{collectionKey:za,fileName:"merch-dominator-prices-templates.json",processTemplateImport:Ce,extraClass:"w-full justify-end align-items-center",templateLimit:v}):s.jsx("div",{className:"w-full position-relative",children:s.jsx(ke,{name:"searchText",control:o,render:({field:{onChange:U,value:ne}})=>s.jsx(qe,{onChange:U,value:ne,uniqueId:"price-template-search_input",labelClass:"flex !font-medium",inputClassName:"merch-input",placeHolder:"Search by title",isDisabled:!1,allowClear:{clearIcon:s.jsx($t,{size:16})},addonBefore:s.jsx("div",{className:"d-flex h-full pl-2 align-items-center text-primary",children:s.jsx(Gt,{})}),hasPermission:!0})})})]}),K?s.jsx(bt,{...r,children:s.jsxs("form",{onSubmit:r.handleSubmit(Z),className:"d-flex flex-col gap-y-3 h-full",id:"price-template-form",children:[s.jsx("div",{className:"w-full d-flex flex-col gap-y-4",children:s.jsx(ke,{control:o,name:"templateTitle",render:({field:{onChange:U,value:ne}})=>{var H;return s.jsx(qe,{uniqueId:"price-template-title-input",type:"text",placeholder:"Enter template name",value:ne,onChange:U,label:"Add New Template*",extraClass:"w-full",helperText:(H=c==null?void 0:c.templateTitle)==null?void 0:H.message})}})}),s.jsxs("div",{className:"merch-dominator-selection-container highlighted-selection-container",id:"mrdn-price-selection-container",children:[s.jsx(At,{message:((re=c==null?void 0:c.selection)==null?void 0:re.message)||""}),s.jsx("div",{className:"mrdn-price-inputs",children:Jd.map(({marketPlace:U,symbol:ne,label:H},V)=>s.jsxs("div",{className:"d-flex",children:[s.jsx("span",{className:"price-label bg-merch-dominator d-flex align-items-center justify-center px-4 h-38px w-38px mt-auto color-white w-38px",children:ne}),s.jsx(ke,{name:`prices.${U}`,control:o,render:({field:{onChange:ce,value:k}})=>s.jsx(qe,{onChange:L=>{ce(ss(L.target.value))},onBlur:L=>{const B=L.target.value;p("selection"),B.endsWith(".")&&i(`prices.${U}`,B.slice(0,-1))},value:k,uniqueId:`price_${V}_input`,label:s.jsxs("span",{className:"text-uppercase",children:[U,s.jsxs("span",{className:"text-lowercase text-13px text-faded",children:[" (",H,")"]})]}),labelClass:"flex !font-medium",inputClassName:"merch-input",placeHolder:"Default",isDisabled:!1,extraClass:"w-full",hasPermission:!0})})]},U))})]}),s.jsxs("div",{className:"d-flex w-full justify-end gap-x-2 pr-2 template-actions mt-auto",children:[s.jsx(fe,{buttonContent:"Save",isLoading:T,isDisabled:w||T,onclickHandler:()=>{},extraClass:"color-white border-none",buttonProps:{type:"submit"},tooltipId:"save-price-selection"}),_.name&&s.jsx(fe,{buttonContent:"Cancel",onclickHandler:Q,isDisabled:T,tooltipId:"cancel-price-selection",extraClass:"outlined-btn",buttonProps:{type:"button"}})]})]})}):s.jsx(tf,{templates:S,onEdit:q,onDelete:oe,isProcessing:w||T,handleApply:pe})]})})}),s.jsx(Je,{open:x.modalOpen,children:s.jsx(Yt,{deleteModalState:x,handleModalVisibility:oe,handleDeleteAction:de,modalId:"delete-price-selection-template",deleteHeaderTitle:"Confirm template deletion",confirmationMessage:"Are you sure you want to delete this template?"})})]})},af=({selectors:e,priceRanges:t,priceAdjustmentIncrement:a})=>{const n=Na(m=>m.updateLoadingState),[r,o]=b.useState(!1),[l,i]=b.useState(!1),{price:d,royalty:f,updatePrice:c,updateRoyalty:p}=Zp(m=>m),{input:u,assetContainer:h,input_container:g,activeProductCard:v,editBtnPostfix:C,royalty_container:j}=e,N=(m,T)=>{var A;m.stopPropagation();const E=(A=m.target)==null?void 0:A.closest(e.listing_details),O=Mt(h);E==null||E.querySelectorAll(e.input).forEach(_=>{const D=ra(_,g),x=T==="increment"?+d:-1*+d,{price:y}=yn(t,_.value,x,O,D);_.value=y,aa(_)})},M=(m,T,E)=>{var x;m.stopPropagation();const O=E===oa.jp,A=(parseFloat(T.value)||0)*(O?.01:1),_=(x=m.target)==null?void 0:x.closest(e.listing_details),D=Mt(h);_==null||_.querySelectorAll(e.input).forEach(y=>{const G=ra(y,g),{price:$}=yn(t,"0",A,D,G);y.value=$,aa(y)})},I=(m,T,E)=>{const O=Mt(h),_=yn(t,m.value,T,O,E).price;m.value=_,aa(m)},F=(m,T)=>{var x,y;const E=(m==null?void 0:m.target)||m;if(!E)return;const O=Mt(h),{isMaxLimitReached:A,isMinLimitReached:_}=yn(t,E.value,0,O,T),D=E.parentElement;(x=D==null?void 0:D.querySelector(".mrdn-single-price-increase"))==null||x.classList.toggle("mrdn-disabled",A),(y=D==null?void 0:D.querySelector(".mrdn-single-price-decrease"))==null||y.classList.toggle("mrdn-disabled",_)};b.useEffect(()=>{const m=setInterval(()=>{document.querySelectorAll(u).forEach((T,E)=>{var x,y,G,$;const O=ra(T,g),A=Ro("mrdn-single-price-decrease bg-merch-dominator","-",K=>I(K,-1,O),T==null?void 0:T.previousElementSibling,T,`decrease_price_${E}`);A&&((x=T==null?void 0:T.parentNode)==null||x.insertBefore(A,T));const _=Ro("mrdn-single-price-increase bg-merch-dominator","+",K=>I(K,1,O),T.nextElementSibling,T,`increase_price_${E}`);if(_&&((y=T==null?void 0:T.parentNode)==null||y.insertBefore(_,T.nextSibling)),!((G=T.parentNode)==null?void 0:G.querySelector(".mrdn-price-assign"))){const K=document.createElement("span");K.className="mrdn-price-assign",K.setAttribute("data-tooltip-id",`assign-price-${E}`),K.onclick=Y=>M(Y,T,O),($=T.parentNode)==null||$.appendChild(K);const J=document.createElement("div");J.className="mrdn-price-assign-icon bg-merch-dominator ml-1 h-full",J.innerHTML='<svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 512 512" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="none" stroke-linejoin="round" stroke-width="32" d="M416 221.25V416a48 48 0 0 1-48 48H144a48 48 0 0 1-48-48V96a48 48 0 0 1 48-48h98.75a32 32 0 0 1 22.62 9.37l141.26 141.26a32 32 0 0 1 9.37 22.62z"></path><path fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M256 56v120a32 32 0 0 0 32 32h120m-232 80h160m-160 80h160"></path></svg>',K.appendChild(J),T.addEventListener("input",Y=>F(Y,O))}F(T,O)})},1e3);return()=>{clearInterval(m)}},[]);const S=async m=>{var _;if(l){se("Please wait for the current operation to complete.");return}i(!0);const T=(_=m.target)==null?void 0:_.closest(e.listing_details),E=Mt(h),O=(T==null?void 0:T.querySelectorAll(e.input))||[],A=Array.from(O).map(async D=>{await Uo(t,a,g,D,E,+f)});await Promise.all(A),i(!1)},R=async m=>{var E;if(m.stopPropagation(),l){se("Please wait for the current operation to complete.");return}i(!0),Os(h,C),n(!0,"Applying royalty..."),await Me(1500);const T=Ms(v);for(const O of T){(E=document.querySelector(`.product-card .${O}${C}`))==null||E.click(),await Me(700),await cp(j,"calculating",400,100,3e4);const A=(document==null?void 0:document.querySelectorAll(`${g} ${e.input}`))||[],_=Array.from(A).map(async D=>{await Uo(t,a,g,D,O,+f)});await Promise.all(_)}n(!1),i(!1)},w=async m=>{const T=Mt(h);yl(t,m,T,`${g} ${e.input}`,g)};return s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"d-flex justify-end pt-2 px-4",children:s.jsx(fe,{buttonContent:"Templates",onclickHandler:()=>o(!0),tooltipId:"product-prices-templates"})}),s.jsxs("div",{className:"d-flex gap-2 py-2 px-4",children:[s.jsx(Ut,{contentChildren:s.jsx(qe,{onChange:m=>{p(ss(m.target.value)||"1")},onBlur:m=>{const T=m.target.value;T.endsWith(".")&&p(T.slice(0,-1))},value:f,label:s.jsxs(s.Fragment,{children:["Royalties",s.jsx(vr,{extraClass:"i-p-0 d-flex-important align-items-center",children:s.jsx(ws,{size:14}),isButtonType:!1,tooltipChildren:s.jsx("div",{className:"d-flex flex-col",children:s.jsxs("ul",{style:{color:"var(--color-white) !important",marginBlock:"0.25rem"},children:[s.jsxs("li",{className:"text-left",children:["Royalty is calculated based on your"," ",s.jsx("strong",{children:"entered price"}),"."]}),s.jsxs("li",{className:"text-left",children:["If the calculated price after applying royalty falls outside the allowed ",s.jsx("strong",{children:"Minimum"})," or"," ",s.jsx("strong",{children:"Maximum"})," price range, the system will adjust the royalty using the closest valid price limit."]}),s.jsx("li",{className:"text-left",children:"Price increase or decrease actions will be disabled for each marketplace if the minimum or maximum price limit is already reached."})]})}),tooltipId:"royalty-guide-tooltip"})]}),uniqueId:"royalty_input",placeHolder:"Enter Royalty",inputClassName:"merch-input h-31px",labelClass:"flex !font-medium",hasPermission:!0,"data-tooltip-id":"royalty-tooltip"}),tooltipChildren:"The amount you earn per sale (0 for min price)",tooltipId:"royalty-tooltip",isClickable:!1}),s.jsx(fe,{buttonContent:"To Product",onclickHandler:S,tooltipId:"apply-current-prices",extraClass:"outlined-btn mt-auto h-31px",isDisabled:l}),s.jsx(fe,{buttonContent:"To All Products",onclickHandler:R,tooltipId:"apply-current-prices-all",extraClass:"outlined-btn mt-auto h-31px",isDisabled:l}),s.jsxs("div",{className:"d-flex prices-action mt-auto",children:[s.jsx(Ut,{contentChildren:s.jsx(fe,{buttonContent:"-",onclickHandler:N,tooltipId:"mrdn-price-decrease"}),tooltipChildren:"Decrement this price to the whole product",tooltipId:"mrdn-price-decrease"}),s.jsx(qe,{onChange:m=>{c(ss(m.target.value)||"1")},onBlur:m=>{const T=m.target.value;T.endsWith(".")&&c(T.slice(0,-1))},value:d,uniqueId:"price-action_input",inputClassName:"merch-input h-31px px-2 text-center",labelClass:"flex !font-medium",hasPermission:!0}),s.jsx(Ut,{contentChildren:s.jsx(fe,{buttonContent:"+",onclickHandler:m=>N(m,"increment"),tooltipId:"mrdn-price-increase"}),tooltipChildren:"increment this price to the whole product",tooltipId:"mrdn-price-increase"})]})]}),s.jsxs("div",{className:"d-flex text-white py-2 font-semibold",style:{background:"var(--text-main-color)",paddingInline:"10px"},children:[s.jsx("span",{style:{visibility:"hidden"},className:"mrdn-marketplace-container",children:"Domain"}),s.jsxs("div",{className:"d-flex mrdn-price-royalty-container",children:[s.jsx("span",{className:"mrdn-price-container px-1",children:"Price"}),s.jsxs("span",{className:"mrdn-royalty-container px-4",children:[" ","Estimated royalty"]})]})]}),Array.from({length:7}).map((m,T)=>s.jsx(Ut,{contentChildren:null,tooltipChildren:"Assign this price to the whole product",tooltipId:`assign-price-${T}`},T)),r&&s.jsx(nf,{applyTemplate:w,setOpen:o,priceInputSelector:`${g} ${e.input}`,priceInputContainerSelector:g})]})},rf=({value:e,uniqueId:t,label:a,onChange:n,isDisabled:r=!1,extraClass:o="",placeHolder:l="",inputClassName:i="",labelClass:d="",helperText:f="",hasPermission:c=!0,allowDecimal:p=!0,showPercentIcon:u=!0,...h})=>{const g=r||!c,v=`h-38px mrdn-input h-[38px] ${i} bg-main ${g?"cursor-disabled opacity-80":""} w-full d-flex-important`,C=N=>{if(g)return;const M=N.target.value;!p&&(M.includes(".")||M&&!/^\d*$/.test(M))||n(N)},j=N=>{var M,I,F;if(!p){let S=(F=(I=(M=N.clipboardData)==null?void 0:M.getData("text"))==null?void 0:I.split(".")[0])==null?void 0:F.replace(/\D/g,"");N.preventDefault(),n({target:{value:S}})}};return s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:`d-flex flex-col ${o}`,children:[a&&s.jsx("label",{className:`d-flex align-items-center gap-x-1 text-primary montserrat-medium color-gray-200 ${d}`,children:a}),s.jsx(Ic,{type:"number",id:t,value:e,onChange:C,onPaste:j,disabled:g,className:v,autoComplete:"off",style:{color:"var(--text-primary-color)"},placeholder:l,...h}),f&&s.jsx("p",{className:"text-red-500 error-message color-danger ml-2 mt-1 font-bold",children:f})]}),u&&s.jsx("div",{className:"d-flex bg-merch-dominator border-r-1 align-items-center justify-center color-white h-31px w-31px mrdn-scale-percent-icon mt-auto ",style:{width:"31px"},children:s.jsx(Oc,{size:20})})]})},nn=b.memo(rf),Sl=({label:e,name:t,value:a,onChange:n,options:r,extraClass:o=""})=>s.jsxs("div",{className:`mrdn-radio-group-container d-flex flex-col gap-1 ${o}`,children:[s.jsx("label",{className:"font-medium mb-0 d-flex gap-2",children:e}),s.jsx(ad,{name:t,value:a,onChange:n,className:"mrdn-radio-group d-flex gap-2",inline:!0,children:r.map(l=>s.jsx(rd,{value:l.value,children:l.label},l.value))})]});var _t={},Ia={},sf=sd,of=od;function lf(e,t){return e&&sf(e,of(t))}var Bs=lf,cf=id,df=ld;function uf(e,t){var a=-1,n=df(e)?Array(e.length):[];return cf(e,function(r,o,l){n[++a]=t(r,o,l)}),n}var pf=uf,ff=dd,hf=cd,mf=pf,gf=ud;function bf(e,t){var a=gf(e)?ff:mf;return a(e,hf(t))}var xf=bf;Object.defineProperty(Ia,"__esModule",{value:!0});Ia.flattenNames=void 0;var yf=pd,vf=Er(yf),wf=Bs,Cf=Er(wf),Tf=fd,jf=Er(Tf),Sf=xf,Ef=Er(Sf);function Er(e){return e&&e.__esModule?e:{default:e}}var _f=Ia.flattenNames=function e(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],a=[];return(0,Ef.default)(t,function(n){Array.isArray(n)?e(n).map(function(r){return a.push(r)}):(0,jf.default)(n)?(0,Cf.default)(n,function(r,o){r===!0&&a.push(o),a.push(o+"-"+r)}):(0,vf.default)(n)&&a.push(n)}),a};Ia.default=_f;var Oa={};Object.defineProperty(Oa,"__esModule",{value:!0});Oa.mergeClasses=void 0;var Af=Bs,kf=El(Af),Pf=hd,Nf=El(Pf),If=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e};function El(e){return e&&e.__esModule?e:{default:e}}var Of=Oa.mergeClasses=function(t){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],n=t.default&&(0,Nf.default)(t.default)||{};return a.map(function(r){var o=t[r];return o&&(0,kf.default)(o,function(l,i){n[i]||(n[i]={}),n[i]=If({},n[i],o[i])}),r}),n};Oa.default=Of;var Ma={};Object.defineProperty(Ma,"__esModule",{value:!0});Ma.autoprefix=void 0;var Mf=Bs,Wo=Lf(Mf),Df=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e};function Lf(e){return e&&e.__esModule?e:{default:e}}var $f={borderRadius:function(t){return{msBorderRadius:t,MozBorderRadius:t,OBorderRadius:t,WebkitBorderRadius:t,borderRadius:t}},boxShadow:function(t){return{msBoxShadow:t,MozBoxShadow:t,OBoxShadow:t,WebkitBoxShadow:t,boxShadow:t}},userSelect:function(t){return{WebkitTouchCallout:t,KhtmlUserSelect:t,MozUserSelect:t,msUserSelect:t,WebkitUserSelect:t,userSelect:t}},flex:function(t){return{WebkitBoxFlex:t,MozBoxFlex:t,WebkitFlex:t,msFlex:t,flex:t}},flexBasis:function(t){return{WebkitFlexBasis:t,flexBasis:t}},justifyContent:function(t){return{WebkitJustifyContent:t,justifyContent:t}},transition:function(t){return{msTransition:t,MozTransition:t,OTransition:t,WebkitTransition:t,transition:t}},transform:function(t){return{msTransform:t,MozTransform:t,OTransform:t,WebkitTransform:t,transform:t}},absolute:function(t){var a=t&&t.split(" ");return{position:"absolute",top:a&&a[0],right:a&&a[1],bottom:a&&a[2],left:a&&a[3]}},extend:function(t,a){var n=a[t];return n||{extend:t}}},Ff=Ma.autoprefix=function(t){var a={};return(0,Wo.default)(t,function(n,r){var o={};(0,Wo.default)(n,function(l,i){var d=$f[i];d?o=Df({},o,d(l)):o[i]=l}),a[r]=o}),a};Ma.default=Ff;var Da={};Object.defineProperty(Da,"__esModule",{value:!0});Da.hover=void 0;var Bf=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},Hf=b,Vr=Rf(Hf);function Rf(e){return e&&e.__esModule?e:{default:e}}function Uf(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Go(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function Vf(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var zf=Da.hover=function(t){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"span";return function(n){Vf(r,n);function r(){var o,l,i,d;Uf(this,r);for(var f=arguments.length,c=Array(f),p=0;p<f;p++)c[p]=arguments[p];return d=(l=(i=Go(this,(o=r.__proto__||Object.getPrototypeOf(r)).call.apply(o,[this].concat(c))),i),i.state={hover:!1},i.handleMouseOver=function(){return i.setState({hover:!0})},i.handleMouseOut=function(){return i.setState({hover:!1})},i.render=function(){return Vr.default.createElement(a,{onMouseOver:i.handleMouseOver,onMouseOut:i.handleMouseOut},Vr.default.createElement(t,Bf({},i.props,i.state)))},l),Go(i,d)}return r}(Vr.default.Component)};Da.default=zf;var La={};Object.defineProperty(La,"__esModule",{value:!0});La.active=void 0;var Kf=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},qf=b,zr=Wf(qf);function Wf(e){return e&&e.__esModule?e:{default:e}}function Gf(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Yo(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function Yf(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var Xf=La.active=function(t){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"span";return function(n){Yf(r,n);function r(){var o,l,i,d;Gf(this,r);for(var f=arguments.length,c=Array(f),p=0;p<f;p++)c[p]=arguments[p];return d=(l=(i=Yo(this,(o=r.__proto__||Object.getPrototypeOf(r)).call.apply(o,[this].concat(c))),i),i.state={active:!1},i.handleMouseDown=function(){return i.setState({active:!0})},i.handleMouseUp=function(){return i.setState({active:!1})},i.render=function(){return zr.default.createElement(a,{onMouseDown:i.handleMouseDown,onMouseUp:i.handleMouseUp},zr.default.createElement(t,Kf({},i.props,i.state)))},l),Yo(i,d)}return r}(zr.default.Component)};La.default=Xf;var Hs={};Object.defineProperty(Hs,"__esModule",{value:!0});var Qf=function(t,a){var n={},r=function(l){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;n[l]=i};return t===0&&r("first-child"),t===a-1&&r("last-child"),(t===0||t%2===0)&&r("even"),Math.abs(t%2)===1&&r("odd"),r("nth-child",t),n};Hs.default=Qf;Object.defineProperty(_t,"__esModule",{value:!0});_t.ReactCSS=_t.loop=_t.handleActive=Rs=_t.handleHover=_t.hover=void 0;var Zf=Ia,Jf=la(Zf),eh=Oa,th=la(eh),nh=Ma,ah=la(nh),rh=Da,_l=la(rh),sh=La,oh=la(sh),ih=Hs,lh=la(ih);function la(e){return e&&e.__esModule?e:{default:e}}_t.hover=_l.default;var Rs=_t.handleHover=_l.default;_t.handleActive=oh.default;_t.loop=lh.default;var ch=_t.ReactCSS=function(t){for(var a=arguments.length,n=Array(a>1?a-1:0),r=1;r<a;r++)n[r-1]=arguments[r];var o=(0,Jf.default)(n),l=(0,th.default)(t,o);return(0,ah.default)(l)},Ie=_t.default=ch,dh=function(t,a,n,r,o){var l=o.clientWidth,i=o.clientHeight,d=typeof t.pageX=="number"?t.pageX:t.touches[0].pageX,f=typeof t.pageY=="number"?t.pageY:t.touches[0].pageY,c=d-(o.getBoundingClientRect().left+window.pageXOffset),p=f-(o.getBoundingClientRect().top+window.pageYOffset);if(n==="vertical"){var u=void 0;if(p<0?u=0:p>i?u=1:u=Math.round(p*100/i)/100,a.a!==u)return{h:a.h,s:a.s,l:a.l,a:u,source:"rgb"}}else{var h=void 0;if(c<0?h=0:c>l?h=1:h=Math.round(c*100/l)/100,r!==h)return{h:a.h,s:a.s,l:a.l,a:h,source:"rgb"}}return null},Kr={},uh=function(t,a,n,r){if(typeof document>"u"&&!r)return null;var o=r?new r:document.createElement("canvas");o.width=n*2,o.height=n*2;var l=o.getContext("2d");return l?(l.fillStyle=t,l.fillRect(0,0,o.width,o.height),l.fillStyle=a,l.fillRect(0,0,n,n),l.translate(n,n),l.fillRect(0,0,n,n),o.toDataURL()):null},ph=function(t,a,n,r){var o=t+"-"+a+"-"+n+(r?"-server":"");if(Kr[o])return Kr[o];var l=uh(t,a,n,r);return Kr[o]=l,l},Xo=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},ca=function(t){var a=t.white,n=t.grey,r=t.size,o=t.renderers,l=t.borderRadius,i=t.boxShadow,d=t.children,f=Ie({default:{grid:{borderRadius:l,boxShadow:i,absolute:"0px 0px 0px 0px",background:"url("+ph(a,n,r,o.canvas)+") center left"}}});return b.isValidElement(d)?P.cloneElement(d,Xo({},d.props,{style:Xo({},d.props.style,f.grid)})):P.createElement("div",{style:f.grid})};ca.defaultProps={size:8,white:"transparent",grey:"rgba(0,0,0,.08)",renderers:{}};var fh=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},hh=function(){function e(t,a){for(var n=0;n<a.length;n++){var r=a[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(t,a,n){return a&&e(t.prototype,a),n&&e(t,n),t}}();function mh(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Qo(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function gh(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var Us=function(e){gh(t,e);function t(){var a,n,r,o;mh(this,t);for(var l=arguments.length,i=Array(l),d=0;d<l;d++)i[d]=arguments[d];return o=(n=(r=Qo(this,(a=t.__proto__||Object.getPrototypeOf(t)).call.apply(a,[this].concat(i))),r),r.handleChange=function(f){var c=dh(f,r.props.hsl,r.props.direction,r.props.a,r.container);c&&typeof r.props.onChange=="function"&&r.props.onChange(c,f)},r.handleMouseDown=function(f){r.handleChange(f),window.addEventListener("mousemove",r.handleChange),window.addEventListener("mouseup",r.handleMouseUp)},r.handleMouseUp=function(){r.unbindEventListeners()},r.unbindEventListeners=function(){window.removeEventListener("mousemove",r.handleChange),window.removeEventListener("mouseup",r.handleMouseUp)},n),Qo(r,o)}return hh(t,[{key:"componentWillUnmount",value:function(){this.unbindEventListeners()}},{key:"render",value:function(){var n=this,r=this.props.rgb,o=Ie({default:{alpha:{absolute:"0px 0px 0px 0px",borderRadius:this.props.radius},checkboard:{absolute:"0px 0px 0px 0px",overflow:"hidden",borderRadius:this.props.radius},gradient:{absolute:"0px 0px 0px 0px",background:"linear-gradient(to right, rgba("+r.r+","+r.g+","+r.b+`, 0) 0%,
           rgba(`+r.r+","+r.g+","+r.b+", 1) 100%)",boxShadow:this.props.shadow,borderRadius:this.props.radius},container:{position:"relative",height:"100%",margin:"0 3px"},pointer:{position:"absolute",left:r.a*100+"%"},slider:{width:"4px",borderRadius:"1px",height:"8px",boxShadow:"0 0 2px rgba(0, 0, 0, .6)",background:"#fff",marginTop:"1px",transform:"translateX(-2px)"}},vertical:{gradient:{background:"linear-gradient(to bottom, rgba("+r.r+","+r.g+","+r.b+`, 0) 0%,
           rgba(`+r.r+","+r.g+","+r.b+", 1) 100%)"},pointer:{left:0,top:r.a*100+"%"}},overwrite:fh({},this.props.style)},{vertical:this.props.direction==="vertical",overwrite:!0});return P.createElement("div",{style:o.alpha},P.createElement("div",{style:o.checkboard},P.createElement(ca,{renderers:this.props.renderers})),P.createElement("div",{style:o.gradient}),P.createElement("div",{style:o.container,ref:function(i){return n.container=i},onMouseDown:this.handleMouseDown,onTouchMove:this.handleChange,onTouchStart:this.handleChange},P.createElement("div",{style:o.pointer},this.props.pointer?P.createElement(this.props.pointer,this.props):P.createElement("div",{style:o.slider}))))}}]),t}(b.PureComponent||b.Component),bh=function(){function e(t,a){for(var n=0;n<a.length;n++){var r=a[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(t,a,n){return a&&e(t.prototype,a),n&&e(t,n),t}}();function xh(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function yh(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function vh(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function wh(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var Ch=1,Al=38,Th=40,jh=[Al,Th],Sh=function(t){return jh.indexOf(t)>-1},Eh=function(t){return Number(String(t).replace(/%/g,""))},_h=1,Be=function(e){wh(t,e);function t(a){yh(this,t);var n=vh(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return n.handleBlur=function(){n.state.blurValue&&n.setState({value:n.state.blurValue,blurValue:null})},n.handleChange=function(r){n.setUpdatedValue(r.target.value,r)},n.handleKeyDown=function(r){var o=Eh(r.target.value);if(!isNaN(o)&&Sh(r.keyCode)){var l=n.getArrowOffset(),i=r.keyCode===Al?o+l:o-l;n.setUpdatedValue(i,r)}},n.handleDrag=function(r){if(n.props.dragLabel){var o=Math.round(n.props.value+r.movementX);o>=0&&o<=n.props.dragMax&&n.props.onChange&&n.props.onChange(n.getValueObjectWithLabel(o),r)}},n.handleMouseDown=function(r){n.props.dragLabel&&(r.preventDefault(),n.handleDrag(r),window.addEventListener("mousemove",n.handleDrag),window.addEventListener("mouseup",n.handleMouseUp))},n.handleMouseUp=function(){n.unbindEventListeners()},n.unbindEventListeners=function(){window.removeEventListener("mousemove",n.handleDrag),window.removeEventListener("mouseup",n.handleMouseUp)},n.state={value:String(a.value).toUpperCase(),blurValue:String(a.value).toUpperCase()},n.inputId="rc-editable-input-"+_h++,n}return bh(t,[{key:"componentDidUpdate",value:function(n,r){this.props.value!==this.state.value&&(n.value!==this.props.value||r.value!==this.state.value)&&(this.input===document.activeElement?this.setState({blurValue:String(this.props.value).toUpperCase()}):this.setState({value:String(this.props.value).toUpperCase(),blurValue:!this.state.blurValue&&String(this.props.value).toUpperCase()}))}},{key:"componentWillUnmount",value:function(){this.unbindEventListeners()}},{key:"getValueObjectWithLabel",value:function(n){return xh({},this.props.label,n)}},{key:"getArrowOffset",value:function(){return this.props.arrowOffset||Ch}},{key:"setUpdatedValue",value:function(n,r){var o=this.props.label?this.getValueObjectWithLabel(n):n;this.props.onChange&&this.props.onChange(o,r),this.setState({value:n})}},{key:"render",value:function(){var n=this,r=Ie({default:{wrap:{position:"relative"}},"user-override":{wrap:this.props.style&&this.props.style.wrap?this.props.style.wrap:{},input:this.props.style&&this.props.style.input?this.props.style.input:{},label:this.props.style&&this.props.style.label?this.props.style.label:{}},"dragLabel-true":{label:{cursor:"ew-resize"}}},{"user-override":!0},this.props);return P.createElement("div",{style:r.wrap},P.createElement("input",{id:this.inputId,style:r.input,ref:function(l){return n.input=l},value:this.state.value,onKeyDown:this.handleKeyDown,onChange:this.handleChange,onBlur:this.handleBlur,placeholder:this.props.placeholder,spellCheck:"false"}),this.props.label&&!this.props.hideLabel?P.createElement("label",{htmlFor:this.inputId,style:r.label,onMouseDown:this.handleMouseDown},this.props.label):null)}}]),t}(b.PureComponent||b.Component),Ah=function(t,a,n,r){var o=r.clientWidth,l=r.clientHeight,i=typeof t.pageX=="number"?t.pageX:t.touches[0].pageX,d=typeof t.pageY=="number"?t.pageY:t.touches[0].pageY,f=i-(r.getBoundingClientRect().left+window.pageXOffset),c=d-(r.getBoundingClientRect().top+window.pageYOffset);if(a==="vertical"){var p=void 0;if(c<0)p=359;else if(c>l)p=0;else{var u=-(c*100/l)+100;p=360*u/100}if(n.h!==p)return{h:p,s:n.s,l:n.l,a:n.a,source:"hsl"}}else{var h=void 0;if(f<0)h=0;else if(f>o)h=359;else{var g=f*100/o;h=360*g/100}if(n.h!==h)return{h,s:n.s,l:n.l,a:n.a,source:"hsl"}}return null},kh=function(){function e(t,a){for(var n=0;n<a.length;n++){var r=a[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(t,a,n){return a&&e(t.prototype,a),n&&e(t,n),t}}();function Ph(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Zo(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function Nh(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var da=function(e){Nh(t,e);function t(){var a,n,r,o;Ph(this,t);for(var l=arguments.length,i=Array(l),d=0;d<l;d++)i[d]=arguments[d];return o=(n=(r=Zo(this,(a=t.__proto__||Object.getPrototypeOf(t)).call.apply(a,[this].concat(i))),r),r.handleChange=function(f){var c=Ah(f,r.props.direction,r.props.hsl,r.container);c&&typeof r.props.onChange=="function"&&r.props.onChange(c,f)},r.handleMouseDown=function(f){r.handleChange(f),window.addEventListener("mousemove",r.handleChange),window.addEventListener("mouseup",r.handleMouseUp)},r.handleMouseUp=function(){r.unbindEventListeners()},n),Zo(r,o)}return kh(t,[{key:"componentWillUnmount",value:function(){this.unbindEventListeners()}},{key:"unbindEventListeners",value:function(){window.removeEventListener("mousemove",this.handleChange),window.removeEventListener("mouseup",this.handleMouseUp)}},{key:"render",value:function(){var n=this,r=this.props.direction,o=r===void 0?"horizontal":r,l=Ie({default:{hue:{absolute:"0px 0px 0px 0px",borderRadius:this.props.radius,boxShadow:this.props.shadow},container:{padding:"0 2px",position:"relative",height:"100%",borderRadius:this.props.radius},pointer:{position:"absolute",left:this.props.hsl.h*100/360+"%"},slider:{marginTop:"1px",width:"4px",borderRadius:"1px",height:"8px",boxShadow:"0 0 2px rgba(0, 0, 0, .6)",background:"#fff",transform:"translateX(-2px)"}},vertical:{pointer:{left:"0px",top:-(this.props.hsl.h*100/360)+100+"%"}}},{vertical:o==="vertical"});return P.createElement("div",{style:l.hue},P.createElement("div",{className:"hue-"+o,style:l.container,ref:function(d){return n.container=d},onMouseDown:this.handleMouseDown,onTouchMove:this.handleChange,onTouchStart:this.handleChange},P.createElement("style",null,`
            .hue-horizontal {
              background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0
                33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);
              background: -webkit-linear-gradient(to right, #f00 0%, #ff0
                17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);
            }

            .hue-vertical {
              background: linear-gradient(to top, #f00 0%, #ff0 17%, #0f0 33%,
                #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);
              background: -webkit-linear-gradient(to top, #f00 0%, #ff0 17%,
                #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);
            }
          `),P.createElement("div",{style:l.pointer},this.props.pointer?P.createElement(this.props.pointer,this.props):P.createElement("div",{style:l.slider}))))}}]),t}(b.PureComponent||b.Component);function Ih(){this.__data__=[],this.size=0}function $a(e,t){return e===t||e!==e&&t!==t}function _r(e,t){for(var a=e.length;a--;)if($a(e[a][0],t))return a;return-1}var Oh=Array.prototype,Mh=Oh.splice;function Dh(e){var t=this.__data__,a=_r(t,e);if(a<0)return!1;var n=t.length-1;return a==n?t.pop():Mh.call(t,a,1),--this.size,!0}function Lh(e){var t=this.__data__,a=_r(t,e);return a<0?void 0:t[a][1]}function $h(e){return _r(this.__data__,e)>-1}function Fh(e,t){var a=this.__data__,n=_r(a,e);return n<0?(++this.size,a.push([e,t])):a[n][1]=t,this}function Zt(e){var t=-1,a=e==null?0:e.length;for(this.clear();++t<a;){var n=e[t];this.set(n[0],n[1])}}Zt.prototype.clear=Ih;Zt.prototype.delete=Dh;Zt.prototype.get=Lh;Zt.prototype.has=$h;Zt.prototype.set=Fh;function Bh(){this.__data__=new Zt,this.size=0}function Hh(e){var t=this.__data__,a=t.delete(e);return this.size=t.size,a}function Rh(e){return this.__data__.get(e)}function Uh(e){return this.__data__.has(e)}var Vh=typeof global=="object"&&global&&global.Object===Object&&global;const kl=Vh;var zh=typeof self=="object"&&self&&self.Object===Object&&self,Kh=kl||zh||Function("return this")();const Ft=Kh;var qh=Ft.Symbol;const an=qh;var Pl=Object.prototype,Wh=Pl.hasOwnProperty,Gh=Pl.toString,ha=an?an.toStringTag:void 0;function Yh(e){var t=Wh.call(e,ha),a=e[ha];try{e[ha]=void 0;var n=!0}catch{}var r=Gh.call(e);return n&&(t?e[ha]=a:delete e[ha]),r}var Xh=Object.prototype,Qh=Xh.toString;function Zh(e){return Qh.call(e)}var Jh="[object Null]",em="[object Undefined]",Jo=an?an.toStringTag:void 0;function An(e){return e==null?e===void 0?em:Jh:Jo&&Jo in Object(e)?Yh(e):Zh(e)}function kt(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var tm="[object AsyncFunction]",nm="[object Function]",am="[object GeneratorFunction]",rm="[object Proxy]";function Vs(e){if(!kt(e))return!1;var t=An(e);return t==nm||t==am||t==tm||t==rm}var sm=Ft["__core-js_shared__"];const qr=sm;var ei=function(){var e=/[^.]+$/.exec(qr&&qr.keys&&qr.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function om(e){return!!ei&&ei in e}var im=Function.prototype,lm=im.toString;function kn(e){if(e!=null){try{return lm.call(e)}catch{}try{return e+""}catch{}}return""}var cm=/[\\^$.*+?()[\]{}|]/g,dm=/^\[object .+?Constructor\]$/,um=Function.prototype,pm=Object.prototype,fm=um.toString,hm=pm.hasOwnProperty,mm=RegExp("^"+fm.call(hm).replace(cm,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function gm(e){if(!kt(e)||om(e))return!1;var t=Vs(e)?mm:dm;return t.test(kn(e))}function bm(e,t){return e==null?void 0:e[t]}function Pn(e,t){var a=bm(e,t);return gm(a)?a:void 0}var xm=Pn(Ft,"Map");const Aa=xm;var ym=Pn(Object,"create");const ka=ym;function vm(){this.__data__=ka?ka(null):{},this.size=0}function wm(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var Cm="__lodash_hash_undefined__",Tm=Object.prototype,jm=Tm.hasOwnProperty;function Sm(e){var t=this.__data__;if(ka){var a=t[e];return a===Cm?void 0:a}return jm.call(t,e)?t[e]:void 0}var Em=Object.prototype,_m=Em.hasOwnProperty;function Am(e){var t=this.__data__;return ka?t[e]!==void 0:_m.call(t,e)}var km="__lodash_hash_undefined__";function Pm(e,t){var a=this.__data__;return this.size+=this.has(e)?0:1,a[e]=ka&&t===void 0?km:t,this}function Cn(e){var t=-1,a=e==null?0:e.length;for(this.clear();++t<a;){var n=e[t];this.set(n[0],n[1])}}Cn.prototype.clear=vm;Cn.prototype.delete=wm;Cn.prototype.get=Sm;Cn.prototype.has=Am;Cn.prototype.set=Pm;function Nm(){this.size=0,this.__data__={hash:new Cn,map:new(Aa||Zt),string:new Cn}}function Im(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function Ar(e,t){var a=e.__data__;return Im(t)?a[typeof t=="string"?"string":"hash"]:a.map}function Om(e){var t=Ar(this,e).delete(e);return this.size-=t?1:0,t}function Mm(e){return Ar(this,e).get(e)}function Dm(e){return Ar(this,e).has(e)}function Lm(e,t){var a=Ar(this,e),n=a.size;return a.set(e,t),this.size+=a.size==n?0:1,this}function Jt(e){var t=-1,a=e==null?0:e.length;for(this.clear();++t<a;){var n=e[t];this.set(n[0],n[1])}}Jt.prototype.clear=Nm;Jt.prototype.delete=Om;Jt.prototype.get=Mm;Jt.prototype.has=Dm;Jt.prototype.set=Lm;var $m=200;function Fm(e,t){var a=this.__data__;if(a instanceof Zt){var n=a.__data__;if(!Aa||n.length<$m-1)return n.push([e,t]),this.size=++a.size,this;a=this.__data__=new Jt(n)}return a.set(e,t),this.size=a.size,this}function Vt(e){var t=this.__data__=new Zt(e);this.size=t.size}Vt.prototype.clear=Bh;Vt.prototype.delete=Hh;Vt.prototype.get=Rh;Vt.prototype.has=Uh;Vt.prototype.set=Fm;var Bm=function(){try{var e=Pn(Object,"defineProperty");return e({},"",{}),e}catch{}}();const dr=Bm;function zs(e,t,a){t=="__proto__"&&dr?dr(e,t,{configurable:!0,enumerable:!0,value:a,writable:!0}):e[t]=a}function hs(e,t,a){(a!==void 0&&!$a(e[t],a)||a===void 0&&!(t in e))&&zs(e,t,a)}function Hm(e){return function(t,a,n){for(var r=-1,o=Object(t),l=n(t),i=l.length;i--;){var d=l[e?i:++r];if(a(o[d],d,o)===!1)break}return t}}var Rm=Hm();const Nl=Rm;var Il=typeof exports=="object"&&exports&&!exports.nodeType&&exports,ti=Il&&typeof module=="object"&&module&&!module.nodeType&&module,Um=ti&&ti.exports===Il,ni=Um?Ft.Buffer:void 0,ai=ni?ni.allocUnsafe:void 0;function Vm(e,t){if(t)return e.slice();var a=e.length,n=ai?ai(a):new e.constructor(a);return e.copy(n),n}var zm=Ft.Uint8Array;const ur=zm;function Km(e){var t=new e.constructor(e.byteLength);return new ur(t).set(new ur(e)),t}function qm(e,t){var a=t?Km(e.buffer):e.buffer;return new e.constructor(a,e.byteOffset,e.length)}function Wm(e,t){var a=-1,n=e.length;for(t||(t=Array(n));++a<n;)t[a]=e[a];return t}var ri=Object.create,Gm=function(){function e(){}return function(t){if(!kt(t))return{};if(ri)return ri(t);e.prototype=t;var a=new e;return e.prototype=void 0,a}}();const Ym=Gm;function Ol(e,t){return function(a){return e(t(a))}}var Xm=Ol(Object.getPrototypeOf,Object);const Ml=Xm;var Qm=Object.prototype;function Ks(e){var t=e&&e.constructor,a=typeof t=="function"&&t.prototype||Qm;return e===a}function Zm(e){return typeof e.constructor=="function"&&!Ks(e)?Ym(Ml(e)):{}}function rn(e){return e!=null&&typeof e=="object"}var Jm="[object Arguments]";function si(e){return rn(e)&&An(e)==Jm}var Dl=Object.prototype,eg=Dl.hasOwnProperty,tg=Dl.propertyIsEnumerable,ng=si(function(){return arguments}())?si:function(e){return rn(e)&&eg.call(e,"callee")&&!tg.call(e,"callee")};const pr=ng;var ag=Array.isArray;const Ct=ag;var rg=9007199254740991;function qs(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=rg}function ua(e){return e!=null&&qs(e.length)&&!Vs(e)}function sg(e){return rn(e)&&ua(e)}function og(){return!1}var Ll=typeof exports=="object"&&exports&&!exports.nodeType&&exports,oi=Ll&&typeof module=="object"&&module&&!module.nodeType&&module,ig=oi&&oi.exports===Ll,ii=ig?Ft.Buffer:void 0,lg=ii?ii.isBuffer:void 0,cg=lg||og;const fr=cg;var dg="[object Object]",ug=Function.prototype,pg=Object.prototype,$l=ug.toString,fg=pg.hasOwnProperty,hg=$l.call(Object);function mg(e){if(!rn(e)||An(e)!=dg)return!1;var t=Ml(e);if(t===null)return!0;var a=fg.call(t,"constructor")&&t.constructor;return typeof a=="function"&&a instanceof a&&$l.call(a)==hg}var gg="[object Arguments]",bg="[object Array]",xg="[object Boolean]",yg="[object Date]",vg="[object Error]",wg="[object Function]",Cg="[object Map]",Tg="[object Number]",jg="[object Object]",Sg="[object RegExp]",Eg="[object Set]",_g="[object String]",Ag="[object WeakMap]",kg="[object ArrayBuffer]",Pg="[object DataView]",Ng="[object Float32Array]",Ig="[object Float64Array]",Og="[object Int8Array]",Mg="[object Int16Array]",Dg="[object Int32Array]",Lg="[object Uint8Array]",$g="[object Uint8ClampedArray]",Fg="[object Uint16Array]",Bg="[object Uint32Array]",We={};We[Ng]=We[Ig]=We[Og]=We[Mg]=We[Dg]=We[Lg]=We[$g]=We[Fg]=We[Bg]=!0;We[gg]=We[bg]=We[kg]=We[xg]=We[Pg]=We[yg]=We[vg]=We[wg]=We[Cg]=We[Tg]=We[jg]=We[Sg]=We[Eg]=We[_g]=We[Ag]=!1;function Hg(e){return rn(e)&&qs(e.length)&&!!We[An(e)]}function Rg(e){return function(t){return e(t)}}var Fl=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Ea=Fl&&typeof module=="object"&&module&&!module.nodeType&&module,Ug=Ea&&Ea.exports===Fl,Wr=Ug&&kl.process,Vg=function(){try{var e=Ea&&Ea.require&&Ea.require("util").types;return e||Wr&&Wr.binding&&Wr.binding("util")}catch{}}();const li=Vg;var ci=li&&li.isTypedArray,zg=ci?Rg(ci):Hg;const Ws=zg;function ms(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}var Kg=Object.prototype,qg=Kg.hasOwnProperty;function Wg(e,t,a){var n=e[t];(!(qg.call(e,t)&&$a(n,a))||a===void 0&&!(t in e))&&zs(e,t,a)}function Gg(e,t,a,n){var r=!a;a||(a={});for(var o=-1,l=t.length;++o<l;){var i=t[o],d=n?n(a[i],e[i],i,a,e):void 0;d===void 0&&(d=e[i]),r?zs(a,i,d):Wg(a,i,d)}return a}function Yg(e,t){for(var a=-1,n=Array(e);++a<e;)n[a]=t(a);return n}var Xg=9007199254740991,Qg=/^(?:0|[1-9]\d*)$/;function Gs(e,t){var a=typeof e;return t=t??Xg,!!t&&(a=="number"||a!="symbol"&&Qg.test(e))&&e>-1&&e%1==0&&e<t}var Zg=Object.prototype,Jg=Zg.hasOwnProperty;function Bl(e,t){var a=Ct(e),n=!a&&pr(e),r=!a&&!n&&fr(e),o=!a&&!n&&!r&&Ws(e),l=a||n||r||o,i=l?Yg(e.length,String):[],d=i.length;for(var f in e)(t||Jg.call(e,f))&&!(l&&(f=="length"||r&&(f=="offset"||f=="parent")||o&&(f=="buffer"||f=="byteLength"||f=="byteOffset")||Gs(f,d)))&&i.push(f);return i}function e0(e){var t=[];if(e!=null)for(var a in Object(e))t.push(a);return t}var t0=Object.prototype,n0=t0.hasOwnProperty;function a0(e){if(!kt(e))return e0(e);var t=Ks(e),a=[];for(var n in e)n=="constructor"&&(t||!n0.call(e,n))||a.push(n);return a}function Hl(e){return ua(e)?Bl(e,!0):a0(e)}function r0(e){return Gg(e,Hl(e))}function s0(e,t,a,n,r,o,l){var i=ms(e,a),d=ms(t,a),f=l.get(d);if(f){hs(e,a,f);return}var c=o?o(i,d,a+"",e,t,l):void 0,p=c===void 0;if(p){var u=Ct(d),h=!u&&fr(d),g=!u&&!h&&Ws(d);c=d,u||h||g?Ct(i)?c=i:sg(i)?c=Wm(i):h?(p=!1,c=Vm(d,!0)):g?(p=!1,c=qm(d,!0)):c=[]:mg(d)||pr(d)?(c=i,pr(i)?c=r0(i):(!kt(i)||Vs(i))&&(c=Zm(d))):p=!1}p&&(l.set(d,c),r(c,d,n,o,l),l.delete(d)),hs(e,a,c)}function Rl(e,t,a,n,r){e!==t&&Nl(t,function(o,l){if(r||(r=new Vt),kt(o))s0(e,t,l,a,Rl,n,r);else{var i=n?n(ms(e,l),o,l+"",e,t,r):void 0;i===void 0&&(i=o),hs(e,l,i)}},Hl)}function kr(e){return e}function o0(e,t,a){switch(a.length){case 0:return e.call(t);case 1:return e.call(t,a[0]);case 2:return e.call(t,a[0],a[1]);case 3:return e.call(t,a[0],a[1],a[2])}return e.apply(t,a)}var di=Math.max;function i0(e,t,a){return t=di(t===void 0?e.length-1:t,0),function(){for(var n=arguments,r=-1,o=di(n.length-t,0),l=Array(o);++r<o;)l[r]=n[t+r];r=-1;for(var i=Array(t+1);++r<t;)i[r]=n[r];return i[t]=a(l),o0(e,this,i)}}function l0(e){return function(){return e}}var c0=dr?function(e,t){return dr(e,"toString",{configurable:!0,enumerable:!1,value:l0(t),writable:!0})}:kr;const d0=c0;var u0=800,p0=16,f0=Date.now;function h0(e){var t=0,a=0;return function(){var n=f0(),r=p0-(n-a);if(a=n,r>0){if(++t>=u0)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var m0=h0(d0);const g0=m0;function b0(e,t){return g0(i0(e,t,kr),e+"")}function x0(e,t,a){if(!kt(a))return!1;var n=typeof t;return(n=="number"?ua(a)&&Gs(t,a.length):n=="string"&&t in a)?$a(a[t],e):!1}function y0(e){return b0(function(t,a){var n=-1,r=a.length,o=r>1?a[r-1]:void 0,l=r>2?a[2]:void 0;for(o=e.length>3&&typeof o=="function"?(r--,o):void 0,l&&x0(a[0],a[1],l)&&(o=r<3?void 0:o,r=1),t=Object(t);++n<r;){var i=a[n];i&&e(t,i,n,o)}return t})}var v0=y0(function(e,t,a){Rl(e,t,a)});const xt=v0;var Fa=function(t){var a=t.zDepth,n=t.radius,r=t.background,o=t.children,l=t.styles,i=l===void 0?{}:l,d=Ie(xt({default:{wrap:{position:"relative",display:"inline-block"},content:{position:"relative"},bg:{absolute:"0px 0px 0px 0px",boxShadow:"0 "+a+"px "+a*4+"px rgba(0,0,0,.24)",borderRadius:n,background:r}},"zDepth-0":{bg:{boxShadow:"none"}},"zDepth-1":{bg:{boxShadow:"0 2px 10px rgba(0,0,0,.12), 0 2px 5px rgba(0,0,0,.16)"}},"zDepth-2":{bg:{boxShadow:"0 6px 20px rgba(0,0,0,.19), 0 8px 17px rgba(0,0,0,.2)"}},"zDepth-3":{bg:{boxShadow:"0 17px 50px rgba(0,0,0,.19), 0 12px 15px rgba(0,0,0,.24)"}},"zDepth-4":{bg:{boxShadow:"0 25px 55px rgba(0,0,0,.21), 0 16px 28px rgba(0,0,0,.22)"}},"zDepth-5":{bg:{boxShadow:"0 40px 77px rgba(0,0,0,.22), 0 27px 24px rgba(0,0,0,.2)"}},square:{bg:{borderRadius:"0"}},circle:{bg:{borderRadius:"50%"}}},i),{"zDepth-1":a===1});return P.createElement("div",{style:d.wrap},P.createElement("div",{style:d.bg}),P.createElement("div",{style:d.content},o))};Fa.propTypes={background:xe.string,zDepth:xe.oneOf([0,1,2,3,4,5]),radius:xe.number,styles:xe.object};Fa.defaultProps={background:"#fff",zDepth:1,radius:2,styles:{}};var w0=function(){return Ft.Date.now()};const Gr=w0;var C0=/\s/;function T0(e){for(var t=e.length;t--&&C0.test(e.charAt(t)););return t}var j0=/^\s+/;function S0(e){return e&&e.slice(0,T0(e)+1).replace(j0,"")}var E0="[object Symbol]";function Pr(e){return typeof e=="symbol"||rn(e)&&An(e)==E0}var ui=0/0,_0=/^[-+]0x[0-9a-f]+$/i,A0=/^0b[01]+$/i,k0=/^0o[0-7]+$/i,P0=parseInt;function pi(e){if(typeof e=="number")return e;if(Pr(e))return ui;if(kt(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=kt(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=S0(e);var a=A0.test(e);return a||k0.test(e)?P0(e.slice(2),a?2:8):_0.test(e)?ui:+e}var N0="Expected a function",I0=Math.max,O0=Math.min;function Ul(e,t,a){var n,r,o,l,i,d,f=0,c=!1,p=!1,u=!0;if(typeof e!="function")throw new TypeError(N0);t=pi(t)||0,kt(a)&&(c=!!a.leading,p="maxWait"in a,o=p?I0(pi(a.maxWait)||0,t):o,u="trailing"in a?!!a.trailing:u);function h(S){var R=n,w=r;return n=r=void 0,f=S,l=e.apply(w,R),l}function g(S){return f=S,i=setTimeout(j,t),c?h(S):l}function v(S){var R=S-d,w=S-f,m=t-R;return p?O0(m,o-w):m}function C(S){var R=S-d,w=S-f;return d===void 0||R>=t||R<0||p&&w>=o}function j(){var S=Gr();if(C(S))return N(S);i=setTimeout(j,v(S))}function N(S){return i=void 0,u&&n?h(S):(n=r=void 0,l)}function M(){i!==void 0&&clearTimeout(i),f=0,n=d=r=i=void 0}function I(){return i===void 0?l:N(Gr())}function F(){var S=Gr(),R=C(S);if(n=arguments,r=this,d=S,R){if(i===void 0)return g(d);if(p)return clearTimeout(i),i=setTimeout(j,t),h(d)}return i===void 0&&(i=setTimeout(j,t)),l}return F.cancel=M,F.flush=I,F}var M0="Expected a function";function D0(e,t,a){var n=!0,r=!0;if(typeof e!="function")throw new TypeError(M0);return kt(a)&&(n="leading"in a?!!a.leading:n,r="trailing"in a?!!a.trailing:r),Ul(e,t,{leading:n,maxWait:t,trailing:r})}var L0=function(t,a,n){var r=n.getBoundingClientRect(),o=r.width,l=r.height,i=typeof t.pageX=="number"?t.pageX:t.touches[0].pageX,d=typeof t.pageY=="number"?t.pageY:t.touches[0].pageY,f=i-(n.getBoundingClientRect().left+window.pageXOffset),c=d-(n.getBoundingClientRect().top+window.pageYOffset);f<0?f=0:f>o&&(f=o),c<0?c=0:c>l&&(c=l);var p=f/o,u=1-c/l;return{h:a.h,s:p,v:u,a:a.a,source:"hsv"}},$0=function(){function e(t,a){for(var n=0;n<a.length;n++){var r=a[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(t,a,n){return a&&e(t.prototype,a),n&&e(t,n),t}}();function F0(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function B0(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function H0(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var Nr=function(e){H0(t,e);function t(a){F0(this,t);var n=B0(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,a));return n.handleChange=function(r){typeof n.props.onChange=="function"&&n.throttle(n.props.onChange,L0(r,n.props.hsl,n.container),r)},n.handleMouseDown=function(r){n.handleChange(r);var o=n.getContainerRenderWindow();o.addEventListener("mousemove",n.handleChange),o.addEventListener("mouseup",n.handleMouseUp)},n.handleMouseUp=function(){n.unbindEventListeners()},n.throttle=D0(function(r,o,l){r(o,l)},50),n}return $0(t,[{key:"componentWillUnmount",value:function(){this.throttle.cancel(),this.unbindEventListeners()}},{key:"getContainerRenderWindow",value:function(){for(var n=this.container,r=window;!r.document.contains(n)&&r.parent!==r;)r=r.parent;return r}},{key:"unbindEventListeners",value:function(){var n=this.getContainerRenderWindow();n.removeEventListener("mousemove",this.handleChange),n.removeEventListener("mouseup",this.handleMouseUp)}},{key:"render",value:function(){var n=this,r=this.props.style||{},o=r.color,l=r.white,i=r.black,d=r.pointer,f=r.circle,c=Ie({default:{color:{absolute:"0px 0px 0px 0px",background:"hsl("+this.props.hsl.h+",100%, 50%)",borderRadius:this.props.radius},white:{absolute:"0px 0px 0px 0px",borderRadius:this.props.radius},black:{absolute:"0px 0px 0px 0px",boxShadow:this.props.shadow,borderRadius:this.props.radius},pointer:{position:"absolute",top:-(this.props.hsv.v*100)+100+"%",left:this.props.hsv.s*100+"%",cursor:"default"},circle:{width:"4px",height:"4px",boxShadow:`0 0 0 1.5px #fff, inset 0 0 1px 1px rgba(0,0,0,.3),
            0 0 1px 2px rgba(0,0,0,.4)`,borderRadius:"50%",cursor:"hand",transform:"translate(-2px, -2px)"}},custom:{color:o,white:l,black:i,pointer:d,circle:f}},{custom:!!this.props.style});return P.createElement("div",{style:c.color,ref:function(u){return n.container=u},onMouseDown:this.handleMouseDown,onTouchMove:this.handleChange,onTouchStart:this.handleChange},P.createElement("style",null,`
          .saturation-white {
            background: -webkit-linear-gradient(to right, #fff, rgba(255,255,255,0));
            background: linear-gradient(to right, #fff, rgba(255,255,255,0));
          }
          .saturation-black {
            background: -webkit-linear-gradient(to top, #000, rgba(0,0,0,0));
            background: linear-gradient(to top, #000, rgba(0,0,0,0));
          }
        `),P.createElement("div",{style:c.white,className:"saturation-white"},P.createElement("div",{style:c.black,className:"saturation-black"}),P.createElement("div",{style:c.pointer},this.props.pointer?P.createElement(this.props.pointer,this.props):P.createElement("div",{style:c.circle}))))}}]),t}(b.PureComponent||b.Component);function R0(e,t){for(var a=-1,n=e==null?0:e.length;++a<n&&t(e[a],a,e)!==!1;);return e}var U0=Ol(Object.keys,Object);const V0=U0;var z0=Object.prototype,K0=z0.hasOwnProperty;function q0(e){if(!Ks(e))return V0(e);var t=[];for(var a in Object(e))K0.call(e,a)&&a!="constructor"&&t.push(a);return t}function Ys(e){return ua(e)?Bl(e):q0(e)}function W0(e,t){return e&&Nl(e,t,Ys)}function G0(e,t){return function(a,n){if(a==null)return a;if(!ua(a))return e(a,n);for(var r=a.length,o=t?r:-1,l=Object(a);(t?o--:++o<r)&&n(l[o],o,l)!==!1;);return a}}var Y0=G0(W0);const Vl=Y0;function X0(e){return typeof e=="function"?e:kr}function Q0(e,t){var a=Ct(e)?R0:Vl;return a(e,X0(t))}function hr(e){"@babel/helpers - typeof";return hr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},hr(e)}var Z0=/^\s+/,J0=/\s+$/;function Se(e,t){if(e=e||"",t=t||{},e instanceof Se)return e;if(!(this instanceof Se))return new Se(e,t);var a=eb(e);this._originalInput=e,this._r=a.r,this._g=a.g,this._b=a.b,this._a=a.a,this._roundA=Math.round(100*this._a)/100,this._format=t.format||a.format,this._gradientType=t.gradientType,this._r<1&&(this._r=Math.round(this._r)),this._g<1&&(this._g=Math.round(this._g)),this._b<1&&(this._b=Math.round(this._b)),this._ok=a.ok}Se.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var t=this.toRgb();return(t.r*299+t.g*587+t.b*114)/1e3},getLuminance:function(){var t=this.toRgb(),a,n,r,o,l,i;return a=t.r/255,n=t.g/255,r=t.b/255,a<=.03928?o=a/12.92:o=Math.pow((a+.055)/1.055,2.4),n<=.03928?l=n/12.92:l=Math.pow((n+.055)/1.055,2.4),r<=.03928?i=r/12.92:i=Math.pow((r+.055)/1.055,2.4),.2126*o+.7152*l+.0722*i},setAlpha:function(t){return this._a=zl(t),this._roundA=Math.round(100*this._a)/100,this},toHsv:function(){var t=hi(this._r,this._g,this._b);return{h:t.h*360,s:t.s,v:t.v,a:this._a}},toHsvString:function(){var t=hi(this._r,this._g,this._b),a=Math.round(t.h*360),n=Math.round(t.s*100),r=Math.round(t.v*100);return this._a==1?"hsv("+a+", "+n+"%, "+r+"%)":"hsva("+a+", "+n+"%, "+r+"%, "+this._roundA+")"},toHsl:function(){var t=fi(this._r,this._g,this._b);return{h:t.h*360,s:t.s,l:t.l,a:this._a}},toHslString:function(){var t=fi(this._r,this._g,this._b),a=Math.round(t.h*360),n=Math.round(t.s*100),r=Math.round(t.l*100);return this._a==1?"hsl("+a+", "+n+"%, "+r+"%)":"hsla("+a+", "+n+"%, "+r+"%, "+this._roundA+")"},toHex:function(t){return mi(this._r,this._g,this._b,t)},toHexString:function(t){return"#"+this.toHex(t)},toHex8:function(t){return rb(this._r,this._g,this._b,this._a,t)},toHex8String:function(t){return"#"+this.toHex8(t)},toRgb:function(){return{r:Math.round(this._r),g:Math.round(this._g),b:Math.round(this._b),a:this._a}},toRgbString:function(){return this._a==1?"rgb("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+")":"rgba("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:Math.round(Ye(this._r,255)*100)+"%",g:Math.round(Ye(this._g,255)*100)+"%",b:Math.round(Ye(this._b,255)*100)+"%",a:this._a}},toPercentageRgbString:function(){return this._a==1?"rgb("+Math.round(Ye(this._r,255)*100)+"%, "+Math.round(Ye(this._g,255)*100)+"%, "+Math.round(Ye(this._b,255)*100)+"%)":"rgba("+Math.round(Ye(this._r,255)*100)+"%, "+Math.round(Ye(this._g,255)*100)+"%, "+Math.round(Ye(this._b,255)*100)+"%, "+this._roundA+")"},toName:function(){return this._a===0?"transparent":this._a<1?!1:gb[mi(this._r,this._g,this._b,!0)]||!1},toFilter:function(t){var a="#"+gi(this._r,this._g,this._b,this._a),n=a,r=this._gradientType?"GradientType = 1, ":"";if(t){var o=Se(t);n="#"+gi(o._r,o._g,o._b,o._a)}return"progid:DXImageTransform.Microsoft.gradient("+r+"startColorstr="+a+",endColorstr="+n+")"},toString:function(t){var a=!!t;t=t||this._format;var n=!1,r=this._a<1&&this._a>=0,o=!a&&r&&(t==="hex"||t==="hex6"||t==="hex3"||t==="hex4"||t==="hex8"||t==="name");return o?t==="name"&&this._a===0?this.toName():this.toRgbString():(t==="rgb"&&(n=this.toRgbString()),t==="prgb"&&(n=this.toPercentageRgbString()),(t==="hex"||t==="hex6")&&(n=this.toHexString()),t==="hex3"&&(n=this.toHexString(!0)),t==="hex4"&&(n=this.toHex8String(!0)),t==="hex8"&&(n=this.toHex8String()),t==="name"&&(n=this.toName()),t==="hsl"&&(n=this.toHslString()),t==="hsv"&&(n=this.toHsvString()),n||this.toHexString())},clone:function(){return Se(this.toString())},_applyModification:function(t,a){var n=t.apply(null,[this].concat([].slice.call(a)));return this._r=n._r,this._g=n._g,this._b=n._b,this.setAlpha(n._a),this},lighten:function(){return this._applyModification(lb,arguments)},brighten:function(){return this._applyModification(cb,arguments)},darken:function(){return this._applyModification(db,arguments)},desaturate:function(){return this._applyModification(sb,arguments)},saturate:function(){return this._applyModification(ob,arguments)},greyscale:function(){return this._applyModification(ib,arguments)},spin:function(){return this._applyModification(ub,arguments)},_applyCombination:function(t,a){return t.apply(null,[this].concat([].slice.call(a)))},analogous:function(){return this._applyCombination(hb,arguments)},complement:function(){return this._applyCombination(pb,arguments)},monochromatic:function(){return this._applyCombination(mb,arguments)},splitcomplement:function(){return this._applyCombination(fb,arguments)},triad:function(){return this._applyCombination(bi,[3])},tetrad:function(){return this._applyCombination(bi,[4])}};Se.fromRatio=function(e,t){if(hr(e)=="object"){var a={};for(var n in e)e.hasOwnProperty(n)&&(n==="a"?a[n]=e[n]:a[n]=va(e[n]));e=a}return Se(e,t)};function eb(e){var t={r:0,g:0,b:0},a=1,n=null,r=null,o=null,l=!1,i=!1;return typeof e=="string"&&(e=vb(e)),hr(e)=="object"&&(zt(e.r)&&zt(e.g)&&zt(e.b)?(t=tb(e.r,e.g,e.b),l=!0,i=String(e.r).substr(-1)==="%"?"prgb":"rgb"):zt(e.h)&&zt(e.s)&&zt(e.v)?(n=va(e.s),r=va(e.v),t=ab(e.h,n,r),l=!0,i="hsv"):zt(e.h)&&zt(e.s)&&zt(e.l)&&(n=va(e.s),o=va(e.l),t=nb(e.h,n,o),l=!0,i="hsl"),e.hasOwnProperty("a")&&(a=e.a)),a=zl(a),{ok:l,format:e.format||i,r:Math.min(255,Math.max(t.r,0)),g:Math.min(255,Math.max(t.g,0)),b:Math.min(255,Math.max(t.b,0)),a}}function tb(e,t,a){return{r:Ye(e,255)*255,g:Ye(t,255)*255,b:Ye(a,255)*255}}function fi(e,t,a){e=Ye(e,255),t=Ye(t,255),a=Ye(a,255);var n=Math.max(e,t,a),r=Math.min(e,t,a),o,l,i=(n+r)/2;if(n==r)o=l=0;else{var d=n-r;switch(l=i>.5?d/(2-n-r):d/(n+r),n){case e:o=(t-a)/d+(t<a?6:0);break;case t:o=(a-e)/d+2;break;case a:o=(e-t)/d+4;break}o/=6}return{h:o,s:l,l:i}}function nb(e,t,a){var n,r,o;e=Ye(e,360),t=Ye(t,100),a=Ye(a,100);function l(f,c,p){return p<0&&(p+=1),p>1&&(p-=1),p<1/6?f+(c-f)*6*p:p<1/2?c:p<2/3?f+(c-f)*(2/3-p)*6:f}if(t===0)n=r=o=a;else{var i=a<.5?a*(1+t):a+t-a*t,d=2*a-i;n=l(d,i,e+1/3),r=l(d,i,e),o=l(d,i,e-1/3)}return{r:n*255,g:r*255,b:o*255}}function hi(e,t,a){e=Ye(e,255),t=Ye(t,255),a=Ye(a,255);var n=Math.max(e,t,a),r=Math.min(e,t,a),o,l,i=n,d=n-r;if(l=n===0?0:d/n,n==r)o=0;else{switch(n){case e:o=(t-a)/d+(t<a?6:0);break;case t:o=(a-e)/d+2;break;case a:o=(e-t)/d+4;break}o/=6}return{h:o,s:l,v:i}}function ab(e,t,a){e=Ye(e,360)*6,t=Ye(t,100),a=Ye(a,100);var n=Math.floor(e),r=e-n,o=a*(1-t),l=a*(1-r*t),i=a*(1-(1-r)*t),d=n%6,f=[a,l,o,o,i,a][d],c=[i,a,a,l,o,o][d],p=[o,o,i,a,a,l][d];return{r:f*255,g:c*255,b:p*255}}function mi(e,t,a,n){var r=[Dt(Math.round(e).toString(16)),Dt(Math.round(t).toString(16)),Dt(Math.round(a).toString(16))];return n&&r[0].charAt(0)==r[0].charAt(1)&&r[1].charAt(0)==r[1].charAt(1)&&r[2].charAt(0)==r[2].charAt(1)?r[0].charAt(0)+r[1].charAt(0)+r[2].charAt(0):r.join("")}function rb(e,t,a,n,r){var o=[Dt(Math.round(e).toString(16)),Dt(Math.round(t).toString(16)),Dt(Math.round(a).toString(16)),Dt(Kl(n))];return r&&o[0].charAt(0)==o[0].charAt(1)&&o[1].charAt(0)==o[1].charAt(1)&&o[2].charAt(0)==o[2].charAt(1)&&o[3].charAt(0)==o[3].charAt(1)?o[0].charAt(0)+o[1].charAt(0)+o[2].charAt(0)+o[3].charAt(0):o.join("")}function gi(e,t,a,n){var r=[Dt(Kl(n)),Dt(Math.round(e).toString(16)),Dt(Math.round(t).toString(16)),Dt(Math.round(a).toString(16))];return r.join("")}Se.equals=function(e,t){return!e||!t?!1:Se(e).toRgbString()==Se(t).toRgbString()};Se.random=function(){return Se.fromRatio({r:Math.random(),g:Math.random(),b:Math.random()})};function sb(e,t){t=t===0?0:t||10;var a=Se(e).toHsl();return a.s-=t/100,a.s=Ir(a.s),Se(a)}function ob(e,t){t=t===0?0:t||10;var a=Se(e).toHsl();return a.s+=t/100,a.s=Ir(a.s),Se(a)}function ib(e){return Se(e).desaturate(100)}function lb(e,t){t=t===0?0:t||10;var a=Se(e).toHsl();return a.l+=t/100,a.l=Ir(a.l),Se(a)}function cb(e,t){t=t===0?0:t||10;var a=Se(e).toRgb();return a.r=Math.max(0,Math.min(255,a.r-Math.round(255*-(t/100)))),a.g=Math.max(0,Math.min(255,a.g-Math.round(255*-(t/100)))),a.b=Math.max(0,Math.min(255,a.b-Math.round(255*-(t/100)))),Se(a)}function db(e,t){t=t===0?0:t||10;var a=Se(e).toHsl();return a.l-=t/100,a.l=Ir(a.l),Se(a)}function ub(e,t){var a=Se(e).toHsl(),n=(a.h+t)%360;return a.h=n<0?360+n:n,Se(a)}function pb(e){var t=Se(e).toHsl();return t.h=(t.h+180)%360,Se(t)}function bi(e,t){if(isNaN(t)||t<=0)throw new Error("Argument to polyad must be a positive number");for(var a=Se(e).toHsl(),n=[Se(e)],r=360/t,o=1;o<t;o++)n.push(Se({h:(a.h+o*r)%360,s:a.s,l:a.l}));return n}function fb(e){var t=Se(e).toHsl(),a=t.h;return[Se(e),Se({h:(a+72)%360,s:t.s,l:t.l}),Se({h:(a+216)%360,s:t.s,l:t.l})]}function hb(e,t,a){t=t||6,a=a||30;var n=Se(e).toHsl(),r=360/a,o=[Se(e)];for(n.h=(n.h-(r*t>>1)+720)%360;--t;)n.h=(n.h+r)%360,o.push(Se(n));return o}function mb(e,t){t=t||6;for(var a=Se(e).toHsv(),n=a.h,r=a.s,o=a.v,l=[],i=1/t;t--;)l.push(Se({h:n,s:r,v:o})),o=(o+i)%1;return l}Se.mix=function(e,t,a){a=a===0?0:a||50;var n=Se(e).toRgb(),r=Se(t).toRgb(),o=a/100,l={r:(r.r-n.r)*o+n.r,g:(r.g-n.g)*o+n.g,b:(r.b-n.b)*o+n.b,a:(r.a-n.a)*o+n.a};return Se(l)};Se.readability=function(e,t){var a=Se(e),n=Se(t);return(Math.max(a.getLuminance(),n.getLuminance())+.05)/(Math.min(a.getLuminance(),n.getLuminance())+.05)};Se.isReadable=function(e,t,a){var n=Se.readability(e,t),r,o;switch(o=!1,r=wb(a),r.level+r.size){case"AAsmall":case"AAAlarge":o=n>=4.5;break;case"AAlarge":o=n>=3;break;case"AAAsmall":o=n>=7;break}return o};Se.mostReadable=function(e,t,a){var n=null,r=0,o,l,i,d;a=a||{},l=a.includeFallbackColors,i=a.level,d=a.size;for(var f=0;f<t.length;f++)o=Se.readability(e,t[f]),o>r&&(r=o,n=Se(t[f]));return Se.isReadable(e,n,{level:i,size:d})||!l?n:(a.includeFallbackColors=!1,Se.mostReadable(e,["#fff","#000"],a))};var gs=Se.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},gb=Se.hexNames=bb(gs);function bb(e){var t={};for(var a in e)e.hasOwnProperty(a)&&(t[e[a]]=a);return t}function zl(e){return e=parseFloat(e),(isNaN(e)||e<0||e>1)&&(e=1),e}function Ye(e,t){xb(e)&&(e="100%");var a=yb(e);return e=Math.min(t,Math.max(0,parseFloat(e))),a&&(e=parseInt(e*t,10)/100),Math.abs(e-t)<1e-6?1:e%t/parseFloat(t)}function Ir(e){return Math.min(1,Math.max(0,e))}function vt(e){return parseInt(e,16)}function xb(e){return typeof e=="string"&&e.indexOf(".")!=-1&&parseFloat(e)===1}function yb(e){return typeof e=="string"&&e.indexOf("%")!=-1}function Dt(e){return e.length==1?"0"+e:""+e}function va(e){return e<=1&&(e=e*100+"%"),e}function Kl(e){return Math.round(parseFloat(e)*255).toString(16)}function xi(e){return vt(e)/255}var Nt=function(){var e="[-\\+]?\\d+%?",t="[-\\+]?\\d*\\.\\d+%?",a="(?:"+t+")|(?:"+e+")",n="[\\s|\\(]+("+a+")[,|\\s]+("+a+")[,|\\s]+("+a+")\\s*\\)?",r="[\\s|\\(]+("+a+")[,|\\s]+("+a+")[,|\\s]+("+a+")[,|\\s]+("+a+")\\s*\\)?";return{CSS_UNIT:new RegExp(a),rgb:new RegExp("rgb"+n),rgba:new RegExp("rgba"+r),hsl:new RegExp("hsl"+n),hsla:new RegExp("hsla"+r),hsv:new RegExp("hsv"+n),hsva:new RegExp("hsva"+r),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/}}();function zt(e){return!!Nt.CSS_UNIT.exec(e)}function vb(e){e=e.replace(Z0,"").replace(J0,"").toLowerCase();var t=!1;if(gs[e])e=gs[e],t=!0;else if(e=="transparent")return{r:0,g:0,b:0,a:0,format:"name"};var a;return(a=Nt.rgb.exec(e))?{r:a[1],g:a[2],b:a[3]}:(a=Nt.rgba.exec(e))?{r:a[1],g:a[2],b:a[3],a:a[4]}:(a=Nt.hsl.exec(e))?{h:a[1],s:a[2],l:a[3]}:(a=Nt.hsla.exec(e))?{h:a[1],s:a[2],l:a[3],a:a[4]}:(a=Nt.hsv.exec(e))?{h:a[1],s:a[2],v:a[3]}:(a=Nt.hsva.exec(e))?{h:a[1],s:a[2],v:a[3],a:a[4]}:(a=Nt.hex8.exec(e))?{r:vt(a[1]),g:vt(a[2]),b:vt(a[3]),a:xi(a[4]),format:t?"name":"hex8"}:(a=Nt.hex6.exec(e))?{r:vt(a[1]),g:vt(a[2]),b:vt(a[3]),format:t?"name":"hex"}:(a=Nt.hex4.exec(e))?{r:vt(a[1]+""+a[1]),g:vt(a[2]+""+a[2]),b:vt(a[3]+""+a[3]),a:xi(a[4]+""+a[4]),format:t?"name":"hex8"}:(a=Nt.hex3.exec(e))?{r:vt(a[1]+""+a[1]),g:vt(a[2]+""+a[2]),b:vt(a[3]+""+a[3]),format:t?"name":"hex"}:!1}function wb(e){var t,a;return e=e||{level:"AA",size:"small"},t=(e.level||"AA").toUpperCase(),a=(e.size||"small").toLowerCase(),t!=="AA"&&t!=="AAA"&&(t="AA"),a!=="small"&&a!=="large"&&(a="small"),{level:t,size:a}}var yi=function(t){var a=["r","g","b","a","h","s","l","v"],n=0,r=0;return Q0(a,function(o){if(t[o]&&(n+=1,isNaN(t[o])||(r+=1),o==="s"||o==="l")){var l=/^\d+%$/;l.test(t[o])&&(r+=1)}}),n===r?t:!1},wa=function(t,a){var n=t.hex?Se(t.hex):Se(t),r=n.toHsl(),o=n.toHsv(),l=n.toRgb(),i=n.toHex();r.s===0&&(r.h=a||0,o.h=a||0);var d=i==="000000"&&l.a===0;return{hsl:r,hex:d?"transparent":"#"+i,rgb:l,hsv:o,oldHue:t.h||a||r.h,source:t.source}},bn=function(t){if(t==="transparent")return!0;var a=String(t).charAt(0)==="#"?1:0;return t.length!==4+a&&t.length<7+a&&Se(t).isValid()},Xs=function(t){if(!t)return"#fff";var a=wa(t);if(a.hex==="transparent")return"rgba(0,0,0,0.4)";var n=(a.rgb.r*299+a.rgb.g*587+a.rgb.b*114)/1e3;return n>=128?"#000":"#fff"},Yr=function(t,a){var n=t.replace("°","");return Se(a+" ("+n+")")._ok},ma=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},Cb=function(){function e(t,a){for(var n=0;n<a.length;n++){var r=a[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(t,a,n){return a&&e(t.prototype,a),n&&e(t,n),t}}();function Tb(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function jb(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function Sb(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var yt=function(t){var a=function(n){Sb(r,n);function r(o){Tb(this,r);var l=jb(this,(r.__proto__||Object.getPrototypeOf(r)).call(this));return l.handleChange=function(i,d){var f=yi(i);if(f){var c=wa(i,i.h||l.state.oldHue);l.setState(c),l.props.onChangeComplete&&l.debounce(l.props.onChangeComplete,c,d),l.props.onChange&&l.props.onChange(c,d)}},l.handleSwatchHover=function(i,d){var f=yi(i);if(f){var c=wa(i,i.h||l.state.oldHue);l.props.onSwatchHover&&l.props.onSwatchHover(c,d)}},l.state=ma({},wa(o.color,0)),l.debounce=Ul(function(i,d,f){i(d,f)},100),l}return Cb(r,[{key:"render",value:function(){var l={};return this.props.onSwatchHover&&(l.onSwatchHover=this.handleSwatchHover),P.createElement(t,ma({},this.props,this.state,{onChange:this.handleChange},l))}}],[{key:"getDerivedStateFromProps",value:function(l,i){return ma({},wa(l.color,i.oldHue))}}]),r}(b.PureComponent||b.Component);return a.propTypes=ma({},t.propTypes),a.defaultProps=ma({},t.defaultProps,{color:{h:250,s:.5,l:.2,a:1}}),a},Eb=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},_b=function(){function e(t,a){for(var n=0;n<a.length;n++){var r=a[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(t,a,n){return a&&e(t.prototype,a),n&&e(t,n),t}}();function Ab(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function vi(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function kb(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var Pb=function(t){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"span";return function(n){kb(r,n);function r(){var o,l,i,d;Ab(this,r);for(var f=arguments.length,c=Array(f),p=0;p<f;p++)c[p]=arguments[p];return d=(l=(i=vi(this,(o=r.__proto__||Object.getPrototypeOf(r)).call.apply(o,[this].concat(c))),i),i.state={focus:!1},i.handleFocus=function(){return i.setState({focus:!0})},i.handleBlur=function(){return i.setState({focus:!1})},l),vi(i,d)}return _b(r,[{key:"render",value:function(){return P.createElement(a,{onFocus:this.handleFocus,onBlur:this.handleBlur},P.createElement(t,Eb({},this.props,this.state)))}}]),r}(P.Component)},wi=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},Nb=13,Ib=function(t){var a=t.color,n=t.style,r=t.onClick,o=r===void 0?function(){}:r,l=t.onHover,i=t.title,d=i===void 0?a:i,f=t.children,c=t.focus,p=t.focusStyle,u=p===void 0?{}:p,h=a==="transparent",g=Ie({default:{swatch:wi({background:a,height:"100%",width:"100%",cursor:"pointer",position:"relative",outline:"none"},n,c?u:{})}}),v=function(I){return o(a,I)},C=function(I){return I.keyCode===Nb&&o(a,I)},j=function(I){return l(a,I)},N={};return l&&(N.onMouseOver=j),P.createElement("div",wi({style:g.swatch,onClick:v,title:d,tabIndex:0,onKeyDown:C},N),f,h&&P.createElement(ca,{borderRadius:g.swatch.borderRadius,boxShadow:"inset 0 0 0 1px rgba(0,0,0,0.1)"}))};const Nn=Pb(Ib);var Ob=function(t){var a=t.direction,n=Ie({default:{picker:{width:"18px",height:"18px",borderRadius:"50%",transform:"translate(-9px, -1px)",backgroundColor:"rgb(248, 248, 248)",boxShadow:"0 1px 4px 0 rgba(0, 0, 0, 0.37)"}},vertical:{picker:{transform:"translate(-3px, -9px)"}}},{vertical:a==="vertical"});return P.createElement("div",{style:n.picker})},Mb=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},ql=function(t){var a=t.rgb,n=t.hsl,r=t.width,o=t.height,l=t.onChange,i=t.direction,d=t.style,f=t.renderers,c=t.pointer,p=t.className,u=p===void 0?"":p,h=Ie({default:{picker:{position:"relative",width:r,height:o},alpha:{radius:"2px",style:d}}});return P.createElement("div",{style:h.picker,className:"alpha-picker "+u},P.createElement(Us,Mb({},h.alpha,{rgb:a,hsl:n,pointer:c,renderers:f,onChange:l,direction:i})))};ql.defaultProps={width:"316px",height:"16px",direction:"horizontal",pointer:Ob};yt(ql);function Wl(e,t){for(var a=-1,n=e==null?0:e.length,r=Array(n);++a<n;)r[a]=t(e[a],a,e);return r}var Db="__lodash_hash_undefined__";function Lb(e){return this.__data__.set(e,Db),this}function $b(e){return this.__data__.has(e)}function mr(e){var t=-1,a=e==null?0:e.length;for(this.__data__=new Jt;++t<a;)this.add(e[t])}mr.prototype.add=mr.prototype.push=Lb;mr.prototype.has=$b;function Fb(e,t){for(var a=-1,n=e==null?0:e.length;++a<n;)if(t(e[a],a,e))return!0;return!1}function Bb(e,t){return e.has(t)}var Hb=1,Rb=2;function Gl(e,t,a,n,r,o){var l=a&Hb,i=e.length,d=t.length;if(i!=d&&!(l&&d>i))return!1;var f=o.get(e),c=o.get(t);if(f&&c)return f==t&&c==e;var p=-1,u=!0,h=a&Rb?new mr:void 0;for(o.set(e,t),o.set(t,e);++p<i;){var g=e[p],v=t[p];if(n)var C=l?n(v,g,p,t,e,o):n(g,v,p,e,t,o);if(C!==void 0){if(C)continue;u=!1;break}if(h){if(!Fb(t,function(j,N){if(!Bb(h,N)&&(g===j||r(g,j,a,n,o)))return h.push(N)})){u=!1;break}}else if(!(g===v||r(g,v,a,n,o))){u=!1;break}}return o.delete(e),o.delete(t),u}function Ub(e){var t=-1,a=Array(e.size);return e.forEach(function(n,r){a[++t]=[r,n]}),a}function Vb(e){var t=-1,a=Array(e.size);return e.forEach(function(n){a[++t]=n}),a}var zb=1,Kb=2,qb="[object Boolean]",Wb="[object Date]",Gb="[object Error]",Yb="[object Map]",Xb="[object Number]",Qb="[object RegExp]",Zb="[object Set]",Jb="[object String]",ex="[object Symbol]",tx="[object ArrayBuffer]",nx="[object DataView]",Ci=an?an.prototype:void 0,Xr=Ci?Ci.valueOf:void 0;function ax(e,t,a,n,r,o,l){switch(a){case nx:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case tx:return!(e.byteLength!=t.byteLength||!o(new ur(e),new ur(t)));case qb:case Wb:case Xb:return $a(+e,+t);case Gb:return e.name==t.name&&e.message==t.message;case Qb:case Jb:return e==t+"";case Yb:var i=Ub;case Zb:var d=n&zb;if(i||(i=Vb),e.size!=t.size&&!d)return!1;var f=l.get(e);if(f)return f==t;n|=Kb,l.set(e,t);var c=Gl(i(e),i(t),n,r,o,l);return l.delete(e),c;case ex:if(Xr)return Xr.call(e)==Xr.call(t)}return!1}function rx(e,t){for(var a=-1,n=t.length,r=e.length;++a<n;)e[r+a]=t[a];return e}function sx(e,t,a){var n=t(e);return Ct(e)?n:rx(n,a(e))}function ox(e,t){for(var a=-1,n=e==null?0:e.length,r=0,o=[];++a<n;){var l=e[a];t(l,a,e)&&(o[r++]=l)}return o}function ix(){return[]}var lx=Object.prototype,cx=lx.propertyIsEnumerable,Ti=Object.getOwnPropertySymbols,dx=Ti?function(e){return e==null?[]:(e=Object(e),ox(Ti(e),function(t){return cx.call(e,t)}))}:ix;const ux=dx;function ji(e){return sx(e,Ys,ux)}var px=1,fx=Object.prototype,hx=fx.hasOwnProperty;function mx(e,t,a,n,r,o){var l=a&px,i=ji(e),d=i.length,f=ji(t),c=f.length;if(d!=c&&!l)return!1;for(var p=d;p--;){var u=i[p];if(!(l?u in t:hx.call(t,u)))return!1}var h=o.get(e),g=o.get(t);if(h&&g)return h==t&&g==e;var v=!0;o.set(e,t),o.set(t,e);for(var C=l;++p<d;){u=i[p];var j=e[u],N=t[u];if(n)var M=l?n(N,j,u,t,e,o):n(j,N,u,e,t,o);if(!(M===void 0?j===N||r(j,N,a,n,o):M)){v=!1;break}C||(C=u=="constructor")}if(v&&!C){var I=e.constructor,F=t.constructor;I!=F&&"constructor"in e&&"constructor"in t&&!(typeof I=="function"&&I instanceof I&&typeof F=="function"&&F instanceof F)&&(v=!1)}return o.delete(e),o.delete(t),v}var gx=Pn(Ft,"DataView");const bs=gx;var bx=Pn(Ft,"Promise");const xs=bx;var xx=Pn(Ft,"Set");const ys=xx;var yx=Pn(Ft,"WeakMap");const vs=yx;var Si="[object Map]",vx="[object Object]",Ei="[object Promise]",_i="[object Set]",Ai="[object WeakMap]",ki="[object DataView]",wx=kn(bs),Cx=kn(Aa),Tx=kn(xs),jx=kn(ys),Sx=kn(vs),xn=An;(bs&&xn(new bs(new ArrayBuffer(1)))!=ki||Aa&&xn(new Aa)!=Si||xs&&xn(xs.resolve())!=Ei||ys&&xn(new ys)!=_i||vs&&xn(new vs)!=Ai)&&(xn=function(e){var t=An(e),a=t==vx?e.constructor:void 0,n=a?kn(a):"";if(n)switch(n){case wx:return ki;case Cx:return Si;case Tx:return Ei;case jx:return _i;case Sx:return Ai}return t});const Pi=xn;var Ex=1,Ni="[object Arguments]",Ii="[object Array]",er="[object Object]",_x=Object.prototype,Oi=_x.hasOwnProperty;function Ax(e,t,a,n,r,o){var l=Ct(e),i=Ct(t),d=l?Ii:Pi(e),f=i?Ii:Pi(t);d=d==Ni?er:d,f=f==Ni?er:f;var c=d==er,p=f==er,u=d==f;if(u&&fr(e)){if(!fr(t))return!1;l=!0,c=!1}if(u&&!c)return o||(o=new Vt),l||Ws(e)?Gl(e,t,a,n,r,o):ax(e,t,d,a,n,r,o);if(!(a&Ex)){var h=c&&Oi.call(e,"__wrapped__"),g=p&&Oi.call(t,"__wrapped__");if(h||g){var v=h?e.value():e,C=g?t.value():t;return o||(o=new Vt),r(v,C,a,n,o)}}return u?(o||(o=new Vt),mx(e,t,a,n,r,o)):!1}function Qs(e,t,a,n,r){return e===t?!0:e==null||t==null||!rn(e)&&!rn(t)?e!==e&&t!==t:Ax(e,t,a,n,Qs,r)}var kx=1,Px=2;function Nx(e,t,a,n){var r=a.length,o=r,l=!n;if(e==null)return!o;for(e=Object(e);r--;){var i=a[r];if(l&&i[2]?i[1]!==e[i[0]]:!(i[0]in e))return!1}for(;++r<o;){i=a[r];var d=i[0],f=e[d],c=i[1];if(l&&i[2]){if(f===void 0&&!(d in e))return!1}else{var p=new Vt;if(n)var u=n(f,c,d,e,t,p);if(!(u===void 0?Qs(c,f,kx|Px,n,p):u))return!1}}return!0}function Yl(e){return e===e&&!kt(e)}function Ix(e){for(var t=Ys(e),a=t.length;a--;){var n=t[a],r=e[n];t[a]=[n,r,Yl(r)]}return t}function Xl(e,t){return function(a){return a==null?!1:a[e]===t&&(t!==void 0||e in Object(a))}}function Ox(e){var t=Ix(e);return t.length==1&&t[0][2]?Xl(t[0][0],t[0][1]):function(a){return a===e||Nx(a,e,t)}}var Mx=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Dx=/^\w*$/;function Zs(e,t){if(Ct(e))return!1;var a=typeof e;return a=="number"||a=="symbol"||a=="boolean"||e==null||Pr(e)?!0:Dx.test(e)||!Mx.test(e)||t!=null&&e in Object(t)}var Lx="Expected a function";function Js(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(Lx);var a=function(){var n=arguments,r=t?t.apply(this,n):n[0],o=a.cache;if(o.has(r))return o.get(r);var l=e.apply(this,n);return a.cache=o.set(r,l)||o,l};return a.cache=new(Js.Cache||Jt),a}Js.Cache=Jt;var $x=500;function Fx(e){var t=Js(e,function(n){return a.size===$x&&a.clear(),n}),a=t.cache;return t}var Bx=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Hx=/\\(\\)?/g,Rx=Fx(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(Bx,function(a,n,r,o){t.push(r?o.replace(Hx,"$1"):n||a)}),t});const Ux=Rx;var Vx=1/0,Mi=an?an.prototype:void 0,Di=Mi?Mi.toString:void 0;function Ql(e){if(typeof e=="string")return e;if(Ct(e))return Wl(e,Ql)+"";if(Pr(e))return Di?Di.call(e):"";var t=e+"";return t=="0"&&1/e==-Vx?"-0":t}function zx(e){return e==null?"":Ql(e)}function Zl(e,t){return Ct(e)?e:Zs(e,t)?[e]:Ux(zx(e))}var Kx=1/0;function Or(e){if(typeof e=="string"||Pr(e))return e;var t=e+"";return t=="0"&&1/e==-Kx?"-0":t}function Jl(e,t){t=Zl(t,e);for(var a=0,n=t.length;e!=null&&a<n;)e=e[Or(t[a++])];return a&&a==n?e:void 0}function qx(e,t,a){var n=e==null?void 0:Jl(e,t);return n===void 0?a:n}function Wx(e,t){return e!=null&&t in Object(e)}function Gx(e,t,a){t=Zl(t,e);for(var n=-1,r=t.length,o=!1;++n<r;){var l=Or(t[n]);if(!(o=e!=null&&a(e,l)))break;e=e[l]}return o||++n!=r?o:(r=e==null?0:e.length,!!r&&qs(r)&&Gs(l,r)&&(Ct(e)||pr(e)))}function Yx(e,t){return e!=null&&Gx(e,t,Wx)}var Xx=1,Qx=2;function Zx(e,t){return Zs(e)&&Yl(t)?Xl(Or(e),t):function(a){var n=qx(a,e);return n===void 0&&n===t?Yx(a,e):Qs(t,n,Xx|Qx)}}function Jx(e){return function(t){return t==null?void 0:t[e]}}function ey(e){return function(t){return Jl(t,e)}}function ty(e){return Zs(e)?Jx(Or(e)):ey(e)}function ny(e){return typeof e=="function"?e:e==null?kr:typeof e=="object"?Ct(e)?Zx(e[0],e[1]):Ox(e):ty(e)}function ay(e,t){var a=-1,n=ua(e)?Array(e.length):[];return Vl(e,function(r,o,l){n[++a]=t(r,o,l)}),n}function In(e,t){var a=Ct(e)?Wl:ay;return a(e,ny(t))}var ry=function(t){var a=t.colors,n=t.onClick,r=t.onSwatchHover,o=Ie({default:{swatches:{marginRight:"-10px"},swatch:{width:"22px",height:"22px",float:"left",marginRight:"10px",marginBottom:"10px",borderRadius:"4px"},clear:{clear:"both"}}});return P.createElement("div",{style:o.swatches},In(a,function(l){return P.createElement(Nn,{key:l,color:l,style:o.swatch,onClick:n,onHover:r,focusStyle:{boxShadow:"0 0 4px "+l}})}),P.createElement("div",{style:o.clear}))},eo=function(t){var a=t.onChange,n=t.onSwatchHover,r=t.hex,o=t.colors,l=t.width,i=t.triangle,d=t.styles,f=d===void 0?{}:d,c=t.className,p=c===void 0?"":c,u=r==="transparent",h=function(C,j){bn(C)&&a({hex:C,source:"hex"},j)},g=Ie(xt({default:{card:{width:l,background:"#fff",boxShadow:"0 1px rgba(0,0,0,.1)",borderRadius:"6px",position:"relative"},head:{height:"110px",background:r,borderRadius:"6px 6px 0 0",display:"flex",alignItems:"center",justifyContent:"center",position:"relative"},body:{padding:"10px"},label:{fontSize:"18px",color:Xs(r),position:"relative"},triangle:{width:"0px",height:"0px",borderStyle:"solid",borderWidth:"0 10px 10px 10px",borderColor:"transparent transparent "+r+" transparent",position:"absolute",top:"-10px",left:"50%",marginLeft:"-10px"},input:{width:"100%",fontSize:"12px",color:"#666",border:"0px",outline:"none",height:"22px",boxShadow:"inset 0 0 0 1px #ddd",borderRadius:"4px",padding:"0 7px",boxSizing:"border-box"}},"hide-triangle":{triangle:{display:"none"}}},f),{"hide-triangle":i==="hide"});return P.createElement("div",{style:g.card,className:"block-picker "+p},P.createElement("div",{style:g.triangle}),P.createElement("div",{style:g.head},u&&P.createElement(ca,{borderRadius:"6px 6px 0 0"}),P.createElement("div",{style:g.label},r)),P.createElement("div",{style:g.body},P.createElement(ry,{colors:o,onClick:h,onSwatchHover:n}),P.createElement(Be,{style:{input:g.input},value:r,onChange:h})))};eo.propTypes={width:xe.oneOfType([xe.string,xe.number]),colors:xe.arrayOf(xe.string),triangle:xe.oneOf(["top","hide"]),styles:xe.object};eo.defaultProps={width:170,colors:["#D9E3F0","#F47373","#697689","#37D67A","#2CCCE4","#555555","#dce775","#ff8a65","#ba68c8"],triangle:"top",styles:{}};yt(eo);var Dn={50:"#ffebee",100:"#ffcdd2",200:"#ef9a9a",300:"#e57373",400:"#ef5350",500:"#f44336",600:"#e53935",700:"#d32f2f",800:"#c62828",900:"#b71c1c",a100:"#ff8a80",a200:"#ff5252",a400:"#ff1744",a700:"#d50000"},Ln={50:"#fce4ec",100:"#f8bbd0",200:"#f48fb1",300:"#f06292",400:"#ec407a",500:"#e91e63",600:"#d81b60",700:"#c2185b",800:"#ad1457",900:"#880e4f",a100:"#ff80ab",a200:"#ff4081",a400:"#f50057",a700:"#c51162"},$n={50:"#f3e5f5",100:"#e1bee7",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",600:"#8e24aa",700:"#7b1fa2",800:"#6a1b9a",900:"#4a148c",a100:"#ea80fc",a200:"#e040fb",a400:"#d500f9",a700:"#aa00ff"},Fn={50:"#ede7f6",100:"#d1c4e9",200:"#b39ddb",300:"#9575cd",400:"#7e57c2",500:"#673ab7",600:"#5e35b1",700:"#512da8",800:"#4527a0",900:"#311b92",a100:"#b388ff",a200:"#7c4dff",a400:"#651fff",a700:"#6200ea"},Bn={50:"#e8eaf6",100:"#c5cae9",200:"#9fa8da",300:"#7986cb",400:"#5c6bc0",500:"#3f51b5",600:"#3949ab",700:"#303f9f",800:"#283593",900:"#1a237e",a100:"#8c9eff",a200:"#536dfe",a400:"#3d5afe",a700:"#304ffe"},Hn={50:"#e3f2fd",100:"#bbdefb",200:"#90caf9",300:"#64b5f6",400:"#42a5f5",500:"#2196f3",600:"#1e88e5",700:"#1976d2",800:"#1565c0",900:"#0d47a1",a100:"#82b1ff",a200:"#448aff",a400:"#2979ff",a700:"#2962ff"},Rn={50:"#e1f5fe",100:"#b3e5fc",200:"#81d4fa",300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",600:"#039be5",700:"#0288d1",800:"#0277bd",900:"#01579b",a100:"#80d8ff",a200:"#40c4ff",a400:"#00b0ff",a700:"#0091ea"},Un={50:"#e0f7fa",100:"#b2ebf2",200:"#80deea",300:"#4dd0e1",400:"#26c6da",500:"#00bcd4",600:"#00acc1",700:"#0097a7",800:"#00838f",900:"#006064",a100:"#84ffff",a200:"#18ffff",a400:"#00e5ff",a700:"#00b8d4"},Vn={50:"#e0f2f1",100:"#b2dfdb",200:"#80cbc4",300:"#4db6ac",400:"#26a69a",500:"#009688",600:"#00897b",700:"#00796b",800:"#00695c",900:"#004d40",a100:"#a7ffeb",a200:"#64ffda",a400:"#1de9b6",a700:"#00bfa5"},Ca={50:"#e8f5e9",100:"#c8e6c9",200:"#a5d6a7",300:"#81c784",400:"#66bb6a",500:"#4caf50",600:"#43a047",700:"#388e3c",800:"#2e7d32",900:"#1b5e20",a100:"#b9f6ca",a200:"#69f0ae",a400:"#00e676",a700:"#00c853"},zn={50:"#f1f8e9",100:"#dcedc8",200:"#c5e1a5",300:"#aed581",400:"#9ccc65",500:"#8bc34a",600:"#7cb342",700:"#689f38",800:"#558b2f",900:"#33691e",a100:"#ccff90",a200:"#b2ff59",a400:"#76ff03",a700:"#64dd17"},Kn={50:"#f9fbe7",100:"#f0f4c3",200:"#e6ee9c",300:"#dce775",400:"#d4e157",500:"#cddc39",600:"#c0ca33",700:"#afb42b",800:"#9e9d24",900:"#827717",a100:"#f4ff81",a200:"#eeff41",a400:"#c6ff00",a700:"#aeea00"},qn={50:"#fffde7",100:"#fff9c4",200:"#fff59d",300:"#fff176",400:"#ffee58",500:"#ffeb3b",600:"#fdd835",700:"#fbc02d",800:"#f9a825",900:"#f57f17",a100:"#ffff8d",a200:"#ffff00",a400:"#ffea00",a700:"#ffd600"},Wn={50:"#fff8e1",100:"#ffecb3",200:"#ffe082",300:"#ffd54f",400:"#ffca28",500:"#ffc107",600:"#ffb300",700:"#ffa000",800:"#ff8f00",900:"#ff6f00",a100:"#ffe57f",a200:"#ffd740",a400:"#ffc400",a700:"#ffab00"},Gn={50:"#fff3e0",100:"#ffe0b2",200:"#ffcc80",300:"#ffb74d",400:"#ffa726",500:"#ff9800",600:"#fb8c00",700:"#f57c00",800:"#ef6c00",900:"#e65100",a100:"#ffd180",a200:"#ffab40",a400:"#ff9100",a700:"#ff6d00"},Yn={50:"#fbe9e7",100:"#ffccbc",200:"#ffab91",300:"#ff8a65",400:"#ff7043",500:"#ff5722",600:"#f4511e",700:"#e64a19",800:"#d84315",900:"#bf360c",a100:"#ff9e80",a200:"#ff6e40",a400:"#ff3d00",a700:"#dd2c00"},Xn={50:"#efebe9",100:"#d7ccc8",200:"#bcaaa4",300:"#a1887f",400:"#8d6e63",500:"#795548",600:"#6d4c41",700:"#5d4037",800:"#4e342e",900:"#3e2723"},Qn={50:"#eceff1",100:"#cfd8dc",200:"#b0bec5",300:"#90a4ae",400:"#78909c",500:"#607d8b",600:"#546e7a",700:"#455a64",800:"#37474f",900:"#263238"},ec=function(t){var a=t.color,n=t.onClick,r=t.onSwatchHover,o=t.hover,l=t.active,i=t.circleSize,d=t.circleSpacing,f=Ie({default:{swatch:{width:i,height:i,marginRight:d,marginBottom:d,transform:"scale(1)",transition:"100ms transform ease"},Swatch:{borderRadius:"50%",background:"transparent",boxShadow:"inset 0 0 0 "+(i/2+1)+"px "+a,transition:"100ms box-shadow ease"}},hover:{swatch:{transform:"scale(1.2)"}},active:{Swatch:{boxShadow:"inset 0 0 0 3px "+a}}},{hover:o,active:l});return P.createElement("div",{style:f.swatch},P.createElement(Nn,{style:f.Swatch,color:a,onClick:n,onHover:r,focusStyle:{boxShadow:f.Swatch.boxShadow+", 0 0 5px "+a}}))};ec.defaultProps={circleSize:28,circleSpacing:14};const sy=Rs(ec);var to=function(t){var a=t.width,n=t.onChange,r=t.onSwatchHover,o=t.colors,l=t.hex,i=t.circleSize,d=t.styles,f=d===void 0?{}:d,c=t.circleSpacing,p=t.className,u=p===void 0?"":p,h=Ie(xt({default:{card:{width:a,display:"flex",flexWrap:"wrap",marginRight:-c,marginBottom:-c}}},f)),g=function(C,j){return n({hex:C,source:"hex"},j)};return P.createElement("div",{style:h.card,className:"circle-picker "+u},In(o,function(v){return P.createElement(sy,{key:v,color:v,onClick:g,onSwatchHover:r,active:l===v.toLowerCase(),circleSize:i,circleSpacing:c})}))};to.propTypes={width:xe.oneOfType([xe.string,xe.number]),circleSize:xe.number,circleSpacing:xe.number,styles:xe.object};to.defaultProps={width:252,circleSize:28,circleSpacing:14,colors:[Dn[500],Ln[500],$n[500],Fn[500],Bn[500],Hn[500],Rn[500],Un[500],Vn[500],Ca[500],zn[500],Kn[500],qn[500],Wn[500],Gn[500],Yn[500],Xn[500],Qn[500]],styles:{}};yt(to);function Li(e){return e===void 0}var tc={};Object.defineProperty(tc,"__esModule",{value:!0});var $i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},oy=b,Fi=iy(oy);function iy(e){return e&&e.__esModule?e:{default:e}}function ly(e,t){var a={};for(var n in e)t.indexOf(n)>=0||Object.prototype.hasOwnProperty.call(e,n)&&(a[n]=e[n]);return a}var tr=24,cy=tc.default=function(e){var t=e.fill,a=t===void 0?"currentColor":t,n=e.width,r=n===void 0?tr:n,o=e.height,l=o===void 0?tr:o,i=e.style,d=i===void 0?{}:i,f=ly(e,["fill","width","height","style"]);return Fi.default.createElement("svg",$i({viewBox:"0 0 "+tr+" "+tr,style:$i({fill:a,width:r,height:l},d)},f),Fi.default.createElement("path",{d:"M12,18.17L8.83,15L7.42,16.41L12,21L16.59,16.41L15.17,15M12,5.83L15.17,9L16.58,7.59L12,3L7.41,7.59L8.83,9L12,5.83Z"}))},dy=function(){function e(t,a){for(var n=0;n<a.length;n++){var r=a[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(t,a,n){return a&&e(t.prototype,a),n&&e(t,n),t}}();function uy(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function py(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function fy(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var nc=function(e){fy(t,e);function t(a){uy(this,t);var n=py(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return n.toggleViews=function(){n.state.view==="hex"?n.setState({view:"rgb"}):n.state.view==="rgb"?n.setState({view:"hsl"}):n.state.view==="hsl"&&(n.props.hsl.a===1?n.setState({view:"hex"}):n.setState({view:"rgb"}))},n.handleChange=function(r,o){r.hex?bn(r.hex)&&n.props.onChange({hex:r.hex,source:"hex"},o):r.r||r.g||r.b?n.props.onChange({r:r.r||n.props.rgb.r,g:r.g||n.props.rgb.g,b:r.b||n.props.rgb.b,source:"rgb"},o):r.a?(r.a<0?r.a=0:r.a>1&&(r.a=1),n.props.onChange({h:n.props.hsl.h,s:n.props.hsl.s,l:n.props.hsl.l,a:Math.round(r.a*100)/100,source:"rgb"},o)):(r.h||r.s||r.l)&&(typeof r.s=="string"&&r.s.includes("%")&&(r.s=r.s.replace("%","")),typeof r.l=="string"&&r.l.includes("%")&&(r.l=r.l.replace("%","")),r.s==1?r.s=.01:r.l==1&&(r.l=.01),n.props.onChange({h:r.h||n.props.hsl.h,s:Number(Li(r.s)?n.props.hsl.s:r.s),l:Number(Li(r.l)?n.props.hsl.l:r.l),source:"hsl"},o))},n.showHighlight=function(r){r.currentTarget.style.background="#eee"},n.hideHighlight=function(r){r.currentTarget.style.background="transparent"},a.hsl.a!==1&&a.view==="hex"?n.state={view:"rgb"}:n.state={view:a.view},n}return dy(t,[{key:"render",value:function(){var n=this,r=Ie({default:{wrap:{paddingTop:"16px",display:"flex"},fields:{flex:"1",display:"flex",marginLeft:"-6px"},field:{paddingLeft:"6px",width:"100%"},alpha:{paddingLeft:"6px",width:"100%"},toggle:{width:"32px",textAlign:"right",position:"relative"},icon:{marginRight:"-4px",marginTop:"12px",cursor:"pointer",position:"relative"},iconHighlight:{position:"absolute",width:"24px",height:"28px",background:"#eee",borderRadius:"4px",top:"10px",left:"12px",display:"none"},input:{fontSize:"11px",color:"#333",width:"100%",borderRadius:"2px",border:"none",boxShadow:"inset 0 0 0 1px #dadada",height:"21px",textAlign:"center"},label:{textTransform:"uppercase",fontSize:"11px",lineHeight:"11px",color:"#969696",textAlign:"center",display:"block",marginTop:"12px"},svg:{fill:"#333",width:"24px",height:"24px",border:"1px transparent solid",borderRadius:"5px"}},disableAlpha:{alpha:{display:"none"}}},this.props,this.state),o=void 0;return this.state.view==="hex"?o=P.createElement("div",{style:r.fields,className:"flexbox-fix"},P.createElement("div",{style:r.field},P.createElement(Be,{style:{input:r.input,label:r.label},label:"hex",value:this.props.hex,onChange:this.handleChange}))):this.state.view==="rgb"?o=P.createElement("div",{style:r.fields,className:"flexbox-fix"},P.createElement("div",{style:r.field},P.createElement(Be,{style:{input:r.input,label:r.label},label:"r",value:this.props.rgb.r,onChange:this.handleChange})),P.createElement("div",{style:r.field},P.createElement(Be,{style:{input:r.input,label:r.label},label:"g",value:this.props.rgb.g,onChange:this.handleChange})),P.createElement("div",{style:r.field},P.createElement(Be,{style:{input:r.input,label:r.label},label:"b",value:this.props.rgb.b,onChange:this.handleChange})),P.createElement("div",{style:r.alpha},P.createElement(Be,{style:{input:r.input,label:r.label},label:"a",value:this.props.rgb.a,arrowOffset:.01,onChange:this.handleChange}))):this.state.view==="hsl"&&(o=P.createElement("div",{style:r.fields,className:"flexbox-fix"},P.createElement("div",{style:r.field},P.createElement(Be,{style:{input:r.input,label:r.label},label:"h",value:Math.round(this.props.hsl.h),onChange:this.handleChange})),P.createElement("div",{style:r.field},P.createElement(Be,{style:{input:r.input,label:r.label},label:"s",value:Math.round(this.props.hsl.s*100)+"%",onChange:this.handleChange})),P.createElement("div",{style:r.field},P.createElement(Be,{style:{input:r.input,label:r.label},label:"l",value:Math.round(this.props.hsl.l*100)+"%",onChange:this.handleChange})),P.createElement("div",{style:r.alpha},P.createElement(Be,{style:{input:r.input,label:r.label},label:"a",value:this.props.hsl.a,arrowOffset:.01,onChange:this.handleChange})))),P.createElement("div",{style:r.wrap,className:"flexbox-fix"},o,P.createElement("div",{style:r.toggle},P.createElement("div",{style:r.icon,onClick:this.toggleViews,ref:function(i){return n.icon=i}},P.createElement(cy,{style:r.svg,onMouseOver:this.showHighlight,onMouseEnter:this.showHighlight,onMouseOut:this.hideHighlight}))))}}],[{key:"getDerivedStateFromProps",value:function(n,r){return n.hsl.a!==1&&r.view==="hex"?{view:"rgb"}:null}}]),t}(P.Component);nc.defaultProps={view:"hex"};var Bi=function(){var t=Ie({default:{picker:{width:"12px",height:"12px",borderRadius:"6px",transform:"translate(-6px, -1px)",backgroundColor:"rgb(248, 248, 248)",boxShadow:"0 1px 4px 0 rgba(0, 0, 0, 0.37)"}}});return P.createElement("div",{style:t.picker})},hy=function(){var t=Ie({default:{picker:{width:"12px",height:"12px",borderRadius:"6px",boxShadow:"inset 0 0 0 1px #fff",transform:"translate(-6px, -6px)"}}});return P.createElement("div",{style:t.picker})},no=function(t){var a=t.width,n=t.onChange,r=t.disableAlpha,o=t.rgb,l=t.hsl,i=t.hsv,d=t.hex,f=t.renderers,c=t.styles,p=c===void 0?{}:c,u=t.className,h=u===void 0?"":u,g=t.defaultView,v=Ie(xt({default:{picker:{width:a,background:"#fff",borderRadius:"2px",boxShadow:"0 0 2px rgba(0,0,0,.3), 0 4px 8px rgba(0,0,0,.3)",boxSizing:"initial",fontFamily:"Menlo"},saturation:{width:"100%",paddingBottom:"55%",position:"relative",borderRadius:"2px 2px 0 0",overflow:"hidden"},Saturation:{radius:"2px 2px 0 0"},body:{padding:"16px 16px 12px"},controls:{display:"flex"},color:{width:"32px"},swatch:{marginTop:"6px",width:"16px",height:"16px",borderRadius:"8px",position:"relative",overflow:"hidden"},active:{absolute:"0px 0px 0px 0px",borderRadius:"8px",boxShadow:"inset 0 0 0 1px rgba(0,0,0,.1)",background:"rgba("+o.r+", "+o.g+", "+o.b+", "+o.a+")",zIndex:"2"},toggles:{flex:"1"},hue:{height:"10px",position:"relative",marginBottom:"8px"},Hue:{radius:"2px"},alpha:{height:"10px",position:"relative"},Alpha:{radius:"2px"}},disableAlpha:{color:{width:"22px"},alpha:{display:"none"},hue:{marginBottom:"0px"},swatch:{width:"10px",height:"10px",marginTop:"0px"}}},p),{disableAlpha:r});return P.createElement("div",{style:v.picker,className:"chrome-picker "+h},P.createElement("div",{style:v.saturation},P.createElement(Nr,{style:v.Saturation,hsl:l,hsv:i,pointer:hy,onChange:n})),P.createElement("div",{style:v.body},P.createElement("div",{style:v.controls,className:"flexbox-fix"},P.createElement("div",{style:v.color},P.createElement("div",{style:v.swatch},P.createElement("div",{style:v.active}),P.createElement(ca,{renderers:f}))),P.createElement("div",{style:v.toggles},P.createElement("div",{style:v.hue},P.createElement(da,{style:v.Hue,hsl:l,pointer:Bi,onChange:n})),P.createElement("div",{style:v.alpha},P.createElement(Us,{style:v.Alpha,rgb:o,hsl:l,pointer:Bi,renderers:f,onChange:n})))),P.createElement(nc,{rgb:o,hsl:l,hex:d,view:g,onChange:n,disableAlpha:r})))};no.propTypes={width:xe.oneOfType([xe.string,xe.number]),disableAlpha:xe.bool,styles:xe.object,defaultView:xe.oneOf(["hex","rgb","hsl"])};no.defaultProps={width:225,disableAlpha:!1,styles:{}};yt(no);var my=function(t){var a=t.color,n=t.onClick,r=n===void 0?function(){}:n,o=t.onSwatchHover,l=t.active,i=Ie({default:{color:{background:a,width:"15px",height:"15px",float:"left",marginRight:"5px",marginBottom:"5px",position:"relative",cursor:"pointer"},dot:{absolute:"5px 5px 5px 5px",background:Xs(a),borderRadius:"50%",opacity:"0"}},active:{dot:{opacity:"1"}},"color-#FFFFFF":{color:{boxShadow:"inset 0 0 0 1px #ddd"},dot:{background:"#000"}},transparent:{dot:{background:"#000"}}},{active:l,"color-#FFFFFF":a==="#FFFFFF",transparent:a==="transparent"});return P.createElement(Nn,{style:i.color,color:a,onClick:r,onHover:o,focusStyle:{boxShadow:"0 0 4px "+a}},P.createElement("div",{style:i.dot}))},gy=function(t){var a=t.hex,n=t.rgb,r=t.onChange,o=Ie({default:{fields:{display:"flex",paddingBottom:"6px",paddingRight:"5px",position:"relative"},active:{position:"absolute",top:"6px",left:"5px",height:"9px",width:"9px",background:a},HEXwrap:{flex:"6",position:"relative"},HEXinput:{width:"80%",padding:"0px",paddingLeft:"20%",border:"none",outline:"none",background:"none",fontSize:"12px",color:"#333",height:"16px"},HEXlabel:{display:"none"},RGBwrap:{flex:"3",position:"relative"},RGBinput:{width:"70%",padding:"0px",paddingLeft:"30%",border:"none",outline:"none",background:"none",fontSize:"12px",color:"#333",height:"16px"},RGBlabel:{position:"absolute",top:"3px",left:"0px",lineHeight:"16px",textTransform:"uppercase",fontSize:"12px",color:"#999"}}}),l=function(d,f){d.r||d.g||d.b?r({r:d.r||n.r,g:d.g||n.g,b:d.b||n.b,source:"rgb"},f):r({hex:d.hex,source:"hex"},f)};return P.createElement("div",{style:o.fields,className:"flexbox-fix"},P.createElement("div",{style:o.active}),P.createElement(Be,{style:{wrap:o.HEXwrap,input:o.HEXinput,label:o.HEXlabel},label:"hex",value:a,onChange:l}),P.createElement(Be,{style:{wrap:o.RGBwrap,input:o.RGBinput,label:o.RGBlabel},label:"r",value:n.r,onChange:l}),P.createElement(Be,{style:{wrap:o.RGBwrap,input:o.RGBinput,label:o.RGBlabel},label:"g",value:n.g,onChange:l}),P.createElement(Be,{style:{wrap:o.RGBwrap,input:o.RGBinput,label:o.RGBlabel},label:"b",value:n.b,onChange:l}))},ao=function(t){var a=t.onChange,n=t.onSwatchHover,r=t.colors,o=t.hex,l=t.rgb,i=t.styles,d=i===void 0?{}:i,f=t.className,c=f===void 0?"":f,p=Ie(xt({default:{Compact:{background:"#f6f6f6",radius:"4px"},compact:{paddingTop:"5px",paddingLeft:"5px",boxSizing:"initial",width:"240px"},clear:{clear:"both"}}},d)),u=function(g,v){g.hex?bn(g.hex)&&a({hex:g.hex,source:"hex"},v):a(g,v)};return P.createElement(Fa,{style:p.Compact,styles:d},P.createElement("div",{style:p.compact,className:"compact-picker "+c},P.createElement("div",null,In(r,function(h){return P.createElement(my,{key:h,color:h,active:h.toLowerCase()===o,onClick:u,onSwatchHover:n})}),P.createElement("div",{style:p.clear})),P.createElement(gy,{hex:o,rgb:l,onChange:u})))};ao.propTypes={colors:xe.arrayOf(xe.string),styles:xe.object};ao.defaultProps={colors:["#4D4D4D","#999999","#FFFFFF","#F44E3B","#FE9200","#FCDC00","#DBDF00","#A4DD00","#68CCCA","#73D8FF","#AEA1FF","#FDA1FF","#333333","#808080","#cccccc","#D33115","#E27300","#FCC400","#B0BC00","#68BC00","#16A5A5","#009CE0","#7B64FF","#FA28FF","#000000","#666666","#B3B3B3","#9F0500","#C45100","#FB9E00","#808900","#194D33","#0C797D","#0062B1","#653294","#AB149E"],styles:{}};yt(ao);var by=function(t){var a=t.hover,n=t.color,r=t.onClick,o=t.onSwatchHover,l={position:"relative",zIndex:"2",outline:"2px solid #fff",boxShadow:"0 0 5px 2px rgba(0,0,0,0.25)"},i=Ie({default:{swatch:{width:"25px",height:"25px",fontSize:"0"}},hover:{swatch:l}},{hover:a});return P.createElement("div",{style:i.swatch},P.createElement(Nn,{color:n,onClick:r,onHover:o,focusStyle:l}))};const xy=Rs(by);var ro=function(t){var a=t.width,n=t.colors,r=t.onChange,o=t.onSwatchHover,l=t.triangle,i=t.styles,d=i===void 0?{}:i,f=t.className,c=f===void 0?"":f,p=Ie(xt({default:{card:{width:a,background:"#fff",border:"1px solid rgba(0,0,0,0.2)",boxShadow:"0 3px 12px rgba(0,0,0,0.15)",borderRadius:"4px",position:"relative",padding:"5px",display:"flex",flexWrap:"wrap"},triangle:{position:"absolute",border:"7px solid transparent",borderBottomColor:"#fff"},triangleShadow:{position:"absolute",border:"8px solid transparent",borderBottomColor:"rgba(0,0,0,0.15)"}},"hide-triangle":{triangle:{display:"none"},triangleShadow:{display:"none"}},"top-left-triangle":{triangle:{top:"-14px",left:"10px"},triangleShadow:{top:"-16px",left:"9px"}},"top-right-triangle":{triangle:{top:"-14px",right:"10px"},triangleShadow:{top:"-16px",right:"9px"}},"bottom-left-triangle":{triangle:{top:"35px",left:"10px",transform:"rotate(180deg)"},triangleShadow:{top:"37px",left:"9px",transform:"rotate(180deg)"}},"bottom-right-triangle":{triangle:{top:"35px",right:"10px",transform:"rotate(180deg)"},triangleShadow:{top:"37px",right:"9px",transform:"rotate(180deg)"}}},d),{"hide-triangle":l==="hide","top-left-triangle":l==="top-left","top-right-triangle":l==="top-right","bottom-left-triangle":l==="bottom-left","bottom-right-triangle":l==="bottom-right"}),u=function(g,v){return r({hex:g,source:"hex"},v)};return P.createElement("div",{style:p.card,className:"github-picker "+c},P.createElement("div",{style:p.triangleShadow}),P.createElement("div",{style:p.triangle}),In(n,function(h){return P.createElement(xy,{color:h,key:h,onClick:u,onSwatchHover:o})}))};ro.propTypes={width:xe.oneOfType([xe.string,xe.number]),colors:xe.arrayOf(xe.string),triangle:xe.oneOf(["hide","top-left","top-right","bottom-left","bottom-right"]),styles:xe.object};ro.defaultProps={width:200,colors:["#B80000","#DB3E00","#FCCB00","#008B02","#006B76","#1273DE","#004DCF","#5300EB","#EB9694","#FAD0C3","#FEF3BD","#C1E1C5","#BEDADC","#C4DEF6","#BED3F3","#D4C4FB"],triangle:"top-left",styles:{}};yt(ro);var yy=function(t){var a=t.direction,n=Ie({default:{picker:{width:"18px",height:"18px",borderRadius:"50%",transform:"translate(-9px, -1px)",backgroundColor:"rgb(248, 248, 248)",boxShadow:"0 1px 4px 0 rgba(0, 0, 0, 0.37)"}},vertical:{picker:{transform:"translate(-3px, -9px)"}}},{vertical:a==="vertical"});return P.createElement("div",{style:n.picker})},vy=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},so=function(t){var a=t.width,n=t.height,r=t.onChange,o=t.hsl,l=t.direction,i=t.pointer,d=t.styles,f=d===void 0?{}:d,c=t.className,p=c===void 0?"":c,u=Ie(xt({default:{picker:{position:"relative",width:a,height:n},hue:{radius:"2px"}}},f)),h=function(v){return r({a:1,h:v.h,l:.5,s:1})};return P.createElement("div",{style:u.picker,className:"hue-picker "+p},P.createElement(da,vy({},u.hue,{hsl:o,pointer:i,onChange:h,direction:l})))};so.propTypes={styles:xe.object};so.defaultProps={width:"316px",height:"16px",direction:"horizontal",pointer:yy,styles:{}};yt(so);var wy=function(t){var a=t.onChange,n=t.hex,r=t.rgb,o=t.styles,l=o===void 0?{}:o,i=t.className,d=i===void 0?"":i,f=Ie(xt({default:{material:{width:"98px",height:"98px",padding:"16px",fontFamily:"Roboto"},HEXwrap:{position:"relative"},HEXinput:{width:"100%",marginTop:"12px",fontSize:"15px",color:"#333",padding:"0px",border:"0px",borderBottom:"2px solid "+n,outline:"none",height:"30px"},HEXlabel:{position:"absolute",top:"0px",left:"0px",fontSize:"11px",color:"#999999",textTransform:"capitalize"},Hex:{style:{}},RGBwrap:{position:"relative"},RGBinput:{width:"100%",marginTop:"12px",fontSize:"15px",color:"#333",padding:"0px",border:"0px",borderBottom:"1px solid #eee",outline:"none",height:"30px"},RGBlabel:{position:"absolute",top:"0px",left:"0px",fontSize:"11px",color:"#999999",textTransform:"capitalize"},split:{display:"flex",marginRight:"-10px",paddingTop:"11px"},third:{flex:"1",paddingRight:"10px"}}},l)),c=function(u,h){u.hex?bn(u.hex)&&a({hex:u.hex,source:"hex"},h):(u.r||u.g||u.b)&&a({r:u.r||r.r,g:u.g||r.g,b:u.b||r.b,source:"rgb"},h)};return P.createElement(Fa,{styles:l},P.createElement("div",{style:f.material,className:"material-picker "+d},P.createElement(Be,{style:{wrap:f.HEXwrap,input:f.HEXinput,label:f.HEXlabel},label:"hex",value:n,onChange:c}),P.createElement("div",{style:f.split,className:"flexbox-fix"},P.createElement("div",{style:f.third},P.createElement(Be,{style:{wrap:f.RGBwrap,input:f.RGBinput,label:f.RGBlabel},label:"r",value:r.r,onChange:c})),P.createElement("div",{style:f.third},P.createElement(Be,{style:{wrap:f.RGBwrap,input:f.RGBinput,label:f.RGBlabel},label:"g",value:r.g,onChange:c})),P.createElement("div",{style:f.third},P.createElement(Be,{style:{wrap:f.RGBwrap,input:f.RGBinput,label:f.RGBlabel},label:"b",value:r.b,onChange:c})))))};yt(wy);var Cy=function(t){var a=t.onChange,n=t.rgb,r=t.hsv,o=t.hex,l=Ie({default:{fields:{paddingTop:"5px",paddingBottom:"9px",width:"80px",position:"relative"},divider:{height:"5px"},RGBwrap:{position:"relative"},RGBinput:{marginLeft:"40%",width:"40%",height:"18px",border:"1px solid #888888",boxShadow:"inset 0 1px 1px rgba(0,0,0,.1), 0 1px 0 0 #ECECEC",marginBottom:"5px",fontSize:"13px",paddingLeft:"3px",marginRight:"10px"},RGBlabel:{left:"0px",top:"0px",width:"34px",textTransform:"uppercase",fontSize:"13px",height:"18px",lineHeight:"22px",position:"absolute"},HEXwrap:{position:"relative"},HEXinput:{marginLeft:"20%",width:"80%",height:"18px",border:"1px solid #888888",boxShadow:"inset 0 1px 1px rgba(0,0,0,.1), 0 1px 0 0 #ECECEC",marginBottom:"6px",fontSize:"13px",paddingLeft:"3px"},HEXlabel:{position:"absolute",top:"0px",left:"0px",width:"14px",textTransform:"uppercase",fontSize:"13px",height:"18px",lineHeight:"22px"},fieldSymbols:{position:"absolute",top:"5px",right:"-7px",fontSize:"13px"},symbol:{height:"20px",lineHeight:"22px",paddingBottom:"7px"}}}),i=function(f,c){f["#"]?bn(f["#"])&&a({hex:f["#"],source:"hex"},c):f.r||f.g||f.b?a({r:f.r||n.r,g:f.g||n.g,b:f.b||n.b,source:"rgb"},c):(f.h||f.s||f.v)&&a({h:f.h||r.h,s:f.s||r.s,v:f.v||r.v,source:"hsv"},c)};return P.createElement("div",{style:l.fields},P.createElement(Be,{style:{wrap:l.RGBwrap,input:l.RGBinput,label:l.RGBlabel},label:"h",value:Math.round(r.h),onChange:i}),P.createElement(Be,{style:{wrap:l.RGBwrap,input:l.RGBinput,label:l.RGBlabel},label:"s",value:Math.round(r.s*100),onChange:i}),P.createElement(Be,{style:{wrap:l.RGBwrap,input:l.RGBinput,label:l.RGBlabel},label:"v",value:Math.round(r.v*100),onChange:i}),P.createElement("div",{style:l.divider}),P.createElement(Be,{style:{wrap:l.RGBwrap,input:l.RGBinput,label:l.RGBlabel},label:"r",value:n.r,onChange:i}),P.createElement(Be,{style:{wrap:l.RGBwrap,input:l.RGBinput,label:l.RGBlabel},label:"g",value:n.g,onChange:i}),P.createElement(Be,{style:{wrap:l.RGBwrap,input:l.RGBinput,label:l.RGBlabel},label:"b",value:n.b,onChange:i}),P.createElement("div",{style:l.divider}),P.createElement(Be,{style:{wrap:l.HEXwrap,input:l.HEXinput,label:l.HEXlabel},label:"#",value:o.replace("#",""),onChange:i}),P.createElement("div",{style:l.fieldSymbols},P.createElement("div",{style:l.symbol},"°"),P.createElement("div",{style:l.symbol},"%"),P.createElement("div",{style:l.symbol},"%")))},Ty=function(t){var a=t.hsl,n=Ie({default:{picker:{width:"12px",height:"12px",borderRadius:"6px",boxShadow:"inset 0 0 0 1px #fff",transform:"translate(-6px, -6px)"}},"black-outline":{picker:{boxShadow:"inset 0 0 0 1px #000"}}},{"black-outline":a.l>.5});return P.createElement("div",{style:n.picker})},jy=function(){var t=Ie({default:{triangle:{width:0,height:0,borderStyle:"solid",borderWidth:"4px 0 4px 6px",borderColor:"transparent transparent transparent #fff",position:"absolute",top:"1px",left:"1px"},triangleBorder:{width:0,height:0,borderStyle:"solid",borderWidth:"5px 0 5px 8px",borderColor:"transparent transparent transparent #555"},left:{Extend:"triangleBorder",transform:"translate(-13px, -4px)"},leftInside:{Extend:"triangle",transform:"translate(-8px, -5px)"},right:{Extend:"triangleBorder",transform:"translate(20px, -14px) rotate(180deg)"},rightInside:{Extend:"triangle",transform:"translate(-8px, -5px)"}}});return P.createElement("div",{style:t.pointer},P.createElement("div",{style:t.left},P.createElement("div",{style:t.leftInside})),P.createElement("div",{style:t.right},P.createElement("div",{style:t.rightInside})))},Hi=function(t){var a=t.onClick,n=t.label,r=t.children,o=t.active,l=Ie({default:{button:{backgroundImage:"linear-gradient(-180deg, #FFFFFF 0%, #E6E6E6 100%)",border:"1px solid #878787",borderRadius:"2px",height:"20px",boxShadow:"0 1px 0 0 #EAEAEA",fontSize:"14px",color:"#000",lineHeight:"20px",textAlign:"center",marginBottom:"10px",cursor:"pointer"}},active:{button:{boxShadow:"0 0 0 1px #878787"}}},{active:o});return P.createElement("div",{style:l.button,onClick:a},n||r)},Sy=function(t){var a=t.rgb,n=t.currentColor,r=Ie({default:{swatches:{border:"1px solid #B3B3B3",borderBottom:"1px solid #F0F0F0",marginBottom:"2px",marginTop:"1px"},new:{height:"34px",background:"rgb("+a.r+","+a.g+", "+a.b+")",boxShadow:"inset 1px 0 0 #000, inset -1px 0 0 #000, inset 0 1px 0 #000"},current:{height:"34px",background:n,boxShadow:"inset 1px 0 0 #000, inset -1px 0 0 #000, inset 0 -1px 0 #000"},label:{fontSize:"14px",color:"#000",textAlign:"center"}}});return P.createElement("div",null,P.createElement("div",{style:r.label},"new"),P.createElement("div",{style:r.swatches},P.createElement("div",{style:r.new}),P.createElement("div",{style:r.current})),P.createElement("div",{style:r.label},"current"))},Ey=function(){function e(t,a){for(var n=0;n<a.length;n++){var r=a[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(t,a,n){return a&&e(t.prototype,a),n&&e(t,n),t}}();function _y(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ay(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function ky(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var oo=function(e){ky(t,e);function t(a){_y(this,t);var n=Ay(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return n.state={currentColor:a.hex},n}return Ey(t,[{key:"render",value:function(){var n=this.props,r=n.styles,o=r===void 0?{}:r,l=n.className,i=l===void 0?"":l,d=Ie(xt({default:{picker:{background:"#DCDCDC",borderRadius:"4px",boxShadow:"0 0 0 1px rgba(0,0,0,.25), 0 8px 16px rgba(0,0,0,.15)",boxSizing:"initial",width:"513px"},head:{backgroundImage:"linear-gradient(-180deg, #F0F0F0 0%, #D4D4D4 100%)",borderBottom:"1px solid #B1B1B1",boxShadow:"inset 0 1px 0 0 rgba(255,255,255,.2), inset 0 -1px 0 0 rgba(0,0,0,.02)",height:"23px",lineHeight:"24px",borderRadius:"4px 4px 0 0",fontSize:"13px",color:"#4D4D4D",textAlign:"center"},body:{padding:"15px 15px 0",display:"flex"},saturation:{width:"256px",height:"256px",position:"relative",border:"2px solid #B3B3B3",borderBottom:"2px solid #F0F0F0",overflow:"hidden"},hue:{position:"relative",height:"256px",width:"19px",marginLeft:"10px",border:"2px solid #B3B3B3",borderBottom:"2px solid #F0F0F0"},controls:{width:"180px",marginLeft:"10px"},top:{display:"flex"},previews:{width:"60px"},actions:{flex:"1",marginLeft:"20px"}}},o));return P.createElement("div",{style:d.picker,className:"photoshop-picker "+i},P.createElement("div",{style:d.head},this.props.header),P.createElement("div",{style:d.body,className:"flexbox-fix"},P.createElement("div",{style:d.saturation},P.createElement(Nr,{hsl:this.props.hsl,hsv:this.props.hsv,pointer:Ty,onChange:this.props.onChange})),P.createElement("div",{style:d.hue},P.createElement(da,{direction:"vertical",hsl:this.props.hsl,pointer:jy,onChange:this.props.onChange})),P.createElement("div",{style:d.controls},P.createElement("div",{style:d.top,className:"flexbox-fix"},P.createElement("div",{style:d.previews},P.createElement(Sy,{rgb:this.props.rgb,currentColor:this.state.currentColor})),P.createElement("div",{style:d.actions},P.createElement(Hi,{label:"OK",onClick:this.props.onAccept,active:!0}),P.createElement(Hi,{label:"Cancel",onClick:this.props.onCancel}),P.createElement(Cy,{onChange:this.props.onChange,rgb:this.props.rgb,hsv:this.props.hsv,hex:this.props.hex}))))))}}]),t}(P.Component);oo.propTypes={header:xe.string,styles:xe.object};oo.defaultProps={header:"Color Picker",styles:{}};yt(oo);var Py=function(t){var a=t.onChange,n=t.rgb,r=t.hsl,o=t.hex,l=t.disableAlpha,i=Ie({default:{fields:{display:"flex",paddingTop:"4px"},single:{flex:"1",paddingLeft:"6px"},alpha:{flex:"1",paddingLeft:"6px"},double:{flex:"2"},input:{width:"80%",padding:"4px 10% 3px",border:"none",boxShadow:"inset 0 0 0 1px #ccc",fontSize:"11px"},label:{display:"block",textAlign:"center",fontSize:"11px",color:"#222",paddingTop:"3px",paddingBottom:"4px",textTransform:"capitalize"}},disableAlpha:{alpha:{display:"none"}}},{disableAlpha:l}),d=function(c,p){c.hex?bn(c.hex)&&a({hex:c.hex,source:"hex"},p):c.r||c.g||c.b?a({r:c.r||n.r,g:c.g||n.g,b:c.b||n.b,a:n.a,source:"rgb"},p):c.a&&(c.a<0?c.a=0:c.a>100&&(c.a=100),c.a/=100,a({h:r.h,s:r.s,l:r.l,a:c.a,source:"rgb"},p))};return P.createElement("div",{style:i.fields,className:"flexbox-fix"},P.createElement("div",{style:i.double},P.createElement(Be,{style:{input:i.input,label:i.label},label:"hex",value:o.replace("#",""),onChange:d})),P.createElement("div",{style:i.single},P.createElement(Be,{style:{input:i.input,label:i.label},label:"r",value:n.r,onChange:d,dragLabel:"true",dragMax:"255"})),P.createElement("div",{style:i.single},P.createElement(Be,{style:{input:i.input,label:i.label},label:"g",value:n.g,onChange:d,dragLabel:"true",dragMax:"255"})),P.createElement("div",{style:i.single},P.createElement(Be,{style:{input:i.input,label:i.label},label:"b",value:n.b,onChange:d,dragLabel:"true",dragMax:"255"})),P.createElement("div",{style:i.alpha},P.createElement(Be,{style:{input:i.input,label:i.label},label:"a",value:Math.round(n.a*100),onChange:d,dragLabel:"true",dragMax:"100"})))},Ny=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},ac=function(t){var a=t.colors,n=t.onClick,r=n===void 0?function(){}:n,o=t.onSwatchHover,l=Ie({default:{colors:{margin:"0 -10px",padding:"10px 0 0 10px",borderTop:"1px solid #eee",display:"flex",flexWrap:"wrap",position:"relative"},swatchWrap:{width:"16px",height:"16px",margin:"0 10px 10px 0"},swatch:{borderRadius:"3px",boxShadow:"inset 0 0 0 1px rgba(0,0,0,.15)"}},"no-presets":{colors:{display:"none"}}},{"no-presets":!a||!a.length}),i=function(f,c){r({hex:f,source:"hex"},c)};return P.createElement("div",{style:l.colors,className:"flexbox-fix"},a.map(function(d){var f=typeof d=="string"?{color:d}:d,c=""+f.color+(f.title||"");return P.createElement("div",{key:c,style:l.swatchWrap},P.createElement(Nn,Ny({},f,{style:l.swatch,onClick:i,onHover:o,focusStyle:{boxShadow:"inset 0 0 0 1px rgba(0,0,0,.15), 0 0 4px "+f.color}})))}))};ac.propTypes={colors:xe.arrayOf(xe.oneOfType([xe.string,xe.shape({color:xe.string,title:xe.string})])).isRequired};var Iy=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},io=function(t){var a=t.width,n=t.rgb,r=t.hex,o=t.hsv,l=t.hsl,i=t.onChange,d=t.onSwatchHover,f=t.disableAlpha,c=t.presetColors,p=t.renderers,u=t.styles,h=u===void 0?{}:u,g=t.className,v=g===void 0?"":g,C=Ie(xt({default:Iy({picker:{width:a,padding:"10px 10px 0",boxSizing:"initial",background:"#fff",borderRadius:"4px",boxShadow:"0 0 0 1px rgba(0,0,0,.15), 0 8px 16px rgba(0,0,0,.15)"},saturation:{width:"100%",paddingBottom:"75%",position:"relative",overflow:"hidden"},Saturation:{radius:"3px",shadow:"inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)"},controls:{display:"flex"},sliders:{padding:"4px 0",flex:"1"},color:{width:"24px",height:"24px",position:"relative",marginTop:"4px",marginLeft:"4px",borderRadius:"3px"},activeColor:{absolute:"0px 0px 0px 0px",borderRadius:"2px",background:"rgba("+n.r+","+n.g+","+n.b+","+n.a+")",boxShadow:"inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)"},hue:{position:"relative",height:"10px",overflow:"hidden"},Hue:{radius:"2px",shadow:"inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)"},alpha:{position:"relative",height:"10px",marginTop:"4px",overflow:"hidden"},Alpha:{radius:"2px",shadow:"inset 0 0 0 1px rgba(0,0,0,.15), inset 0 0 4px rgba(0,0,0,.25)"}},h),disableAlpha:{color:{height:"10px"},hue:{height:"10px"},alpha:{display:"none"}}},h),{disableAlpha:f});return P.createElement("div",{style:C.picker,className:"sketch-picker "+v},P.createElement("div",{style:C.saturation},P.createElement(Nr,{style:C.Saturation,hsl:l,hsv:o,onChange:i})),P.createElement("div",{style:C.controls,className:"flexbox-fix"},P.createElement("div",{style:C.sliders},P.createElement("div",{style:C.hue},P.createElement(da,{style:C.Hue,hsl:l,onChange:i})),P.createElement("div",{style:C.alpha},P.createElement(Us,{style:C.Alpha,rgb:n,hsl:l,renderers:p,onChange:i}))),P.createElement("div",{style:C.color},P.createElement(ca,null),P.createElement("div",{style:C.activeColor}))),P.createElement(Py,{rgb:n,hsl:l,hex:r,onChange:i,disableAlpha:f}),P.createElement(ac,{colors:c,onClick:i,onSwatchHover:d}))};io.propTypes={disableAlpha:xe.bool,width:xe.oneOfType([xe.string,xe.number]),styles:xe.object};io.defaultProps={disableAlpha:!1,width:200,styles:{},presetColors:["#D0021B","#F5A623","#F8E71C","#8B572A","#7ED321","#417505","#BD10E0","#9013FE","#4A90E2","#50E3C2","#B8E986","#000000","#4A4A4A","#9B9B9B","#FFFFFF"]};const Oy=yt(io);var ga=function(t){var a=t.hsl,n=t.offset,r=t.onClick,o=r===void 0?function(){}:r,l=t.active,i=t.first,d=t.last,f=Ie({default:{swatch:{height:"12px",background:"hsl("+a.h+", 50%, "+n*100+"%)",cursor:"pointer"}},first:{swatch:{borderRadius:"2px 0 0 2px"}},last:{swatch:{borderRadius:"0 2px 2px 0"}},active:{swatch:{transform:"scaleY(1.8)",borderRadius:"3.6px/2px"}}},{active:l,first:i,last:d}),c=function(u){return o({h:a.h,s:.5,l:n,source:"hsl"},u)};return P.createElement("div",{style:f.swatch,onClick:c})},My=function(t){var a=t.onClick,n=t.hsl,r=Ie({default:{swatches:{marginTop:"20px"},swatch:{boxSizing:"border-box",width:"20%",paddingRight:"1px",float:"left"},clear:{clear:"both"}}}),o=.1;return P.createElement("div",{style:r.swatches},P.createElement("div",{style:r.swatch},P.createElement(ga,{hsl:n,offset:".80",active:Math.abs(n.l-.8)<o&&Math.abs(n.s-.5)<o,onClick:a,first:!0})),P.createElement("div",{style:r.swatch},P.createElement(ga,{hsl:n,offset:".65",active:Math.abs(n.l-.65)<o&&Math.abs(n.s-.5)<o,onClick:a})),P.createElement("div",{style:r.swatch},P.createElement(ga,{hsl:n,offset:".50",active:Math.abs(n.l-.5)<o&&Math.abs(n.s-.5)<o,onClick:a})),P.createElement("div",{style:r.swatch},P.createElement(ga,{hsl:n,offset:".35",active:Math.abs(n.l-.35)<o&&Math.abs(n.s-.5)<o,onClick:a})),P.createElement("div",{style:r.swatch},P.createElement(ga,{hsl:n,offset:".20",active:Math.abs(n.l-.2)<o&&Math.abs(n.s-.5)<o,onClick:a,last:!0})),P.createElement("div",{style:r.clear}))},Dy=function(){var t=Ie({default:{picker:{width:"14px",height:"14px",borderRadius:"6px",transform:"translate(-7px, -1px)",backgroundColor:"rgb(248, 248, 248)",boxShadow:"0 1px 4px 0 rgba(0, 0, 0, 0.37)"}}});return P.createElement("div",{style:t.picker})},lo=function(t){var a=t.hsl,n=t.onChange,r=t.pointer,o=t.styles,l=o===void 0?{}:o,i=t.className,d=i===void 0?"":i,f=Ie(xt({default:{hue:{height:"12px",position:"relative"},Hue:{radius:"2px"}}},l));return P.createElement("div",{style:f.wrap||{},className:"slider-picker "+d},P.createElement("div",{style:f.hue},P.createElement(da,{style:f.Hue,hsl:a,pointer:r,onChange:n})),P.createElement("div",{style:f.swatches},P.createElement(My,{hsl:a,onClick:n})))};lo.propTypes={styles:xe.object};lo.defaultProps={pointer:Dy,styles:{}};yt(lo);var rc={};Object.defineProperty(rc,"__esModule",{value:!0});var Ri=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},Ly=b,Ui=$y(Ly);function $y(e){return e&&e.__esModule?e:{default:e}}function Fy(e,t){var a={};for(var n in e)t.indexOf(n)>=0||Object.prototype.hasOwnProperty.call(e,n)&&(a[n]=e[n]);return a}var nr=24,By=rc.default=function(e){var t=e.fill,a=t===void 0?"currentColor":t,n=e.width,r=n===void 0?nr:n,o=e.height,l=o===void 0?nr:o,i=e.style,d=i===void 0?{}:i,f=Fy(e,["fill","width","height","style"]);return Ui.default.createElement("svg",Ri({viewBox:"0 0 "+nr+" "+nr,style:Ri({fill:a,width:r,height:l},d)},f),Ui.default.createElement("path",{d:"M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"}))},Hy=function(t){var a=t.color,n=t.onClick,r=n===void 0?function(){}:n,o=t.onSwatchHover,l=t.first,i=t.last,d=t.active,f=Ie({default:{color:{width:"40px",height:"24px",cursor:"pointer",background:a,marginBottom:"1px"},check:{color:Xs(a),marginLeft:"8px",display:"none"}},first:{color:{overflow:"hidden",borderRadius:"2px 2px 0 0"}},last:{color:{overflow:"hidden",borderRadius:"0 0 2px 2px"}},active:{check:{display:"block"}},"color-#FFFFFF":{color:{boxShadow:"inset 0 0 0 1px #ddd"},check:{color:"#333"}},transparent:{check:{color:"#333"}}},{first:l,last:i,active:d,"color-#FFFFFF":a==="#FFFFFF",transparent:a==="transparent"});return P.createElement(Nn,{color:a,style:f.color,onClick:r,onHover:o,focusStyle:{boxShadow:"0 0 4px "+a}},P.createElement("div",{style:f.check},P.createElement(By,null)))},Ry=function(t){var a=t.onClick,n=t.onSwatchHover,r=t.group,o=t.active,l=Ie({default:{group:{paddingBottom:"10px",width:"40px",float:"left",marginRight:"10px"}}});return P.createElement("div",{style:l.group},In(r,function(i,d){return P.createElement(Hy,{key:i,color:i,active:i.toLowerCase()===o,first:d===0,last:d===r.length-1,onClick:a,onSwatchHover:n})}))},co=function(t){var a=t.width,n=t.height,r=t.onChange,o=t.onSwatchHover,l=t.colors,i=t.hex,d=t.styles,f=d===void 0?{}:d,c=t.className,p=c===void 0?"":c,u=Ie(xt({default:{picker:{width:a,height:n},overflow:{height:n,overflowY:"scroll"},body:{padding:"16px 0 6px 16px"},clear:{clear:"both"}}},f)),h=function(v,C){return r({hex:v,source:"hex"},C)};return P.createElement("div",{style:u.picker,className:"swatches-picker "+p},P.createElement(Fa,null,P.createElement("div",{style:u.overflow},P.createElement("div",{style:u.body},In(l,function(g){return P.createElement(Ry,{key:g.toString(),group:g,active:i,onClick:h,onSwatchHover:o})}),P.createElement("div",{style:u.clear})))))};co.propTypes={width:xe.oneOfType([xe.string,xe.number]),height:xe.oneOfType([xe.string,xe.number]),colors:xe.arrayOf(xe.arrayOf(xe.string)),styles:xe.object};co.defaultProps={width:320,height:240,colors:[[Dn[900],Dn[700],Dn[500],Dn[300],Dn[100]],[Ln[900],Ln[700],Ln[500],Ln[300],Ln[100]],[$n[900],$n[700],$n[500],$n[300],$n[100]],[Fn[900],Fn[700],Fn[500],Fn[300],Fn[100]],[Bn[900],Bn[700],Bn[500],Bn[300],Bn[100]],[Hn[900],Hn[700],Hn[500],Hn[300],Hn[100]],[Rn[900],Rn[700],Rn[500],Rn[300],Rn[100]],[Un[900],Un[700],Un[500],Un[300],Un[100]],[Vn[900],Vn[700],Vn[500],Vn[300],Vn[100]],["#194D33",Ca[700],Ca[500],Ca[300],Ca[100]],[zn[900],zn[700],zn[500],zn[300],zn[100]],[Kn[900],Kn[700],Kn[500],Kn[300],Kn[100]],[qn[900],qn[700],qn[500],qn[300],qn[100]],[Wn[900],Wn[700],Wn[500],Wn[300],Wn[100]],[Gn[900],Gn[700],Gn[500],Gn[300],Gn[100]],[Yn[900],Yn[700],Yn[500],Yn[300],Yn[100]],[Xn[900],Xn[700],Xn[500],Xn[300],Xn[100]],[Qn[900],Qn[700],Qn[500],Qn[300],Qn[100]],["#000000","#525252","#969696","#D9D9D9","#FFFFFF"]],styles:{}};yt(co);var uo=function(t){var a=t.onChange,n=t.onSwatchHover,r=t.hex,o=t.colors,l=t.width,i=t.triangle,d=t.styles,f=d===void 0?{}:d,c=t.className,p=c===void 0?"":c,u=Ie(xt({default:{card:{width:l,background:"#fff",border:"0 solid rgba(0,0,0,0.25)",boxShadow:"0 1px 4px rgba(0,0,0,0.25)",borderRadius:"4px",position:"relative"},body:{padding:"15px 9px 9px 15px"},label:{fontSize:"18px",color:"#fff"},triangle:{width:"0px",height:"0px",borderStyle:"solid",borderWidth:"0 9px 10px 9px",borderColor:"transparent transparent #fff transparent",position:"absolute"},triangleShadow:{width:"0px",height:"0px",borderStyle:"solid",borderWidth:"0 9px 10px 9px",borderColor:"transparent transparent rgba(0,0,0,.1) transparent",position:"absolute"},hash:{background:"#F0F0F0",height:"30px",width:"30px",borderRadius:"4px 0 0 4px",float:"left",color:"#98A1A4",display:"flex",alignItems:"center",justifyContent:"center"},input:{width:"100px",fontSize:"14px",color:"#666",border:"0px",outline:"none",height:"28px",boxShadow:"inset 0 0 0 1px #F0F0F0",boxSizing:"content-box",borderRadius:"0 4px 4px 0",float:"left",paddingLeft:"8px"},swatch:{width:"30px",height:"30px",float:"left",borderRadius:"4px",margin:"0 6px 6px 0"},clear:{clear:"both"}},"hide-triangle":{triangle:{display:"none"},triangleShadow:{display:"none"}},"top-left-triangle":{triangle:{top:"-10px",left:"12px"},triangleShadow:{top:"-11px",left:"12px"}},"top-right-triangle":{triangle:{top:"-10px",right:"12px"},triangleShadow:{top:"-11px",right:"12px"}}},f),{"hide-triangle":i==="hide","top-left-triangle":i==="top-left","top-right-triangle":i==="top-right"}),h=function(v,C){bn(v)&&a({hex:v,source:"hex"},C)};return P.createElement("div",{style:u.card,className:"twitter-picker "+p},P.createElement("div",{style:u.triangleShadow}),P.createElement("div",{style:u.triangle}),P.createElement("div",{style:u.body},In(o,function(g,v){return P.createElement(Nn,{key:v,color:g,hex:g,style:u.swatch,onClick:h,onHover:n,focusStyle:{boxShadow:"0 0 4px "+g}})}),P.createElement("div",{style:u.hash},"#"),P.createElement(Be,{label:null,style:{input:u.input},value:r.replace("#",""),onChange:h}),P.createElement("div",{style:u.clear})))};uo.propTypes={width:xe.oneOfType([xe.string,xe.number]),triangle:xe.oneOf(["hide","top-left","top-right"]),colors:xe.arrayOf(xe.string),styles:xe.object};uo.defaultProps={width:276,colors:["#FF6900","#FCB900","#7BDCB5","#00D084","#8ED1FC","#0693E3","#ABB8C3","#EB144C","#F78DA7","#9900EF"],triangle:"top-left",styles:{}};yt(uo);var po=function(t){var a=Ie({default:{picker:{width:"20px",height:"20px",borderRadius:"22px",border:"2px #fff solid",transform:"translate(-12px, -13px)",background:"hsl("+Math.round(t.hsl.h)+", "+Math.round(t.hsl.s*100)+"%, "+Math.round(t.hsl.l*100)+"%)"}}});return P.createElement("div",{style:a.picker})};po.propTypes={hsl:xe.shape({h:xe.number,s:xe.number,l:xe.number,a:xe.number})};po.defaultProps={hsl:{a:1,h:249.94,l:.2,s:.5}};var fo=function(t){var a=Ie({default:{picker:{width:"20px",height:"20px",borderRadius:"22px",transform:"translate(-10px, -7px)",background:"hsl("+Math.round(t.hsl.h)+", 100%, 50%)",border:"2px white solid"}}});return P.createElement("div",{style:a.picker})};fo.propTypes={hsl:xe.shape({h:xe.number,s:xe.number,l:xe.number,a:xe.number})};fo.defaultProps={hsl:{a:1,h:249.94,l:.2,s:.5}};var Uy=function(t){var a=t.onChange,n=t.rgb,r=t.hsl,o=t.hex,l=t.hsv,i=function(h,g){if(h.hex)bn(h.hex)&&a({hex:h.hex,source:"hex"},g);else if(h.rgb){var v=h.rgb.split(",");Yr(h.rgb,"rgb")&&a({r:v[0],g:v[1],b:v[2],a:1,source:"rgb"},g)}else if(h.hsv){var C=h.hsv.split(",");Yr(h.hsv,"hsv")&&(C[2]=C[2].replace("%",""),C[1]=C[1].replace("%",""),C[0]=C[0].replace("°",""),C[1]==1?C[1]=.01:C[2]==1&&(C[2]=.01),a({h:Number(C[0]),s:Number(C[1]),v:Number(C[2]),source:"hsv"},g))}else if(h.hsl){var j=h.hsl.split(",");Yr(h.hsl,"hsl")&&(j[2]=j[2].replace("%",""),j[1]=j[1].replace("%",""),j[0]=j[0].replace("°",""),p[1]==1?p[1]=.01:p[2]==1&&(p[2]=.01),a({h:Number(j[0]),s:Number(j[1]),v:Number(j[2]),source:"hsl"},g))}},d=Ie({default:{wrap:{display:"flex",height:"100px",marginTop:"4px"},fields:{width:"100%"},column:{paddingTop:"10px",display:"flex",justifyContent:"space-between"},double:{padding:"0px 4.4px",boxSizing:"border-box"},input:{width:"100%",height:"38px",boxSizing:"border-box",padding:"4px 10% 3px",textAlign:"center",border:"1px solid #dadce0",fontSize:"11px",textTransform:"lowercase",borderRadius:"5px",outline:"none",fontFamily:"Roboto,Arial,sans-serif"},input2:{height:"38px",width:"100%",border:"1px solid #dadce0",boxSizing:"border-box",fontSize:"11px",textTransform:"lowercase",borderRadius:"5px",outline:"none",paddingLeft:"10px",fontFamily:"Roboto,Arial,sans-serif"},label:{textAlign:"center",fontSize:"12px",background:"#fff",position:"absolute",textTransform:"uppercase",color:"#3c4043",width:"35px",top:"-6px",left:"0",right:"0",marginLeft:"auto",marginRight:"auto",fontFamily:"Roboto,Arial,sans-serif"},label2:{left:"10px",textAlign:"center",fontSize:"12px",background:"#fff",position:"absolute",textTransform:"uppercase",color:"#3c4043",width:"32px",top:"-6px",fontFamily:"Roboto,Arial,sans-serif"},single:{flexGrow:"1",margin:"0px 4.4px"}}}),f=n.r+", "+n.g+", "+n.b,c=Math.round(r.h)+"°, "+Math.round(r.s*100)+"%, "+Math.round(r.l*100)+"%",p=Math.round(l.h)+"°, "+Math.round(l.s*100)+"%, "+Math.round(l.v*100)+"%";return P.createElement("div",{style:d.wrap,className:"flexbox-fix"},P.createElement("div",{style:d.fields},P.createElement("div",{style:d.double},P.createElement(Be,{style:{input:d.input,label:d.label},label:"hex",value:o,onChange:i})),P.createElement("div",{style:d.column},P.createElement("div",{style:d.single},P.createElement(Be,{style:{input:d.input2,label:d.label2},label:"rgb",value:f,onChange:i})),P.createElement("div",{style:d.single},P.createElement(Be,{style:{input:d.input2,label:d.label2},label:"hsv",value:p,onChange:i})),P.createElement("div",{style:d.single},P.createElement(Be,{style:{input:d.input2,label:d.label2},label:"hsl",value:c,onChange:i})))))},ho=function(t){var a=t.width,n=t.onChange,r=t.rgb,o=t.hsl,l=t.hsv,i=t.hex,d=t.header,f=t.styles,c=f===void 0?{}:f,p=t.className,u=p===void 0?"":p,h=Ie(xt({default:{picker:{width:a,background:"#fff",border:"1px solid #dfe1e5",boxSizing:"initial",display:"flex",flexWrap:"wrap",borderRadius:"8px 8px 0px 0px"},head:{height:"57px",width:"100%",paddingTop:"16px",paddingBottom:"16px",paddingLeft:"16px",fontSize:"20px",boxSizing:"border-box",fontFamily:"Roboto-Regular,HelveticaNeue,Arial,sans-serif"},saturation:{width:"70%",padding:"0px",position:"relative",overflow:"hidden"},swatch:{width:"30%",height:"228px",padding:"0px",background:"rgba("+r.r+", "+r.g+", "+r.b+", 1)",position:"relative",overflow:"hidden"},body:{margin:"auto",width:"95%"},controls:{display:"flex",boxSizing:"border-box",height:"52px",paddingTop:"22px"},color:{width:"32px"},hue:{height:"8px",position:"relative",margin:"0px 16px 0px 16px",width:"100%"},Hue:{radius:"2px"}}},c));return P.createElement("div",{style:h.picker,className:"google-picker "+u},P.createElement("div",{style:h.head},d),P.createElement("div",{style:h.swatch}),P.createElement("div",{style:h.saturation},P.createElement(Nr,{hsl:o,hsv:l,pointer:po,onChange:n})),P.createElement("div",{style:h.body},P.createElement("div",{style:h.controls,className:"flexbox-fix"},P.createElement("div",{style:h.hue},P.createElement(da,{style:h.Hue,hsl:o,radius:"4px",pointer:fo,onChange:n}))),P.createElement(Uy,{rgb:r,hsl:o,hex:i,hsv:l,onChange:n})))};ho.propTypes={width:xe.oneOfType([xe.string,xe.number]),styles:xe.object,header:xe.string};ho.defaultProps={width:652,styles:{},header:"Color picker"};yt(ho);const Vy=({value:e,label:t="",uniqueId:a,onChange:n,isDisabled:r=!1,extraClass:o="",labelClass:l="",helperText:i="",hasPermission:d=!0,...f})=>{const[c,p]=b.useState(!1),u=b.useRef(null),h=r||!d,g=b.useCallback(N=>{h||n(N.hex)},[h,n]),v=b.useCallback(()=>{h||n("")},[h,n]),C=()=>{h||p(!c)};b.useEffect(()=>{const N=M=>{u.current&&!u.current.contains(M.target)&&p(!1)};return c&&document.addEventListener("mousedown",N),()=>{document.removeEventListener("mousedown",N)}},[c]);const j=b.useCallback(N=>{N.stopPropagation()},[]);return s.jsxs("div",{className:`d-flex flex-col ${o}`,children:[t?s.jsx("label",{className:`d-flex align-items-center gap-x-1 text-primary montserrat-medium ${l}`,children:t}):null,s.jsxs("div",{className:"position-relative d-flex align-items-center gap-2",ref:u,children:[s.jsx("div",{id:a,onClick:C,className:`h-31px cursor-pointer ${h?"cursor-disabled opacity-80":""}`,style:{backgroundColor:e,border:"1px solid var(--primary-border)",borderRadius:"4px",width:"70px"}}),s.jsx(vr,{extraClass:"i-p-0 d-flex-important align-items-center",children:s.jsxs("div",{onClick:v,className:`d-flex align-items-center gap-1 text-main hover-text-underline cursor-pointer ${h?"opacity-80":""}`,children:[s.jsx(yd,{})," Reset"]}),isButtonType:!1,tooltipTitle:"Reset background color",tooltipId:a}),c&&s.jsx("div",{className:"position-absolute",style:{left:0,top:"100%"},onClick:j,children:s.jsx("div",{className:"bg-main rounded",children:s.jsx(Oy,{color:e,onChange:g,...f})})})]}),i&&s.jsx(At,{message:i})]})},zy=b.memo(Vy),Ky=({template:e,onEdit:t,onDelete:a,isProcessing:n,handleApply:r})=>{const[o,l]=b.useState(!1),i=b.useRef(null),d=b.useMemo(()=>Ge(e.template_metadata,{values:{}}),[e.template_metadata]),{scale:f,printType:c,bgColor:p}=(d==null?void 0:d.values)??{},u=(v,C)=>{v.stopPropagation(),v.preventDefault(),C?t(e):a(v,e)},h=v=>{v.stopPropagation(),v.preventDefault(),r(e)},g=v=>{v.stopPropagation(),v.preventDefault(),l(C=>!C)};return b.useEffect(()=>{var v;i.current&&(o?((v=i.current)==null||v.scrollIntoView({behavior:"smooth",block:"nearest"}),i.current.style.height=`${i.current.scrollHeight+2}px`):i.current.style.height="0")},[o]),s.jsxs("li",{className:`d-flex w-full flex-col justify-between secondary-border-bottom ${o?"gap-1 pb-2":""}`,children:[s.jsxs("div",{className:"d-flex justify-between gap-2 action-container",children:[s.jsx("div",{className:"text-base w-full item-title cursor-pointer text-break-word",onClick:h,children:e.name}),s.jsxs("div",{className:"action-buttons d-flex h-fit gap-3",children:[s.jsx("button",{onClick:v=>{u(v,"edit")},disabled:n,children:s.jsx(Tn,{size:12})}),s.jsx("button",{onClick:u,disabled:n,style:{background:"var(--color-danger-light)"},children:s.jsx(sn,{size:12})}),s.jsx("button",{onClick:g,disabled:n,className:"i-text-main i-main-border bg-main",children:s.jsx("div",{className:"d-flex",style:o?{transform:"rotate(-90deg)",transition:"0.3s ease-in-out"}:{transition:"0.3s ease-in-out"},children:s.jsx(on,{size:12})})})]})]}),s.jsxs("div",{className:`d-flex flex-wrap gap-4 text-capitalize expandable-section ${o?"expanded":""}`,ref:i,children:[s.jsxs("div",{className:"d-flex gap-2",children:[s.jsx("span",{className:"font-semibold",children:"Scale:"})," ",f,"%"]}),c&&s.jsxs("div",{className:"d-flex gap-2",children:[s.jsx("span",{className:"font-semibold",children:"Print:"})," ",c]}),p&&s.jsxs("div",{className:"d-flex gap-2",children:[s.jsx("span",{className:"font-semibold",children:"Background Color:"})," ",s.jsx("div",{style:{backgroundColor:p},className:"mrdn-bg-color-container"})," ",s.jsx("span",{children:p})]})]})]})},qy=({templates:e,onEdit:t,onDelete:a,isProcessing:n=!1,handleApply:r})=>s.jsx("div",{className:"item-list-container position-relative",children:e.length?s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"d-flex justify-end text-main font-semibold",children:["Total Templates: ",e.length]}),s.jsx("ul",{className:"d-flex flex-col text-primary i-pb-4 i-pt-3",children:e.map(o=>{const l=`${o.id}_${o.name}`;return s.jsx(Ky,{template:o,onEdit:t,onDelete:a,handleApply:r,isProcessing:n},l)})})]}):s.jsx("div",{className:"no-content-text text-center",children:n?s.jsx(mn,{}):"No Templates Found"})}),Wy=b.memo(qy),ar={name:"",category:"",template_metadata:"",id:0},mo=P.memo(({setOpen:e,applyTemplate:t,showColorPicker:a,bgColorSelectors:n})=>{var k;const r=b.useMemo(()=>a?tu:nu,[a]),o=b.useMemo(()=>a?"mrdn-scale-bg-color-modal":"mrdn-scale-modal",[a]),l=b.useMemo(()=>a?"Scale & Background Color Templates":"Scale Templates",[a]),i=b.useMemo(()=>a?"mrdn-scale-bg-color-templates.json":"mrdn-scale-templates.json",[a]),d=gt({defaultValues:au,mode:"onChange",resolver:Lt(ia)}),{control:f,getValues:c,setValue:p,watch:u,formState:{errors:h},clearErrors:g}=d,{maxTemplateCount:v,hasUnlimitedAccess:C,importTemplateCount:j,exportTemplateCount:N}=Pt(L=>L),M=Tt(L=>L.setUpgradePlanModal),I=b.useRef(null),[F,S]=b.useState(et.ADD),[R,w]=b.useState([]),[m,T]=b.useState([]),[E,O]=b.useState(!0),[A,_]=b.useState(!1),[D,x]=b.useState(!1),[y,G]=b.useState({...ar}),[$,K]=b.useState({modalOpen:!1,template:null,processing:!1}),J=u("searchText"),Y=pn(J,500),ee=F===et.ADD,Z=b.useCallback(async()=>{const L=await Xt(r);L!=null&&L.templates&&w(L==null?void 0:L.templates),O(!1)},[]),q=b.useCallback((L,B)=>{if(!L)return T(B);T(B.filter(W=>W.name.toLowerCase().includes(L.toLowerCase())))},[]);b.useEffect(()=>{E||q(Y,R)},[Y,E,q,R]),b.useEffect(()=>{var W;Z();const L=(W=ye(".mat-slider"))==null?void 0:W.getAttribute("aria-valuenow");if(L&&p("scale",Math.floor(+L||100)),!a)return;const B=ml(n==null?void 0:n.color_btn);B.length&&p("bgColor",B[0])},[]);const Q=b.useCallback(L=>{S(L),jn(I,"#modal-scale-templates .mrdn-modal-body")},[]),oe=async L=>{if(!A){_(!0);try{const{templateTitle:B,scale:W,printType:z,bgColor:te}=c(),X={scale:W};a?te&&(X.bgColor=te):X.printType=z;const ae=B.trim(),me=y.id,ge={name:ae,category:r,template_metadata:{values:X}},he=me?await _n(me,ge):await Qt(ge);if(he!=null&&he.name){De("Template",me?"updated":"saved"),me&&G(_e=>({..._e,...ar})),await Z();return}if((he==null?void 0:he.status)===429){M({isVisible:!0,showTemplateText:!0,modalTitle:(he==null?void 0:he.title)||"Template Limit Reached",modalDescription:(he==null?void 0:he.description)||dn(v,"save","template")});return}await Z(),se(he==null?void 0:he.message)}catch{se("An error occurred while managing the template")}finally{_(!1)}}},de=b.useCallback(L=>{const B=Ge(L.template_metadata);G(W=>({...W,...L})),Object.entries(B.values).forEach(([W,z])=>p(W,z)),p("templateTitle",L.name),Q(et.ADD),g("selection")},[]),pe=b.useCallback(()=>{p("templateTitle",""),G(L=>({...L,...ar})),g("selection")},[]),Ce=L=>{if(D||A)return;x(!0);const B=Ge(L.template_metadata);t(B.values||[]),x(!1),e(!1)},re=b.useCallback((L,B)=>{$.processing||K(W=>({...W,modalOpen:!W.modalOpen,template:(B==null?void 0:B.id)||null}))},[$.processing]),U=async()=>{if(!$.processing){K(L=>({...L,processing:!0}));try{const{template:L}=$,B=await fn(L);if(B!=null&&B.success){De("Template","deleted"),await Z();return}await Z(),se(B==null?void 0:B.message)}catch{se("An error occurred while deleting the template")}finally{$.template===y.id&&G(L=>({...L,...ar})),K(L=>({...L,modalOpen:!1,template:null,processing:!1}))}}},ne=b.useCallback(async L=>{const B=[],W=new Set;for(const{name:te,template_metadata:X,category:ae}of L){if(ae!==r)continue;const me=te==null?void 0:te.trim(),ge=(X==null?void 0:X.values)||[];if(!me||W.has(te.trim())||!(ge!=null&&ge.scale))continue;const he={scale:rs(+(ge==null?void 0:ge.scale)||0,100)};a?ge!=null&&ge.bgColor&&(he.bgColor=ge.bgColor):he.printType=Ta.some(_e=>_e.value===(ge==null?void 0:ge.printType))?ge.printType:Ta[0].value,W.add(me),B.push({name:me,category:ae,template_metadata:{values:he}})}if(!B.length){De("Import complete. No valid templates were available to import.");return}const z=await hn(B);if(z!=null&&z.success){De((z==null?void 0:z.message)||ln((z==null?void 0:z.count)||0)),await Z();return}if((z==null?void 0:z.status)===429){const te=(z==null?void 0:z.description)||cn(z==null?void 0:z.count,j);M({isVisible:!0,showTemplateText:!0,modalTitle:(z==null?void 0:z.title)||"Template Limit Reached",modalDescription:te}),(+te.split(" ")[0]||0)&&await Z();return}se(z==null?void 0:z.message)},[j,r]),H=b.useCallback(L=>{d.setValue("scale",+wt(L.target.value))},[d]),V=b.useCallback(L=>{L.stopPropagation(),e(!1)},[e]),ce=b.useMemo(()=>s.jsx("div",{className:"w-full position-relative",children:s.jsx(ke,{name:"searchText",control:f,render:({field:{onChange:L,value:B}})=>s.jsx(qe,{onChange:L,value:B,uniqueId:"scales-search_input",labelClass:"flex !font-medium",inputClassName:"merch-input",placeHolder:"Search by title",isDisabled:!1,allowClear:{clearIcon:s.jsx($t,{size:16})},addonBefore:s.jsx("div",{className:"d-flex h-full pl-2 align-items-center product-search-box text-primary",children:s.jsx(Gt,{})}),hasPermission:!0})})}),[f]);return s.jsxs(s.Fragment,{children:[s.jsx(Je,{open:!0,children:s.jsx(pt,{open:!0,width:"50vw",handleClose:V,uniqueId:"product-scale-templates",modalTitle:l,extraClass:o,modalTitleChildren:C?null:s.jsx(un,{showTemplateLimit:!0,templateLimit:v,buttonId:"scale-templates-upgrade-button"}),bodySection:s.jsxs("div",{className:"d-flex flex-col gap-y-3 h-full",children:[s.jsxs("div",{className:"d-flex justify-between",children:[s.jsx(Wt,{tabOptions:Sn,activeTab:F,handleActiveTab:Q}),ee?s.jsx(gn,{collectionKey:r,fileName:i,processTemplateImport:ne,extraClass:"w-full justify-end align-items-center",templateLimit:N}):ce]}),ee?s.jsx(bt,{...d,children:s.jsxs("form",{onSubmit:d.handleSubmit(oe),className:"d-flex flex-col gap-y-3 h-full",id:"scale-template-form",children:[s.jsx("div",{className:"w-full d-flex flex-col gap-y-4",children:s.jsx(ke,{control:f,name:"templateTitle",render:({field:{onChange:L,value:B}})=>{var W;return s.jsx(qe,{uniqueId:"scale-template-title-input",type:"text",placeholder:"Enter template name",value:B,onChange:L,label:"Add New Template*",extraClass:"w-full",helperText:(W=h==null?void 0:h.templateTitle)==null?void 0:W.message})}})}),s.jsxs("div",{className:"merch-dominator-selection-container highlighted-selection-container",id:"mrdn-scales-selection-container",children:[s.jsx(At,{message:(k=h==null?void 0:h.selection)==null?void 0:k.message}),s.jsxs("div",{className:"d-flex gap-2",children:[s.jsx("div",{className:"d-flex",children:s.jsx(ke,{control:f,name:"scale",render:({field:{value:L}})=>s.jsx(nn,{onChange:H,value:L,label:"Scale",uniqueId:"mrdn-scale_input",placeHolder:"Enter Scale",inputClassName:"merch-input h-31px mt-auto",labelClass:"flex !font-medium",hasPermission:!0,allowDecimal:!1,min:1,max:100})})}),a?s.jsx(ke,{control:f,name:"bgColor",render:({field:{onChange:L,value:B}})=>s.jsx(zy,{value:B,onChange:L,uniqueId:"mrdn-color-picker",label:s.jsxs(s.Fragment,{children:["Background Color",s.jsx(vr,{extraClass:"i-p-0 d-flex-important align-items-center",children:s.jsx(ws,{size:14}),isButtonType:!1,tooltipChildren:s.jsx("div",{className:"d-flex flex-col",children:s.jsxs("ul",{style:{color:"var(--color-white) !important",marginBlock:"0.25rem"},children:[s.jsxs("li",{className:"text-left",children:["Background color selection is only supported for"," ",s.jsx("strong",{children:"Popsocket"}),","," ",s.jsx("strong",{children:"Phone Case"}),","," ",s.jsx("strong",{children:"Tote Bag"}),", and"," ",s.jsx("strong",{children:"Throw Pillow"})," ","categories."]}),s.jsxs("li",{className:"text-left",children:["Templates with a background color option can be applied to any category, but the"," ",s.jsx("strong",{children:"background color"})," ","will only be applied if the selected category supports color selection."]})]})}),tooltipId:"bg-color-tooltip"})]}),isDisabled:!1,hasPermission:!0,disableAlpha:!0,extraClass:"i-ml-1"})}):s.jsx(ke,{control:f,name:"printType",render:({field:{onChange:L,value:B}})=>s.jsx(Sl,{label:"Print",name:"printType",value:B,onChange:L,options:Ta,extraClass:"gap-0 print-radio-group"})})]})]}),s.jsxs("div",{className:"d-flex w-full justify-end gap-x-2 pr-2 template-actions mt-auto",children:[s.jsx(fe,{buttonContent:"Save",isLoading:A,isDisabled:E||A,onclickHandler:()=>{},extraClass:"color-white border-none",buttonProps:{type:"submit"},tooltipId:"scale-types-selection"}),y.name&&s.jsx(fe,{buttonContent:"Cancel",onclickHandler:pe,isDisabled:A,tooltipId:"cancel-scale-selection",extraClass:"outlined-btn",buttonProps:{type:"button"}})]})]})}):s.jsx(Wy,{templates:m,onEdit:de,onDelete:re,isProcessing:E||A,handleApply:Ce})]})})}),s.jsx(Je,{open:$.modalOpen,children:s.jsx(Yt,{deleteModalState:$,handleModalVisibility:re,handleDeleteAction:U,modalId:"delete-scale-selection-template",deleteHeaderTitle:"Confirm template deletion",confirmationMessage:"Are you sure you want to delete this template?"})})]})});mo.displayName="ScaleModal";const Gy=({selectors:e})=>{const{canvas:t,back_btn:a,front_btn:n,globalUploader:r,delete_btn:o,assetContainer:l,pending_uploading_container:i,single_product_uploading_container:d,design_view_prefix:f,center_horizontal_btn:c,center_vertical_btn:p,bgColorSelectors:u}=e,[h,g]=b.useState(!1),[v,C]=b.useState(!1),{scale:j,updateScale:N}=En(x=>x),[M,I]=b.useState(!1),[F,S]=b.useState(!1),[R,w]=b.useState(!1),[m,T]=b.useState(!1);b.useEffect(()=>{const x=ye(e.dropzone_container);if(!x)return;let y=null,G=null,$=!1;function K(){const Y=ye(".mat-slider");Y&&(G=Y.getAttribute("aria-valuenow"),y&&y.disconnect(),y=new MutationObserver(()=>{const ee=Y.getAttribute("aria-valuenow");ee!==G&&$&&(G=ee,Cl(c,p))}),y.observe(Y,{attributes:!0,attributeFilter:["aria-valuenow"]}),$=!0)}const J=new MutationObserver(()=>{if(ye(e.tool_container)){const ee=Mt(e.assetContainer);C(!0),ja[ee]?S(!0):S(!1),K()}else C(!1),S(!1),y&&(y.disconnect(),y=null),$=!1});if(J.observe(x,{childList:!0,subtree:!1}),ye(e.tool_container)){const Y=Mt(e.assetContainer);C(!0),ja[Y]?S(!0):S(!1),K()}return()=>{J==null||J.disconnect(),y&&y.disconnect()}},[]);const E=async(x="back")=>{var y;try{if(!!ye(i))throw se("Please wait for the design to finish uploading."),new Error("Image is still uploading.");if(M)return;I(!0);const $=ye(r==null?void 0:r.uploaded_img);if(!$)throw Et("Image element not found."),Kt(r==null?void 0:r.uploader),se("Please upload a design to transfer."),new Error("Image element not found.");const K=(y=document.querySelector(a))==null?void 0:y.classList.contains("active"),J=ye(o);if(K&&!J)throw se("Please switch to the front view to transfer the design."),new Error("Please switch to the front view to transfer the design.");if(!K&&!J)throw se("Please switch to the back view to transfer the design."),new Error("Please switch to the back view to transfer the design.");J&&(J.click(),await Me(500));const Y=$.src;if(!(Y!=null&&Y.startsWith("data:image/")))throw Et("Invalid image source."),new Error("Invalid image source.");let ee=vl($.src);if(!ee)throw Et("Blob not created. Cannot proceed with upload."),se("Blob not created. Cannot proceed with upload."),new Error("Blob not created. Cannot proceed with upload.");const Z=x===tt.front,q=Mt(l),Q=ye(Z?n:a);Q==null||Q.click(),await Ze(`label[for='${q}-${x}-wizzy']`);const oe=ye(o);oe&&(oe.click(),await Ze(`label[for='${q}-${x}-wizzy']`));const de=ye(`label[for='${q}-${x}-wizzy']`);if(!de)throw Et("Upload label element not found. Cannot proceed with upload."),se("Upload label element not found. Cannot proceed with upload."),new Error("Upload label element not found.");const pe=de.getAttribute("for");if(!pe)throw Et("Input ID not found. Cannot proceed with upload."),se("Input ID not found. Cannot proceed with upload."),new Error("Input not found. Cannot proceed with upload.");const Ce=document.getElementById(pe);if(!Ce)throw Et("Input element not found. Cannot proceed with upload."),se("Input element not found. Cannot proceed with upload."),new Error("Input element not found.");Ls(Ce,ee),ee=null,Me(500),await mt(d,1e3)}catch{}finally{I(!1)}},O=async x=>{var y;if(R){se("A template is already being applied. Please wait.");return}w(!0),I(!0);try{N(x.scale);const G=Mt(e.assetContainer);if(!ja[G]){_a(G)&&x.bgColor&&await xl(u.color_btn,u.color_input,x.bgColor),await Ht(void 0,i,x.scale,!1);return}const $=tt[x.printType],J=((y=document.querySelector(a))==null?void 0:y.classList.contains("active"))?tt.back:tt.front,Y=$===tt.front?tt.back:tt.front,ee=async Q=>{const oe=ye(`${f}${Q}`);oe&&(oe.click(),Me(500),await mt(d,1e3))},Z=()=>!!ye(o),q=async Q=>(await ee(Q),Z());if(J===$&&Z()){await Ht(void 0,i,x.scale,!1);return}if(await q(Y)){await E($),await Ze(t,500,6e4),await Me(1e3),J!==$&&await ee($),await Ht(void 0,i,x.scale,!1);return}if(await q($)){await Ht(void 0,i,x.scale,!1);return}se("No design found to transfer or scale.")}catch{se("Failed to apply template. Please try again.")}finally{g(!1),I(!1),w(!1)}},A=async()=>{var y;const x=(y=document.querySelector(a))==null?void 0:y.classList.contains("active");try{await E(x?tt.front:tt.back)}catch{}finally{I(!1)}},_=b.useCallback(x=>{N(+wt(x.target.value))},[]),D=b.useCallback(()=>{const x=Mt(e.assetContainer);_a(x)?T(!0):T(!1),g(!0)},[]);return s.jsxs(s.Fragment,{children:[v&&s.jsxs("div",{className:"d-flex gap-2 justify-center text-14px mb-2",children:[s.jsx("div",{className:"d-flex",children:s.jsx(nn,{onChange:_,value:j,uniqueId:"scale_input",placeHolder:"Enter Scale",inputClassName:"merch-input h-31px mt-auto",labelClass:"flex !font-medium",hasPermission:!0,isDisabled:M,allowDecimal:!1,min:1,max:100})}),s.jsx(fe,{buttonContent:"Scale",onclickHandler:x=>Ht(x,i,j),tooltipId:"custom-scale-action",isDisabled:M}),s.jsx(fe,{buttonContent:"100%",onclickHandler:x=>Ht(x,i),tooltipId:"full-scale-action",isDisabled:M})]}),s.jsxs("div",{className:"d-flex gap-2 justify-center text-14px",children:[F&&s.jsx(fe,{buttonContent:"Transfer",onclickHandler:A,tooltipId:"product-design-transfer-action",tooltipTitle:"Design will be transferred to the opposite side of the product (Front ↔ Back) based on your current view",isDisabled:M}),v&&s.jsx(fe,{buttonContent:"Templates",onclickHandler:D,tooltipId:"product-scale-templates",isDisabled:M||R})]}),h&&s.jsx(mo,{setOpen:g,applyTemplate:O,showColorPicker:m,bgColorSelectors:u})]})};function Yy(e){return el({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"g",attr:{id:"Grid_4-2"},child:[{tag:"g",attr:{},child:[{tag:"g",attr:{},child:[{tag:"path",attr:{d:"M8.5,13.933H5.563a2.5,2.5,0,0,1-2.5-2.5V5.563a2.5,2.5,0,0,1,2.5-2.5H8.5a2.5,2.5,0,0,1,2.5,2.5v5.87A2.5,2.5,0,0,1,8.5,13.933ZM5.563,4.063a1.5,1.5,0,0,0-1.5,1.5v5.87a1.5,1.5,0,0,0,1.5,1.5H8.5a1.5,1.5,0,0,0,1.5-1.5V5.563a1.5,1.5,0,0,0-1.5-1.5Z"},child:[]},{tag:"path",attr:{d:"M8.5,20.935H5.564a2.5,2.5,0,0,1,0-5H8.5a2.5,2.5,0,1,1,0,5Zm-2.934-4a1.5,1.5,0,0,0,0,3H8.5a1.5,1.5,0,1,0,0-3Z"},child:[]}]},{tag:"g",attr:{},child:[{tag:"path",attr:{d:"M18.436,20.935H15.5a2.5,2.5,0,0,1-2.5-2.5v-5.87a2.5,2.5,0,0,1,2.5-2.5h2.934a2.5,2.5,0,0,1,2.5,2.5v5.87A2.5,2.5,0,0,1,18.436,20.935ZM15.5,11.065a1.5,1.5,0,0,0-1.5,1.5v5.87a1.5,1.5,0,0,0,1.5,1.5h2.934a1.5,1.5,0,0,0,1.5-1.5v-5.87a1.5,1.5,0,0,0-1.5-1.5Z"},child:[]},{tag:"path",attr:{d:"M18.436,8.063H15.5a2.5,2.5,0,0,1,0-5h2.934a2.5,2.5,0,0,1,0,5Zm-2.934-4a1.5,1.5,0,0,0,0,3h2.934a1.5,1.5,0,0,0,0-3Z"},child:[]}]}]}]}]})(e)}function sc(e){return el({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"g",attr:{id:"Image_On"},child:[{tag:"g",attr:{},child:[{tag:"path",attr:{d:"M18.435,3.06H5.565a2.5,2.5,0,0,0-2.5,2.5V18.44a2.507,2.507,0,0,0,2.5,2.5h12.87a2.507,2.507,0,0,0,2.5-2.5V5.56A2.5,2.5,0,0,0,18.435,3.06ZM4.065,5.56a1.5,1.5,0,0,1,1.5-1.5h12.87a1.5,1.5,0,0,1,1.5,1.5v8.66l-3.88-3.88a1.509,1.509,0,0,0-2.12,0l-4.56,4.57a.513.513,0,0,1-.71,0l-.56-.56a1.522,1.522,0,0,0-2.12,0l-1.92,1.92Zm15.87,12.88a1.5,1.5,0,0,1-1.5,1.5H5.565a1.5,1.5,0,0,1-1.5-1.5v-.75L6.7,15.06a.5.5,0,0,1,.35-.14.524.524,0,0,1,.36.14l.55.56a1.509,1.509,0,0,0,2.12,0l4.57-4.57a.5.5,0,0,1,.71,0l4.58,4.58Z"},child:[]},{tag:"path",attr:{d:"M8.062,10.565a2.5,2.5,0,1,1,2.5-2.5A2.5,2.5,0,0,1,8.062,10.565Zm0-4a1.5,1.5,0,1,0,1.5,1.5A1.5,1.5,0,0,0,8.062,6.565Z"},child:[]}]}]}]})(e)}const tn={container:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"20px",border:"2px dashed var(--text-main-color)",borderRadius:"10px",backgroundColor:"#ffebf5",transition:"border-color 0.2s, background-color 0.2s",cursor:"pointer"},uploadContainer:{display:"flex",flexDirection:"column",alignItems:"center",gap:"10px",textAlign:"center"},uploadLabel:{padding:"15px",background:"var(--primary-bg-color)",color:"#fff",borderRadius:"8px",cursor:"pointer",fontWeight:"bold",width:"180px",margin:0},fileInput:{display:"none"},previewContainer:{display:"flex",flexDirection:"column",alignItems:"center",gap:"10px"},imageWrapper:{position:"relative",width:"180px",height:"130px",borderRadius:"10px",overflow:"hidden",border:"2px solid var(--secondary-border)",display:"flex",alignItems:"center",justifyContent:"center"},imagePreview:{width:"100%",height:"100%",objectFit:"contain"},overlay:{position:"absolute",bottom:"8px",right:"8px",width:"40px",height:"40px",display:"flex",alignItems:"center",justifyContent:"center",opacity:"0.9",borderRadius:"20px",border:"none"},tickIcon:{width:"50px",height:"50px",borderRadius:"25px",fontSize:"30px",fontWeight:"bold"}},Vi=({uploadedImage:e,setUploadedImage:t,label:a,error:n})=>{const[r,o]=b.useState(!1),l=b.useCallback(p=>{var h;let u;if(p instanceof File?u=p:u=(h=p.target.files)==null?void 0:h[0],u){const g=new FileReader;g.onload=()=>{if(g.result){const v=new Image;v.src=g.result;let C=null;v.onload=()=>{(v.width<485||v.height<485)&&(C="Image dimensions must be at least 485x485 pixels."),t(g.result,C)}}},g.readAsDataURL(u)}},[t]),i=b.useCallback(()=>{t(null)},[t]),d=b.useCallback(p=>{p.preventDefault(),o(!0)},[]),f=b.useCallback(p=>{p.preventDefault(),o(!1)},[]),c=b.useCallback(p=>{p.preventDefault(),o(!1);const u=Array.from(p.dataTransfer.files);if(!u.length)return;const h=u.filter(v=>v.type==="image/png");if(!h.length){tl.error("Only PNG images are allowed!");return}const g={target:{files:[h[0]]}};l(g)},[l]);return s.jsxs("div",{className:"d-flex flex-col w-full mrdn-image-uploader",children:[a&&s.jsx("div",{className:"mb-1",children:a}),s.jsx("div",{style:tn.container,className:`mrdn-drop-zone-container${r?" drag-active":""}`,onDragOver:d,onDragLeave:f,onDrop:c,children:e?s.jsx("div",{style:tn.previewContainer,children:s.jsxs("div",{style:tn.imageWrapper,children:[s.jsx("img",{src:e,alt:"Uploaded preview",style:tn.imagePreview}),s.jsx("button",{style:tn.overlay,className:"badge-red cursor-pointer",onClick:i,children:s.jsx(Mc,{size:20})})]})}):s.jsx("div",{style:tn.uploadContainer,children:s.jsx("label",{style:tn.uploadLabel,children:s.jsxs("div",{className:"d-flex flex-col align-items-center",children:[s.jsx(sc,{size:40}),s.jsx("span",{className:"i-mt-0",children:"Choose File"}),s.jsx("div",{style:{fontSize:"12px",color:"#c9ced2"},children:"or drag and drop (PNG only)"}),s.jsx("input",{type:"file",accept:"image/*",onChange:l,style:tn.fileInput})]})})})}),s.jsx(At,{message:n||""})]})},Xy=({template:e,onEdit:t,onDelete:a,isProcessing:n,handleApply:r})=>{const[o,l]=b.useState(!1),i=b.useRef(null),d=b.useMemo(()=>Ge(e.template_metadata,{values:{}}),[e.template_metadata]),{downloadEnabled:f,isPattern:c,scale:p,tumblerTab:u}=(d==null?void 0:d.values)??{},h=(C,j)=>{C.stopPropagation(),C.preventDefault(),j?t(e):a(C,e)},g=C=>{C.stopPropagation(),C.preventDefault(),r(e)},v=C=>{C.stopPropagation(),C.preventDefault(),l(j=>!j)};return b.useEffect(()=>{var C;i.current&&(o?((C=i.current)==null||C.scrollIntoView({behavior:"smooth",block:"nearest"}),i.current.style.height=`${i.current.scrollHeight+2}px`):i.current.style.height="0")},[o]),s.jsxs("li",{className:`d-flex w-full flex-col justify-between secondary-border-bottom ${o?"gap-1 pb-2":""}`,children:[s.jsxs("div",{className:"d-flex justify-between gap-2 action-container",children:[s.jsx("div",{className:"text-base w-full item-title cursor-pointer text-break-word",onClick:g,children:e.name}),s.jsxs("div",{className:"action-buttons d-flex h-fit gap-3",children:[s.jsx("button",{onClick:C=>{h(C,"edit")},disabled:n,children:s.jsx(Tn,{size:12})}),s.jsx("button",{onClick:h,disabled:n,style:{background:"var(--color-danger-light)"},children:s.jsx(sn,{size:12})}),s.jsx("button",{onClick:v,disabled:n,className:"i-text-main i-main-border bg-main",children:s.jsx("div",{className:"d-flex",style:o?{transform:"rotate(-90deg)",transition:"0.3s ease-in-out"}:{transition:"0.3s ease-in-out"},children:s.jsx(on,{size:12})})})]})]}),s.jsxs("div",{className:`d-flex flex-wrap gap-4 text-capitalize expandable-section ${o?"expanded":""}`,ref:i,children:[s.jsxs("div",{className:"d-flex gap-2",children:[s.jsx("span",{className:"font-semibold",children:"Selected Side:"})," ",Bt[u]]}),c?s.jsxs("div",{className:"d-flex gap-2",children:[s.jsx("span",{className:"font-semibold",children:"Pattern Mode Enabled:"})," Yes"]}):s.jsxs("div",{className:"d-flex gap-2",children:[s.jsx("span",{className:"font-semibold",children:"Scale:"})," ",p,"%"]}),s.jsxs("div",{className:"d-flex gap-2",children:[s.jsx("span",{className:"font-semibold",children:"Download after Upload:"})," ",f?"Yes":"No"]})]})]})},Qy=({templates:e,onEdit:t,onDelete:a,isProcessing:n=!1,handleApply:r})=>s.jsx("div",{className:"item-list-container position-relative",children:e.length?s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"d-flex justify-end text-main font-semibold",children:["Total Templates: ",e.length]}),s.jsx("ul",{className:"d-flex flex-col text-primary i-pb-4 i-pt-3",children:e.map(o=>{const l=`${o.name}-${o.id}`;return s.jsx(Xy,{template:o,onEdit:t,onDelete:a,handleApply:r,isProcessing:n},l)})})]}):s.jsx("div",{className:"no-content-text text-center",children:n?s.jsx(mn,{}):"No Templates Found"})}),Zy=b.memo(Qy),rr={name:"",category:"",template_metadata:"",id:0},Jy=({setOpen:e,applyTemplate:t,initFormState:a=!0})=>{const n=gt({defaultValues:ru,mode:"onChange",resolver:Lt(ia)}),{tumblerScale:r,tumblerTab:o,isPattern:l}=En(H=>H),{control:i,getValues:d,setValue:f,watch:c,formState:{errors:p}}=n,u=b.useRef(null),[h,g]=b.useState(et.ADD),[v,C]=b.useState([]),[j,N]=b.useState([]),[M,I]=b.useState(!0),[F,S]=b.useState(!1),[R,w]=b.useState(!1),[m,T]=b.useState({...rr}),[E,O]=b.useState({modalOpen:!1,template:null,processing:!1}),A=c("searchText"),_=pn(A,500),D=h===et.ADD,x=c("tumblerTab"),y=c("isPattern"),{maxTemplateCount:G,hasUnlimitedAccess:$,importTemplateCount:K,exportTemplateCount:J}=Pt(H=>H),Y=Tt(H=>H.setUpgradePlanModal),ee=b.useCallback(async()=>{const H=await Xt(Ka);H!=null&&H.templates&&C(H==null?void 0:H.templates),I(!1)},[]);b.useEffect(()=>{a&&(f("scale",r),f("tumblerTab",o),f("isPattern",l)),ee()},[]),b.useEffect(()=>{M||N(_?v.filter(H=>H.name.toLowerCase().includes(_.toLowerCase())):v)},[_,v,M]);const Z=H=>{g(H),jn(u,"#modal-tumbler-templates .mrdn-modal-body")},q=async()=>{if(!F){S(!0);try{const{templateTitle:H,searchText:V,...ce}=d(),k=H.trim(),L=m.id;ce.tumblerTab!="duplicate"&&(ce.isPattern=!1);const B={name:k,category:Ka,template_metadata:{values:ce}},W=L?await _n(L,B):await Qt(B);if(W!=null&&W.name){De("Template",L?"updated":"saved"),L&&T(z=>({...z,...rr})),await ee();return}if((W==null?void 0:W.status)===429){Y({isVisible:!0,showTemplateText:!0,modalTitle:(W==null?void 0:W.title)||"Template Limit Reached",modalDescription:(W==null?void 0:W.description)||dn(G,"save","template")});return}await ee(),se(W==null?void 0:W.message)}catch{se("An error occurred while managing the template")}finally{S(!1)}}},Q=H=>{const V=Ge(H.template_metadata);T(ce=>({...ce,...H})),Object.entries(V.values).forEach(([ce,k])=>f(ce,k)),f("templateTitle",H.name),Z(et.ADD)},oe=b.useCallback(()=>{n.setValue("templateTitle",""),T(H=>({...H,...rr}))},[]),de=H=>{if(R||F)return;w(!0);const V=Ge(H.template_metadata);t(V.values||[]),w(!1),e(!1)},pe=b.useCallback((H,V)=>{E.processing||O(ce=>({...ce,modalOpen:!ce.modalOpen,template:(V==null?void 0:V.id)||null}))},[E.processing]),Ce=async()=>{if(!E.processing){O(H=>({...H,processing:!0}));try{const{template:H}=E,V=await fn(H);if(V!=null&&V.success){De("Template","deleted"),await ee();return}await ee(),se(V==null?void 0:V.message)}catch{se("An error occurred while deleting the template")}finally{E.template===m.id&&T(H=>({...H,...rr})),O(H=>({...H,modalOpen:!1,template:null,processing:!1}))}}},re=b.useCallback(async H=>{const V=[],ce=new Set;for(const{name:L,template_metadata:B,category:W}of H){if(W!==Ka)continue;const z=(B==null?void 0:B.values)||[],te=L==null?void 0:L.trim();if(!te||ce.has(te)||!(z!=null&&z.scale))continue;const X={scale:+wt(+(z==null?void 0:z.scale)),tumblerTab:wn.some(ae=>ae.value===(z==null?void 0:z.tumblerTab))?z.tumblerTab:wn[0].value,isPattern:!!(z!=null&&z.isPattern),downloadEnabled:!!(z!=null&&z.downloadEnabled)};ce.add(te),V.push({name:te,category:W,template_metadata:{values:X}})}if(!V.length){De("Import complete. No valid templates were available to import.");return}const k=await hn(V);if(k!=null&&k.success){De((k==null?void 0:k.message)||ln((k==null?void 0:k.count)||0)),await ee();return}if((k==null?void 0:k.status)===429){const L=(k==null?void 0:k.description)||cn(k==null?void 0:k.count,K);Y({isVisible:!0,showTemplateText:!0,modalTitle:(k==null?void 0:k.title)||"Template Limit Reached",modalDescription:L}),(+L.split(" ")[0]||0)&&await ee();return}se(k==null?void 0:k.message)},[K]),U=b.useCallback(H=>{n.setValue("scale",+wt(H.target.value)),n.setValue("isPattern",!1)},[]),ne=H=>n.setValue("tumblerTab",H);return s.jsxs(s.Fragment,{children:[s.jsx(Je,{open:!0,children:s.jsx(pt,{open:!0,width:"50vw",handleClose:H=>{H.stopPropagation(),e(!1)},uniqueId:"tumbler-templates",modalTitle:"Tumbler Templates",modalTitleChildren:$?null:s.jsx(un,{showTemplateLimit:!0,templateLimit:G,buttonId:"tumbler-templates-upgrade-button"}),bodySection:s.jsxs("div",{className:"d-flex flex-col gap-y-3 h-full",children:[s.jsxs("div",{className:"d-flex justify-between",children:[s.jsx(Wt,{tabOptions:Sn,activeTab:h,handleActiveTab:Z}),D?s.jsx(gn,{collectionKey:Ka,fileName:"merch-dominator-tumbler-templates.json",processTemplateImport:re,extraClass:"w-full justify-end align-items-center",templateLimit:J}):s.jsx("div",{className:"w-full position-relative",children:s.jsx(ke,{name:"searchText",control:i,render:({field:{onChange:H,value:V}})=>s.jsx(qe,{onChange:H,value:V,uniqueId:"tumbler-search_input",labelClass:"flex !font-medium",inputClassName:"merch-input",placeHolder:"Search by title",isDisabled:!1,allowClear:{clearIcon:s.jsx($t,{size:16})},addonBefore:s.jsx("div",{className:"d-flex h-full pl-2 align-items-center product-search-box text-primary",children:s.jsx(Gt,{})}),hasPermission:!0})})})]}),D?s.jsx(bt,{...n,children:s.jsxs("form",{onSubmit:n.handleSubmit(q),className:"d-flex flex-col gap-y-3 h-full",id:"tumbler-template-form",children:[s.jsx("div",{className:"w-full d-flex flex-col gap-y-4",children:s.jsx(ke,{control:i,name:"templateTitle",render:({field:{onChange:H,value:V}})=>{var ce;return s.jsx(qe,{uniqueId:"tumbler-template-title-input",type:"text",placeholder:"Enter template name",value:V,onChange:H,label:"Add New Template*",extraClass:"w-full",helperText:(ce=p==null?void 0:p.templateTitle)==null?void 0:ce.message})}})}),s.jsxs("div",{className:"merch-dominator-selection-container  d-flex flex-col",id:"mrdn-tumbler-scale-container",children:[s.jsx("div",{className:"mb-2",children:"Edit and Re-Upload"}),s.jsx(Wt,{tabOptions:wn,activeTab:x,handleActiveTab:ne}),s.jsx("div",{className:"d-flex i-mt-2",children:s.jsx(ke,{control:i,name:"scale",render:({field:{value:H}})=>s.jsxs("div",{className:"d-flex gap-4 scale-actions",children:[[100,85,75].map(V=>s.jsx(fe,{buttonContent:`${V}%`,onclickHandler:()=>{f("scale",V),f("isPattern",!1)},tooltipId:`${V}-percent-scale-selection`,extraClass:"outlined-btn",buttonProps:{type:"button"}},V)),s.jsx("div",{className:"d-flex",children:s.jsx(nn,{onChange:U,value:H,uniqueId:"mrdn-tumbler-scale_input",placeHolder:"Enter Scale",inputClassName:"merch-input h-31px mt-auto",labelClass:"flex !font-medium",hasPermission:!0,allowDecimal:!1,min:1,max:100})}),x==="duplicate"&&s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"d-flex align-items-center",children:"OR"}),s.jsx(fe,{buttonContent:"Pattern",onclickHandler:()=>f("isPattern",!d("isPattern")),tooltipId:"pattern-selection",extraClass:y?"":"outlined-btn",buttonProps:{type:"button"}})]})]})})}),s.jsx(ke,{name:"downloadEnabled",control:i,render:({field:{onChange:H,value:V}})=>s.jsx(wr,{name:"downloadEnabled",checked:V,onChangeHandler:H,switchTitle:"Download After Upload",extraClass:"small-switch i-mt-2",hasPermission:!0,id:"download-enabled-selection"})})]}),s.jsxs("div",{className:"d-flex w-full justify-end gap-x-2 pr-2 template-actions mt-auto",children:[s.jsx(fe,{buttonContent:"Save",isLoading:F,isDisabled:M||F,onclickHandler:()=>{},extraClass:"color-white border-none",buttonProps:{type:"submit"},tooltipId:"save-tumbler-selection"}),m.name&&s.jsx(fe,{buttonContent:"Cancel",onclickHandler:oe,isDisabled:F,tooltipId:"cancel-tumbler-selection",extraClass:"outlined-btn",buttonProps:{type:"button"}})]})]})}):s.jsx(Zy,{templates:j,onEdit:Q,onDelete:pe,isProcessing:M||F,handleApply:de})]})})}),s.jsx(Je,{open:E.modalOpen,children:s.jsx(Yt,{deleteModalState:E,handleModalVisibility:pe,handleDeleteAction:Ce,modalId:"delete-tumbler-selection-template",deleteHeaderTitle:"Confirm template deletion",confirmationMessage:"Are you sure you want to delete this template?"})})]})},ev=({selectors:e})=>{const{globalUploader:t,productEditorContainer:a,delete_btn:n,pending_uploading_container:r,center_horizontal_btn:o,center_vertical_btn:l,single_product_uploading_container:i,tumbler_slider:d,tumbler_uploader:f,tumbler_dropzone_container:c,helpers_tool_container:p,tumbler_canvas:u}=e,{tumblerScale:h,tumblerImages:g,errors:v,scale:C,tumblerTab:j,isPattern:N,updateTumblerScale:M,updateTumblerImage:I,updateErrors:F,updateScale:S,updateTumblerTab:R,updateIsPattern:w}=En(X=>X),m=b.useRef(!1),[T,E]=b.useState(!1),[O,A]=b.useState(!1),[_,D]=b.useState(null),[x,y]=b.useState(!1),[G,$]=b.useState(!1),[K,J]=b.useState(!1),[Y,ee]=b.useState(!1),[Z,q]=b.useState(!1),[Q,oe]=b.useState(!1),de=g.front&&g.back,pe=Y||T||O||m.current,Ce=async()=>{const X=ye(t==null?void 0:t.uploaded_img);if(X&&X.src)try{const ae=await fetch(X.src);if(ae.ok){const me=await ae.blob(),ge=new FileReader;ge.onload=()=>{I("front",ge.result)},ge.readAsDataURL(me)}}catch{}};b.useEffect(()=>{g.front||Ce()},[]);const re=X=>m.current?(se(`Please wait for the current operation to complete before ${X}.`),!1):ye(r)?(se("Please wait for your design to finish uploading."),!1):ye(i)?(se("Please wait for your design to finish uploading."),!1):!0,U=b.useCallback(X=>(ae,me)=>{I(X,ae),F(X,me||null)},[]);b.useEffect(()=>{const X=ye(c);if(!X)return;Ze(a,500,5e3).then(Oe=>{var ie;if(!Oe)return;const{targetContainer:Ne,wrapperContainer:le}=Ot(Oe,p,{id:"tumbler-mrdn-scale-container",className:"mrdn-scale-container merch-dominator-style-container"});le&&((ie=Ne==null?void 0:Ne.parentNode)==null||ie.insertBefore(le,Ne),D(le))});let ae=null,me=null,ge=!1;function he(){const Oe=ye(d);Oe&&(me=Oe.getAttribute("aria-valuenow"),ae&&ae.disconnect(),ae=new MutationObserver(()=>{const Ne=Oe.getAttribute("aria-valuenow");Ne!==me&&ge&&(me=Ne,Cl(o,l))}),ae.observe(Oe,{attributes:!0,attributeFilter:["aria-valuenow"]}),ge=!0)}const _e=new MutationObserver(()=>{const Oe=ye(e.tool_container),Ne=ye(c);if(Oe&&Ne){if(y(!0),m.current){let le=100;const ie=ye(d);if(ie){let ue=Math.round(parseFloat(ie.getAttribute("aria-valuenow")||"0"));const be=ue<le?1:-1,Te=Math.abs(le-ue);for(let Le=0;Le<Te;Le++){const ze=new KeyboardEvent("keydown",{bubbles:!0,cancelable:!0,key:be>0?"ArrowRight":"ArrowLeft",code:be>0?"ArrowRight":"ArrowLeft",keyCode:be>0?39:37,which:be>0?39:37});ie.dispatchEvent(ze)}}}he()}else y(!1),ae&&(ae.disconnect(),ae=null),ge=!1});return _e.observe(X,{childList:!0,subtree:!1}),ye(e.tool_container)&&(y(!0),he()),()=>{_e==null||_e.disconnect(),ae&&ae.disconnect()}},[]);const ne=b.useCallback(X=>{w(!1),M(X)},[]),H=async(X=!1,ae)=>{try{if(!re("transferring design"))return;m.current=!0,A(!0);const me=g.front,ge=g.back;if(!me||!ge){Et("Image elements not found."),se("Please upload both a front and a back image to proceed with combining them.");return}await Rr(n,500);const[he,_e]=await Promise.all([new Promise((Qe,Ue)=>{const $e=new Image;$e.onload=()=>Qe($e),$e.onerror=Ue,$e.src=me}),new Promise((Qe,Ue)=>{const $e=new Image;$e.onload=()=>Qe($e),$e.onerror=Ue,$e.src=ge})]),Oe=await Ga(he),Ne=await Ga(_e),le=Ur(3e3,1400);if(!le)return;const{canvas:ie,ctx:ue}=le,be=1400,Te=31,Le=1566.6667,ze=(1400-be)/2,Ke=Number(ae)/100||1,He=Math.min(be/Oe.width,be/Oe.height)*Ke,we=Math.min(be/Ne.width,be/Ne.height)*Ke,ve=Oe.width*He,Ee=Oe.height*He,Pe=Ne.width*we,je=Ne.height*we,Fe=Te+(be-ve)/2,Xe=ze+(be-Ee)/2,Re=Le+(be-Pe)/2,Ae=ze+(be-je)/2;ue.drawImage(Oe,0,0,Oe.width,Oe.height,Fe,Xe,ve,Ee),ue.drawImage(Ne,0,0,Ne.width,Ne.height,Re,Ae,Pe,je);const ut=await new Promise(Qe=>ie.toBlob(Qe,"image/png"));if(!ut){se("Unable to process the design for transfer.");return}X&&$r(ut,"TUMBLER_design","png",!0),await Hr(f,i,ut)}catch{se("An error occurred while transferring the design. Please try again.")}finally{m.current=!1,A(!1)}},V=async(X=!1,ae,me)=>{try{if(!re("duplicating design"))return;m.current=!0,E(!0);const ge=ye(t==null?void 0:t.uploaded_img);if(!ge){Et("Image element not found."),Kt(t==null?void 0:t.uploader),se("Please upload a design first to duplicate it.");return}ge.complete||await new Promise(ie=>{ge.onload=ie,ge.onerror=ie});const he=Ur(3e3,1400);if(!he)return;const{canvas:_e,ctx:Oe}=he;if(me??N){const ue=await dp(ge,.15),be=3,Te=7,Le=3e3,ze=1400,Ke=10,He=(Le-Ke*(Te+1))/Te,we=ze/be,ve=be>1?(ze-we*be)/(be-1):0;for(let Ee=0;Ee<be;Ee++){const Pe=Ee===1,je=Pe?Te+1:Te;for(let Fe=0;Fe<je;Fe++){let Xe=Ke+Fe*(He+Ke);Pe&&(Xe-=He/2);const Re=Ee*(we+ve),Ae=Math.min(He*.75/ue.width,we*.75/ue.height),ut=ue.width*Ae,Qe=ue.height*Ae,Ue=Xe+(He-ut)/2,$e=Re+(we-Qe)/2;Oe.drawImage(ue,0,0,ue.width,ue.height,Ue,$e,ut,Qe)}}}else{const ie=await Ga(ge),ue=ie.width,be=ie.height,Te=1400,Le=31,ze=1566.6667,Ke=(1400-Te)/2,He=Math.min(Te/ue,Te/be),we=ue*He,ve=be*He,Ee=Number(ae)/100||1,Pe=we*Ee,je=ve*Ee,Fe=Le+(Te-Pe)/2,Xe=ze+(Te-Pe)/2,Re=Ke+(Te-je)/2;Oe.drawImage(ie,0,0,ue,be,Fe,Re,Pe,je),Oe.drawImage(ie,0,0,ue,be,Xe,Re,Pe,je)}const le=await new Promise(ie=>_e.toBlob(ie,"image/png"));if(!le){se("Unable to process the design for duplication.");return}await Rr(n,500),X&&$r(le,"TUMBLER_design","png",!0),await Hr(f,i,le)}catch{se("An error occurred while duplicating the design. Please try again.")}finally{m.current=!1,E(!1)}},ce=async(X=!1,ae)=>{try{if(!re("applying one side design")||Y)return;ee(!0);const me=ye(t==null?void 0:t.uploaded_img);if(!me){Et("Image element not found."),Kt(t==null?void 0:t.uploader),se("Please upload a design first to apply one side.");return}me.complete||await new Promise(he=>{me.onload=he,me.onerror=he});let ge=null;if(ae!==100){m.current=!0;const he=await Ga(me),_e=he.width,Oe=he.height,Ne=Ur(3e3,1400);if(!Ne)return;const{canvas:le,ctx:ie}=Ne,ue=1400,be=31,Te=(1400-ue)/2,Le=Math.min(ue/_e,ue/Oe),ze=_e*Le,Ke=Oe*Le,He=Number(ae)/100||1,we=ze*He,ve=Ke*He,Ee=be+(ue-we)/2,Pe=Te+(ue-ve)/2;if(ie.drawImage(he,0,0,_e,Oe,Ee,Pe,we,ve),ge=await new Promise(je=>le.toBlob(je,"image/png")),!ge){se("Unable to process the design for one side placement.");return}}else ge=await(await fetch(me.src)).blob();await Rr(n,500),X&&ge&&$r(ge,"TUMBLER_design","png",!0),await Hr(f,i,ge)}catch{se("An error occurred while applying one side design. Please try again.")}finally{m.current=!1,ee(!1)}},k=async X=>{if(K){se("A template is already being applied. Please wait.");return}J(!0);try{S(X.scale),await Ht(void 0,r,X.scale,!1)}catch{se("Failed to apply template. Please try again.")}finally{$(!1),J(!1)}},L=b.useCallback(X=>{R(X)},[]),B=b.useCallback(X=>{M(+wt(X.target.value)),w(!1)},[]),W=b.useCallback(X=>{S(+wt(X.target.value))},[]),z=b.useCallback(async()=>{if(!!ye(i)){se("Please wait for your design to finish uploading.");return}const ae=ye(u);if(!ae){se("Image not uploaded. Please upload your design first.");return}ae.toBlob(me=>{if(!me){se("Image not uploaded. Please upload your design first.");return}fs(me,"TUMBLER_design.png")},"image/png",1)},[]),te=async X=>{if(Q){se("A template is already being applied. Please wait.");return}oe(!0);try{const{downloadEnabled:ae,isPattern:me,tumblerTab:ge,scale:he}=X;switch(R(ge),ge){case Bt.oneSide:M(he),await ce(ae,he);break;case Bt.duplicateSide:me?w(me):M(he),await V(ae,he,me);break;case Bt.differentSide:M(he),await H(ae,he);break;default:break}}catch{se("Failed to apply template. Please try again.")}finally{oe(!1)}};return s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"d-flex i-pb-4 flex-col gap-4 secondary-border bg-main text-primary tumbler-editor-container",children:[s.jsxs("div",{className:"d-flex justify-between py-2 px-4 font-semibold align-items-center",style:{backgroundColor:"var(--actions-bg)"},children:["Edit and Re-Upload",s.jsx(fe,{buttonContent:"Templates",onclickHandler:()=>q(!0),tooltipId:"tumbler-editor-templates"})]}),s.jsx("div",{className:"d-flex px-4",children:s.jsx(Wt,{tabOptions:wn,activeTab:j,handleActiveTab:L})}),s.jsxs("div",{className:"d-flex gap-4 scale-actions px-4",children:[[100,85,75].map(X=>s.jsx(fe,{buttonContent:`${X}%`,onclickHandler:()=>ne(X),tooltipId:`${X}-percent-scale-selection`,extraClass:"outlined-btn",isDisabled:m.current},X)),s.jsx("div",{className:"d-flex",children:s.jsx(nn,{onChange:B,value:h,uniqueId:"mrdn-tumbler-scale_input",placeHolder:"Enter Scale",inputClassName:"merch-input h-31px mt-auto",labelClass:"flex !font-medium",hasPermission:!0,isDisabled:m.current,allowDecimal:!1,min:1,max:100})}),j==="duplicate"&&s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"d-flex align-items-center",children:"OR"}),s.jsx(fe,{buttonContent:"Pattern",onclickHandler:()=>w(!N),tooltipId:"pattern-selection",extraClass:N?"":"outlined-btn",isDisabled:m.current})]})]}),j===Bt.oneSide&&s.jsxs("div",{className:"d-flex gap-4 px-4 justify-end mrdn-tumbler-actions",children:[s.jsx(fe,{buttonContent:"Apply and Upload",onclickHandler:()=>ce(!1,h),tooltipId:"one-side-tumbler-scale-selection",isDisabled:pe,extraClass:"flex-fill"}),s.jsx(fe,{buttonContent:"Apply, Upload & Download",onclickHandler:()=>ce(!0,h),tooltipId:"one-side-download-scale-selection",isDisabled:pe,extraClass:"flex-fill"})]}),j===Bt.duplicateSide&&s.jsxs("div",{className:"d-flex gap-4 px-4 justify-end mrdn-tumbler-actions",children:[s.jsx(fe,{buttonContent:"Duplicate and Upload",onclickHandler:()=>V(!1,h,N),tooltipId:"duplicate-tumbler-scale-selection",isDisabled:pe,extraClass:"flex-fill"}),s.jsx(fe,{buttonContent:"Duplicate, Upload & Download",onclickHandler:()=>V(!0,h,N),tooltipId:"duplicate-download-scale-selection",isDisabled:pe,extraClass:"flex-fill"})]}),j===Bt.differentSide&&s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"d-flex gap-4 px-4",children:[s.jsx(Vi,{uploadedImage:g.front,setUploadedImage:U("front"),label:"Front side image",error:v.front}),s.jsx(Vi,{uploadedImage:g.back,setUploadedImage:U("back"),label:"Back side image",error:v.back})]}),s.jsxs("div",{className:"d-flex gap-4 px-4 justify-end mrdn-tumbler-actions",children:[s.jsx(fe,{buttonContent:"Apply and Upload",onclickHandler:()=>H(!1,h),tooltipId:"apply-image-selection",isDisabled:!de||pe,extraClass:"flex-fill"}),s.jsx(fe,{buttonContent:"Apply, Upload & Download",onclickHandler:()=>H(!0,h),tooltipId:"apply-download-image-selection",isDisabled:!de||pe,extraClass:"flex-fill"})]})]})]}),_&&ft.createPortal(s.jsxs(s.Fragment,{children:[x&&s.jsxs("div",{className:"d-flex gap-2 justify-center text-14px mb-2",children:[s.jsx("div",{className:"d-flex",children:s.jsx(nn,{onChange:W,value:C,uniqueId:"scale_input",placeHolder:"Enter Scale",inputClassName:"merch-input h-31px mt-auto",labelClass:"flex !font-medium",hasPermission:!0,isDisabled:O,allowDecimal:!1,min:1,max:100})}),s.jsx(fe,{buttonContent:"Scale",onclickHandler:X=>Ht(X,r,C),tooltipId:"tumbler-custom-scale-action",isDisabled:pe}),s.jsx(fe,{buttonContent:"100%",onclickHandler:X=>Ht(X,r),tooltipId:"tumbler-full-scale-action",isDisabled:pe})]}),s.jsx("div",{className:"d-flex gap-2 justify-center text-14px",children:x&&s.jsxs(s.Fragment,{children:[s.jsx(fe,{buttonContent:"Download Design",onclickHandler:z,tooltipId:"tumbler-download-design",isDisabled:pe||K}),s.jsx(fe,{buttonContent:"Templates",onclickHandler:()=>$(!0),tooltipId:"tumbler-product-scale-templates",isDisabled:pe||K})]})}),G&&s.jsx(mo,{setOpen:$,applyTemplate:k})]}),_),Z&&s.jsx(Jy,{setOpen:q,applyTemplate:te})]})};const tv=({title:e,content:t,accordionCallback:a,extraClass:n=""})=>{const[r,o]=b.useState(!0),l=b.useRef(null),i=b.useRef(null),d=b.useRef(null);b.useEffect(()=>{if(r&&i.current){const c=()=>{const p=i.current.scrollHeight;l.current.style.maxHeight!==`${p}px`&&(l.current.style.maxHeight=`${p}px`)};return c(),d.current=new ResizeObserver(()=>{requestAnimationFrame(c)}),d.current.observe(i.current),()=>{var p;return(p=d.current)==null?void 0:p.disconnect()}}else l.current&&(l.current.style.maxHeight="0")},[r]);const f=()=>{o(!r),a==null||a(r)};return s.jsxs("div",{className:`animated-accordion__section ${n}`,children:[s.jsxs("div",{className:`animated-accordion__header ${r?"active":""}`,onClick:f,children:[s.jsx("div",{className:"animated-accordion__title",children:e}),s.jsx("div",{className:`animated-accordion__icon ${r?"rotate":""}`,style:{marginLeft:"20px",transition:"transform 0.3s ease"},children:s.jsx(on,{size:20})})]}),s.jsx("div",{ref:l,style:{maxHeight:"0",overflow:"hidden",transition:"max-height 0.4s ease"},className:"animated-accordion__content",children:s.jsx("div",{ref:i,className:"animated-accordion__content-inner",children:t})})]})},nv=({value:e,onChange:t,fieldName:a,disallowedWordsRef:n,inputSelector:r=null,maxLimit:o=1/0,label:l,trackTextStatistics:i=!0,isDisabled:d=!1,extraClass:f="",helperText:c,shouldValidate:p,removeDisallowedWords:u,minCharacters:h,allowMinValidation:g=!1,...v})=>{var S,R,w,m;const C=b.useRef(null),[j,N]=b.useState({words:0,characters:0,error:"",minValidationError:""});b.useEffect(()=>{if(r&&(C.current&&clearTimeout(C.current),C.current=setTimeout(()=>{requestAnimationFrame(()=>{const _=document.querySelector(r);_&&(_.value=(e==null?void 0:e.slice(0,o))||"",aa(_))})},500)),!i)return;const T=((e==null?void 0:e.split(/\S+/g).length)??1)-1,E=(e==null?void 0:e.length)??0;if(n&&a){const _=(e==null?void 0:e.toLowerCase().match(dl))||[];n.current[a]=[...new Set(_)]}let O="",A="";return E>o&&(O="You have exceeded the limit of characters."),g&&e&&E<Number(h)&&(A=`You must enter at least ${h} characters.`),N({words:T,characters:E,error:O,minValidationError:A}),()=>{C.current&&clearTimeout(C.current)}},[e]);const M=T=>{if(d)return;let E=T.target.value;p&&(E==null?void 0:E.length)>o&&(E=E.slice(0,o),T.target.value=E),t==null||t(T)},I=T=>{if(d)return;const E=T.target.value;T.target.value=(E==null?void 0:E.trim())||"",t==null||t(T)},F=()=>{u==null||u(a)};return s.jsxs("div",{className:"d-flex flex-col",children:[l&&s.jsx("label",{className:"d-flex align-items-center gap-x-1 text-primary montserrat-medium color-gray-200",children:l}),s.jsx("textarea",{value:e||"",onChange:M,onBlur:I,spellCheck:!1,disabled:d,className:`mrdn-textarea text-primary scrollbar-style ${f}`,...v}),i&&s.jsxs("div",{className:"d-flex gap-2 justify-end",style:{fontSize:"0.75rem"},children:[s.jsxs("div",{className:"color-white bg-purple px-4 border-r-1 text-nowrap h-fit",style:{paddingBlock:".10rem"},children:["Words: ",j.words]}),s.jsxs("div",{className:`d-flex bg-purple color-white bg-purple px-4 border-r-1 ${j.error&&"bg-danger-light"}`,style:{paddingBlock:".10rem"},children:[j.error&&s.jsx("p",{className:"error-message font-semibold mr-2",children:j.error}),s.jsxs("span",{className:"text-nowrap",children:[j.characters," / ",o]})]})]}),j.minValidationError&&s.jsx(At,{message:j.minValidationError}),!!((R=(S=n==null?void 0:n.current)==null?void 0:S[a])!=null&&R.length)&&s.jsxs("div",{className:"d-flex color-danger gap-1 mt-2",style:{fontSize:"0.725rem"},children:[s.jsx(Cs,{style:{marginTop:"3px"}}),s.jsxs("span",{children:["Disallowed words:"," ",(m=(w=n==null?void 0:n.current)==null?void 0:w[a])==null?void 0:m.join(", "),s.jsxs("span",{className:"w-fit text-main cursor-pointer text-underline ml-2",onClick:F,title:"Replace keywords with product, graphic, design, or print",style:{whiteSpace:"nowrap"},children:[s.jsx(Es,{style:{marginBottom:"-2px"}})," Replace"]})]})]}),c&&s.jsx(At,{message:c})]})},Zn=b.memo(nv),av=[{icon:s.jsx(Es,{size:16}),title:"Regenerate"},{icon:s.jsx(Ts,{size:14}),title:"Copy"},{icon:s.jsx(vd,{size:16}),title:"Paste"},{icon:s.jsx(Cs,{size:16}),title:"Clear"},{icon:"AB",title:"Convert to uppercase"},{icon:"Ab",title:"Convert to capitalize"},{icon:"ab",title:"Convert to lowercase"}],ba=({marketPlace:e,control:t,placeholder:a,title:n,fieldName:r,disallowedWordsRef:o,inputFieldName:l,fieldValue:i,trademarkInfo:d,hasPermission:f,wordHandler:c,actionHandler:p,rows:u=3,maxCharacters:h,minCharacters:g,allowMinValidation:v,helperText:C,removeDisallowedWords:j})=>{const N=b.useMemo(()=>l&&`#${As[e]} #${as[l]}`,[]),{isOpen:M,trademarkData:{common:I,brand:F}}=d;return s.jsxs("div",{className:`textarea-input d-flex flex-col w-full gap-4 mrdn-${r}-container`,children:[s.jsxs("div",{className:"d-flex justify-between ",children:[s.jsx("span",{style:{fontSize:"0.75"},className:"d-flex align-items-center font-medium",children:n}),s.jsx("div",{className:"d-flex listing-actions-container",children:av.map((S,R)=>s.jsx("button",{className:"d-flex align-items-center justify-center bg-main border-purple color-purple flex-fill cursor-pointer px-2 py-1 listing-action",title:S.title,onClick:()=>p(S.title,r),disabled:!f,type:"button",children:S.icon},`${e}-${R}`))})]}),s.jsx(ke,{name:r,control:t,render:({field:{onChange:S,value:R}})=>s.jsx(Zn,{onChange:S,value:R||"",fieldName:l,disallowedWordsRef:o,inputSelector:N,id:`${e}-${r}-input`,placeholder:a,isDisabled:!f,rows:u,maxLimit:h,extraClass:"flex-fill",removeDisallowedWords:j,minCharacters:g,allowMinValidation:v,helperText:C})}),M&&s.jsx(Dc,{title:r,text:i,commonWords:I,brandWords:F,wordClickHandler:c})]})},rv=({children:e,className:t="",style:a,dragSensitivity:n=1,disableWheelScroll:r=!1})=>{const o=b.useRef(null),l=b.useRef(!1),i=b.useRef(0),d=b.useRef(0),f=b.useCallback(h=>{o.current&&(l.current=!0,i.current=h.pageX-o.current.offsetLeft,d.current=o.current.scrollLeft)},[]),c=b.useCallback(h=>{if(!l.current||!o.current)return;const g=(h.pageX-i.current)*n;o.current.scrollLeft=d.current-g},[n]),p=b.useCallback(()=>{l.current=!1},[]),u=b.useCallback(h=>{r||!o.current||(h.preventDefault(),requestAnimationFrame(()=>{o.current.scrollLeft+=h.deltaY}))},[r]);return s.jsx("div",{ref:o,className:`hide-scrollbar ${t}`,style:{display:"flex",gap:"0.5rem",paddingInline:"0.625rem",overflowX:"auto",overflowAnchor:"none",msOverflowStyle:"none",touchAction:"auto",msTouchAction:"auto",cursor:"grab",userSelect:"none",...a},onMouseDown:f,onMouseMove:c,onMouseUp:p,onMouseLeave:p,onWheelCapture:u,children:e})},sv=rv,ov={prefix:[],suffix:[],variations:[],all:[],1:[],2:[],3:[],4:[],5:[]},iv=["prefix","suffix","variations"];let xa=null;const lv=({marketPlace:e,value:t,onChange:a,setValue:n,getValues:r,keywordFieldName:o,extraKeywordChangeHandler:l})=>{var w;const[i,d]=b.useState(!0),[f,c]=b.useState("amazon"),[p,u]=b.useState("prefix"),h=b.useRef(null),g=b.useRef(null),[v,C]=b.useState({prefix:[],suffix:[],variations:[],all:[],1:[],2:[],3:[],4:[],5:[]}),[j,N]=b.useState(!1),M=pn(t,500);b.useEffect(()=>{function m(T){var E;g.current&&!g.current.contains(T.target)&&!((E=h.current)!=null&&E.contains(T.target))&&d(!1)}return document.addEventListener("click",m),()=>document.removeEventListener("click",m)},[]);const I=b.useCallback(m=>{if(!m.trim())return;const T=r(o),E=new Set(T.map(_=>_.toLowerCase())),A=su(m).split(",").map(_=>_.trim()).filter(_=>!E.has(_.toLowerCase()));A.length&&(n(o,[...T,...A]),l&&l(A))},[]),F=b.useCallback(m=>{const T=m.target.value;a(T)},[a]),S=async m=>{try{if(N(!0),!m){C(ov);return}if(f==="amazon"){const T=Lo[e],E=await at.processAmazonKeywordSuggestions({keyword:m,domain:e,host:T,alias:"aps"});C(O=>({...O,prefix:E.keywordPrefixSuggestions.split(","),suffix:E.keywordPostfixSuggestions.split(","),variations:E.restKeywordsSuggestions.split(",")}))}else{const T=Lo[e],[E,O]=await Promise.all([at.processAmazonKeywordSuggestions({keyword:m,domain:e,host:T,alias:"aps"}),Ko({keyword:m,market_place_id:ea[e],domain:e,word_count:p==="All"?p:+p})]);C(A=>({...A,prefix:E.keywordPrefixSuggestions.split(","),suffix:E.keywordPostfixSuggestions.split(","),variations:E.restKeywordsSuggestions.split(","),[p]:(O==null?void 0:O.result)||[]}))}}catch{}finally{N(!1)}},R=async m=>{if(p===m||j)return;if(iv.includes(m)){u(m);return}u(m),N(!0);const T=await Ko({keyword:M,market_place_id:ea[e],domain:e,word_count:m==="All"?m:+m});C(E=>({...E,[m]:(T==null?void 0:T.result)||[]})),N(!1)};return b.useEffect(()=>{if(!M){S(""),d(!1);return}return xa&&clearTimeout(xa),xa=setTimeout(()=>{d(!0),S(M)}),()=>{xa&&clearTimeout(xa)}},[M]),s.jsxs("div",{className:"position-relative w-full h-fit",children:[s.jsxs("div",{className:"d-flex position-relative suggestion-word-input",ref:h,children:[s.jsx("input",{value:t,type:"text",placeholder:"Search Term",className:"w-full text-primary border-r-1 h-38px",style:{padding:"3px 12px",outline:"none"},onFocus:()=>{M&&d(!0)},onChange:F}),s.jsxs("div",{className:"d-flex align-items-center h-38px position-absolute right-0 top-0",children:[s.jsx(jo,{isButton:!1,copyText:t,extraClass:"h-full px-3"}),s.jsx("button",{style:{border:"none",borderTopRightRadius:"4px",borderBottomRightRadius:"4px",background:"none",padding:"0.25rem 0.75rem"},onClick:()=>{I(t)},className:"d-flex h-full align-items-center bg-merch-dominator color-white cursor-pointer",children:s.jsx(So,{})})]})]}),s.jsxs("div",{ref:g,style:{display:i?"block":"none",pointerEvents:j?"none":"auto",filter:j?"blur(1px)":"none"},id:`${e}-keyword-dropdown`,className:"keyword-dropdown secondary-border w-full",children:[j&&s.jsx(na,{size:20}),s.jsx("div",{className:"keyword-tabs d-flex secondary-border-bottom",children:["amazon","redBubble"].map(m=>s.jsx("button",{className:"sub-tab cursor-pointer text-center font-weight-bold",style:{padding:"0.625rem",color:f===m?"var(--text-main-color)":"var(--text-primary-color)",border:"none",borderBottom:f===m?"2px solid var(--text-main-color)":"0px solid transparent",background:"none"},onClick:()=>{if(f===m||j)return;c(m);const T=Do[m][0].value;if(m=="amazon"){u(T);return}R(T)},children:m==="amazon"?"Amazon Keyword Suggestions":"Redbubble Tags"},m))}),s.jsx("div",{style:{borderBottom:"1px solid var(--secondary-border)",paddingInline:"0.5rem"},children:s.jsx(sv,{children:Do[f].map(m=>s.jsx("button",{style:{color:p===m.value?"var(--text-main-color)":"var(--text-primary-color)",border:"none",borderBottom:p===m.value?"2px solid var(--text-main-color)":"0px solid transparent",background:"none",padding:"1rem 0.5rem 0.25rem",whiteSpace:"nowrap"},className:"cursor-pointer font-weight-bold",onClick:()=>R(m.value),children:m.title},m.value))})}),s.jsx("div",{className:"d-flex w-full i-p-3",children:s.jsx("div",{style:{maxHeight:"202px",overflowY:"auto"},className:"suggestions-container w-full scrollbar-style d-flex flex-col gap-1",children:(w=v[p])==null?void 0:w.map((m,T)=>m?s.jsxs("div",{style:{borderRadius:"6px",padding:"0.5rem 0.75rem",background:"var(--color-gray-700)",transition:"background 0.2s ease",marginInline:"0.375rem"},className:"suggestion-item d-flex justify-between align-items-center gap-1",children:[s.jsx("span",{children:m}),s.jsxs("div",{className:"suggestion-actions d-flex gap-2",children:[s.jsx("button",{style:{border:"none",background:"none",padding:"0.25rem 0.375rem"},className:"d-flex align-items-center cursor-pointer",children:s.jsx(jo,{copyText:m,isButton:!1})}),s.jsx("button",{style:{border:"none",background:"none",padding:"0.25rem 0.375rem"},onClick:()=>I(m),className:"d-flex align-items-center cursor-pointer",children:s.jsx(So,{})})]})]},T):null)})})]})]})},cv=({uniqueId:e,fieldName:t,label:a="",placeHolder:n="",extraClass:r="",labelClass:o="",value:l=[],onChange:i,setValue:d,getValues:f,hasPermission:c=!1,allowPinning:p=!0,extraChangeHandler:u})=>{const h=b.useRef(null),g="merch-tag-input",v=`merch-tag-input-${e}`,C=!c,j=async w=>{var T,E,O,A;if((T=w==null?void 0:w.preventDefault)==null||T.call(w),(E=w==null?void 0:w.stopPropagation)==null||E.call(w),C||!(h!=null&&h.current))return;const m=(O=h.current)==null?void 0:O.querySelector(".keyword-input");m.autocomplete="off",(A=m==null?void 0:m.focus)==null||A.call(m)},N=(w,m)=>{const T=w.trim().toLowerCase(),E=w.split(",").filter(Boolean);return!T||!E.length?!1:!m.some(O=>O.toLowerCase()===T)},M=w=>{var m,T;(m=w==null?void 0:w.preventDefault)==null||m.call(w),(T=w==null?void 0:w.stopPropagation)==null||T.call(w),!C&&d(t,[])},I=p?(w,m)=>{var D;const T=w.target.closest(".keywords-badge-tag");if(!T)return;const E=T.getAttribute("data-pinned"),O=(f==null?void 0:f(t))||[],A=document.querySelector(`#${v} .rti--container`);if(!A)return;const _=Array.from(A.querySelectorAll(".keywords-badge-tag"));if(E==="false")T.setAttribute("data-pinned","true"),A.prepend(T);else{T.setAttribute("data-pinned","false");const x=O.indexOf(m);if(x!==-1){let y=_.find((G,$)=>$>x&&G.getAttribute("data-pinned")==="false");y?(D=y.parentNode)==null||D.insertBefore(T,y):A.insertBefore(T,A.lastChild)}}}:void 0,F=p?()=>{document.querySelectorAll(`#${v} .keywords-badge-tag`).forEach((m,T)=>{var E,O;if(!m.querySelector(".pin-icon")){const A=((O=(E=m.querySelector("span"))==null?void 0:E.textContent)==null?void 0:O.trim())||"",_=m.querySelector("button");if(_){const D=document.createElement("span");D.onclick=x=>I==null?void 0:I(x,A||""),D.classList.add("pin-icon"),D.setAttribute("role","button"),D.innerHTML='<svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 16 16" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M9.828.722a.5.5 0 0 1 .354.146l4.95 4.95a.5.5 0 0 1 0 .707c-.48.48-1.072.588-1.503.588-.177 0-.335-.018-.46-.039l-3.134 3.134a6 6 0 0 1 .16 1.013c.046.702-.032 1.687-.72 2.375a.5.5 0 0 1-.707 0l-2.829-2.828-3.182 3.182c-.195.195-1.219.902-1.414.707s.512-1.22.707-1.414l3.182-3.182-2.828-2.829a.5.5 0 0 1 0-.707c.688-.688 1.673-.767 2.375-.72a6 6 0 0 1 1.013.16l3.134-3.133a3 3 0 0 1-.04-.461c0-.43.108-1.022.589-1.503a.5.5 0 0 1 .353-.146"></path></svg>',m.insertBefore(D,_),m.setAttribute("data-pinned","false")}}})}:void 0,S=w=>{if(C)return;const m=l.length,T=w[w.length-1];let E=m,O=[];if(T!=null&&T.includes(",")){const _=T.replace(/,+/,",").split(",").map(D=>D.trim()).filter(Boolean);if(_.length){const D=Lc(w.slice(0,-1),_);E=D.length,O=D.slice(m,E),i(D)}}else E=w.length,O=w.slice(m,E),i(w);if(E>m){u&&u(O);const A=document.querySelector(`#${v} .rti--container`);A&&(A.scrollTop=A.scrollHeight)}p&&(F==null||F())},R=w=>{var T;const m=(T=w.target)==null?void 0:T.value;m&&(S([...l,`${m},`]),w.target.value="")};return s.jsxs("div",{className:`d-flex flex-col text-primary ${r}`,id:`${e}-container`,children:[a&&s.jsx("label",{className:`d-flex align-items-center gap-x-1 montserrat-medium color-gray-200 ${o}`,children:a}),s.jsxs("div",{className:`position-relative ${g} keywords-tag-input-container`,id:v,onClick:j,ref:h,children:[s.jsx(fe,{onclickHandler:M,extraClass:"clear-words-btn bg-main i-p-0 text-main w-fit gap-1 border-none hover-disabled",buttonName:"clear-filter",buttonContent:"Clear All",buttonIcon:s.jsx(Cs,{}),buttonProps:{type:"button"},isDisabled:C,tooltipId:`${e}-clear-all`,tooltipTitle:"Clear all keywords"}),s.jsx(wd,{value:l,onChange:S,classNames:{tag:"keywords-badge-tag",input:"keyword-input"},beforeAddValidate:N,name:t,onBlur:R,placeHolder:n,disabled:C})]})]})},oc=b.memo(cv),dv=({methods:e,marketPlace:t,hasPermission:a})=>{const n=b.useRef(null),{watch:r}=e,[o,l]=b.useState({topFocusKeywords:new Map([]),topLongKeywords:new Map([])}),i=r(`listing.${t}.designTitle`),d=r(`listing.${t}.designBrand`),f=r(`listing.${t}.designBullet1`),c=r(`listing.${t}.designBullet2`),p=r(`listing.${t}.designDescription`),u=()=>{if(i||d||f||c||p){const h=tp(t,i,d,[f,c].filter(Boolean),p,a);l(()=>({topFocusKeywords:h.topFocusKeywords,topLongKeywords:h.topLongKeywords}))}};return b.useEffect(()=>{i||d||f||c||p?(n.current&&clearTimeout(n.current),n.current=setTimeout(u,1e3)):l({topFocusKeywords:new Map([]),topLongKeywords:new Map([])})},[i,d,f,c,p]),s.jsx(Cd,{topFocusKeywords:o.topFocusKeywords,topLongKeywords:o.topLongKeywords,searchedTerm:"",showDetails:!0,hasPermission:a,hasProductSearchPermission:!1,activeMarketplace:t})},zi="mrdn-field-copied-text",Ki="mrdn-single-listing-text",qi=50,uv=200,pv=b.memo(({keyword:e,onDelete:t,onReplace:a})=>s.jsxs("div",{className:"d-flex py-1 gap-1 keyword-section text-default cursor-pointer restricted-section",children:[s.jsx("div",{className:"text-break-word",children:e}),s.jsx("div",{className:"d-flex align-items-center px-1 cursor-pointer",onClick:()=>t(e),children:s.jsx($c,{size:14})}),s.jsx("div",{className:"d-flex align-items-center pl-1 cursor-pointer",onClick:()=>a(e),children:s.jsx(Td,{size:18})})]})),Qr=nl,Wi={common:{},brand:{}},fv={designBullet1:"",designBullet2:"",designDescription:"",designTitle:"",designBrand:""},hv=({marketPlace:e,methods:t,handleModalVisibility:a,saveListingTemplate:n,globalUploaderSelectors:r,isCreatePage:o=!0})=>{var me,ge,he,_e,Oe,Ne;const{uploaded_img:i,uploader:d}=r??{},f=`merch.${e}-trademark-filters`,c=`Translate Listing To ${cu[e]}`,[p,u]=b.useState(!1),[h,g]=b.useState({asin:e,trademarkPayload:"",keyword:""}),v=b.useRef({isChecking:!1,hasBannedKeywords:!1}),C=Tt(le=>le.setUpgradePlanModal),j=Fc(le=>le.setModules),N=b.useRef(null),[M,I]=b.useState([]),[F,S]=b.useState({fetching:!1,isOpen:!1,trademarkData:{...Wi}}),[R,w]=b.useState({...fv}),{control:m,setValue:T,getValues:E,watch:O,formState:{errors:A}}=t,_=Bc(!0,ou[e]),[D,x]=b.useState(!1),[y,G]=b.useState(!1),[$,K]=b.useState({open:!1,keywords:"",processing:!1}),J=b.useRef({designTitle:[],designBrand:[],designBullet1:[],designBullet2:[],designDescription:[]}),[Y,ee]=b.useState(!1),[Z,q]=b.useState({isOpen:!1,errorTitle:"",errorMessage:""}),Q=D||!1,oe=F.fetching||y||!1,de=O("resetKey");b.useEffect(()=>{const le=async ie=>{var Te,Le;const ue=qa(((Te=ie.payload)==null?void 0:Te.common)||[]),be=qa(((Le=ie.payload)==null?void 0:Le.brand)||[]);S(ze=>({...ze,trademarkData:{common:ue,brand:be}}))};return Qr.addEventListener(`${$o}-${e}`,le),()=>{Qr.removeEventListener(`${$o}-${e}`,le)}},[]),b.useEffect(()=>{de&&U()},[de]),b.useEffect(()=>()=>{v.current={isChecking:!1,hasBannedKeywords:!1}},[]);const pe=()=>{const le=E().listing[e],{oldBannedKeyword:ie,newBannedKeyword:ue}=le,be=ie.trim(),Te=ue.trim();if(!be||!Te){se("Please enter both the word to replace and the new word");return}if(be===Te){se("You are trying to replace with the same word. Please enter a different word.");return}nt.forEach(Le=>{t.setValue(`listing.${e}.${Le}`,or(le[Le],be,ue.trim()),{shouldValidate:!0})}),q({isOpen:!0,errorTitle:"Replaced",errorMessage:`Text "${be}" is replaced with "${Te}" from all the input fields`}),ee(!1),T(`listing.${e}.newBannedKeyword`,"")},Ce=async le=>{var ie,ue,be;if(!D){x(!0);try{let Te=0;for(;Te<qi&&((ie=v.current)!=null&&ie.isChecking);)await Me(uv),Te++;if(Te>=qi){se("Banned words check is taking too long. Please try again."),v.current={isChecking:!1,hasBannedKeywords:!1};return}if((ue=v.current)!=null&&ue.hasBannedKeywords)return;const Le=typeof le=="string",{listing:ze}=E(),Ke=ze[e],{keywords:He,productType:we,keywordSearchEnabled:ve}=Ke,Ee=`merch-tag-input-${e}-keywords`;if(ve&&!He.length){se("Please add at least one keyword");return}let Pe="";if(Le&&(Pe=le.split(".")[2],!E(le))){se(`${Ps[Pe]} is required`);return}const je=Array.from(document.querySelectorAll(`#${Ee} .rti--container [data-pinned="true"]`)).map(Ue=>{var $e,Ve;return((Ve=($e=Ue.querySelector("span"))==null?void 0:$e.textContent)==null?void 0:Ve.trim())||""}).filter(Boolean),Fe=new Set(je),Xe=je.length?He.filter(Ue=>!Fe.has(Ue)):He;let Re={title:He.join(","),market_place:ea[e],product_type:we,normalTags:Xe,primaryTags:je};if(Le&&(Re.previous_response={[Fr[Pe]]:E(le)}),!ve&&o){const Ue=document.querySelector(i),$e=Ue==null?void 0:Ue.getAttribute("src"),Ve=Ue==null?void 0:Ue.getAttribute("alt");if(!$e){se("Please upload a global image"),Kt(d);return}Re.image=await ap($e,Ve)}const Ae=await vp(Re);if(!(Ae!=null&&Ae.success)){if(Et(Ae==null?void 0:Ae.message),(Ae==null?void 0:Ae.status)===429){C({isVisible:!0,modalTitle:(Ae==null?void 0:Ae.title)||"Limit reached",modalDescription:(Ae==null?void 0:Ae.description)||""});return}se(Ae==null?void 0:Ae.message);return}const{allowedCredits:ut=0,spentCredits:Qe=0}=(Ae==null?void 0:Ae.result.credit_details)||{};if(Ao(sa.AI_LISTING_CREATOR,ut,Qe,j),Le){const Ue=(((be=Ae==null?void 0:Ae.result)==null?void 0:be[Fr[le.split(".")[2]]])||"").replace(/^[*"']+|[*"']+$/g,"");t.setValue(le,Ue,{shouldValidate:!0})}else nt.forEach(Ue=>{var Ve;const $e=(((Ve=Ae==null?void 0:Ae.result)==null?void 0:Ve[Fr[Ue]])||"").replace(/^[*"']+|[*"']+$/g,"");t.setValue(`listing.${e}.${Ue}`,$e,{shouldValidate:!0})}),De("Listing","generated")}catch{}finally{x(!1)}}},re=b.useCallback(async(le,ie)=>{const ue=E(ie);switch(le){case"Regenerate":await Ce(ie);break;case"Copy":if(!ue)return;iu(ue),es(zi,ue);break;case"Paste":const be=Jr(zi);be&&t.setValue(ie,be,{shouldValidate:!0});break;case"Clear":t.setValue(ie,"",{shouldValidate:!0});break;case"Convert to uppercase":t.setValue(ie,ue==null?void 0:ue.toUpperCase());break;case"Convert to capitalize":t.setValue(ie,ul(ue));break;case"Convert to lowercase":t.setValue(ie,ue==null?void 0:ue.toLowerCase());break}},[]),U=b.useCallback(()=>{w({...Mn}),S(le=>({...le,fetching:!1,isOpen:!1,trademarkData:{...Wi}})),I([]),T(`listing.${e}.newBannedKeyword`,"")},[]),ne=b.useCallback(le=>{try{const{listing:ie}=E(),ue=ie[e];if(y)return;switch(G(!0),le){case"replace":const{searchWord:be,replaceWord:Te}=ue;nt.forEach(je=>{t.setValue(`listing.${e}.${je}`,or(ue[je],be,Te),{shouldValidate:!0})});break;case"copy":const Le={...ue};is.forEach(je=>{delete Le[je]}),es(Ki,Le),De("listing have been copied successfully!");break;case"paste":const ze=Jr(Ki);if(!ze)break;os.forEach(je=>{is.includes(je)||t.setValue(`listing.${e}.${je}`,ze[je]||Mn[je],{shouldValidate:!0})});break;case"paste-one-to-all":if(!nt.some(je=>{var Fe;return(Fe=ue[je])==null?void 0:Fe.trim()}))break;for(const je in ie)je!==e&&os.forEach(Fe=>{t.setValue(`listing.${je}.${Fe}`,(ue==null?void 0:ue[Fe])||Mn[Fe],{shouldValidate:!0})});De("All listing have been pasted successfully!");break;case"clear":for(const je in Mn)t.setValue(`listing.${e}.${je}`,Mn[je]);U(),t.clearErrors(`listing.${e}`);break;case"bullets":const Ke=ue.designBullet1.trim()+" "+ue.designBullet2.trim();if(!Ke.trim()){se("Please add at least one bullet point");break}t.setValue(`listing.${e}.designDescription`,Ke);break;case"bullets-plus":const{designBullet1:He,designBullet2:we,designTitle:ve,designBrand:Ee}=ue;if(![He,we,ve,Ee].some(je=>je==null?void 0:je.trim())){se("Please fill at least one field to generate the description");break}const Pe=`${He.trim()} ${we.trim()} This ${ve.trim()} item is designed by ${Ee.trim()}`.trim();t.setValue(`listing.${e}.designDescription`,Pe);break;default:break}G(!1)}catch{x(!1)}},[y]),H=async()=>{if(D||y)return;G(!0),G(!0);const{listing:le}=E(),ie=le[e],ue={};if(nt.forEach(Te=>{ie[Te]&&(ue[Te]=ie[Te])}),!ue.designTitle||!ue.designBrand){se("Please add title and brand field to translate"),G(!1);return}const be=await wp(ue,As[e]);if(be!=null&&be.status)for(const Te in(be==null?void 0:be.data)||{})t.setValue(`listing.${e}.${Te}`,(be==null?void 0:be.data[Te])||ie[Te],{shouldValidate:!0});G(!1)},V=b.useCallback(()=>{var le;(le=N.current)==null||le.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})},[]),ce=async()=>{var Qe,Ue;if(F.fetching||y||D)return;const{listing:le}=E(),{designTitle:ie,designBrand:ue,designBullet1:be,designBullet2:Te,designDescription:Le}=le[e],ze=`${ie} ${ue} ${be} ${Te} ${Le}`.trim();if(!ze){se("Please add at least one product field to check for trademark");return}S($e=>({...$e,fetching:!0})),ee(!1),T(`listing.${e}.newBannedKeyword`,"");const Ke=((Qe=await at.getStringifyValue([f]))==null?void 0:Qe[f])??{},we={...pl(du,Ke),keyword:"",criteria:uu[0].value},[ve,Ee]=await Promise.all([Uc(we,{sortBy:"",sortOrder:""},ze,1,!0,!0),await qo(ea[e])]);if((ve==null?void 0:ve.status)===429){C({isVisible:!0,modalTitle:(ve==null?void 0:ve.title)||"Limit reached",modalDescription:(ve==null?void 0:ve.description)||""}),S($e=>({...$e,fetching:!1}));return}const{allowedCredits:Pe=0,spentCredits:je=0}=(ve==null?void 0:ve.credit_details)||{};Ao(sa.TRADEMARK_SEARCH,Pe,je,j);const Fe=new Set((Ue=Ee==null?void 0:Ee.result)==null?void 0:Ue.map($e=>$e.toLowerCase())),Xe=new Set(ze.toLowerCase().match(/\b\w+\b/g)||[]),Re=[];for(const $e of Xe)Fe.has($e)&&Re.push($e);I(Re);const Ae=qa((ve==null?void 0:ve.common)||[]),ut=qa((ve==null?void 0:ve.brand)||[]);w($e=>({...$e,designTitle:ie,designBrand:ue,designBullet1:be,designBullet2:Te,designDescription:Le})),S($e=>({...$e,fetching:!1,isOpen:!0,trademarkData:{common:Ae,brand:ut}})),V()},k=b.useCallback(le=>{const{listing:ie}=E(),{designTitle:ue,designBrand:be,designBullet1:Te,designBullet2:Le}=ie[e],ze=`${ue} ${be} ${Te} ${Le}`.trim();g(Ke=>({...Ke,keyword:le,trademarkPayload:ze})),u(!0)},[]),L=b.useCallback(async le=>{v.current={isChecking:!0,hasBannedKeywords:!1};const ie=await qo(ea[e]);if(ie!=null&&ie.success){const ue=new Set(le.map(Te=>Te.toLowerCase())),be=((ie==null?void 0:ie.result)||[]).filter(Te=>ue.has(Te.toLowerCase()));be.length&&(K(Te=>({...Te,open:!0,keywords:be.join(",")})),v.current.hasBannedKeywords=!0)}v.current.isChecking=!1},[]),B=le=>{if(!le){const ie=E(`listing.${e}.keywords`),ue=new Set($.keywords.split(",").map(be=>be.toLowerCase()));T(`listing.${e}.keywords`,ie.filter(be=>!ue.has(be.toLowerCase()))),v.current={isChecking:!1,hasBannedKeywords:!1}}K(ie=>({...ie,open:!ie.open,keywords:le??""}))},W=async()=>{if($.processing)return;K(ie=>({...ie,processing:!0}));const le=await jl({marketplace:ea[e],status:2,tags:$.keywords.split(",")});le!=null&&le.success?De((le==null?void 0:le.message)||"Keywords Disabled successfully."):se((le==null?void 0:le.message)||"Something went wrong, please try again."),v.current={isChecking:!1,hasBannedKeywords:!1},K(ie=>({...ie,processing:!1,open:!1,keywords:""}))},z=b.useCallback(le=>{const ie=E(`listing.${e}`);nt.forEach(ue=>{const be=ie[ue];if(!be)return;const Te=be.replace(dl,lu(pu)).replace(/\s+/g," ").trim();T(`listing.${e}.${ue}`,Te,{shouldValidate:!0})}),J.current={}},[]),te=b.useCallback(le=>{if(le&&typeof le=="string"){const ie=E().listing[e];nt.forEach(ue=>{t.setValue(`listing.${e}.${ue}`,or(ie[ue],le,""),{shouldValidate:!0})}),q({isOpen:!0,errorTitle:"Deleted",errorMessage:`Text "${le}" is removed from all the input fields`});return}q(ie=>({...ie,isOpen:!1,keyword:""}))},[]),X=b.useCallback(async le=>{T(`listing.${e}.oldBannedKeyword`,le),ee(!0),Ze(`#${e}-new-banned-keyword-input`,500,2e3).then(ie=>{if(!ie)return;ie.focus();const ue=ie.getBoundingClientRect();(ue.top<0||ue.bottom>globalThis.innerHeight)&&ie.scrollIntoView({block:"nearest"})})},[]),ae=b.useCallback(le=>{te(le)},[te]);return s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"d-flex flex-col gap-4 ai-listing-creator",style:{paddingBottom:"1px"},children:[s.jsxs("section",{className:"d-flex flex-col gap-4 i-p-4 secondary-border border-r-1 i-border-t-0 position-relative",style:{borderTopLeftRadius:"0px",borderTopRightRadius:"0px"},children:[(D||y)&&s.jsx("div",{className:"position-absolute left-0 top-0 z-index-1 pointer-events-auto user-events-none w-full h-full bg-spinner-shadow",children:s.jsx(na,{extraClass:"z-index-1",size:20})}),s.jsxs("div",{className:"listing-actions d-flex flex-wrap gap-2",children:[s.jsx(fe,{buttonContent:"Copy",onclickHandler:()=>ne("copy"),buttonIcon:s.jsx(Ts,{}),tooltipId:`${e}-copy-listing-text-action-`,buttonProps:{title:"Copy this listing to the clipboard"}}),s.jsx(fe,{buttonContent:"Paste",onclickHandler:()=>ne("paste"),buttonIcon:s.jsx(ts,{}),tooltipId:`${e}-paste-text-action`,buttonProps:{title:"Paste listing from clipboard"}}),s.jsx(fe,{buttonContent:"Paste to All",onclickHandler:()=>ne("paste-one-to-all"),buttonIcon:s.jsx(ts,{}),tooltipId:`${e}-paste-one-to-all-text-action`,buttonProps:{title:"Paste this listing into all listings"}}),s.jsx(fe,{buttonContent:"Clear",onclickHandler:()=>ne("clear"),buttonIcon:s.jsx($t,{}),tooltipId:`${e}-clear-text-action`,buttonProps:{title:"Clear this listing"}}),s.jsx(fe,{buttonContent:"Check For Trademark",onclickHandler:ce,buttonIcon:s.jsx(Eo,{}),tooltipId:`${e}-trademark-check-action`,isLoading:F.fetching,isDisabled:oe}),s.jsx(fe,{buttonContent:"Trademark Settings",onclickHandler:()=>{g(le=>({...le,trademarkPayload:"",keyword:""})),u(!0)},buttonIcon:s.jsx(Eo,{}),tooltipId:`${e}-trademark-settings-action`,isDisabled:oe}),s.jsx(fe,{buttonContent:"Bullets",onclickHandler:()=>ne("bullets"),buttonIcon:s.jsx(_o,{}),tooltipId:`${e}-bullets-action`,buttonProps:{title:"Use bullet points to generate and fill the description"}}),s.jsx(fe,{buttonContent:"Bullets +",onclickHandler:()=>ne("bullets-plus"),buttonIcon:s.jsx(_o,{}),tooltipId:`${e}-bullets-plus-action`,buttonProps:{title:"Use bullet points with brand and title to generate and fill the description"}}),s.jsx(fe,{buttonContent:c,onclickHandler:H,buttonIcon:s.jsx(Hc,{}),tooltipId:`${e}-translate-action`}),s.jsx(fe,{buttonContent:"Templates",onclickHandler:()=>a(e),buttonIcon:s.jsx(Yy,{}),tooltipId:`${e}-template-action`}),s.jsx(ke,{name:`listing.${e}.keywordSearchEnabled`,control:m,render:({field:{onChange:le,value:ie}})=>{const ue=ie?"AI Listing From Keywords":"AI Listing From Design";return s.jsx(wr,{name:ue,checked:ie,onChangeHandler:le,switchTitle:ue,extraClass:"small-switch listing-generation-toggle",hasPermission:!0,id:`${e}-listing-gen-type`})}})]}),s.jsxs("div",{className:"keywords-input-container gap-4",children:[s.jsxs("div",{className:"keywords-input d-flex flex-col gap-4",children:[s.jsx("div",{className:"product-type-container mb-4",style:{display:"grid",gridTemplateColumns:"1.86fr 0.4fr"},children:s.jsx(ke,{name:`listing.${e}.productType`,control:m,render:({field:{onChange:le,value:ie}})=>s.jsx(rl,{fieldName:"productType",options:_,value:ie,onChange:({value:ue})=>{le(ue)},placeholder:"Select Product Type",label:"Product Type",isDisabled:!1})})}),s.jsxs("div",{className:"keywords-inputs gap-4",style:{display:"grid",gridAutoColumns:"minmax(0, 1fr)",gridTemplateColumns:"1fr 1fr 0.4fr"},children:[s.jsx(ke,{name:`listing.${e}.suggestionWord`,control:m,render:({field:{onChange:le,value:ie}})=>s.jsx(lv,{marketPlace:e,value:ie,onChange:le,setValue:T,getValues:E,keywordFieldName:`listing.${e}.keywords`,hasPermission:!0,extraKeywordChangeHandler:L})}),s.jsx(ke,{name:`listing.${e}.keywords`,control:m,render:({field:{onChange:le,value:ie}})=>s.jsx(oc,{uniqueId:`${e}-keywords`,placeHolder:"Enter Keywords",fieldName:`listing.${e}.keywords`,value:ie,onChange:le,setValue:T,getValues:E,hasPermission:!0,extraChangeHandler:L})}),s.jsx(fe,{buttonContent:"Generate",onclickHandler:Ce,buttonIcon:s.jsx(Rc,{}),tooltipId:`${e}-generate-listing-action`,extraClass:"mt-auto h-38px generate-listing-action",isDisabled:Q,isLoading:Q,tooltipTitle:"Let Merch Dominator AI Listing Creator take care of everything!"})]}),s.jsxs("div",{className:"search-replace-words-inputs gap-4",style:{display:"grid",gridTemplateColumns:"1fr 1fr 0.4fr"},children:[s.jsx(ke,{name:`listing.${e}.searchWord`,control:m,render:({field:{onChange:le,value:ie}})=>s.jsx(qe,{label:"Search for",onChange:le,value:ie,uniqueId:`${e}-search-word-input`,placeHolder:"e.g. T-shirt",hasPermission:!0,extraClass:"flex-fill"})}),s.jsx(ke,{name:`listing.${e}.replaceWord`,control:m,render:({field:{onChange:le,value:ie}})=>s.jsx(qe,{label:"Replace with",onChange:le,value:ie,uniqueId:`${e}-replace-word-input`,placeHolder:"e.g. Design",hasPermission:!0,extraClass:"flex-fill"})}),s.jsx(fe,{buttonContent:"Replace",onclickHandler:()=>ne("replace"),buttonIcon:s.jsx(Es,{}),tooltipId:`${e}-replace-text-action`,extraClass:"mt-auto h-38px"})]})]}),s.jsx("div",{})]})]}),s.jsx("section",{className:"d-flex",children:s.jsx(fe,{buttonContent:"Save As Listing Template",onclickHandler:()=>n(e),tooltipId:`${e}-save-listing-action`,extraClass:"ml-auto i-mr-4",buttonProps:{type:"button"},tooltipTitle:"Save this listing as a template for future use!"})}),s.jsxs("section",{className:"d-flex flex-col gap-4 secondary-border border-r-1 i-p-4 position-relative",ref:N,children:[(D||y)&&s.jsx("div",{className:"position-absolute left-0 top-0 z-index-1 pointer-events-auto user-events-none w-full h-full bg-spinner-shadow",children:s.jsx(na,{extraClass:"z-index-1",size:20})}),s.jsxs("div",{children:[s.jsx("span",{className:"font-semibold text-base",children:"Product Details"})," ",s.jsx("span",{style:{color:"var(--primary-border)"},children:"(Required)"})]}),s.jsx("div",{style:{fontSize:"0.75rem"},children:'Product names will be appended to this design title e.g. a design title of "Funny Cat" will be displayed as "Funny Cat T-Shirt" on the t-shirt product details page.'}),s.jsxs("div",{className:"listing-title-brand-container d-flex gap-4",children:[s.jsx(ba,{marketPlace:e,control:m,placeholder:"Enter listing title",title:"Design Title *",fieldName:`listing.${e}.designTitle`,disallowedWordsRef:J,inputFieldName:"designTitle",hasPermission:!0,fieldValue:R.designTitle,trademarkInfo:F,wordHandler:k,actionHandler:re,rows:2,maxCharacters:60,removeDisallowedWords:z,helperText:(he=(ge=(me=A==null?void 0:A.listing)==null?void 0:me[e])==null?void 0:ge.designTitle)==null?void 0:he.message},`listing-${e}-designTitle`),s.jsx(ba,{marketPlace:e,control:m,placeholder:"Enter listing brand",title:"Brand *",fieldName:`listing.${e}.designBrand`,disallowedWordsRef:J,inputFieldName:"designBrand",hasPermission:!0,fieldValue:R.designBrand,trademarkInfo:F,wordHandler:k,actionHandler:re,rows:2,maxCharacters:50,removeDisallowedWords:z,helperText:(Ne=(Oe=(_e=A==null?void 0:A.listing)==null?void 0:_e[e])==null?void 0:Oe.designBrand)==null?void 0:Ne.message},`listing-${e}-designBrand`)]})]}),s.jsxs("section",{className:"d-flex flex-col gap-4 secondary-border border-r-1 i-p-4 position-relative",children:[(D||y)&&s.jsx("div",{className:"position-absolute left-0 top-0 z-index-1 pointer-events-auto user-events-none w-full h-full bg-spinner-shadow",children:s.jsx(na,{extraClass:"z-index-1",size:20})}),s.jsxs("div",{children:[s.jsx("span",{className:"font-semibold text-base",children:"Product Features"})," ",s.jsx("span",{style:{color:"var(--primary-border)"},children:"(Optional)"})]}),s.jsx("div",{style:{fontSize:"0.75rem"},children:"Summarize the unique details of your design. They'll appear in a bulleted list, along with other product information we automatically include."}),s.jsxs("div",{className:"listing-features-container d-flex gap-4",children:[s.jsx(ba,{marketPlace:e,control:m,placeholder:"Enter feature bullet",title:"Feature Bullet 1",fieldName:`listing.${e}.designBullet1`,disallowedWordsRef:J,inputFieldName:"designBullet1",hasPermission:!0,fieldValue:R.designBullet1,trademarkInfo:F,wordHandler:k,actionHandler:re,rows:4,maxCharacters:256,removeDisallowedWords:z},`listing-${e}-designBullet1`),s.jsx(ba,{marketPlace:e,control:m,placeholder:"Enter feature bullet",title:"Feature Bullet 2",fieldName:`listing.${e}.designBullet2`,disallowedWordsRef:J,inputFieldName:"designBullet2",hasPermission:!0,fieldValue:R.designBullet2,trademarkInfo:F,wordHandler:k,actionHandler:re,rows:4,maxCharacters:256,removeDisallowedWords:z},`listing-${e}-designBullet2`)]}),s.jsx(ba,{marketPlace:e,control:m,placeholder:"Enter description (Minimum 75 characters)",title:"Description",fieldName:`listing.${e}.designDescription`,disallowedWordsRef:J,inputFieldName:"designDescription",hasPermission:!0,fieldValue:R.designDescription,trademarkInfo:F,wordHandler:k,actionHandler:re,rows:4,maxCharacters:2e3,removeDisallowedWords:z,minCharacters:75,allowMinValidation:!0},`listing-${e}-designDescription`),!F.fetching&&!!M.length&&s.jsxs("div",{className:"d-flex flex-col gap-2",children:[s.jsx("div",{className:"font-semibold text-base",children:"Banned Amazon Keywords"}),s.jsx("div",{className:"d-flex flex-wrap gap-2 p-2 primary-border border-r-1 banned-keywords-container scrollbar-style",children:M.map(le=>s.jsx(pv,{keyword:le,onDelete:ae,onReplace:X},`${e}-${le}`))}),Y&&s.jsxs("div",{className:"d-flex",children:[s.jsx(ke,{name:`listing.${e}.oldBannedKeyword`,control:m,render:({field:{onChange:le,value:ie}})=>s.jsx(qe,{onChange:le,value:ie,uniqueId:`${e}-old-banned-keyword-input`,labelClass:"flex !font-medium",label:"Replace old word with new",inputClassName:"merch-input i-border-br-0 i-border-tr-0",placeHolder:"Old word",isDisabled:!1,hasPermission:!0,extraClass:"w-full"})}),s.jsx(ke,{name:`listing.${e}.newBannedKeyword`,control:m,render:({field:{onChange:le,value:ie}})=>s.jsx(qe,{onChange:le,value:ie,uniqueId:`${e}-new-banned-keyword-input`,labelClass:"flex !font-medium",inputClassName:"merch-input border-r-0",placeHolder:"New word",isDisabled:!1,hasPermission:!0,extraClass:"w-full mt-auto"})}),s.jsx(fe,{buttonContent:"Replace",isLoading:!1,isDisabled:!1,onclickHandler:pe,extraClass:"color-white border-none mt-auto h-38px i-border-tl-0 i-border-bl-0",buttonProps:{type:"button"},tooltipId:`${e}-replace-action`})]})]}),s.jsx(dv,{methods:t,marketPlace:e,hasPermission:!0})]})]}),p&&s.jsx(Je,{open:!0,children:s.jsx(jd,{emitter:Qr,open:!0,modalHandler:u,trademarkKeywordPayload:h,tradeMarkUniqueId:e,marketPlace:e})}),$.open&&s.jsx(pt,{open:!0,handleClose:()=>B(),uniqueId:"banned-word-action",extraClass:"modal-delete-action",modalTitle:"Are you sure?",bodySection:s.jsxs("div",{className:"w-full",children:[s.jsx("div",{className:"d-flex justify-center color-danger",style:{opacity:.7},children:s.jsx(js,{size:70})}),s.jsx("span",{className:"d-flex px-4 py-2 justify-center font-weight-bold border-r-1",style:{fontSize:"1.7rem",color:"var(--color-gray-200)"},children:"Oops..."}),s.jsxs("div",{className:"px-4 gap-2 py-2 border-r-1",children:[s.jsxs("span",{className:"font-weight-bold mr-2",children:['"',$.keywords,'"']}),s.jsx("span",{children:"is a banned keyword. Click Okay if you want to use it and deactivate it from your banned keywords. Click Cancel if you don't want to use it and keep it activated in your banned keywords."})]}),s.jsxs("div",{className:"d-flex w-full justify-center mt-4 gap-1 pr-2",children:[s.jsx(fe,{buttonContent:"Okay",isLoading:$.processing,isDisabled:$.processing,onclickHandler:W,extraClass:"color-white border-none",buttonProps:{type:"button"},tooltipId:"disable-banned-word-action"}),s.jsx(fe,{buttonContent:"Cancel",isLoading:!1,isDisabled:$.processing,onclickHandler:()=>B(),extraClass:"outlined-btn",buttonProps:{type:"button"},tooltipId:"prevent-banned-word-action"})]})]}),footerSection:null}),s.jsx(_s,{id:"banned-words-alert",modalTitle:"Attention Required",isOpen:Z.isOpen,alertIcon:s.jsx(Sd,{size:70}),alertIconClass:"color-success",onClose:te,onBlur:te,errorMessage:Z.errorMessage,errorTitle:Z.errorTitle})]})},mv=({template:e,onEdit:t,onDelete:a,isProcessing:n,handleApply:r})=>{const[o,l]=b.useState(!1),i=b.useRef(null),d=b.useMemo(()=>Ge(e.template_metadata,{values:{}}),[e.template_metadata]),f=(u,h)=>{u.stopPropagation(),u.preventDefault(),h?t(e):a(u,e)},c=u=>{u.stopPropagation(),u.preventDefault(),r(e)},p=u=>{u.stopPropagation(),u.preventDefault(),l(h=>!h)};return b.useEffect(()=>{var u;i.current&&(o?((u=i.current)==null||u.scrollIntoView({behavior:"smooth",block:"nearest"}),i.current.style.height=`${i.current.scrollHeight}px`):i.current.style.height="0")},[o]),s.jsxs("li",{className:`d-flex w-full flex-col justify-between secondary-border-bottom ${o?"gap-1 pb-2":""}`,children:[s.jsxs("div",{className:"d-flex justify-between gap-2 action-container",children:[s.jsx("div",{className:"text-base w-full item-title cursor-pointer text-break-word",onClick:c,children:e==null?void 0:e.name}),s.jsxs("div",{className:"action-buttons d-flex h-fit gap-3",children:[s.jsx("button",{onClick:u=>{f(u,"edit")},disabled:n,children:s.jsx(Tn,{size:12})}),s.jsx("button",{onClick:f,disabled:n,style:{background:"var(--color-danger-light)"},children:s.jsx(sn,{size:12})}),s.jsx("button",{onClick:p,disabled:n,className:"i-text-main i-main-border bg-main expand-action",children:s.jsx("div",{className:"d-flex",style:o?{transform:"rotate(-90deg)",transition:"0.3s ease-in-out"}:{transition:"0.3s ease-in-out"},children:s.jsx(on,{size:12})})})]})]}),s.jsx("div",{className:`expandable-section ${o?"expanded":""}`,ref:i,children:o&&nt.map((u,h)=>{var v;const g=(v=d==null?void 0:d.values)==null?void 0:v[h];return g?s.jsxs("div",{className:"d-flex gap-2 mb-2",children:[s.jsxs("span",{style:{minWidth:"140px",fontWeight:"600"},children:[Ps[u]," :"]}),s.jsx("div",{className:"d-flex align-items-center gap-2",children:g})]},u):null})})]})},gv=({templates:e,onEdit:t,onDelete:a,isProcessing:n=!1,handleApply:r})=>s.jsx("div",{className:"item-list-container position-relative",children:e.length?s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"d-flex justify-end text-main font-semibold",children:["Total Templates: ",e.length]}),s.jsx("ul",{className:"d-flex flex-col text-primary i-pb-4 i-pt-3",children:e.map(o=>{const l=`${o.name}-${o.id}`;return s.jsx(mv,{template:o,onEdit:t,onDelete:a,handleApply:r,isProcessing:n},l)})})]}):s.jsx("div",{className:"no-content-text text-center",children:n?s.jsx(mn,{}):"No Templates Found"})}),bv=b.memo(gv),sr={name:"",category:"",template_metadata:"",id:0},xv=({handleModalVisibility:e,applyTemplate:t,allListingMethods:a,currentMarketPlace:n})=>{const r=gt({defaultValues:fu,mode:"onChange",resolver:Lt(hp)}),{control:o,getValues:l,setValue:i,watch:d,formState:{errors:f}}=r,{maxTemplateCount:c,hasUnlimitedAccess:p,importTemplateCount:u,exportTemplateCount:h}=Pt(re=>re),g=Tt(re=>re.setUpgradePlanModal),v=b.useRef(null),[C,j]=b.useState(et.ADD),[N,M]=b.useState([]),[I,F]=b.useState([]),[S,R]=b.useState(!0),[w,m]=b.useState(!1),[T,E]=b.useState(!1),[O,A]=b.useState({...sr}),[_,D]=b.useState({modalOpen:!1,template:null,processing:!1}),x=d("searchText"),y=pn(x,500),G=C===et.ADD,$=b.useCallback(async()=>{const re=await Xt(ya);re!=null&&re.templates&&M(re==null?void 0:re.templates),R(!1)},[]),K=b.useCallback((re,U)=>{if(!re)return F(U);F(U.filter(ne=>ne.name.toLowerCase().includes(re.toLowerCase())))},[]);b.useEffect(()=>{const re=a.getValues().listing[n];nt.forEach(U=>{i(U,re[U])}),$()},[]),b.useEffect(()=>{S||K(y,N)},[y,S,K,N]);const J=b.useCallback(re=>{j(re),jn(v,"#modal-product-listing-templates .mrdn-modal-body")},[]),Y=async re=>{if(!w){m(!0);try{const U=l(),ne=O.id,H=U.templateTitle.trim(),V=nt.map(L=>U[L].replace(/\n/g," ")),ce={name:H,category:ya,template_metadata:{values:V}},k=ne?await _n(ne,ce):await Qt(ce);if(k!=null&&k.name){De("Template",ne?"updated":"saved"),ne&&A(L=>({...L,...sr})),await $();return}if((k==null?void 0:k.status)===429){g({isVisible:!0,showTemplateText:!0,modalTitle:(k==null?void 0:k.title)||"Template Limit Reached",modalDescription:(k==null?void 0:k.description)||dn(c,"save","template")});return}await $(),se(k==null?void 0:k.message)}catch{se("An error occurred while managing the template")}finally{m(!1)}}},ee=re=>{const U=Ge(re.template_metadata);A(ne=>({...ne,...re})),nt.forEach((ne,H)=>{i(ne,U.values[H]||"",{shouldValidate:!0})}),i("templateTitle",re.name,{shouldValidate:!0}),J(et.ADD)},Z=b.useCallback(()=>{r.setValue("templateTitle",""),A(re=>({...re,...sr}))},[r]),q=b.useCallback((re,U)=>{_.processing||D(ne=>({...ne,modalOpen:!ne.modalOpen,template:(U==null?void 0:U.id)||null}))},[_.processing]),Q=async()=>{if(!_.processing){D(re=>({...re,processing:!0}));try{const{template:re}=_,U=await fn(re);if(U!=null&&U.success){De("Template","deleted"),await $();return}await $(),se(U==null?void 0:U.message)}catch{se("An error occurred while deleting the template")}finally{_.template===O.id&&A(sr),D(re=>({...re,modalOpen:!1,template:null,processing:!1}))}}},oe=re=>{if(T||w)return;E(!0);const U=Ge(re.template_metadata);t(U.values||[]),E(!1),e(!1)},de=b.useCallback(async re=>{const U=[],ne=new Set;for(const{name:V,template_metadata:ce,category:k}of re){if(k!==ya)continue;const L=V==null?void 0:V.trim();if(!L||ne.has(L))continue;const W=((ce==null?void 0:ce.values)||[]).map(z=>typeof z=="string"?z:"").filter((z,te)=>!(te<=1&&!z));W.length&&(ne.add(L),U.push({name:L,category:k,template_metadata:{values:W}}))}if(!U.length){De("Import complete. No valid templates were available to import.");return}const H=await hn(U);if(H!=null&&H.success){De((H==null?void 0:H.message)||ln((H==null?void 0:H.count)||0)),await $();return}if((H==null?void 0:H.status)===429){const V=(H==null?void 0:H.description)||cn(H==null?void 0:H.count,u);g({isVisible:!0,showTemplateText:!0,modalTitle:(H==null?void 0:H.title)||"Template Limit Reached",modalDescription:V}),(+V.split(" ")[0]||0)&&await $();return}se(H==null?void 0:H.message)},[u]),pe=b.useCallback(re=>{const U=document.getElementById(re);U&&(U.scrollIntoView({behavior:"smooth",block:"center"}),U==null||U.focus())},[]),Ce=b.useCallback(re=>{re.templateTitle?pe("listing-template-title-input"):re.designTitle?pe("designTitle-input"):re.designBrand&&pe("designBrand-input")},[]);return s.jsxs(s.Fragment,{children:[s.jsx(Je,{open:!0,children:s.jsx(pt,{open:!0,width:"50vw",handleClose:re=>{re.stopPropagation(),e(!1)},uniqueId:"product-listing-templates",modalTitle:"Templates",modalTitleChildren:p?null:s.jsx(un,{showTemplateLimit:!0,templateLimit:c,buttonId:"listing-templates-upgrade-button"}),bodySection:s.jsxs("div",{className:"d-flex flex-col gap-y-3 h-full",children:[s.jsxs("div",{className:"d-flex justify-between",children:[s.jsx(Wt,{tabOptions:Sn,activeTab:C,handleActiveTab:J}),G?s.jsx(gn,{collectionKey:ya,fileName:"merch-dominator-listing-templates.json",processTemplateImport:de,extraClass:"w-full justify-end align-items-center",templateLimit:h}):s.jsx("div",{className:"w-full position-relative",children:s.jsx(ke,{name:"searchText",control:o,render:({field:{onChange:re,value:U}})=>s.jsx(qe,{onChange:re,value:U,uniqueId:"listing-search_input",labelClass:"flex !font-medium",inputClassName:"merch-input",placeHolder:"Search by title",isDisabled:!1,allowClear:{clearIcon:s.jsx($t,{size:16})},addonBefore:s.jsx("div",{className:"d-flex h-full pl-2 align-items-center product-search-box text-primary",children:s.jsx(Gt,{})}),hasPermission:!0})})})]}),G?s.jsx(bt,{...r,children:s.jsxs("form",{onSubmit:r.handleSubmit(Y,Ce),className:"d-flex flex-col gap-y-3 h-full text-primary",id:"listing-template-form",children:[s.jsxs("div",{className:"merch-dominator-selection-container d-flex flex-col gap-3",id:"mrdn-listing-selection-container",children:[s.jsx(ke,{control:o,name:"templateTitle",render:({field:{onChange:re,value:U}})=>{var ne;return s.jsx(qe,{uniqueId:"listing-template-title-input",type:"text",placeholder:"Enter template name",value:U,onChange:re,label:"Add New Template*",extraClass:"w-full",helperText:(ne=f==null?void 0:f.templateTitle)==null?void 0:ne.message})}}),s.jsx(ke,{name:"designTitle",control:o,render:({field:{onChange:re,value:U}})=>{var ne;return s.jsx(Zn,{label:"Design title *",onChange:re,value:U||"",id:"designTitle-input",placeholder:"Enter design title",isDisabled:!1,rows:1,maxLimit:60,extraClass:"flex-fill",helperText:(ne=f.designTitle)==null?void 0:ne.message,shouldValidate:!0})}}),s.jsx(ke,{name:"designBrand",control:o,render:({field:{onChange:re,value:U}})=>{var ne;return s.jsx(Zn,{label:"Brand *",onChange:re,value:U||"",id:"designBrand-input",placeholder:"Enter brand name",isDisabled:!1,rows:1,maxLimit:50,extraClass:"flex-fill",helperText:(ne=f.designBrand)==null?void 0:ne.message,shouldValidate:!0})}}),s.jsx(ke,{name:"designBullet1",control:o,render:({field:{onChange:re,value:U}})=>s.jsx(Zn,{label:"Feature Bullet 1",onChange:re,value:U||"",id:"designBullet1-input",placeholder:"Enter feature bullet 1",isDisabled:!1,rows:3,maxLimit:256,extraClass:"flex-fill"})}),s.jsx(ke,{name:"designBullet2",control:o,render:({field:{onChange:re,value:U}})=>s.jsx(Zn,{label:"Feature Bullet 2",onChange:re,value:U||"",id:"designTitle-input",placeholder:"Enter feature bullet 2",isDisabled:!1,rows:3,maxLimit:256,extraClass:"flex-fill"})}),s.jsx(ke,{name:"designDescription",control:o,render:({field:{onChange:re,value:U}})=>s.jsx(Zn,{label:"Description",onChange:re,value:U||"",id:"designDescription-input",placeholder:"Enter description (Minimum 75 characters)",isDisabled:!1,rows:3,maxLimit:2e3,extraClass:"flex-fill"})})]}),s.jsxs("div",{className:"d-flex w-full justify-end xl-md-pb-3 gap-x-2 pr-2 template-actions mt-auto",children:[s.jsx(fe,{buttonContent:"Save",isLoading:w,isDisabled:S||w,onclickHandler:()=>{},extraClass:"color-white border-none",buttonProps:{type:"submit"},tooltipId:"save-listing-selection"}),O.name&&s.jsx(fe,{buttonContent:"Cancel",onclickHandler:Z,isDisabled:w,tooltipId:"cancel-listing-selection",extraClass:"outlined-btn",buttonProps:{type:"button"}})]})]})}):s.jsx(bv,{templates:I,onEdit:ee,onDelete:q,isProcessing:S||w,handleApply:oe})]})})}),s.jsx(Je,{open:_.modalOpen,children:s.jsx(Yt,{deleteModalState:_,handleModalVisibility:q,handleDeleteAction:Q,modalId:"delete-listing-selection-template",deleteHeaderTitle:"Confirm template deletion",confirmationMessage:"Are you sure you want to delete this template?"})})]})},yv=({setOpen:e,handleTemplateSave:t})=>{const a=gt({defaultValues:{title:""},mode:"onChange",resolver:Lt(Tl)}),{control:n,getValues:r,formState:{errors:o}}=a,[l,i]=b.useState(!1),d=c=>{c.stopPropagation(),c.preventDefault(),e(!1)},f=async()=>{try{if(l)return;i(!0),await t(r().title),i(!1)}catch{}finally{i(!1)}};return s.jsx(Je,{open:!0,children:s.jsx(pt,{open:!0,handleClose:d,extraClass:"modal-add-action",uniqueId:"save-listing-template",modalTitle:"Save this listing as a template",bodySection:s.jsx("div",{className:"d-flex flex-col h-full",children:s.jsx(bt,{...a,children:s.jsxs("form",{className:"w-full d-flex flex-col gap-y-4 mb-0",onSubmit:a.handleSubmit(f),children:[s.jsx(ke,{name:"title",control:n,render:({field:{onChange:c,value:p}})=>{var u;return s.jsx(qe,{onChange:c,value:p,uniqueId:"save-listing-template-input",labelClass:"flex !font-medium",label:"Template Name*",inputClassName:"merch-input",placeHolder:"Enter template name",isDisabled:!1,hasPermission:!0,helperText:(u=o.title)==null?void 0:u.message})}}),s.jsxs("div",{className:"d-flex gap-2 w-full justify-end pr-2",children:[s.jsx(fe,{buttonContent:"Save Template",isLoading:l,isDisabled:l,onclickHandler:()=>{},extraClass:"color-white",buttonProps:{type:"submit"},tooltipId:"save-listing-template-action-tooltip"}),s.jsx(fe,{buttonContent:"Cancel",isDisabled:l,onclickHandler:d,extraClass:"outlined-btn",buttonProps:{type:"button"},tooltipId:"cancel-listing-template-action-tooltip"})]})]})})})})})},vv=[{marketPlace:"us",language:"English",label:"(Amazon.com, Amazon.co.uk)"},{marketPlace:"de",language:"German",label:"(Amazon.de)"},{marketPlace:"fr",language:"French",label:"(Amazon.fr)"},{marketPlace:"it",language:"Italian",label:"(Amazon.it)"},{marketPlace:"es",language:"Spanish",label:"(Amazon.es)"},{marketPlace:"jp",language:"Japanese",label:"(Amazon.co.jp)"}],wv=({methods:e,toggleAccordion:t,visibilityConfigs:a,globalUploaderSelectors:n})=>{const{maxTemplateCount:r}=Pt(C=>C),o=Tt(C=>C.setUpgradePlanModal),[l,i]=b.useState(!1),[d,f]=b.useState("us"),[c,p]=b.useState(!1),u=C=>{const N=e.getValues().listing[C];if(N.designTitle.trim().length<3){se("The title must be at least 3 characters long. Please enter a valid title.");return}if(N.designBrand.trim().length<3){se("The brand name must be at least 3 characters long. Please enter a valid brand name.");return}f(C),p(!0)},h=async C=>{try{const j=C.trim(),M=e.getValues().listing[d],I=nt.map(R=>M[R].replace(/\n/g," ")),S=await Qt({name:j,category:ya,template_metadata:{values:I}});if(S!=null&&S.name){De("Template","saved"),p(!1);return}if((S==null?void 0:S.status)===429){o({isVisible:!0,modalTitle:"Template Limit Reached",modalDescription:`You've reached your current plan's template limit of ${r}. Upgrade your plan to save and manage more templates.`});return}se(S==null?void 0:S.message)}catch{se("An error occurred while saving the template.")}},g=C=>{f(C),i(!l)},v=C=>{nt.forEach((j,N)=>{e.setValue(`listing.${d}.${j}`,C[N],{shouldValidate:!0})})};return s.jsxs(s.Fragment,{children:[s.jsx(bt,{...e,children:s.jsx("div",{className:"d-flex flex-col mrdn-ai-listing-creator-container text-primary",style:{paddingBottom:"1px"},children:vv.map((C,j)=>{const N=C.marketPlace;return a[N]?s.jsx(tv,{title:s.jsxs("div",{className:"d-flex align-items-center gap-2",children:[s.jsx("img",{className:"logo-mrdn",src:hu(`/img/${N}-flag.svg`),alt:`${N}-flag`}),s.jsx("span",{className:"font-semibold",children:C.language}),s.jsx("span",{style:{fontSize:"1rem",color:"var(--primary-border)"},children:C.label})]}),content:s.jsx(hv,{marketPlace:N,methods:e,handleModalVisibility:g,saveListingTemplate:u,globalUploaderSelectors:n}),accordionCallback:M=>t(M,N),extraClass:`mrdn-${N}-marketplace mrdn-listing-container`},`${N}-${j}-listing`):null})})}),l&&s.jsx(xv,{handleModalVisibility:i,applyTemplate:v,allListingMethods:e,currentMarketPlace:d}),c&&s.jsx(yv,{setOpen:p,handleTemplateSave:h})]})},Gi="mrdn-all-listing-text",Cv=({methods:e})=>{const[t,a]=b.useState(""),[n,r]=b.useState(""),[o,l]=b.useState(!1),i=d=>{if(o)return;l(!0);const{listing:f}=e.getValues();switch(d){case"replace":if(!t.trim()){l(!1);return}for(const p in f){const u=f[p];nt.forEach(h=>{e.setValue(`listing.${p}.${h}`,or(u[h],t,n),{shouldValidate:!0})})}break;case"copy":const c={};for(const p in f)c[p]={...f[p]},is.forEach(u=>{delete c[p][u]});es(Gi,c),De("All listings have been copied successfully!");break;case"paste":try{const p=Jr(Gi);if(!p)return;const{listing:u}=e.getValues();for(const h in u)os.forEach(g=>{var v;e.setValue(`listing.${h}.${g}`,((v=p[h])==null?void 0:v[g])||Mn[g],{shouldValidate:!0})});De("All listings have been pasted successfully!")}catch{}finally{l(!1)}break;case"reset":e.reset(),e.clearErrors(),e.setValue("resetKey",new Date().getTime().toString());break}l(!1)};return s.jsxs("div",{className:"d-flex gap-4 w-full",children:[s.jsx(qe,{onChange:d=>a(d.target.value),value:t,uniqueId:"global-search-text_input",placeHolder:"Search for",inputClassName:"merch-input h-31px",labelClass:"flex !font-medium",extraClass:"flex-fill",hasPermission:!0}),s.jsx(qe,{onChange:d=>r(d.target.value),value:n,uniqueId:"global-replace-text_input",placeHolder:"Replace with",inputClassName:"merch-input h-31px",labelClass:"flex !font-medium",extraClass:"flex-fill",hasPermission:!0}),s.jsx(fe,{buttonContent:"Replace All",onclickHandler:()=>i("replace"),buttonIcon:s.jsx(Ed,{size:14}),tooltipId:"replace-text-action",isDisabled:o}),s.jsx(fe,{buttonContent:"Copy All",onclickHandler:()=>i("copy"),buttonIcon:s.jsx(Ts,{}),extraClass:"color-white ",tooltipId:"copy-text-action",isDisabled:o}),s.jsx(fe,{buttonContent:"Paste All",onclickHandler:()=>i("paste"),buttonIcon:s.jsx(ts,{}),extraClass:"color-white ",tooltipId:"paste-text-action",isDisabled:o}),s.jsx(fe,{buttonContent:"Clear All",onclickHandler:()=>i("reset"),buttonIcon:s.jsx($t,{}),extraClass:"color-white ",tooltipId:"clear-text-action",isDisabled:o})]})},Tv=b.memo(Cv),jv=mu.reduce((e,t)=>(e[t.id]={bannedWords:{},recommendedWords:{}},e),{}),Yi=({label:e,fieldName:t,marketPlaceId:a,onChangeHandler:n,control:r,extraClass:o=""})=>s.jsxs("div",{className:`banned-tags recommended-banned-tags d-flex align-items-center justify-between gap-2${o&&` ${o}`}`,children:[s.jsx("div",{className:"d-flex align-items-center",children:s.jsx("span",{children:e})}),s.jsx("div",{className:"d-flex align-items-center gap-2",children:s.jsx(ke,{name:t,control:r,render:({field:{onChange:l,value:i}})=>s.jsx(wr,{name:t,checked:i,onChangeHandler:d=>{const f=d.target.checked;l(f),n(a,f?"check":"uncheck",[e])},extraClass:"small-switch",hasPermission:!0})})})]}),Sv=({keyword:e,itemId:t,marketPlaceId:a,onEdit:n,onDelete:r,extraClass:o=""})=>{const l=b.useCallback(d=>{r(d,t,e)},[r]),i=b.useCallback(d=>{n(d,t,e,a)},[n]);return s.jsxs("div",{className:`d-flex align-items-center ${o}`,children:[s.jsx("button",{className:"self-banned-keywords-item-btn text-primary border-none bg-main d-flex align-items-center justify-center i-p-0 cursor-pointer",onClick:i,children:s.jsx(Wc,{size:16})}),s.jsx("button",{className:"banned-keyword-delete-btn border-none bg-main color-danger d-flex align-items-center justify-center i-p-0 cursor-pointer",onClick:l,children:s.jsx(sl,{size:16})})]})},Ev=({handleModalVisibility:e,hasPermission:t})=>{var V,ce;const a=b.useRef(null),n=b.useRef(null),r=gt({defaultValues:{keywords:{...jv},bannedWords:{marketPlace:Br[0].value,keywords:[],action:fa.INSERT,id:null},editWord:""},mode:"onChange"}),{control:o,setError:l,clearErrors:i,watch:d,getValues:f,setValue:c,formState:{errors:p}}=r,u=b.useRef(null),[h,g]=b.useState([...gu]),[v,C]=b.useState([]),[j,N]=b.useState(""),[M,I]=b.useState(!0),[F,S]=b.useState("desc"),[R,w]=b.useState(!1),[m,T]=b.useState(!1),[E,O]=b.useState({modalOpen:!1,id:0,processing:!1,keyword:""}),[A,_]=b.useState({modalOpen:!1,processing:!1,keyword:"",id:0,marketPlaceId:0}),[D,x]=b.useState(!1),G=d("bannedWords.action")===fa.INSERT,$=b.useCallback((k,L,B)=>{var X;const W=L.trim().toLowerCase();let z=[];W?z=k.map(ae=>({...ae,bannedKeywords:ae.bannedKeywords.filter(me=>me.toLowerCase().includes(W)),recommendedBannedKeywords:ae.recommendedBannedKeywords.filter(me=>me.toLowerCase().includes(W))})):z=k;const te=z.some(ae=>ae.bannedKeywords.length||ae.recommendedBannedKeywords.length);return(X=n.current)==null||X.classList.toggle("d-flex",!te),B&&z.sort((ae,me)=>B==="asc"?ae.marketPlace.localeCompare(me.marketPlace):me.marketPlace.localeCompare(ae.marketPlace)),z},[]),K=async()=>{I(!0);const k=await Cp();if(k!=null&&k.success){const L=np((k==null?void 0:k.result)||[]),B=L.reduce((W,z)=>{const te=new Set(z.uncheckedTags);return W[z.marketPlaceId]={bannedWords:Object.fromEntries(z.bannedKeywords.map(X=>[X,!te.has(X)])),recommendedWords:Object.fromEntries(z.recommendedBannedKeywords.map(X=>[X,!te.has(X)]))},W},{});g($(L,j,F)),C(L),c("keywords",B)}I(!1)};b.useEffect(()=>{K()},[]),b.useEffect(()=>{M||(u.current&&clearTimeout(u.current),u.current=setTimeout(()=>{g($(v,j,F))},500))},[j]);const J=(k,L)=>d(`keywords.${k}.${L}`)||{},Y=(k,L)=>{const B=J(k,L);return Object.values(B).length&&Object.values(B).every(Boolean)},ee=b.useCallback((k,L)=>{const B=J(k,L),W=Object.values(B).every(Boolean),z=Object.fromEntries(Object.keys(B).map(te=>[te,!W]));c(`keywords.${k}.${L}`,z),U(k,W?"uncheck":"check",Object.keys(B))},[d,c]),Z=()=>{g(k=>{const L=F?F==="desc"?"asc":"desc":"asc",B=[...k].sort((W,z)=>L==="asc"?W.marketPlace.localeCompare(z.marketPlace):z.marketPlace.localeCompare(W.marketPlace));return S(L),B})},q=b.useCallback(k=>{k==null||k.stopPropagation(),k==null||k.preventDefault(),w(!1),c("bannedWords",{keywords:[],marketPlace:Br[0].value,action:fa.INSERT,id:null}),i()},[]),Q=async()=>{if(!m)try{const{keywords:k,marketPlace:L,action:B,id:W}=f("bannedWords");if(!k.length){l("bannedWords.keywords",{message:"Please enter keywords"});return}T(!0);const z=B===fa.UPDATE,te={marketplace:+L,banned_keywords:k.join(","),flag:z?2:1};z&&(te.banned_keyword_id=W);const X=await Tp(te);if(X!=null&&X.success){w(!1),q(),await K();return}se((X==null?void 0:X.message)||"Something went wrong, please try again.")}catch{}finally{T(!1)}},oe=b.useCallback((k,L,B)=>{c("bannedWords",{marketPlace:k.toString(),keywords:L,action:fa.UPDATE,id:B}),w(!0)},[]),de=b.useCallback((k,L,B)=>{E.processing||(k.stopPropagation(),k.preventDefault(),O(W=>({...W,modalOpen:!!L,id:L??0,keyword:B??""})))},[E.processing]),pe=b.useCallback((k,L,B,W)=>{A.processing||(k.stopPropagation(),k.preventDefault(),_(z=>({...z,modalOpen:!!L,id:L??0,keyword:B??"",marketPlaceId:W??0})),c("editWord",B??""))},[A.processing]),Ce=async()=>{if(A.processing)return;_(te=>({...te,processing:!0}));const k=/^[a-zA-Z0-9 -]*$/,{id:L,keyword:B,marketPlaceId:W}=A,z=f("editWord");if(!k.test(z)){x(!0),_(te=>({...te,processing:!1}));return}try{const te=await Sp({old_keyword:B,id:L,new_keyword:z,marketplace:W});if(te!=null&&te.success){_(X=>({...X,modalOpen:!1,id:0,keyword:"",marketPlaceId:0,processing:!1})),await K();return}se((te==null?void 0:te.message)||"Something went wrong, please try again.")}catch{}finally{_(te=>({...te,processing:!1,id:0,keyword:"",modalOpen:!1,marketPlaceId:0}))}},re=async()=>{if(!E.processing){O(k=>({...k,processing:!0}));try{const{id:k,keyword:L}=E,B=L?await Ep({keyword:L,id:k}):await jp(k);if(B!=null&&B.success){O(W=>({...W,modalOpen:!1,id:0,keyword:"",processing:!1})),await K();return}se((B==null?void 0:B.message)||"Something went wrong, please try again.")}catch{}finally{O(k=>({...k,processing:!1,id:0,keyword:"",modalOpen:!1}))}}},U=async(k,L,B)=>{I(!0);const W=await jl({marketplace:k,status:L==="check"?1:2,tags:B});W!=null&&W.success?De((W==null?void 0:W.message)||"Keywords Disabled successfully."):se((W==null?void 0:W.message)||"Something went wrong, please try again."),I(!1)},ne=()=>{x(!1)},H=b.useCallback(k=>{const L=/^[a-zA-Z0-9 -]*$/,B=k.filter(W=>!L.test(W));if(B.length>0){const z=f("bannedWords.keywords").filter(te=>!B.includes(te));c("bannedWords.keywords",z),x(!0)}},[]);return s.jsx(Je,{open:!0,children:s.jsx(pt,{open:!0,width:"80vw",handleClose:k=>{k.stopPropagation(),e(!1)},uniqueId:"banned-keywords",modalTitle:"Banned Keywords",bodySection:s.jsxs(bt,{...r,children:[s.jsx("div",{className:"bg-main text-primary",children:s.jsxs("div",{className:"d-flex flex-col gap-4",children:[s.jsxs("div",{className:"d-flex justify-between align-items-center",children:[s.jsx(qe,{onChange:k=>N(k.target.value),value:j,uniqueId:"banned-word-search_input",labelClass:"flex !font-medium",inputClassName:"merch-input",placeHolder:"Search here...",isDisabled:M,allowClear:{clearIcon:s.jsx($t,{size:16})},addonBefore:s.jsx("div",{className:"d-flex h-full pl-2 align-items-center text-primary",children:s.jsx(Gt,{})}),hasPermission:!0}),s.jsx(fe,{buttonContent:"Add Banned Keywords",onclickHandler:()=>{M||w(!0)},tooltipId:"add-banned-keywords-action"})]}),s.jsx("div",{className:"position-relative",children:s.jsxs("table",{id:"banned-keyword-table",className:"table mb-0",role:"grid","aria-describedby":"banned-keyword-table_info",children:[s.jsxs("colgroup",{children:[s.jsx("col",{style:{width:"20%"}}),s.jsx("col",{style:{width:"40%"}}),s.jsx("col",{style:{width:"40%"}}),s.jsx("col",{style:{width:"25%"}})]}),s.jsx("thead",{style:{background:"var(--text-main-color)"},className:"color-white",children:s.jsxs("tr",{role:"row",children:[s.jsx("th",{className:"py-2",children:s.jsxs("div",{className:"d-flex gap-2 cursor-pointer align-items-center",onClick:Z,children:["MarketPlace",F?F==="asc"?s.jsx(zc,{}):s.jsx(Kc,{}):s.jsx(Vc,{})]})}),s.jsx("th",{className:"py-2",children:"Your Banned Keywords"}),s.jsx("th",{className:"py-2",children:"Recommended Banned Keywords"}),s.jsx("th",{className:"py-2",children:"Action"})]})}),s.jsx("tbody",{children:h.map(k=>{const L=k.bannedKeywords.length,B=k.recommendedBannedKeywords.length;return!B&&!L?null:s.jsxs("tr",{className:"secondary-border-bottom",children:[s.jsx("td",{className:"vertical-center font-semibold",children:k.marketPlace}),s.jsxs("td",{children:[!!L&&s.jsx("div",{className:"table-actions d-flex justify-end mb-2",children:s.jsx(fe,{buttonContent:Y(k.marketPlaceId,"bannedWords")?"Uncheck All":"Check All",onclickHandler:()=>ee(k.marketPlaceId,"bannedWords")})}),s.jsx("div",{className:"md-recommended_column d-flex flex-col gap-2 scrollbar-style pr-1",style:{maxHeight:"226px",overflowY:"auto"},children:k.bannedKeywords.map(W=>W?s.jsxs("div",{className:"d-flex secondary-border border-r-1 i-px-2 gap-3 i-py-2",children:[s.jsx(Yi,{control:o,label:W,marketPlaceId:k.marketPlaceId,onChangeHandler:U,fieldName:`keywords.${k.marketPlaceId}.bannedWords.${W}`,extraClass:"flex-fill"}),s.jsx(Sv,{keyword:W,itemId:k.id,marketPlaceId:k.marketPlaceId,onEdit:pe,onDelete:de,extraClass:"gap-3"})]},W):null)})]}),s.jsxs("td",{children:[!!B&&s.jsx("div",{className:"table-actions d-flex justify-end mb-2",children:s.jsx(fe,{buttonContent:Y(k.marketPlaceId,"recommendedWords")?"Uncheck All":"Check All",onclickHandler:()=>ee(k.marketPlaceId,"recommendedWords")})}),s.jsx("div",{className:"md-recommended_column d-flex flex-col gap-2 pr-1 scrollbar-style",style:{maxHeight:"226px",overflowY:"auto"},children:k.recommendedBannedKeywords.map(W=>W?s.jsx("div",{className:"border-r-1 secondary-border i-px-2 i-py-2",children:s.jsx(Yi,{control:o,marketPlaceId:k.marketPlaceId,onChangeHandler:U,fieldName:`keywords.${k.marketPlaceId}.recommendedWords.${W}`,label:W})},W):null)})]}),s.jsx("td",{className:"vertical-center",children:!!L&&s.jsxs("div",{className:"d-flex gap-3",children:[s.jsx("button",{className:"banned-keywords-item-btn text-primary border-none bg-main i-p-0 cursor-pointer",onClick:()=>oe(k.marketPlaceId,k.bannedKeywords,k.id),children:s.jsx(qc,{size:18})}),s.jsx("button",{className:"banned-keywords-delete-btn color-danger border-none bg-main i-p-0 cursor-pointer",onClick:W=>de(W,k.id),children:s.jsx(sl,{size:20})})]})})]},k.marketPlace)})})]})})]})}),M&&s.jsx("div",{className:"position-absolute d-flex left-0 top-0 z-index-1 pointer-events-auto user-events-none w-full h-full bg-spinner-shadow",children:s.jsx(na,{extraClass:"z-index-1",size:30})}),s.jsx("div",{ref:n,className:"d-none py-4 justify-center",children:"No matching records found"}),t&&s.jsx(Je,{open:R,children:s.jsx(pt,{open:R,handleClose:q,uniqueId:"add-banned-keywords",extraClass:"modal-add-action",modalTitle:"Add Banned Keywords",bodySection:s.jsxs("div",{className:"w-full d-flex flex-col gap-y-4",ref:a,children:[s.jsx(ke,{name:"bannedWords.marketPlace",control:o,render:({field:{onChange:k,value:L}})=>s.jsx(rl,{fieldName:"bannedKeywordMarketplace",options:Br,value:L,onChange:({value:B})=>{G&&k(B)},placeholder:"Marketplace",label:"Product Type",isDisabled:!t||!G})}),s.jsxs("div",{className:"w-full d-flex flex-col",children:[s.jsx(ke,{name:"bannedWords.keywords",control:o,render:({field:{onChange:k,value:L}})=>s.jsx(oc,{label:"Banned Keywords",uniqueId:"add-keywords",placeHolder:"Enter Keywords",fieldName:"bannedWords.keywords",value:L,onChange:k,setValue:c,hasPermission:t,allowPinning:!1,extraChangeHandler:H})}),s.jsx(At,{message:(ce=(V=p==null?void 0:p.bannedWords)==null?void 0:V.keywords)==null?void 0:ce.message})]}),s.jsxs("div",{className:"d-flex gap-2 w-full justify-end pr-2",children:[s.jsx(fe,{buttonContent:"Save",isLoading:m,isDisabled:m,onclickHandler:Q,extraClass:"color-white",buttonProps:{type:"button"},tooltipId:"add-banned-word-tooltip"}),s.jsx(fe,{buttonContent:"Cancel",isDisabled:m,onclickHandler:q,extraClass:"outlined-btn",buttonProps:{type:"button"},tooltipId:"cancel-banned-word-tooltip"})]})]}),footerSection:null})}),A.modalOpen&&s.jsx(pt,{open:A.modalOpen,handleClose:pe,uniqueId:"update-banned-keyword",extraClass:"modal-add-action",modalTitle:"Edit Banned Keywords",bodySection:s.jsxs("div",{className:"w-full d-flex flex-col gap-y-4",children:[s.jsx(ke,{name:"editWord",control:o,render:({field:{onChange:k,value:L}})=>s.jsx(qe,{onChange:k,value:L,uniqueId:"banned-edit-input",labelClass:"flex !font-medium",label:"Banned Keyword",inputClassName:"merch-input",placeHolder:"Search here...",isDisabled:!1,hasPermission:!0})}),s.jsxs("div",{className:"d-flex gap-2 w-full justify-end pr-2",children:[s.jsx(fe,{buttonContent:"Save",isLoading:A.processing,isDisabled:A.processing,onclickHandler:Ce,extraClass:"color-white",buttonProps:{type:"button"},tooltipId:"update-banned-word-tooltip"}),s.jsx(fe,{buttonContent:"Cancel",isDisabled:A.processing,onclickHandler:pe,extraClass:"outlined-btn",buttonProps:{type:"button"},tooltipId:"cancel-single-banned-word-tooltip"})]})]}),footerSection:null}),s.jsx(Yt,{deleteModalState:E,handleModalVisibility:de,handleDeleteAction:re,modalId:"delete-bannedWord-modal",deleteHeaderTitle:"Confirm Banned Keywords Deletion",confirmationMessage:"Are you sure you want to delete?"}),s.jsx(_s,{id:"special-characters-alert",modalTitle:"Attention Required",isOpen:D,alertIcon:s.jsx(js,{size:70}),onClose:ne,errorMessage:"Not Allowed",errorTitle:"Special characters are not allowed"})]})})})},_v=({selectors:e,emitter:t})=>{const{updateExpandConfigs:a,toggleExpandConfigs:n,isLanguageExpanded:r,visibilityConfigs:o}=fl(g=>g),l=b.useRef(!1),[i,d]=b.useState(!1),f=gt({defaultValues:{...bu},mode:"onChange",resolver:Lt(bp)}),c=(g,v)=>{a(g,v)},p=g=>{var v;if(!l.current){l.current=!0;const{listing:C}=f.getValues();if(g){const j=document.querySelector("#mrdn-product-listing-container .animated-accordion__section"),N=(v=j==null?void 0:j.classList.item(1))==null?void 0:v.split("-").at(-1),M=C[N];nt.forEach(I=>{f.setValue(`listing.us.${I}`,(M==null?void 0:M[I])||"")})}else{const j=C.us;for(const N in C)N!=="us"&&nt.forEach(M=>{f.setValue(`listing.${N}.${M}`,j[M])})}}},u=g=>{g.payload.forEach(C=>{const j=C.language;C.fields.forEach(N=>{f.setValue(`listing.${j}.${N.id}`,N.value||"",{shouldValidate:!0})})})};b.useEffect(()=>(t.addEventListener(ls,u),()=>{t.removeEventListener(ls,u)}),[]),b.useEffect(()=>{const g=document.querySelectorAll(e==null?void 0:e.translation_options),v=document.querySelector("#languages-toggle-action");v&&(g.forEach((C,j)=>{!j&&C.checked?v.style.setProperty("display","none","important"):j&&C.checked&&(v.style.display="flex"),C.addEventListener("change",()=>{p(!j)})}),wl(e.listing_marketplaces,e.listing_marketplace_header).then(C=>{const{listing:j}=f.getValues();for(const N in j)C[N]&&nt.forEach(M=>{f.setValue(`listing.${N}.${M}`,C[N][M])})}))},[]);const h=()=>{document.querySelectorAll(`.mrdn-listing-container .animated-accordion__header${r?".active":""}`).forEach(C=>{C==null||C.click()});const v=us(e.listing_marketplaces,e.listing_marketplace_header);n(r,v)};return s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"d-flex flex-col gap-4",children:[s.jsxs("div",{className:"d-flex flex-col gap-4 secondary-border bg-main text-primary border-r-1 i-p-4",style:{boxShadow:"0px 0px 4px 0px rgba(0, 0, 0, 0.25)"},id:"product-global-listing-container",children:[s.jsx("div",{className:"text-base font-semibold",children:"Product details"}),s.jsx(Tv,{methods:f})]}),s.jsxs("div",{className:"font-semibold d-flex",style:{fontSize:"1.2rem"},children:[s.jsx("span",{className:"text-main mr-2",children:"Merch Dominator"}),s.jsx("span",{children:"AI Listing Creator"}),s.jsx(fe,{buttonContent:"Banned Keywords",onclickHandler:()=>d(!0),buttonIcon:s.jsx(Gc,{size:16}),tooltipId:"banned-keyword-action",extraClass:"ml-auto"})]}),s.jsx("button",{id:"languages-toggle-action",className:"d-flex ml-auto text-main border-none cursor-pointer bg-inherit",onClick:h,children:r?"Collapse Languages":"Expand All Languages"}),s.jsx(wv,{methods:f,toggleAccordion:c,visibilityConfigs:o,globalUploaderSelectors:e.globalUploader})]}),i&&s.jsx(Ev,{handleModalVisibility:d,hasPermission:!0})]})},ic=b.memo(({product_list:e})=>s.jsxs("div",{className:"d-flex flex-col gap-2 mrdn-card-container",children:[s.jsx("div",{className:"text-base font-semibold",children:"Product Selection"}),s.jsx("div",{className:"d-flex flex-wrap gap-1 text-capitalize cursor-default text-13px",children:e==null?void 0:e.map(t=>s.jsx("div",{className:"d-flex keyword-section cursor-default",children:Fs(t)},t))})]})),lc=b.memo(({product_metadata:e,groupedProducts:t})=>{const a=b.useCallback((n,r,o)=>n==null?void 0:n.map((l,i)=>{if(!l)return null;const d=r[i];return s.jsxs("div",{className:"d-flex flex-col gap-2",children:[s.jsxs("span",{className:"text-uppercase",children:[d,s.jsxs("span",{className:"text-lowercase text-13px text-faded",children:[" (",oa[d],")"]})]}),s.jsxs("div",{className:"d-flex align-items-center",children:[s.jsx("span",{style:{fontWeight:600},children:cl[d]})," ",l]})]},`${o}-${i}`)}),[]);return s.jsxs("div",{className:"d-flex flex-col gap-2 mrdn-card-container",children:[s.jsx("div",{className:"text-base font-semibold",children:"Product Metadata"}),e==null?void 0:e.map((n,r)=>{var d,f,c,p,u,h,g,v,C;const o=n==null?void 0:n.product_id,l=t==null?void 0:t[o],i=_a(o);return s.jsxs("div",{className:"d-flex flex-col gap-2 mrdn-sub-card-container",children:[s.jsxs("header",{children:[r+1,")"," ",ul(o==null?void 0:o.replace(/_/g," "))]}),s.jsxs("section",{className:"d-flex flex-col gap-2 text-13px",children:[!!((d=n==null?void 0:n.fit_types)!=null&&d.length)&&s.jsxs("div",{className:"d-flex gap-x-2 align-items-center",children:[s.jsx("div",{className:"font-semibold",children:"Fit Types:"}),n.fit_types.map(j=>s.jsx("div",{className:"d-flex keyword-section cursor-default",children:j},j))]}),!!((f=n==null?void 0:n.colors)!=null&&f.length)&&s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"font-semibold",children:"Colors:"}),s.jsx("div",{className:"d-flex flex-wrap gap-4",children:n.colors.map(j=>s.jsx("div",{className:`merch-color-checkbox-container mrdn-color-${j}`,style:i?{backgroundColor:j}:{}},j))})]}),s.jsx("div",{className:"font-semibold",children:"Prices:"}),s.jsx("div",{className:"gap-4 text-13px",style:{display:"grid",gridTemplateColumns:"repeat(7, 1fr)"},children:a((n==null?void 0:n.prices)||[],l,r)}),((c=n==null?void 0:n.scale)==null?void 0:c.percent)&&s.jsxs("div",{className:"d-flex gap-x-2 align-items-center",children:[((p=n.scale)==null?void 0:p.tumblerTab)&&s.jsxs("div",{className:"d-flex gap-2",children:[s.jsx("span",{className:"font-semibold",children:"Selected Side:"})," ",Bt[(u=n.scale)==null?void 0:u.tumblerTab]]}),((h=n.scale)==null?void 0:h.usePattern)&&s.jsxs("div",{className:"d-flex gap-2",children:[s.jsx("span",{className:"font-semibold",children:"Pattern Mode Enabled:"})," ","Yes"]}),s.jsxs("div",{className:"d-flex gap-2",children:[s.jsx("span",{className:"font-semibold",children:"Scale:"})," ",n.scale.percent,"%"]}),((g=n.scale)==null?void 0:g.printType)&&s.jsxs("div",{className:"d-flex gap-2",children:[s.jsx("span",{className:"font-semibold",children:"Print:"})," ",(v=n.scale)==null?void 0:v.printType]}),((C=n.scale)==null?void 0:C.tumblerScale)&&s.jsxs("div",{className:"d-flex gap-2",children:[s.jsx("span",{className:"font-semibold",children:"Tumbler Scale:"})," ",n.scale.tumblerScale,"%"]})]})]})]},o)})]})}),cc=b.memo(({listing:e})=>s.jsxs("div",{className:"d-flex flex-col gap-2 mrdn-card-container",children:[s.jsx("div",{className:"text-base font-semibold",children:"Product details"}),e.map(t=>t?s.jsxs("div",{className:"d-flex flex-col gap-2 mrdn-sub-card-container",children:[s.jsx("span",{className:"text-15px pb-2 secondary-border-bottom",children:xu[t.language]}),nt.map((a,n)=>{var o,l;const r=(l=(o=t==null?void 0:t.fields)==null?void 0:o[n])==null?void 0:l.value;return r?s.jsxs("div",{className:"d-flex gap-2 text-13px",children:[s.jsxs("span",{style:{minWidth:"140px",fontWeight:"600"},children:[Ps[a]," :"]}),s.jsx("div",{className:"d-flex align-items-center gap-2",children:r})]},`${a}-${t.language}`):null})]},t.language):null)]})),dc=b.memo(({template:e,templateName:t,closeModal:a})=>{if(!e)return null;const{product_list:n,product_metadata:r,listing:o}=e,l=Ju(n),i=b.useCallback(f=>{f.stopPropagation(),a()},[a]),d=b.useMemo(()=>s.jsxs("div",{className:"d-flex flex-col gap-4",children:[s.jsxs("div",{className:"text-base font-semibold",children:["Template Title: ",s.jsx("span",{className:"font-medium",children:t})]}),s.jsx(ic,{product_list:n}),s.jsx(lc,{product_metadata:r,groupedProducts:l}),s.jsx(cc,{listing:o})]}),[t,n,r,o]);return s.jsx(Je,{open:!0,children:s.jsx(pt,{open:!0,width:"65vw",handleClose:i,uniqueId:"master-template-overview",modalTitle:"Merch Dominator Master Template Overview",bodySection:d})})});dc.displayName="MasterTemplateOverviewModal";ic.displayName="ProductSelection";lc.displayName="ProductMetadata";cc.displayName="ProductDetails";const Xi={fit_types:!0,colors:!0,prices:!0,scale:!0,tumblerEditEnabled:!0,listing:!0},Av={fit_types:"Fit Types",colors:"Colors",prices:"Prices",scale:"Scale",tumblerEditEnabled:"Tumbler Edit & Upload",listing:"Listing"},kv=({setOpen:e,applyTemplate:t})=>{var q;const a=gt({defaultValues:{...Xi,searchText:"",selection:"",publishType:cs[0].value,marketPlace:[],productType:[],tabsCount:1},mode:"onChange"}),{control:n,getValues:r,setError:o,formState:{errors:l},setValue:i,clearErrors:d}=a,{maxTemplateCount:f,hasUnlimitedAccess:c}=Pt(Q=>Q),p=Tt(Q=>Q.setUpgradePlanModal),[u,h]=b.useState([]),[g,v]=b.useState([]),[C,j]=b.useState(!0),[N,M]=b.useState(!1),[I,F]=b.useState(!1),[S,R]=b.useState({modalOpen:!1,template:null,templateName:""}),[w,m]=b.useState({modalOpen:!1,template:null,processing:!1}),T=b.useCallback(async()=>{try{const Q=await Xt(lr);Q!=null&&Q.templates&&h(Q==null?void 0:Q.templates);const{"merch.globalTemplateFilters":oe}=await at.getStoreValue(["merch.globalTemplateFilters"]);oe!=null&&oe.productType&&a.setValue("productType",oe.productType),oe!=null&&oe.marketPlace&&a.setValue("marketPlace",oe.marketPlace),j(!1)}catch{}},[]);b.useEffect(()=>{T()},[]);const E=b.useCallback(Q=>{var re;if(!Q)return v([]);const{productType:oe,marketPlace:de,searchText:pe}=r(),Ce=((re=pe==null?void 0:pe.trim())==null?void 0:re.toLowerCase())||"";v(Q.filter(U=>{const{product_list:ne}=Ge(U.template_metadata);let H=!1,V=!1;oe.length&&(H=oe.some(k=>ne==null?void 0:ne.some(L=>L.startsWith(k)))),de.length&&(V=de.some(k=>ne==null?void 0:ne.some(L=>L.endsWith(k))));const ce=Ce?U.name.toLowerCase().includes(Ce):!0;return oe.length||de.length?(H||V)&&ce:ce}))},[r]);b.useEffect(()=>{C||E(u)},[C,u]);const O=Q=>{if(N)return;M(!0);const{fit_types:oe,colors:de,prices:pe,listing:Ce}=r();if(!oe&&!de&&!pe&&!Ce){o("root",{type:"manual",message:"Please select at least one option to apply template."}),M(!1);return}t(Q,r()).then(()=>{M(!1)}),e(!1)},A=b.useCallback((Q,oe)=>{w.processing||m(de=>({...de,modalOpen:!de.modalOpen,template:oe||null}))},[w.processing]),_=async()=>{if(!w.processing){m(Q=>({...Q,processing:!0}));try{const{template:Q}=w,oe=await fn(Q);if(oe!=null&&oe.success){De("Template","deleted"),await T();return}await T(),se(oe==null?void 0:oe.message)}catch{se("An error occurred while deleting the template")}finally{m(Q=>({...Q,modalOpen:!1,template:null,processing:!1}))}}},D=Q=>{const oe=r("tabsCount");for(let de=0;de<oe;de++)window.open(`https://merch.amazon.com/designs/new?mrdn_template_id=${Q}`,"_blank","noopener, noreferrer")},x=async()=>{if(I){se("Filters are already being applied. Please wait.");return}F(!0);const{productType:Q,marketPlace:oe}=r(),de={productType:Q,marketPlace:oe};await at.setStoreValue({"merch.globalTemplateFilters":de}),E(u),F(!1)},y=async()=>{if(I){se("Filters are already being applied. Please wait.");return}F(!0),a.setValue("productType",[]),a.setValue("marketPlace",[]),a.setValue("searchText",""),await at.removeStoreValue(["merch.globalTemplateFilters"]),E(u),F(!1)},G=b.useCallback((Q,oe)=>{R({modalOpen:!0,template:Q,templateName:oe})},[]),$=b.useCallback(Q=>{a.setValue("tabsCount",+wt(Q.target.value,1,10))},[]),K=b.useCallback(Q=>{Q.stopPropagation(),e(!1)},[e]),J=b.useMemo(()=>s.jsxs(s.Fragment,{children:[s.jsx(ke,{name:"searchText",control:n,render:({field:{onChange:Q,value:oe}})=>s.jsx(qe,{onChange:Q,value:oe,uniqueId:"mater-template-search_input",labelClass:"flex !font-medium",inputClassName:"merch-input",label:"Search Template",placeHolder:"Search by title",isDisabled:!1,allowClear:{clearIcon:s.jsx($t,{size:16})},addonBefore:s.jsx("div",{className:"d-flex h-full pl-2 align-items-center product-search-box text-primary",children:s.jsx(Gt,{})}),hasPermission:!0,extraClass:"w-full"})}),s.jsx(ke,{control:n,name:"marketPlace",render:({field:{onChange:Q,value:oe}})=>s.jsx(Io,{options:yu,title:"Select Marketplace",fieldKey:"marketPlace",selectedOptions:oe,handleSelectChange:Q,extraClass:"merch-scrollable-multiselect w-full"})}),s.jsx(ke,{control:n,name:"productType",render:({field:{onChange:Q,value:oe}})=>s.jsx(Io,{options:vu,title:"Select Product Category",fieldKey:"productType",selectedOptions:oe,handleSelectChange:Q,extraClass:"merch-scrollable-multiselect w-full"})})]}),[n]),Y=b.useCallback(Q=>{if(!c){p({isVisible:!0,modalTitle:"Upgrade!",modalDescription:"Upgrade to a higher plan to unlock all publishing options"});return}i("publishType",Q)},[c]),ee=b.useMemo(()=>s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"d-flex flex-col gap-y-2",children:[s.jsx("span",{className:"font-semibold",children:"Apply Template to"}),s.jsx("div",{className:"d-flex gap-4 text-capitalize",children:Object.keys(Xi).map(Q=>s.jsx(ke,{name:Q,control:n,render:({field:{onChange:oe,value:de}})=>s.jsx(Jn,{checked:de,label:Av[Q],onChange:pe=>{oe(pe.target.checked),d("root")},extraClass:"listing-options-checkbox"})},Q))})]}),s.jsx(ke,{control:n,name:"publishType",render:({field:{value:Q}})=>s.jsx(Sl,{label:s.jsxs(s.Fragment,{children:["Publish options",s.jsx(vr,{extraClass:"i-p-0 d-flex-important align-items-center",children:s.jsx(ws,{size:14}),isButtonType:!1,tooltipChildren:s.jsx("div",{className:"d-flex flex-col",children:s.jsxs("ul",{style:{color:"var(--color-white) !important",marginBlock:"0.25rem"},children:[s.jsxs("li",{className:"text-left",children:[s.jsx("strong",{children:"None: "})," Apply template only"]}),s.jsxs("li",{className:"text-left",children:[s.jsx("strong",{children:"Save draft: "})," Apply template and save listing as draft"]}),s.jsxs("li",{className:"text-left",children:[s.jsx("strong",{children:"Publish: "})," Apply template and publish listing"]})]})}),tooltipId:"mrdn-publish-options"})]}),name:"publishType",value:Q,onChange:Y,options:cs,extraClass:"gap-0 mrdn-publish-options"})})]}),[n,Y]),Z=b.useCallback(()=>{R(Q=>({...Q,modalOpen:!1,template:null,templateName:""}))},[R]);return s.jsxs(s.Fragment,{children:[s.jsx(Je,{open:!0,children:s.jsx(pt,{open:!0,width:"85vw",handleClose:K,uniqueId:"master-templates",modalTitle:"Merch Dominator Templates",modalTitleChildren:c?null:s.jsx(un,{showTemplateLimit:!0,templateLimit:f,buttonId:"fit-types-templates-upgrade-button"}),bodySection:s.jsxs("div",{className:"d-flex flex-col h-full",children:[s.jsxs(bt,{...a,children:[s.jsxs("div",{className:"d-flex gap-2 position-relative",children:[J,s.jsx(fe,{buttonContent:"Search",onclickHandler:x,tooltipId:"search-global-template-action",extraClass:"mt-7",isDisabled:!1}),s.jsx(fe,{buttonContent:"Reset",onclickHandler:y,tooltipId:"reset-filters-action",extraClass:"mt-7",isDisabled:!1})]}),s.jsxs("div",{className:"merch-dominator-selection-container d-flex highlighted-selection-container i-mt-4",id:"mrdn-templates-options-container",style:{gap:"2rem"},children:[ee,s.jsx(Ut,{contentChildren:s.jsx(ke,{name:"tabsCount",control:a.control,render:({field:{value:Q}})=>s.jsx(nn,{onChange:$,onFocus:oe=>oe.target.select(),min:1,max:10,value:Q,label:"Number of Tabs (Apply in New Tab)",labelClass:"font-semibold",uniqueId:"new-tab-count",extraClass:"h-31px w-100px",hasPermission:!0,onDragStart:oe=>oe.preventDefault(),allowDecimal:!1,"data-tooltip-id":"new-tab-count-input-tooltip",showPercentIcon:!1})}),tooltipChildren:"Enter how many tabs to open when applying the template in a new tab. You can open up to 10 tabs at a time.",tooltipId:"new-tab-count-input-tooltip",isClickable:!1})]}),s.jsx(At,{message:(q=l==null?void 0:l.root)==null?void 0:q.message})]}),s.jsx(Np,{templates:g,onDelete:A,isProcessing:C,handleApply:O,applyTemplateInNewTab:D,handleViewTemplate:G})]})})}),s.jsx(Je,{open:w.modalOpen,children:s.jsx(Yt,{deleteModalState:w,handleModalVisibility:A,handleDeleteAction:_,modalId:"delete-master-template",deleteHeaderTitle:"Confirm template deletion",confirmationMessage:"Are you sure you want to delete this template?"})}),S.modalOpen&&s.jsx(dc,{template:S.template,templateName:S.templateName,closeModal:Z})]})},Pv=({setOpen:e,selectors:t})=>{const{product_selection:a,product_edit:n,product_editor:r,global_uploader:o}=t,{card_postfix:l,asset_container:i,edit_btn_postfix:d,active_product_card:f,colors_container:c,colors_checkbox:p,price:u,fit_types_checkbox:h,bg_color:g,image_upload:{single_product_uploading_container:v,back_btn:C,delete_btn:j}}=n,{product_selection_btn:N,table:M,modal_close:I,checkbox:F}=a,{selected_translation_option:S,language_expansion_toggle:R,listing_marketplaces:w,listing_marketplace_header:m}=r,T=Na(q=>q.updateLoadingState),{maxTemplateCount:E}=Pt(q=>q),O=Tt(q=>q.setUpgradePlanModal),{tumblerScale:A,tumblerTab:_,isPattern:D}=En(q=>q),x=gt({defaultValues:{title:""},mode:"onChange",resolver:Lt(Tl)}),{control:y,getValues:G,formState:{errors:$}}=x,[K,J]=b.useState(!1),Y=q=>{q.stopPropagation(),q.preventDefault(),e(!1)},ee=()=>({tumblerScale:A,tumblerTab:_,usePattern:D}),Z=async q=>{var Q,oe;if(!K){J(!0);try{const{title:de}=G();T(!0,"Saving your template, please wait..."),e(!1),(oe=(Q=ye(N))==null?void 0:Q.click)==null||oe.call(Q);let pe=[],Ce=[],re=null,U=[];await ne(),await mt(o.global_uploading_container,1e3,500,12e4,!0),await H(),await V(),await ce(),await k();async function ne(){var L,B,W;for(;!ye(M);)await Me(500);document.querySelectorAll(F).forEach(z=>{const te=ye("input",z);te!=null&&te.checked&&pe.push(z.classList[0])}),(W=(B=(L=ye(M))==null?void 0:L.closest(".modal-content"))==null?void 0:B.querySelector(I))==null||W.click(),await Me(1e3),Os(i,d),T(!0,"Retrieving product details..."),await Me(1e3),Ce=Ms(f).map(z=>({product_id:z}))}async function H(){var B,W,z,te;const L=ye(o.uploader_input);for(const X of Ce){T(!0,`Processing product ${(B=X.product_id)==null?void 0:B.replace(/_/g," ")}...`),(W=ye(`.product${l} .${X.product_id}${d}`))==null||W.click(),await Me(1e3),L||await mt(v,1e3,500,1e5,!0),X.colors=_a(X.product_id)?ml(g.color_btn):Ds(`${c} ${p}`),X.prices=Zu(u.input),X.fit_types=gl(h);const ae=ja[X.product_id];X.scale=await Gu(".mat-slider",v,!L&&ae,C,j),ae&&((z=X.scale)!=null&&z.percent)&&(X.scale={...X.scale,printType:Yu(C,j)}),(te=X.scale)!=null&&te.percent&&X.product_id===Tr.TUMBLER&&(X.scale={...X.scale,...ee()}),await Me(300)}}async function V(){var L;re=((L=ye(S))==null?void 0:L.getAttribute("id"))||null,document.querySelectorAll(R).forEach(B=>{var W;(W=B==null?void 0:B.click)==null||W.call(B)}),T(!0,"Retrieving listings..."),await Me(500)}async function ce(){const L=await wl(w,m);U=Object.entries(L).map(([B,W])=>({language:B,fields:Object.entries(W).map(([z,te])=>({id:z,value:te||""}))}))}async function k(){const L={name:de.trim(),category:lr,template_metadata:{product_list:pe,product_metadata:Ce,translation_option:re,listing:U}},B=await Qt(L);if((B==null?void 0:B.status)===429){O({isVisible:!0,showTemplateText:!0,modalTitle:(B==null?void 0:B.title)||"Template Limit Reached",modalDescription:(B==null?void 0:B.description)||dn(E,"save","template")}),T(!1);return}if(!(B!=null&&B.name)){se(B==null?void 0:B.message),T(!1);return}T(!0,"Template successfully saved!"),setTimeout(()=>{T(!1),De("Template saved successfully!")},1e3)}}catch{se("Something went wrong, please try again!")}finally{J(!1)}}};return s.jsx(Je,{open:!0,children:s.jsx(pt,{open:!0,handleClose:Y,extraClass:"modal-add-action",uniqueId:"save-global-template",modalTitle:"Save this configuration as a template",bodySection:s.jsx("div",{className:"d-flex flex-col h-full",children:s.jsx(bt,{...x,children:s.jsxs("form",{className:"w-full d-flex flex-col gap-y-4 mb-0",onSubmit:x.handleSubmit(Z),children:[s.jsx(ke,{name:"title",control:y,render:({field:{onChange:q,value:Q}})=>{var oe;return s.jsx(qe,{onChange:q,value:Q,uniqueId:"save-global-template-input",labelClass:"flex !font-medium",label:"Template Name*",inputClassName:"merch-input",placeHolder:"Enter template name",isDisabled:!1,hasPermission:!0,helperText:(oe=$.title)==null?void 0:oe.message})}}),s.jsxs("div",{className:"d-flex gap-2 w-full justify-end pr-2",children:[s.jsx(fe,{buttonContent:"Save Template",isLoading:K,isDisabled:K,onclickHandler:()=>{},extraClass:"color-white",buttonProps:{type:"submit"},tooltipId:"save-template-action-tooltip"}),s.jsx(fe,{buttonContent:"Cancel",isDisabled:K,onclickHandler:Y,extraClass:"outlined-btn",buttonProps:{type:"button"},tooltipId:"cancel-save-template-action-tooltip"})]})]})})})})})},Nv=()=>{const e=gt({defaultValues:{pageCount:1},mode:"onChange"}),t=b.useCallback(()=>{const n=e.getValues("pageCount");for(let r=0;r<n;r++)window.open("https://merch.amazon.com/designs/new","_blank","noopener, noreferrer")},[]),a=b.useCallback(n=>{e.setValue("pageCount",+wt(n.target.value,1,10))},[]);return s.jsx(bt,{...e,children:s.jsxs("form",{className:"d-flex gap-2 mb-0",onSubmit:e.handleSubmit(t),children:[s.jsx(Ut,{contentChildren:s.jsx(ke,{name:"pageCount",control:e.control,render:({field:{value:n}})=>s.jsx(nn,{onChange:a,onFocus:r=>r.target.select(),min:1,max:10,value:n,uniqueId:"create-page-redirection",extraClass:"flex-fill h-31px",placeHolder:"Enter number of pages",hasPermission:!0,onDragStart:r=>r.preventDefault(),allowDecimal:!1,"data-tooltip-id":"create-page-input-tooltip",showPercentIcon:!1})}),tooltipChildren:"Specify how many 'New Product' tabs you want to open. You can open up to 10 tabs at once.",tooltipId:"create-page-input-tooltip",isClickable:!1}),s.jsx(fe,{buttonContent:"New Product",onclickHandler:()=>{},buttonIcon:s.jsx(Yc,{}),tooltipId:"new-product-open-action",buttonProps:{type:"submit"}})]})})},Iv=b.memo(Nv),Ov=({selectors:e,priceRanges:t,emitter:a})=>{const{global_templates:n,product_selection:r,product_edit:o,product_editor:l,global_uploader:i}=e,{card_postfix:d,asset_container:f,edit_btn_postfix:c,colors_container:p,colors_checkbox:u,price:h,fit_types_checkbox:g,bg_color:v,image_upload:{single_product_uploading_container:C,back_btn:j,delete_btn:N,design_view_prefix:M,canvas:I}}=o,{product_selection_btn:F,selected_checkbox:S,table:R,modal_submit:w,checkbox:m}=r,{translation_options:T,languages_expansion_controller:E}=l,{save_draft_btn:O,publish_btn:A,publish_confirmation_btn:_,daily_limit_error_card:D}=n,x=Na(te=>te.updateLoadingState),{importTemplateCount:y,exportTemplateCount:G,hasUnlimitedAccess:$}=Pt(te=>te),{checkAndDeductUserCredits:K}=al(),J=Tt(te=>te.setUpgradePlanModal),{updateIsPattern:Y,updateTumblerScale:ee,updateTumblerTab:Z}=En(te=>({updateTumblerScale:te.updateTumblerScale,updateTumblerTab:te.updateTumblerTab,updateIsPattern:te.updateIsPattern})),[q,Q]=b.useState(null),[oe,de]=b.useState(!1),[pe,Ce]=b.useState(!1),[re,U]=b.useState({isOpen:!1,errorTitle:"Incomplete Action",errorMessage:""}),ne=async()=>{var ae;const te=wu("mrdn_template_id");if(!te)return;const X=await _p(+te);if(X!=null&&X.success){const me=Ge((ae=X==null?void 0:X.data)==null?void 0:ae.template_metadata);await B(me,{colors:!0,fit_types:!0,prices:!0,listing:!0,tumblerEditEnabled:!1,scale:!1,publishType:cs[0].value});return}V("The selected template could not be applied. It may have been deleted or is no longer available. Please select a different template and try again."),Cu(["mrdn_template_id"])};b.useEffect(()=>{Ze(n.save_publish_btn).then(te=>{if(!te)return;const{targetContainer:X,wrapperContainer:ae}=Ot(document.body,n.save_publish_btn,{id:"mrdn-save-template-container",className:"mrdn-save-template-container merch-dominator-style-container"});ae&&(X==null||X.insertAdjacentElement("beforebegin",ae),Q(ae))}),ne()},[]);const H=()=>{const te=ye(n.invalid_card);if(te){se("Kindly resolve the current product errors and try again."),te.scrollIntoView({behavior:"smooth",block:"start"});return}Ce(!0)},V=b.useCallback(te=>{U(X=>({...X,isOpen:!0,errorMessage:te}))},[]),ce=async()=>{ye(i.uploading_container)&&(x(!0,"Please wait until your design has finished uploading..."),await mt(i.uploading_container,2e3,500,24e4))},k=async te=>{var X,ae,me;for(;!ye(R);)await Me(500);await Me(1e3),hl(te,m,S),(me=(ae=(X=ye(R))==null?void 0:X.closest(".modal-content"))==null?void 0:ae.querySelector(w))==null||me.click(),await Me(1e3),Os(f,c),x(!0,"Updating product details.."),await Me(1e3)},L=async(te,X)=>{var ae;a.updateProductData(ls,te),await Me(1e3),x(!0,"Template applied successfully!"),await Me(500);try{if(X==="save-draft"){if(ye(i.artwork_uploading_container)){V("Design is currently uploading. Please wait until the upload is complete before proceeding to save as draft.");return}if(!ye(i.uploaded_img)){V("Please upload a design first to proceed with saving as draft."),Kt(i.uploader);return}const ge=ye(O);if(ge!=null&&ge.disabled){V("You can't save as draft yet because some required details are missing or incorrect. Please check your information and try again."),Kt(".mrdn-ai-listing-creator-container .error-message");return}ge==null||ge.click()}else if(X==="publish"){if(ye(i.artwork_uploading_container)){V("A design is currently being uploaded. Please wait for the upload to finish before you can publish your product.");return}if(!ye(i.uploaded_img)){V("No design has been uploaded. Please upload a design to proceed with publishing your product."),Kt(i.uploader);return}const ge=ye(D);if(ge){const _e=((ae=ge.textContent)==null?void 0:ae.trim())||"";if(_e){V(_e);return}}const he=ye(A);if(he!=null&&he.disabled){V("You can't publish yet because some required details are missing or incorrect. Please check your information and try again."),Kt(".mrdn-ai-listing-creator-container .error-message");return}he==null||he.click(),Ze(_).then(_e=>{!_e||!Tu||_e==null||_e.click()})}}catch{}finally{x(!1)}},B=async(te,X)=>{var ae,me;try{const ge=ye(n.invalid_card);if(ge){se("Kindly resolve the current product errors and try again."),ge.scrollIntoView({behavior:"smooth",block:"start"});return}x(!0,"Applying your template, please wait..");const{hasCredits:he}=await K(sa.TEMPLATE_AUTOMATION,{shouldOnlyFetchCredits:!0});if(!he){x(!1);return}const{product_list:_e,product_metadata:Oe,translation_option:Ne,listing:le}=te,{fit_types:ie,colors:ue,prices:be,scale:Te,listing:Le,publishType:ze,tumblerEditEnabled:Ke}=X,He=$?ze:"none",we=He==="save-draft"||He==="publish",ve=we||Te||Ke;if(ve&&await mt(i.global_uploading_container,2e3,500,24e4,!0),we&&!ye(i.uploaded_img)){V("Please upload a design first to apply the template."),Kt(i.uploader),x(!1);return}(me=(ae=ye(F))==null?void 0:ae.click)==null||me.call(ae),await k(_e),ve&&await ce(),await Ee(),await Pe();async function Ee(){var Re,Ae,ut,Qe,Ue,$e,Ve,ot,pa,yo,vo,wo,Co,To;const je=h.input,Fe=h.input_container,Xe=ye(i.uploader_input);for(const st of Oe){const en=st.product_id;x(!0,`Modifying product ${en.replace(/_/g," ")}...`),(Re=ye(`.product${d} .${en}${c}`))==null||Re.click(),await Me(1e3),!Xe&&ve&&await mt(C,1e3),ie&&bl(st.fit_types||[],g),ue&&((Ae=st.colors)!=null&&Ae.length)&&(_a(en)?await xl(v.color_btn,v.color_input,st.colors[0]):ds(st.colors||[],`${p} ${u}`,(ut=qt[en])==null?void 0:ut.default_color)),be&&yl(t,st.prices||[],en,je,Fe,!0);const Tc=en===Tr.TUMBLER;let Dr=(Qe=st.scale)==null?void 0:Qe.percent;if(Tc&&Ke&&((Ue=st.scale)!=null&&Ue.tumblerTab)){if(Xe)Y(!!(($e=st.scale)!=null&&$e.usePattern)),Z(((Ve=st.scale)==null?void 0:Ve.tumblerTab)||""),ee(((ot=st.scale)==null?void 0:ot.tumblerScale)||100);else if([Bt.duplicateSide,Bt.differentSide].includes(((pa=st.scale)==null?void 0:pa.tumblerTab)||"")||((yo=st.scale)==null?void 0:yo.tumblerScale)!==100){Y(!!((vo=st.scale)!=null&&vo.usePattern)),Z(((wo=st.scale)==null?void 0:wo.tumblerTab)||""),ee(((Co=st.scale)==null?void 0:Co.tumblerScale)||100),await Me(1e3);const Lr=ye(".mrdn-tumbler-actions button");Lr==null||Lr.click(),await Me(1e3),await mt(C,2e3),Dr=100}}if(!Xe&&Te&&Dr){const Ba=(To=st.scale)==null?void 0:To.printType;ja[en]&&Ba&&await up(Ba,en,{global_uploaded_img:i.uploaded_img,back_btn:j,delete_btn:N,single_product_uploading_container:C,design_view_prefix:M,canvas:I}),await Ht(void 0,C,Dr)}await Me(300)}}async function Pe(){var je,Fe;if(!Le){x(!0,"Template applied successfully!"),setTimeout(()=>{x(!1)},1e3);return}Ne&&((Fe=(je=ye(`${T}#${Ne}`))==null?void 0:je.click)==null||Fe.call(je),await Me(500),ps(E)),x(!0,"Setting listings..."),await Me(500),await L(le,He)}await K(sa.TEMPLATE_AUTOMATION)}catch{x(!1)}},W=b.useCallback(async te=>{const{formattedTemplates:X}=ip(te,lr)||{};if(!X.length){De("Import complete. No valid templates were available to import.");return}const ae=await hn(X);if(ae!=null&&ae.success){De((ae==null?void 0:ae.message)||ln((ae==null?void 0:ae.count)||0));return}if((ae==null?void 0:ae.status)===429){J({isVisible:!0,showTemplateText:!0,modalTitle:(ae==null?void 0:ae.title)||"Template Limit Reached",modalDescription:(ae==null?void 0:ae.description)||cn(ae==null?void 0:ae.count,y)});return}se(ae==null?void 0:ae.message)},[y]),z=b.useCallback(()=>{U(te=>({...te,isOpen:!1}))},[]);return s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"d-flex justify-between px-4 i-py-2 secondary-border border-r-1 i-mb-8",style:{backgroundColor:"var(--actions-bg)"},children:[s.jsx(gn,{collectionKey:lr,fileName:"merch-dominator-master-templates.json",processTemplateImport:W,templateLimit:G}),s.jsxs("div",{className:"d-flex gap-2",children:[s.jsx(Iv,{}),s.jsx(fe,{buttonContent:"Merch Dominator Templates",onclickHandler:()=>{de(!0)},buttonIcon:s.jsx(ko,{}),tooltipId:"global-template-action"})]})]}),q&&ft.createPortal(s.jsx("div",{className:"save-template-container mr-2",style:{float:"left"},children:s.jsx(fe,{buttonContent:"Save as Merch Dominator Template",onclickHandler:H,buttonIcon:s.jsx(ko,{}),tooltipId:"save-mrdn-template-action",extraClass:"py-2"})}),q),oe&&s.jsx(kv,{setOpen:de,applyTemplate:B}),pe&&s.jsx(Pv,{selectors:e,setOpen:Ce}),s.jsx(_s,{id:"save-draft-alert",modalTitle:"Attention Required",isOpen:re.isOpen,alertIcon:s.jsx(js,{size:70}),alertIconClass:"color-danger",onClose:z,onBlur:z,errorMessage:re.errorMessage,errorTitle:re.errorTitle})]})};function Mv(e,t){return t.forEach(function(a){a&&typeof a!="string"&&!Array.isArray(a)&&Object.keys(a).forEach(function(n){if(n!=="default"&&!(n in e)){var r=Object.getOwnPropertyDescriptor(a,n);Object.defineProperty(e,n,r.get?r:{enumerable:!0,get:function(){return a[n]}})}})}),Object.freeze(e)}function uc(e,t){return new Promise(function(a,n){let r;return Dv(e).then(function(o){try{return r=o,a(new Blob([t.slice(0,2),r,t.slice(2)],{type:"image/jpeg"}))}catch(l){return n(l)}},n)})}const Dv=e=>new Promise((t,a)=>{const n=new FileReader;n.addEventListener("load",({target:{result:r}})=>{const o=new DataView(r);let l=0;if(o.getUint16(l)!==65496)return a("not a valid JPEG");for(l+=2;;){const i=o.getUint16(l);if(i===65498)break;const d=o.getUint16(l+2);if(i===65505&&o.getUint32(l+4)===1165519206){const f=l+10;let c;switch(o.getUint16(f)){case 18761:c=!0;break;case 19789:c=!1;break;default:return a("TIFF header contains invalid endian")}if(o.getUint16(f+2,c)!==42)return a("TIFF header contains invalid version");const p=o.getUint32(f+4,c),u=f+p+2+12*o.getUint16(f+p,c);for(let h=f+p+2;h<u;h+=12)if(o.getUint16(h,c)==274){if(o.getUint16(h+2,c)!==3)return a("Orientation data type is invalid");if(o.getUint32(h+4,c)!==1)return a("Orientation data count is invalid");o.setUint16(h+8,1,c);break}return t(r.slice(l,l+2+d))}l+=2+d}return t(new Blob)}),n.readAsArrayBuffer(e)});var gr={},Lv={get exports(){return gr},set exports(e){gr=e}};(function(e){var t,a,n={};Lv.exports=n,n.parse=function(r,o){for(var l=n.bin.readUshort,i=n.bin.readUint,d=0,f={},c=new Uint8Array(r),p=c.length-4;i(c,p)!=101010256;)p--;d=p,d+=4;var u=l(c,d+=4);l(c,d+=2);var h=i(c,d+=2),g=i(c,d+=4);d+=4,d=g;for(var v=0;v<u;v++){i(c,d),d+=4,d+=4,d+=4,i(c,d+=4),h=i(c,d+=4);var C=i(c,d+=4),j=l(c,d+=4),N=l(c,d+2),M=l(c,d+4);d+=6;var I=i(c,d+=8);d+=4,d+=j+N+M,n._readLocal(c,I,f,h,C,o)}return f},n._readLocal=function(r,o,l,i,d,f){var c=n.bin.readUshort,p=n.bin.readUint;p(r,o),c(r,o+=4),c(r,o+=2);var u=c(r,o+=2);p(r,o+=2),p(r,o+=4),o+=4;var h=c(r,o+=8),g=c(r,o+=2);o+=2;var v=n.bin.readUTF8(r,o,h);if(o+=h,o+=g,f)l[v]={size:d,csize:i};else{var C=new Uint8Array(r.buffer,o);if(u==0)l[v]=new Uint8Array(C.buffer.slice(o,o+i));else{if(u!=8)throw"unknown compression method: "+u;var j=new Uint8Array(d);n.inflateRaw(C,j),l[v]=j}}},n.inflateRaw=function(r,o){return n.F.inflate(r,o)},n.inflate=function(r,o){return r[0],r[1],n.inflateRaw(new Uint8Array(r.buffer,r.byteOffset+2,r.length-6),o)},n.deflate=function(r,o){o==null&&(o={level:6});var l=0,i=new Uint8Array(50+Math.floor(1.1*r.length));i[l]=120,i[l+1]=156,l+=2,l=n.F.deflateRaw(r,i,l,o.level);var d=n.adler(r,0,r.length);return i[l+0]=d>>>24&255,i[l+1]=d>>>16&255,i[l+2]=d>>>8&255,i[l+3]=d>>>0&255,new Uint8Array(i.buffer,0,l+4)},n.deflateRaw=function(r,o){o==null&&(o={level:6});var l=new Uint8Array(50+Math.floor(1.1*r.length)),i=n.F.deflateRaw(r,l,i,o.level);return new Uint8Array(l.buffer,0,i)},n.encode=function(r,o){o==null&&(o=!1);var l=0,i=n.bin.writeUint,d=n.bin.writeUshort,f={};for(var c in r){var p=!n._noNeed(c)&&!o,u=r[c],h=n.crc.crc(u,0,u.length);f[c]={cpr:p,usize:u.length,crc:h,file:p?n.deflateRaw(u):u}}for(var c in f)l+=f[c].file.length+30+46+2*n.bin.sizeUTF8(c);l+=22;var g=new Uint8Array(l),v=0,C=[];for(var c in f){var j=f[c];C.push(v),v=n._writeHeader(g,v,c,j,0)}var N=0,M=v;for(var c in f)j=f[c],C.push(v),v=n._writeHeader(g,v,c,j,1,C[N++]);var I=v-M;return i(g,v,101010256),v+=4,d(g,v+=4,N),d(g,v+=2,N),i(g,v+=2,I),i(g,v+=4,M),v+=4,v+=2,g.buffer},n._noNeed=function(r){var o=r.split(".").pop().toLowerCase();return"png,jpg,jpeg,zip".indexOf(o)!=-1},n._writeHeader=function(r,o,l,i,d,f){var c=n.bin.writeUint,p=n.bin.writeUshort,u=i.file;return c(r,o,d==0?67324752:33639248),o+=4,d==1&&(o+=2),p(r,o,20),p(r,o+=2,0),p(r,o+=2,i.cpr?8:0),c(r,o+=2,0),c(r,o+=4,i.crc),c(r,o+=4,u.length),c(r,o+=4,i.usize),p(r,o+=4,n.bin.sizeUTF8(l)),p(r,o+=2,0),o+=2,d==1&&(o+=2,o+=2,c(r,o+=6,f),o+=4),o+=n.bin.writeUTF8(r,o,l),d==0&&(r.set(u,o),o+=u.length),o},n.crc={table:function(){for(var r=new Uint32Array(256),o=0;o<256;o++){for(var l=o,i=0;i<8;i++)1&l?l=3988292384^l>>>1:l>>>=1;r[o]=l}return r}(),update:function(r,o,l,i){for(var d=0;d<i;d++)r=n.crc.table[255&(r^o[l+d])]^r>>>8;return r},crc:function(r,o,l){return 4294967295^n.crc.update(4294967295,r,o,l)}},n.adler=function(r,o,l){for(var i=1,d=0,f=o,c=o+l;f<c;){for(var p=Math.min(f+5552,c);f<p;)d+=i+=r[f++];i%=65521,d%=65521}return d<<16|i},n.bin={readUshort:function(r,o){return r[o]|r[o+1]<<8},writeUshort:function(r,o,l){r[o]=255&l,r[o+1]=l>>8&255},readUint:function(r,o){return 16777216*r[o+3]+(r[o+2]<<16|r[o+1]<<8|r[o])},writeUint:function(r,o,l){r[o]=255&l,r[o+1]=l>>8&255,r[o+2]=l>>16&255,r[o+3]=l>>24&255},readASCII:function(r,o,l){for(var i="",d=0;d<l;d++)i+=String.fromCharCode(r[o+d]);return i},writeASCII:function(r,o,l){for(var i=0;i<l.length;i++)r[o+i]=l.charCodeAt(i)},pad:function(r){return r.length<2?"0"+r:r},readUTF8:function(r,o,l){for(var i,d="",f=0;f<l;f++)d+="%"+n.bin.pad(r[o+f].toString(16));try{i=decodeURIComponent(d)}catch{return n.bin.readASCII(r,o,l)}return i},writeUTF8:function(r,o,l){for(var i=l.length,d=0,f=0;f<i;f++){var c=l.charCodeAt(f);if(!(4294967168&c))r[o+d]=c,d++;else if(!(4294965248&c))r[o+d]=192|c>>6,r[o+d+1]=128|c>>0&63,d+=2;else if(!(4294901760&c))r[o+d]=224|c>>12,r[o+d+1]=128|c>>6&63,r[o+d+2]=128|c>>0&63,d+=3;else{if(4292870144&c)throw"e";r[o+d]=240|c>>18,r[o+d+1]=128|c>>12&63,r[o+d+2]=128|c>>6&63,r[o+d+3]=128|c>>0&63,d+=4}}return d},sizeUTF8:function(r){for(var o=r.length,l=0,i=0;i<o;i++){var d=r.charCodeAt(i);if(!(4294967168&d))l++;else if(!(4294965248&d))l+=2;else if(!(4294901760&d))l+=3;else{if(4292870144&d)throw"e";l+=4}}return l}},n.F={},n.F.deflateRaw=function(r,o,l,i){var d=[[0,0,0,0,0],[4,4,8,4,0],[4,5,16,8,0],[4,6,16,16,0],[4,10,16,32,0],[8,16,32,32,0],[8,16,128,128,0],[8,32,128,256,0],[32,128,258,1024,1],[32,258,258,4096,1]][i],f=n.F.U,c=n.F._goodIndex;n.F._hash;var p=n.F._putsE,u=0,h=l<<3,g=0,v=r.length;if(i==0){for(;u<v;)p(o,h,u+(E=Math.min(65535,v-u))==v?1:0),h=n.F._copyExact(r,u,E,o,h+8),u+=E;return h>>>3}var C=f.lits,j=f.strt,N=f.prev,M=0,I=0,F=0,S=0,R=0,w=0;for(v>2&&(j[w=n.F._hash(r,0)]=0),u=0;u<v;u++){if(R=w,u+1<v-2){w=n.F._hash(r,u+1);var m=u+1&32767;N[m]=j[w],j[w]=m}if(g<=u){(M>14e3||I>26697)&&v-u>100&&(g<u&&(C[M]=u-g,M+=2,g=u),h=n.F._writeBlock(u==v-1||g==v?1:0,C,M,S,r,F,u-F,o,h),M=I=S=0,F=u);var T=0;u<v-2&&(T=n.F._bestMatch(r,u,N,R,Math.min(d[2],v-u),d[3]));var E=T>>>16,O=65535&T;if(T!=0){O=65535&T;var A=c(E=T>>>16,f.of0);f.lhst[257+A]++;var _=c(O,f.df0);f.dhst[_]++,S+=f.exb[A]+f.dxb[_],C[M]=E<<23|u-g,C[M+1]=O<<16|A<<8|_,M+=2,g=u+E}else f.lhst[r[u]]++;I++}}for(F==u&&r.length!=0||(g<u&&(C[M]=u-g,M+=2,g=u),h=n.F._writeBlock(1,C,M,S,r,F,u-F,o,h),M=0,I=0,M=I=S=0,F=u);7&h;)h++;return h>>>3},n.F._bestMatch=function(r,o,l,i,d,f){var c=32767&o,p=l[c],u=c-p+32768&32767;if(p==c||i!=n.F._hash(r,o-u))return 0;for(var h=0,g=0,v=Math.min(32767,o);u<=v&&--f!=0&&p!=c;){if(h==0||r[o+h]==r[o+h-u]){var C=n.F._howLong(r,o,u);if(C>h){if(g=u,(h=C)>=d)break;u+2<C&&(C=u+2);for(var j=0,N=0;N<C-2;N++){var M=o-u+N+32768&32767,I=M-l[M]+32768&32767;I>j&&(j=I,p=M)}}}u+=(c=p)-(p=l[c])+32768&32767}return h<<16|g},n.F._howLong=function(r,o,l){if(r[o]!=r[o-l]||r[o+1]!=r[o+1-l]||r[o+2]!=r[o+2-l])return 0;var i=o,d=Math.min(r.length,o+258);for(o+=3;o<d&&r[o]==r[o-l];)o++;return o-i},n.F._hash=function(r,o){return(r[o]<<8|r[o+1])+(r[o+2]<<4)&65535},n.saved=0,n.F._writeBlock=function(r,o,l,i,d,f,c,p,u){var h,g,v,C,j,N,M,I,F,S=n.F.U,R=n.F._putsF,w=n.F._putsE;S.lhst[256]++,g=(h=n.F.getTrees())[0],v=h[1],C=h[2],j=h[3],N=h[4],M=h[5],I=h[6],F=h[7];var m=32+(u+3&7?8-(u+3&7):0)+(c<<3),T=i+n.F.contSize(S.fltree,S.lhst)+n.F.contSize(S.fdtree,S.dhst),E=i+n.F.contSize(S.ltree,S.lhst)+n.F.contSize(S.dtree,S.dhst);E+=14+3*M+n.F.contSize(S.itree,S.ihst)+(2*S.ihst[16]+3*S.ihst[17]+7*S.ihst[18]);for(var O=0;O<286;O++)S.lhst[O]=0;for(O=0;O<30;O++)S.dhst[O]=0;for(O=0;O<19;O++)S.ihst[O]=0;var A=m<T&&m<E?0:T<E?1:2;if(R(p,u,r),R(p,u+1,A),u+=3,A==0){for(;7&u;)u++;u=n.F._copyExact(d,f,c,p,u)}else{var _,D;if(A==1&&(_=S.fltree,D=S.fdtree),A==2){n.F.makeCodes(S.ltree,g),n.F.revCodes(S.ltree,g),n.F.makeCodes(S.dtree,v),n.F.revCodes(S.dtree,v),n.F.makeCodes(S.itree,C),n.F.revCodes(S.itree,C),_=S.ltree,D=S.dtree,w(p,u,j-257),w(p,u+=5,N-1),w(p,u+=5,M-4),u+=4;for(var x=0;x<M;x++)w(p,u+3*x,S.itree[1+(S.ordr[x]<<1)]);u+=3*M,u=n.F._codeTiny(I,S.itree,p,u),u=n.F._codeTiny(F,S.itree,p,u)}for(var y=f,G=0;G<l;G+=2){for(var $=o[G],K=$>>>23,J=y+(8388607&$);y<J;)u=n.F._writeLit(d[y++],_,p,u);if(K!=0){var Y=o[G+1],ee=Y>>16,Z=Y>>8&255,q=255&Y;w(p,u=n.F._writeLit(257+Z,_,p,u),K-S.of0[Z]),u+=S.exb[Z],R(p,u=n.F._writeLit(q,D,p,u),ee-S.df0[q]),u+=S.dxb[q],y+=K}}u=n.F._writeLit(256,_,p,u)}return u},n.F._copyExact=function(r,o,l,i,d){var f=d>>>3;return i[f]=l,i[f+1]=l>>>8,i[f+2]=255-i[f],i[f+3]=255-i[f+1],f+=4,i.set(new Uint8Array(r.buffer,o,l),f),d+(l+4<<3)},n.F.getTrees=function(){for(var r=n.F.U,o=n.F._hufTree(r.lhst,r.ltree,15),l=n.F._hufTree(r.dhst,r.dtree,15),i=[],d=n.F._lenCodes(r.ltree,i),f=[],c=n.F._lenCodes(r.dtree,f),p=0;p<i.length;p+=2)r.ihst[i[p]]++;for(p=0;p<f.length;p+=2)r.ihst[f[p]]++;for(var u=n.F._hufTree(r.ihst,r.itree,7),h=19;h>4&&r.itree[1+(r.ordr[h-1]<<1)]==0;)h--;return[o,l,u,d,c,h,i,f]},n.F.getSecond=function(r){for(var o=[],l=0;l<r.length;l+=2)o.push(r[l+1]);return o},n.F.nonZero=function(r){for(var o="",l=0;l<r.length;l+=2)r[l+1]!=0&&(o+=(l>>1)+",");return o},n.F.contSize=function(r,o){for(var l=0,i=0;i<o.length;i++)l+=o[i]*r[1+(i<<1)];return l},n.F._codeTiny=function(r,o,l,i){for(var d=0;d<r.length;d+=2){var f=r[d],c=r[d+1];i=n.F._writeLit(f,o,l,i);var p=f==16?2:f==17?3:7;f>15&&(n.F._putsE(l,i,c,p),i+=p)}return i},n.F._lenCodes=function(r,o){for(var l=r.length;l!=2&&r[l-1]==0;)l-=2;for(var i=0;i<l;i+=2){var d=r[i+1],f=i+3<l?r[i+3]:-1,c=i+5<l?r[i+5]:-1,p=i==0?-1:r[i-1];if(d==0&&f==d&&c==d){for(var u=i+5;u+2<l&&r[u+2]==d;)u+=2;(h=Math.min(u+1-i>>>1,138))<11?o.push(17,h-3):o.push(18,h-11),i+=2*h-2}else if(d==p&&f==d&&c==d){for(u=i+5;u+2<l&&r[u+2]==d;)u+=2;var h=Math.min(u+1-i>>>1,6);o.push(16,h-3),i+=2*h-2}else o.push(d,0)}return l>>>1},n.F._hufTree=function(r,o,l){var i=[],d=r.length,f=o.length,c=0;for(c=0;c<f;c+=2)o[c]=0,o[c+1]=0;for(c=0;c<d;c++)r[c]!=0&&i.push({lit:c,f:r[c]});var p=i.length,u=i.slice(0);if(p==0)return 0;if(p==1){var h=i[0].lit;return u=h==0?1:0,o[1+(h<<1)]=1,o[1+(u<<1)]=1,1}i.sort(function(I,F){return I.f-F.f});var g=i[0],v=i[1],C=0,j=1,N=2;for(i[0]={lit:-1,f:g.f+v.f,l:g,r:v,d:0};j!=p-1;)g=C!=j&&(N==p||i[C].f<i[N].f)?i[C++]:i[N++],v=C!=j&&(N==p||i[C].f<i[N].f)?i[C++]:i[N++],i[j++]={lit:-1,f:g.f+v.f,l:g,r:v};var M=n.F.setDepth(i[j-1],0);for(M>l&&(n.F.restrictDepth(u,l,M),M=l),c=0;c<p;c++)o[1+(u[c].lit<<1)]=u[c].d;return M},n.F.setDepth=function(r,o){return r.lit!=-1?(r.d=o,o):Math.max(n.F.setDepth(r.l,o+1),n.F.setDepth(r.r,o+1))},n.F.restrictDepth=function(r,o,l){var i=0,d=1<<l-o,f=0;for(r.sort(function(p,u){return u.d==p.d?p.f-u.f:u.d-p.d}),i=0;i<r.length&&r[i].d>o;i++){var c=r[i].d;r[i].d=o,f+=d-(1<<l-c)}for(f>>>=l-o;f>0;)(c=r[i].d)<o?(r[i].d++,f-=1<<o-c-1):i++;for(;i>=0;i--)r[i].d==o&&f<0&&(r[i].d--,f++);f!=0&&console.log("debt left")},n.F._goodIndex=function(r,o){var l=0;return o[16|l]<=r&&(l|=16),o[8|l]<=r&&(l|=8),o[4|l]<=r&&(l|=4),o[2|l]<=r&&(l|=2),o[1|l]<=r&&(l|=1),l},n.F._writeLit=function(r,o,l,i){return n.F._putsF(l,i,o[r<<1]),i+o[1+(r<<1)]},n.F.inflate=function(r,o){var l=Uint8Array;if(r[0]==3&&r[1]==0)return o||new l(0);var i=n.F,d=i._bitsF,f=i._bitsE,c=i._decodeTiny,p=i.makeCodes,u=i.codes2map,h=i._get17,g=i.U,v=o==null;v&&(o=new l(r.length>>>2<<3));for(var C,j,N=0,M=0,I=0,F=0,S=0,R=0,w=0,m=0,T=0;N==0;)if(N=d(r,T,1),M=d(r,T+1,2),T+=3,M!=0){if(v&&(o=n.F._check(o,m+(1<<17))),M==1&&(C=g.flmap,j=g.fdmap,R=511,w=31),M==2){I=f(r,T,5)+257,F=f(r,T+5,5)+1,S=f(r,T+10,4)+4,T+=14;for(var E=0;E<38;E+=2)g.itree[E]=0,g.itree[E+1]=0;var O=1;for(E=0;E<S;E++){var A=f(r,T+3*E,3);g.itree[1+(g.ordr[E]<<1)]=A,A>O&&(O=A)}T+=3*S,p(g.itree,O),u(g.itree,O,g.imap),C=g.lmap,j=g.dmap,T=c(g.imap,(1<<O)-1,I+F,r,T,g.ttree);var _=i._copyOut(g.ttree,0,I,g.ltree);R=(1<<_)-1;var D=i._copyOut(g.ttree,I,F,g.dtree);w=(1<<D)-1,p(g.ltree,_),u(g.ltree,_,C),p(g.dtree,D),u(g.dtree,D,j)}for(;;){var x=C[h(r,T)&R];T+=15&x;var y=x>>>4;if(!(y>>>8))o[m++]=y;else{if(y==256)break;var G=m+y-254;if(y>264){var $=g.ldef[y-257];G=m+($>>>3)+f(r,T,7&$),T+=7&$}var K=j[h(r,T)&w];T+=15&K;var J=K>>>4,Y=g.ddef[J],ee=(Y>>>4)+d(r,T,15&Y);for(T+=15&Y,v&&(o=n.F._check(o,m+(1<<17)));m<G;)o[m]=o[m++-ee],o[m]=o[m++-ee],o[m]=o[m++-ee],o[m]=o[m++-ee];m=G}}}else{7&T&&(T+=8-(7&T));var Z=4+(T>>>3),q=r[Z-4]|r[Z-3]<<8;v&&(o=n.F._check(o,m+q)),o.set(new l(r.buffer,r.byteOffset+Z,q),m),T=Z+q<<3,m+=q}return o.length==m?o:o.slice(0,m)},n.F._check=function(r,o){var l=r.length;if(o<=l)return r;var i=new Uint8Array(Math.max(l<<1,o));return i.set(r,0),i},n.F._decodeTiny=function(r,o,l,i,d,f){for(var c=n.F._bitsE,p=n.F._get17,u=0;u<l;){var h=r[p(i,d)&o];d+=15&h;var g=h>>>4;if(g<=15)f[u]=g,u++;else{var v=0,C=0;g==16?(C=3+c(i,d,2),d+=2,v=f[u-1]):g==17?(C=3+c(i,d,3),d+=3):g==18&&(C=11+c(i,d,7),d+=7);for(var j=u+C;u<j;)f[u]=v,u++}}return d},n.F._copyOut=function(r,o,l,i){for(var d=0,f=0,c=i.length>>>1;f<l;){var p=r[f+o];i[f<<1]=0,i[1+(f<<1)]=p,p>d&&(d=p),f++}for(;f<c;)i[f<<1]=0,i[1+(f<<1)]=0,f++;return d},n.F.makeCodes=function(r,o){for(var l,i,d,f,c=n.F.U,p=r.length,u=c.bl_count,h=0;h<=o;h++)u[h]=0;for(h=1;h<p;h+=2)u[r[h]]++;var g=c.next_code;for(l=0,u[0]=0,i=1;i<=o;i++)l=l+u[i-1]<<1,g[i]=l;for(d=0;d<p;d+=2)(f=r[d+1])!=0&&(r[d]=g[f],g[f]++)},n.F.codes2map=function(r,o,l){for(var i=r.length,d=n.F.U.rev15,f=0;f<i;f+=2)if(r[f+1]!=0)for(var c=f>>1,p=r[f+1],u=c<<4|p,h=o-p,g=r[f]<<h,v=g+(1<<h);g!=v;)l[d[g]>>>15-o]=u,g++},n.F.revCodes=function(r,o){for(var l=n.F.U.rev15,i=15-o,d=0;d<r.length;d+=2){var f=r[d]<<o-r[d+1];r[d]=l[f]>>>i}},n.F._putsE=function(r,o,l){l<<=7&o;var i=o>>>3;r[i]|=l,r[i+1]|=l>>>8},n.F._putsF=function(r,o,l){l<<=7&o;var i=o>>>3;r[i]|=l,r[i+1]|=l>>>8,r[i+2]|=l>>>16},n.F._bitsE=function(r,o,l){return(r[o>>>3]|r[1+(o>>>3)]<<8)>>>(7&o)&(1<<l)-1},n.F._bitsF=function(r,o,l){return(r[o>>>3]|r[1+(o>>>3)]<<8|r[2+(o>>>3)]<<16)>>>(7&o)&(1<<l)-1},n.F._get17=function(r,o){return(r[o>>>3]|r[1+(o>>>3)]<<8|r[2+(o>>>3)]<<16)>>>(7&o)},n.F._get25=function(r,o){return(r[o>>>3]|r[1+(o>>>3)]<<8|r[2+(o>>>3)]<<16|r[3+(o>>>3)]<<24)>>>(7&o)},n.F.U=(t=Uint16Array,a=Uint32Array,{next_code:new t(16),bl_count:new t(16),ordr:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],of0:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],exb:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],ldef:new t(32),df0:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],dxb:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],ddef:new a(32),flmap:new t(512),fltree:[],fdmap:new t(32),fdtree:[],lmap:new t(32768),ltree:[],ttree:[],dmap:new t(32768),dtree:[],imap:new t(512),itree:[],rev15:new t(32768),lhst:new a(286),dhst:new a(30),ihst:new a(19),lits:new a(15e3),strt:new t(65536),prev:new t(32768)}),function(){for(var r=n.F.U,o=0;o<32768;o++){var l=o;l=(4278255360&(l=(4042322160&(l=(3435973836&(l=(2863311530&l)>>>1|(1431655765&l)<<1))>>>2|(858993459&l)<<2))>>>4|(252645135&l)<<4))>>>8|(16711935&l)<<8,r.rev15[o]=(l>>>16|l<<16)>>>17}function i(d,f,c){for(;f--!=0;)d.push(0,c)}for(o=0;o<32;o++)r.ldef[o]=r.of0[o]<<3|r.exb[o],r.ddef[o]=r.df0[o]<<4|r.dxb[o];i(r.fltree,144,8),i(r.fltree,112,9),i(r.fltree,24,7),i(r.fltree,8,8),n.F.makeCodes(r.fltree,9),n.F.codes2map(r.fltree,9,r.flmap),n.F.revCodes(r.fltree,9),i(r.fdtree,32,5),n.F.makeCodes(r.fdtree,5),n.F.codes2map(r.fdtree,5,r.fdmap),n.F.revCodes(r.fdtree,5),i(r.itree,19,0),i(r.ltree,286,0),i(r.dtree,30,0),i(r.ttree,320,0)}()})();var $v=Mv({__proto__:null,default:gr},[gr]);const It=function(){var e={nextZero(c,p){for(;c[p]!=0;)p++;return p},readUshort:(c,p)=>c[p]<<8|c[p+1],writeUshort(c,p,u){c[p]=u>>8&255,c[p+1]=255&u},readUint:(c,p)=>16777216*c[p]+(c[p+1]<<16|c[p+2]<<8|c[p+3]),writeUint(c,p,u){c[p]=u>>24&255,c[p+1]=u>>16&255,c[p+2]=u>>8&255,c[p+3]=255&u},readASCII(c,p,u){let h="";for(let g=0;g<u;g++)h+=String.fromCharCode(c[p+g]);return h},writeASCII(c,p,u){for(let h=0;h<u.length;h++)c[p+h]=u.charCodeAt(h)},readBytes(c,p,u){const h=[];for(let g=0;g<u;g++)h.push(c[p+g]);return h},pad:c=>c.length<2?`0${c}`:c,readUTF8(c,p,u){let h,g="";for(let v=0;v<u;v++)g+=`%${e.pad(c[p+v].toString(16))}`;try{h=decodeURIComponent(g)}catch{return e.readASCII(c,p,u)}return h}};function t(c,p,u,h){const g=p*u,v=o(h),C=Math.ceil(p*v/8),j=new Uint8Array(4*g),N=new Uint32Array(j.buffer),{ctype:M}=h,{depth:I}=h,F=e.readUshort;if(M==6){const $=g<<2;if(I==8)for(var S=0;S<$;S+=4)j[S]=c[S],j[S+1]=c[S+1],j[S+2]=c[S+2],j[S+3]=c[S+3];if(I==16)for(S=0;S<$;S++)j[S]=c[S<<1]}else if(M==2){const $=h.tabs.tRNS;if($==null){if(I==8)for(S=0;S<g;S++){var R=3*S;N[S]=255<<24|c[R+2]<<16|c[R+1]<<8|c[R]}if(I==16)for(S=0;S<g;S++)R=6*S,N[S]=255<<24|c[R+4]<<16|c[R+2]<<8|c[R]}else{var w=$[0];const K=$[1],J=$[2];if(I==8)for(S=0;S<g;S++){var m=S<<2;R=3*S,N[S]=255<<24|c[R+2]<<16|c[R+1]<<8|c[R],c[R]==w&&c[R+1]==K&&c[R+2]==J&&(j[m+3]=0)}if(I==16)for(S=0;S<g;S++)m=S<<2,R=6*S,N[S]=255<<24|c[R+4]<<16|c[R+2]<<8|c[R],F(c,R)==w&&F(c,R+2)==K&&F(c,R+4)==J&&(j[m+3]=0)}}else if(M==3){const $=h.tabs.PLTE,K=h.tabs.tRNS,J=K?K.length:0;if(I==1)for(var T=0;T<u;T++){var E=T*C,O=T*p;for(S=0;S<p;S++){m=O+S<<2;var A=3*(_=c[E+(S>>3)]>>7-((7&S)<<0)&1);j[m]=$[A],j[m+1]=$[A+1],j[m+2]=$[A+2],j[m+3]=_<J?K[_]:255}}if(I==2)for(T=0;T<u;T++)for(E=T*C,O=T*p,S=0;S<p;S++)m=O+S<<2,A=3*(_=c[E+(S>>2)]>>6-((3&S)<<1)&3),j[m]=$[A],j[m+1]=$[A+1],j[m+2]=$[A+2],j[m+3]=_<J?K[_]:255;if(I==4)for(T=0;T<u;T++)for(E=T*C,O=T*p,S=0;S<p;S++)m=O+S<<2,A=3*(_=c[E+(S>>1)]>>4-((1&S)<<2)&15),j[m]=$[A],j[m+1]=$[A+1],j[m+2]=$[A+2],j[m+3]=_<J?K[_]:255;if(I==8)for(S=0;S<g;S++){var _;m=S<<2,A=3*(_=c[S]),j[m]=$[A],j[m+1]=$[A+1],j[m+2]=$[A+2],j[m+3]=_<J?K[_]:255}}else if(M==4){if(I==8)for(S=0;S<g;S++){m=S<<2;var D=c[x=S<<1];j[m]=D,j[m+1]=D,j[m+2]=D,j[m+3]=c[x+1]}if(I==16)for(S=0;S<g;S++){var x;m=S<<2,D=c[x=S<<2],j[m]=D,j[m+1]=D,j[m+2]=D,j[m+3]=c[x+2]}}else if(M==0)for(w=h.tabs.tRNS?h.tabs.tRNS:-1,T=0;T<u;T++){const $=T*C,K=T*p;if(I==1)for(var y=0;y<p;y++){var G=(D=255*(c[$+(y>>>3)]>>>7-(7&y)&1))==255*w?0:255;N[K+y]=G<<24|D<<16|D<<8|D}else if(I==2)for(y=0;y<p;y++)G=(D=85*(c[$+(y>>>2)]>>>6-((3&y)<<1)&3))==85*w?0:255,N[K+y]=G<<24|D<<16|D<<8|D;else if(I==4)for(y=0;y<p;y++)G=(D=17*(c[$+(y>>>1)]>>>4-((1&y)<<2)&15))==17*w?0:255,N[K+y]=G<<24|D<<16|D<<8|D;else if(I==8)for(y=0;y<p;y++)G=(D=c[$+y])==w?0:255,N[K+y]=G<<24|D<<16|D<<8|D;else if(I==16)for(y=0;y<p;y++)D=c[$+(y<<1)],G=F(c,$+(y<<1))==w?0:255,N[K+y]=G<<24|D<<16|D<<8|D}return j}function a(c,p,u,h){const g=o(c),v=Math.ceil(u*g/8),C=new Uint8Array((v+1+c.interlace)*h);return p=c.tabs.CgBI?r(p,C):n(p,C),c.interlace==0?p=l(p,c,0,u,h):c.interlace==1&&(p=function(N,M){const I=M.width,F=M.height,S=o(M),R=S>>3,w=Math.ceil(I*S/8),m=new Uint8Array(F*w);let T=0;const E=[0,0,4,0,2,0,1],O=[0,4,0,2,0,1,0],A=[8,8,8,4,4,2,2],_=[8,8,4,4,2,2,1];let D=0;for(;D<7;){const y=A[D],G=_[D];let $=0,K=0,J=E[D];for(;J<F;)J+=y,K++;let Y=O[D];for(;Y<I;)Y+=G,$++;const ee=Math.ceil($*S/8);l(N,M,T,$,K);let Z=0,q=E[D];for(;q<F;){let Q=O[D],oe=T+Z*ee<<3;for(;Q<I;){var x;if(S==1&&(x=(x=N[oe>>3])>>7-(7&oe)&1,m[q*w+(Q>>3)]|=x<<7-((7&Q)<<0)),S==2&&(x=(x=N[oe>>3])>>6-(7&oe)&3,m[q*w+(Q>>2)]|=x<<6-((3&Q)<<1)),S==4&&(x=(x=N[oe>>3])>>4-(7&oe)&15,m[q*w+(Q>>1)]|=x<<4-((1&Q)<<2)),S>=8){const de=q*w+Q*R;for(let pe=0;pe<R;pe++)m[de+pe]=N[(oe>>3)+pe]}oe+=S,Q+=G}Z++,q+=y}$*K!=0&&(T+=K*(1+ee)),D+=1}return m}(p,c)),p}function n(c,p){return r(new Uint8Array(c.buffer,2,c.length-6),p)}var r=function(){const c={H:{}};return c.H.N=function(p,u){const h=Uint8Array;let g,v,C=0,j=0,N=0,M=0,I=0,F=0,S=0,R=0,w=0;if(p[0]==3&&p[1]==0)return u||new h(0);const m=c.H,T=m.b,E=m.e,O=m.R,A=m.n,_=m.A,D=m.Z,x=m.m,y=u==null;for(y&&(u=new h(p.length>>>2<<5));C==0;)if(C=T(p,w,1),j=T(p,w+1,2),w+=3,j!=0){if(y&&(u=c.H.W(u,R+(1<<17))),j==1&&(g=x.J,v=x.h,F=511,S=31),j==2){N=E(p,w,5)+257,M=E(p,w+5,5)+1,I=E(p,w+10,4)+4,w+=14;let $=1;for(var G=0;G<38;G+=2)x.Q[G]=0,x.Q[G+1]=0;for(G=0;G<I;G++){const Y=E(p,w+3*G,3);x.Q[1+(x.X[G]<<1)]=Y,Y>$&&($=Y)}w+=3*I,A(x.Q,$),_(x.Q,$,x.u),g=x.w,v=x.d,w=O(x.u,(1<<$)-1,N+M,p,w,x.v);const K=m.V(x.v,0,N,x.C);F=(1<<K)-1;const J=m.V(x.v,N,M,x.D);S=(1<<J)-1,A(x.C,K),_(x.C,K,g),A(x.D,J),_(x.D,J,v)}for(;;){const $=g[D(p,w)&F];w+=15&$;const K=$>>>4;if(!(K>>>8))u[R++]=K;else{if(K==256)break;{let J=R+K-254;if(K>264){const Q=x.q[K-257];J=R+(Q>>>3)+E(p,w,7&Q),w+=7&Q}const Y=v[D(p,w)&S];w+=15&Y;const ee=Y>>>4,Z=x.c[ee],q=(Z>>>4)+T(p,w,15&Z);for(w+=15&Z;R<J;)u[R]=u[R++-q],u[R]=u[R++-q],u[R]=u[R++-q],u[R]=u[R++-q];R=J}}}}else{7&w&&(w+=8-(7&w));const $=4+(w>>>3),K=p[$-4]|p[$-3]<<8;y&&(u=c.H.W(u,R+K)),u.set(new h(p.buffer,p.byteOffset+$,K),R),w=$+K<<3,R+=K}return u.length==R?u:u.slice(0,R)},c.H.W=function(p,u){const h=p.length;if(u<=h)return p;const g=new Uint8Array(h<<1);return g.set(p,0),g},c.H.R=function(p,u,h,g,v,C){const j=c.H.e,N=c.H.Z;let M=0;for(;M<h;){const I=p[N(g,v)&u];v+=15&I;const F=I>>>4;if(F<=15)C[M]=F,M++;else{let S=0,R=0;F==16?(R=3+j(g,v,2),v+=2,S=C[M-1]):F==17?(R=3+j(g,v,3),v+=3):F==18&&(R=11+j(g,v,7),v+=7);const w=M+R;for(;M<w;)C[M]=S,M++}}return v},c.H.V=function(p,u,h,g){let v=0,C=0;const j=g.length>>>1;for(;C<h;){const N=p[C+u];g[C<<1]=0,g[1+(C<<1)]=N,N>v&&(v=N),C++}for(;C<j;)g[C<<1]=0,g[1+(C<<1)]=0,C++;return v},c.H.n=function(p,u){const h=c.H.m,g=p.length;let v,C,j,N;const M=h.j;for(var I=0;I<=u;I++)M[I]=0;for(I=1;I<g;I+=2)M[p[I]]++;const F=h.K;for(v=0,M[0]=0,C=1;C<=u;C++)v=v+M[C-1]<<1,F[C]=v;for(j=0;j<g;j+=2)N=p[j+1],N!=0&&(p[j]=F[N],F[N]++)},c.H.A=function(p,u,h){const g=p.length,v=c.H.m.r;for(let C=0;C<g;C+=2)if(p[C+1]!=0){const j=C>>1,N=p[C+1],M=j<<4|N,I=u-N;let F=p[C]<<I;const S=F+(1<<I);for(;F!=S;)h[v[F]>>>15-u]=M,F++}},c.H.l=function(p,u){const h=c.H.m.r,g=15-u;for(let v=0;v<p.length;v+=2){const C=p[v]<<u-p[v+1];p[v]=h[C]>>>g}},c.H.M=function(p,u,h){h<<=7&u;const g=u>>>3;p[g]|=h,p[g+1]|=h>>>8},c.H.I=function(p,u,h){h<<=7&u;const g=u>>>3;p[g]|=h,p[g+1]|=h>>>8,p[g+2]|=h>>>16},c.H.e=function(p,u,h){return(p[u>>>3]|p[1+(u>>>3)]<<8)>>>(7&u)&(1<<h)-1},c.H.b=function(p,u,h){return(p[u>>>3]|p[1+(u>>>3)]<<8|p[2+(u>>>3)]<<16)>>>(7&u)&(1<<h)-1},c.H.Z=function(p,u){return(p[u>>>3]|p[1+(u>>>3)]<<8|p[2+(u>>>3)]<<16)>>>(7&u)},c.H.i=function(p,u){return(p[u>>>3]|p[1+(u>>>3)]<<8|p[2+(u>>>3)]<<16|p[3+(u>>>3)]<<24)>>>(7&u)},c.H.m=function(){const p=Uint16Array,u=Uint32Array;return{K:new p(16),j:new p(16),X:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],S:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],T:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],q:new p(32),p:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],z:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],c:new u(32),J:new p(512),_:[],h:new p(32),$:[],w:new p(32768),C:[],v:[],d:new p(32768),D:[],u:new p(512),Q:[],r:new p(32768),s:new u(286),Y:new u(30),a:new u(19),t:new u(15e3),k:new p(65536),g:new p(32768)}}(),function(){const p=c.H.m;for(var u=0;u<32768;u++){let g=u;g=(2863311530&g)>>>1|(1431655765&g)<<1,g=(3435973836&g)>>>2|(858993459&g)<<2,g=(4042322160&g)>>>4|(252645135&g)<<4,g=(4278255360&g)>>>8|(16711935&g)<<8,p.r[u]=(g>>>16|g<<16)>>>17}function h(g,v,C){for(;v--!=0;)g.push(0,C)}for(u=0;u<32;u++)p.q[u]=p.S[u]<<3|p.T[u],p.c[u]=p.p[u]<<4|p.z[u];h(p._,144,8),h(p._,112,9),h(p._,24,7),h(p._,8,8),c.H.n(p._,9),c.H.A(p._,9,p.J),c.H.l(p._,9),h(p.$,32,5),c.H.n(p.$,5),c.H.A(p.$,5,p.h),c.H.l(p.$,5),h(p.Q,19,0),h(p.C,286,0),h(p.D,30,0),h(p.v,320,0)}(),c.H.N}();function o(c){return[1,null,3,1,2,null,4][c.ctype]*c.depth}function l(c,p,u,h,g){let v=o(p);const C=Math.ceil(h*v/8);let j,N;v=Math.ceil(v/8);let M=c[u],I=0;if(M>1&&(c[u]=[0,0,1][M-2]),M==3)for(I=v;I<C;I++)c[I+1]=c[I+1]+(c[I+1-v]>>>1)&255;for(let F=0;F<g;F++)if(j=u+F*C,N=j+F+1,M=c[N-1],I=0,M==0)for(;I<C;I++)c[j+I]=c[N+I];else if(M==1){for(;I<v;I++)c[j+I]=c[N+I];for(;I<C;I++)c[j+I]=c[N+I]+c[j+I-v]}else if(M==2)for(;I<C;I++)c[j+I]=c[N+I]+c[j+I-C];else if(M==3){for(;I<v;I++)c[j+I]=c[N+I]+(c[j+I-C]>>>1);for(;I<C;I++)c[j+I]=c[N+I]+(c[j+I-C]+c[j+I-v]>>>1)}else{for(;I<v;I++)c[j+I]=c[N+I]+i(0,c[j+I-C],0);for(;I<C;I++)c[j+I]=c[N+I]+i(c[j+I-v],c[j+I-C],c[j+I-v-C])}return c}function i(c,p,u){const h=c+p-u,g=h-c,v=h-p,C=h-u;return g*g<=v*v&&g*g<=C*C?c:v*v<=C*C?p:u}function d(c,p,u){u.width=e.readUint(c,p),p+=4,u.height=e.readUint(c,p),p+=4,u.depth=c[p],p++,u.ctype=c[p],p++,u.compress=c[p],p++,u.filter=c[p],p++,u.interlace=c[p],p++}function f(c,p,u,h,g,v,C,j,N){const M=Math.min(p,g),I=Math.min(u,v);let F=0,S=0;for(let D=0;D<I;D++)for(let x=0;x<M;x++)if(C>=0&&j>=0?(F=D*p+x<<2,S=(j+D)*g+C+x<<2):(F=(-j+D)*p-C+x<<2,S=D*g+x<<2),N==0)h[S]=c[F],h[S+1]=c[F+1],h[S+2]=c[F+2],h[S+3]=c[F+3];else if(N==1){var R=c[F+3]*.00392156862745098,w=c[F]*R,m=c[F+1]*R,T=c[F+2]*R,E=h[S+3]*(1/255),O=h[S]*E,A=h[S+1]*E,_=h[S+2]*E;const y=1-R,G=R+E*y,$=G==0?0:1/G;h[S+3]=255*G,h[S+0]=(w+O*y)*$,h[S+1]=(m+A*y)*$,h[S+2]=(T+_*y)*$}else if(N==2)R=c[F+3],w=c[F],m=c[F+1],T=c[F+2],E=h[S+3],O=h[S],A=h[S+1],_=h[S+2],R==E&&w==O&&m==A&&T==_?(h[S]=0,h[S+1]=0,h[S+2]=0,h[S+3]=0):(h[S]=w,h[S+1]=m,h[S+2]=T,h[S+3]=R);else if(N==3){if(R=c[F+3],w=c[F],m=c[F+1],T=c[F+2],E=h[S+3],O=h[S],A=h[S+1],_=h[S+2],R==E&&w==O&&m==A&&T==_)continue;if(R<220&&E>20)return!1}return!0}return{decode:function(p){const u=new Uint8Array(p);let h=8;const g=e,v=g.readUshort,C=g.readUint,j={tabs:{},frames:[]},N=new Uint8Array(u.length);let M,I=0,F=0;const S=[137,80,78,71,13,10,26,10];for(var R=0;R<8;R++)if(u[R]!=S[R])throw"The input is not a PNG file!";for(;h<u.length;){const D=g.readUint(u,h);h+=4;const x=g.readASCII(u,h,4);if(h+=4,x=="IHDR")d(u,h,j);else if(x=="iCCP"){for(var w=h;u[w]!=0;)w++;g.readASCII(u,h,w-h),u[w+1];const y=u.slice(w+2,h+D);let G=null;try{G=n(y)}catch{G=r(y)}j.tabs[x]=G}else if(x=="CgBI")j.tabs[x]=u.slice(h,h+4);else if(x=="IDAT"){for(R=0;R<D;R++)N[I+R]=u[h+R];I+=D}else if(x=="acTL")j.tabs[x]={num_frames:C(u,h),num_plays:C(u,h+4)},M=new Uint8Array(u.length);else if(x=="fcTL"){F!=0&&((_=j.frames[j.frames.length-1]).data=a(j,M.slice(0,F),_.rect.width,_.rect.height),F=0);const y={x:C(u,h+12),y:C(u,h+16),width:C(u,h+4),height:C(u,h+8)};let G=v(u,h+22);G=v(u,h+20)/(G==0?100:G);const $={rect:y,delay:Math.round(1e3*G),dispose:u[h+24],blend:u[h+25]};j.frames.push($)}else if(x=="fdAT"){for(R=0;R<D-4;R++)M[F+R]=u[h+R+4];F+=D-4}else if(x=="pHYs")j.tabs[x]=[g.readUint(u,h),g.readUint(u,h+4),u[h+8]];else if(x=="cHRM")for(j.tabs[x]=[],R=0;R<8;R++)j.tabs[x].push(g.readUint(u,h+4*R));else if(x=="tEXt"||x=="zTXt"){j.tabs[x]==null&&(j.tabs[x]={});var m=g.nextZero(u,h),T=g.readASCII(u,h,m-h),E=h+D-m-1;if(x=="tEXt")A=g.readASCII(u,m+1,E);else{var O=n(u.slice(m+2,m+2+E));A=g.readUTF8(O,0,O.length)}j.tabs[x][T]=A}else if(x=="iTXt"){j.tabs[x]==null&&(j.tabs[x]={}),m=0,w=h,m=g.nextZero(u,w),T=g.readASCII(u,w,m-w);const y=u[w=m+1];var A;u[w+1],w+=2,m=g.nextZero(u,w),g.readASCII(u,w,m-w),w=m+1,m=g.nextZero(u,w),g.readUTF8(u,w,m-w),E=D-((w=m+1)-h),y==0?A=g.readUTF8(u,w,E):(O=n(u.slice(w,w+E)),A=g.readUTF8(O,0,O.length)),j.tabs[x][T]=A}else if(x=="PLTE")j.tabs[x]=g.readBytes(u,h,D);else if(x=="hIST"){const y=j.tabs.PLTE.length/3;for(j.tabs[x]=[],R=0;R<y;R++)j.tabs[x].push(v(u,h+2*R))}else if(x=="tRNS")j.ctype==3?j.tabs[x]=g.readBytes(u,h,D):j.ctype==0?j.tabs[x]=v(u,h):j.ctype==2&&(j.tabs[x]=[v(u,h),v(u,h+2),v(u,h+4)]);else if(x=="gAMA")j.tabs[x]=g.readUint(u,h)/1e5;else if(x=="sRGB")j.tabs[x]=u[h];else if(x=="bKGD")j.ctype==0||j.ctype==4?j.tabs[x]=[v(u,h)]:j.ctype==2||j.ctype==6?j.tabs[x]=[v(u,h),v(u,h+2),v(u,h+4)]:j.ctype==3&&(j.tabs[x]=u[h]);else if(x=="IEND")break;h+=D,g.readUint(u,h),h+=4}var _;return F!=0&&((_=j.frames[j.frames.length-1]).data=a(j,M.slice(0,F),_.rect.width,_.rect.height)),j.data=a(j,N,j.width,j.height),delete j.compress,delete j.interlace,delete j.filter,j},toRGBA8:function(p){const u=p.width,h=p.height;if(p.tabs.acTL==null)return[t(p.data,u,h,p).buffer];const g=[];p.frames[0].data==null&&(p.frames[0].data=p.data);const v=u*h*4,C=new Uint8Array(v),j=new Uint8Array(v),N=new Uint8Array(v);for(let I=0;I<p.frames.length;I++){const F=p.frames[I],S=F.rect.x,R=F.rect.y,w=F.rect.width,m=F.rect.height,T=t(F.data,w,m,p);if(I!=0)for(var M=0;M<v;M++)N[M]=C[M];if(F.blend==0?f(T,w,m,C,u,h,S,R,0):F.blend==1&&f(T,w,m,C,u,h,S,R,1),g.push(C.buffer.slice(0)),F.dispose!=0){if(F.dispose==1)f(j,w,m,C,u,h,S,R,0);else if(F.dispose==2)for(M=0;M<v;M++)C[M]=N[M]}}return g},_paeth:i,_copyTile:f,_bin:e}}();(function(){const{_copyTile:e}=It,{_bin:t}=It,a=It._paeth;var n={table:function(){const w=new Uint32Array(256);for(let m=0;m<256;m++){let T=m;for(let E=0;E<8;E++)1&T?T=3988292384^T>>>1:T>>>=1;w[m]=T}return w}(),update(w,m,T,E){for(let O=0;O<E;O++)w=n.table[255&(w^m[T+O])]^w>>>8;return w},crc:(w,m,T)=>4294967295^n.update(4294967295,w,m,T)};function r(w,m,T,E){m[T]+=w[0]*E>>4,m[T+1]+=w[1]*E>>4,m[T+2]+=w[2]*E>>4,m[T+3]+=w[3]*E>>4}function o(w){return Math.max(0,Math.min(255,w))}function l(w,m){const T=w[0]-m[0],E=w[1]-m[1],O=w[2]-m[2],A=w[3]-m[3];return T*T+E*E+O*O+A*A}function i(w,m,T,E,O,A,_){_==null&&(_=1);const D=E.length,x=[];for(var y=0;y<D;y++){const q=E[y];x.push([q>>>0&255,q>>>8&255,q>>>16&255,q>>>24&255])}for(y=0;y<D;y++){let q=4294967295;for(var G=0,$=0;$<D;$++){var K=l(x[y],x[$]);$!=y&&K<q&&(q=K,G=$)}}const J=new Uint32Array(O.buffer),Y=new Int16Array(m*T*4),ee=[0,8,2,10,12,4,14,6,3,11,1,9,15,7,13,5];for(y=0;y<ee.length;y++)ee[y]=255*((ee[y]+.5)/16-.5);for(let q=0;q<T;q++)for(let Q=0;Q<m;Q++){var Z;y=4*(q*m+Q),_!=2?Z=[o(w[y]+Y[y]),o(w[y+1]+Y[y+1]),o(w[y+2]+Y[y+2]),o(w[y+3]+Y[y+3])]:(K=ee[4*(3&q)+(3&Q)],Z=[o(w[y]+K),o(w[y+1]+K),o(w[y+2]+K),o(w[y+3]+K)]),G=0;let oe=16777215;for($=0;$<D;$++){const Ce=l(Z,x[$]);Ce<oe&&(oe=Ce,G=$)}const de=x[G],pe=[Z[0]-de[0],Z[1]-de[1],Z[2]-de[2],Z[3]-de[3]];_==1&&(Q!=m-1&&r(pe,Y,y+4,7),q!=T-1&&(Q!=0&&r(pe,Y,y+4*m-4,3),r(pe,Y,y+4*m,5),Q!=m-1&&r(pe,Y,y+4*m+4,1))),A[y>>2]=G,J[y>>2]=E[G]}}function d(w,m,T,E,O){O==null&&(O={});const{crc:A}=n,_=t.writeUint,D=t.writeUshort,x=t.writeASCII;let y=8;const G=w.frames.length>1;let $,K=!1,J=33+(G?20:0);if(O.sRGB!=null&&(J+=13),O.pHYs!=null&&(J+=21),O.iCCP!=null&&($=pako.deflate(O.iCCP),J+=21+$.length+4),w.ctype==3){for(var Y=w.plte.length,ee=0;ee<Y;ee++)w.plte[ee]>>>24!=255&&(K=!0);J+=8+3*Y+4+(K?8+1*Y+4:0)}for(var Z=0;Z<w.frames.length;Z++)G&&(J+=38),J+=(de=w.frames[Z]).cimg.length+12,Z!=0&&(J+=4);J+=12;const q=new Uint8Array(J),Q=[137,80,78,71,13,10,26,10];for(ee=0;ee<8;ee++)q[ee]=Q[ee];if(_(q,y,13),y+=4,x(q,y,"IHDR"),y+=4,_(q,y,m),y+=4,_(q,y,T),y+=4,q[y]=w.depth,y++,q[y]=w.ctype,y++,q[y]=0,y++,q[y]=0,y++,q[y]=0,y++,_(q,y,A(q,y-17,17)),y+=4,O.sRGB!=null&&(_(q,y,1),y+=4,x(q,y,"sRGB"),y+=4,q[y]=O.sRGB,y++,_(q,y,A(q,y-5,5)),y+=4),O.iCCP!=null){const pe=13+$.length;_(q,y,pe),y+=4,x(q,y,"iCCP"),y+=4,x(q,y,"ICC profile"),y+=11,y+=2,q.set($,y),y+=$.length,_(q,y,A(q,y-(pe+4),pe+4)),y+=4}if(O.pHYs!=null&&(_(q,y,9),y+=4,x(q,y,"pHYs"),y+=4,_(q,y,O.pHYs[0]),y+=4,_(q,y,O.pHYs[1]),y+=4,q[y]=O.pHYs[2],y++,_(q,y,A(q,y-13,13)),y+=4),G&&(_(q,y,8),y+=4,x(q,y,"acTL"),y+=4,_(q,y,w.frames.length),y+=4,_(q,y,O.loop!=null?O.loop:0),y+=4,_(q,y,A(q,y-12,12)),y+=4),w.ctype==3){for(_(q,y,3*(Y=w.plte.length)),y+=4,x(q,y,"PLTE"),y+=4,ee=0;ee<Y;ee++){const pe=3*ee,Ce=w.plte[ee],re=255&Ce,U=Ce>>>8&255,ne=Ce>>>16&255;q[y+pe+0]=re,q[y+pe+1]=U,q[y+pe+2]=ne}if(y+=3*Y,_(q,y,A(q,y-3*Y-4,3*Y+4)),y+=4,K){for(_(q,y,Y),y+=4,x(q,y,"tRNS"),y+=4,ee=0;ee<Y;ee++)q[y+ee]=w.plte[ee]>>>24&255;y+=Y,_(q,y,A(q,y-Y-4,Y+4)),y+=4}}let oe=0;for(Z=0;Z<w.frames.length;Z++){var de=w.frames[Z];G&&(_(q,y,26),y+=4,x(q,y,"fcTL"),y+=4,_(q,y,oe++),y+=4,_(q,y,de.rect.width),y+=4,_(q,y,de.rect.height),y+=4,_(q,y,de.rect.x),y+=4,_(q,y,de.rect.y),y+=4,D(q,y,E[Z]),y+=2,D(q,y,1e3),y+=2,q[y]=de.dispose,y++,q[y]=de.blend,y++,_(q,y,A(q,y-30,30)),y+=4);const pe=de.cimg;_(q,y,(Y=pe.length)+(Z==0?0:4)),y+=4;const Ce=y;x(q,y,Z==0?"IDAT":"fdAT"),y+=4,Z!=0&&(_(q,y,oe++),y+=4),q.set(pe,y),y+=Y,_(q,y,A(q,Ce,y-Ce)),y+=4}return _(q,y,0),y+=4,x(q,y,"IEND"),y+=4,_(q,y,A(q,y-4,4)),y+=4,q.buffer}function f(w,m,T){for(let E=0;E<w.frames.length;E++){const O=w.frames[E];O.rect.width;const A=O.rect.height,_=new Uint8Array(A*O.bpl+A);O.cimg=h(O.img,A,O.bpp,O.bpl,_,m,T)}}function c(w,m,T,E,O){const A=O[0],_=O[1],D=O[2],x=O[3],y=O[4],G=O[5];let $=6,K=8,J=255;for(var Y=0;Y<w.length;Y++){const ce=new Uint8Array(w[Y]);for(var ee=ce.length,Z=0;Z<ee;Z+=4)J&=ce[Z+3]}const q=J!=255,Q=function(k,L,B,W,z,te){const X=[];for(var ae=0;ae<k.length;ae++){const Ne=new Uint8Array(k[ae]),le=new Uint32Array(Ne.buffer);var me;let ie=0,ue=0,be=L,Te=B,Le=W?1:0;if(ae!=0){const ze=te||W||ae==1||X[ae-2].dispose!=0?1:2;let Ke=0,He=1e9;for(let we=0;we<ze;we++){var ge=new Uint8Array(k[ae-1-we]);const ve=new Uint32Array(k[ae-1-we]);let Ee=L,Pe=B,je=-1,Fe=-1;for(let Re=0;Re<B;Re++)for(let Ae=0;Ae<L;Ae++)le[_e=Re*L+Ae]!=ve[_e]&&(Ae<Ee&&(Ee=Ae),Ae>je&&(je=Ae),Re<Pe&&(Pe=Re),Re>Fe&&(Fe=Re));je==-1&&(Ee=Pe=je=Fe=0),z&&((1&Ee)==1&&Ee--,(1&Pe)==1&&Pe--);const Xe=(je-Ee+1)*(Fe-Pe+1);Xe<He&&(He=Xe,Ke=we,ie=Ee,ue=Pe,be=je-Ee+1,Te=Fe-Pe+1)}ge=new Uint8Array(k[ae-1-Ke]),Ke==1&&(X[ae-1].dispose=2),me=new Uint8Array(be*Te*4),e(ge,L,B,me,be,Te,-ie,-ue,0),Le=e(Ne,L,B,me,be,Te,-ie,-ue,3)?1:0,Le==1?u(Ne,L,B,me,{x:ie,y:ue,width:be,height:Te}):e(Ne,L,B,me,be,Te,-ie,-ue,0)}else me=Ne.slice(0);X.push({rect:{x:ie,y:ue,width:be,height:Te},img:me,blend:Le,dispose:0})}if(W)for(ae=0;ae<X.length;ae++){if((Oe=X[ae]).blend==1)continue;const Ne=Oe.rect,le=X[ae-1].rect,ie=Math.min(Ne.x,le.x),ue=Math.min(Ne.y,le.y),be={x:ie,y:ue,width:Math.max(Ne.x+Ne.width,le.x+le.width)-ie,height:Math.max(Ne.y+Ne.height,le.y+le.height)-ue};X[ae-1].dispose=1,ae-1!=0&&p(k,L,B,X,ae-1,be,z),p(k,L,B,X,ae,be,z)}let he=0;if(k.length!=1)for(var _e=0;_e<X.length;_e++){var Oe;he+=(Oe=X[_e]).rect.width*Oe.rect.height}return X}(w,m,T,A,_,D),oe={},de=[],pe=[];if(E!=0){const ce=[];for(Z=0;Z<Q.length;Z++)ce.push(Q[Z].img.buffer);const k=function(z){let te=0;for(var X=0;X<z.length;X++)te+=z[X].byteLength;const ae=new Uint8Array(te);let me=0;for(X=0;X<z.length;X++){const ge=new Uint8Array(z[X]),he=ge.length;for(let _e=0;_e<he;_e+=4){let Oe=ge[_e],Ne=ge[_e+1],le=ge[_e+2];const ie=ge[_e+3];ie==0&&(Oe=Ne=le=0),ae[me+_e]=Oe,ae[me+_e+1]=Ne,ae[me+_e+2]=le,ae[me+_e+3]=ie}me+=he}return ae.buffer}(ce),L=v(k,E);for(Z=0;Z<L.plte.length;Z++)de.push(L.plte[Z].est.rgba);let B=0;for(Z=0;Z<Q.length;Z++){const W=(re=Q[Z]).img.length;var Ce=new Uint8Array(L.inds.buffer,B>>2,W>>2);pe.push(Ce);const z=new Uint8Array(L.abuf,B,W);G&&i(re.img,re.rect.width,re.rect.height,de,z,Ce),re.img.set(z),B+=W}}else for(Y=0;Y<Q.length;Y++){var re=Q[Y];const ce=new Uint32Array(re.img.buffer);var U=re.rect.width;for(ee=ce.length,Ce=new Uint8Array(ee),pe.push(Ce),Z=0;Z<ee;Z++){const k=ce[Z];if(Z!=0&&k==ce[Z-1])Ce[Z]=Ce[Z-1];else if(Z>U&&k==ce[Z-U])Ce[Z]=Ce[Z-U];else{let L=oe[k];if(L==null&&(oe[k]=L=de.length,de.push(k),de.length>=300))break;Ce[Z]=L}}}const ne=de.length;for(ne<=256&&y==0&&(K=ne<=2?1:ne<=4?2:ne<=16?4:8,K=Math.max(K,x)),Y=0;Y<Q.length;Y++){(re=Q[Y]).rect.x,re.rect.y,U=re.rect.width;const ce=re.rect.height;let k=re.img;new Uint32Array(k.buffer);let L=4*U,B=4;if(ne<=256&&y==0){L=Math.ceil(K*U/8);var H=new Uint8Array(L*ce);const W=pe[Y];for(let z=0;z<ce;z++){Z=z*L;const te=z*U;if(K==8)for(var V=0;V<U;V++)H[Z+V]=W[te+V];else if(K==4)for(V=0;V<U;V++)H[Z+(V>>1)]|=W[te+V]<<4-4*(1&V);else if(K==2)for(V=0;V<U;V++)H[Z+(V>>2)]|=W[te+V]<<6-2*(3&V);else if(K==1)for(V=0;V<U;V++)H[Z+(V>>3)]|=W[te+V]<<7-1*(7&V)}k=H,$=3,B=1}else if(q==0&&Q.length==1){H=new Uint8Array(U*ce*3);const W=U*ce;for(Z=0;Z<W;Z++){const z=3*Z,te=4*Z;H[z]=k[te],H[z+1]=k[te+1],H[z+2]=k[te+2]}k=H,$=2,B=3,L=3*U}re.img=k,re.bpl=L,re.bpp=B}return{ctype:$,depth:K,plte:de,frames:Q}}function p(w,m,T,E,O,A,_){const D=Uint8Array,x=Uint32Array,y=new D(w[O-1]),G=new x(w[O-1]),$=O+1<w.length?new D(w[O+1]):null,K=new D(w[O]),J=new x(K.buffer);let Y=m,ee=T,Z=-1,q=-1;for(let oe=0;oe<A.height;oe++)for(let de=0;de<A.width;de++){const pe=A.x+de,Ce=A.y+oe,re=Ce*m+pe,U=J[re];U==0||E[O-1].dispose==0&&G[re]==U&&($==null||$[4*re+3]!=0)||(pe<Y&&(Y=pe),pe>Z&&(Z=pe),Ce<ee&&(ee=Ce),Ce>q&&(q=Ce))}Z==-1&&(Y=ee=Z=q=0),_&&((1&Y)==1&&Y--,(1&ee)==1&&ee--),A={x:Y,y:ee,width:Z-Y+1,height:q-ee+1};const Q=E[O];Q.rect=A,Q.blend=1,Q.img=new Uint8Array(A.width*A.height*4),E[O-1].dispose==0?(e(y,m,T,Q.img,A.width,A.height,-A.x,-A.y,0),u(K,m,T,Q.img,A)):e(K,m,T,Q.img,A.width,A.height,-A.x,-A.y,0)}function u(w,m,T,E,O){e(w,m,T,E,O.width,O.height,-O.x,-O.y,2)}function h(w,m,T,E,O,A,_){const D=[];let x,y=[0,1,2,3,4];A!=-1?y=[A]:(m*E>5e5||T==1)&&(y=[0]),_&&(x={level:0});const G=$v;for(var $=0;$<y.length;$++){for(let Y=0;Y<m;Y++)g(O,w,Y,E,T,y[$]);D.push(G.deflate(O,x))}let K,J=1e9;for($=0;$<D.length;$++)D[$].length<J&&(K=$,J=D[$].length);return D[K]}function g(w,m,T,E,O,A){const _=T*E;let D=_+T;if(w[D]=A,D++,A==0)if(E<500)for(var x=0;x<E;x++)w[D+x]=m[_+x];else w.set(new Uint8Array(m.buffer,_,E),D);else if(A==1){for(x=0;x<O;x++)w[D+x]=m[_+x];for(x=O;x<E;x++)w[D+x]=m[_+x]-m[_+x-O]+256&255}else if(T==0){for(x=0;x<O;x++)w[D+x]=m[_+x];if(A==2)for(x=O;x<E;x++)w[D+x]=m[_+x];if(A==3)for(x=O;x<E;x++)w[D+x]=m[_+x]-(m[_+x-O]>>1)+256&255;if(A==4)for(x=O;x<E;x++)w[D+x]=m[_+x]-a(m[_+x-O],0,0)+256&255}else{if(A==2)for(x=0;x<E;x++)w[D+x]=m[_+x]+256-m[_+x-E]&255;if(A==3){for(x=0;x<O;x++)w[D+x]=m[_+x]+256-(m[_+x-E]>>1)&255;for(x=O;x<E;x++)w[D+x]=m[_+x]+256-(m[_+x-E]+m[_+x-O]>>1)&255}if(A==4){for(x=0;x<O;x++)w[D+x]=m[_+x]+256-a(0,m[_+x-E],0)&255;for(x=O;x<E;x++)w[D+x]=m[_+x]+256-a(m[_+x-O],m[_+x-E],m[_+x-O-E])&255}}}function v(w,m){const T=new Uint8Array(w),E=T.slice(0),O=new Uint32Array(E.buffer),A=C(E,m),_=A[0],D=A[1],x=T.length,y=new Uint8Array(x>>2);let G;if(T.length<2e7)for(var $=0;$<x;$+=4)G=j(_,K=T[$]*(1/255),J=T[$+1]*(1/255),Y=T[$+2]*(1/255),ee=T[$+3]*(1/255)),y[$>>2]=G.ind,O[$>>2]=G.est.rgba;else for($=0;$<x;$+=4){var K=T[$]*.00392156862745098,J=T[$+1]*(1/255),Y=T[$+2]*(1/255),ee=T[$+3]*(1/255);for(G=_;G.left;)G=N(G.est,K,J,Y,ee)<=0?G.left:G.right;y[$>>2]=G.ind,O[$>>2]=G.est.rgba}return{abuf:E.buffer,inds:y,plte:D}}function C(w,m,T){T==null&&(T=1e-4);const E=new Uint32Array(w.buffer),O={i0:0,i1:w.length,bst:null,est:null,tdst:0,left:null,right:null};O.bst=F(w,O.i0,O.i1),O.est=S(O.bst);const A=[O];for(;A.length<m;){let D=0,x=0;for(var _=0;_<A.length;_++)A[_].est.L>D&&(D=A[_].est.L,x=_);if(D<T)break;const y=A[x],G=M(w,E,y.i0,y.i1,y.est.e,y.est.eMq255);if(y.i0>=G||y.i1<=G){y.est.L=0;continue}const $={i0:y.i0,i1:G,bst:null,est:null,tdst:0,left:null,right:null};$.bst=F(w,$.i0,$.i1),$.est=S($.bst);const K={i0:G,i1:y.i1,bst:null,est:null,tdst:0,left:null,right:null};for(K.bst={R:[],m:[],N:y.bst.N-$.bst.N},_=0;_<16;_++)K.bst.R[_]=y.bst.R[_]-$.bst.R[_];for(_=0;_<4;_++)K.bst.m[_]=y.bst.m[_]-$.bst.m[_];K.est=S(K.bst),y.left=$,y.right=K,A[x]=$,A.push(K)}for(A.sort((D,x)=>x.bst.N-D.bst.N),_=0;_<A.length;_++)A[_].ind=_;return[O,A]}function j(w,m,T,E,O){if(w.left==null)return w.tdst=function($,K,J,Y,ee){const Z=K-$[0],q=J-$[1],Q=Y-$[2],oe=ee-$[3];return Z*Z+q*q+Q*Q+oe*oe}(w.est.q,m,T,E,O),w;const A=N(w.est,m,T,E,O);let _=w.left,D=w.right;A>0&&(_=w.right,D=w.left);const x=j(_,m,T,E,O);if(x.tdst<=A*A)return x;const y=j(D,m,T,E,O);return y.tdst<x.tdst?y:x}function N(w,m,T,E,O){const{e:A}=w;return A[0]*m+A[1]*T+A[2]*E+A[3]*O-w.eMq}function M(w,m,T,E,O,A){for(E-=4;T<E;){for(;I(w,T,O)<=A;)T+=4;for(;I(w,E,O)>A;)E-=4;if(T>=E)break;const _=m[T>>2];m[T>>2]=m[E>>2],m[E>>2]=_,T+=4,E-=4}for(;I(w,T,O)>A;)T-=4;return T+4}function I(w,m,T){return w[m]*T[0]+w[m+1]*T[1]+w[m+2]*T[2]+w[m+3]*T[3]}function F(w,m,T){const E=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],O=[0,0,0,0],A=T-m>>2;for(let _=m;_<T;_+=4){const D=w[_]*.00392156862745098,x=w[_+1]*(1/255),y=w[_+2]*(1/255),G=w[_+3]*(1/255);O[0]+=D,O[1]+=x,O[2]+=y,O[3]+=G,E[0]+=D*D,E[1]+=D*x,E[2]+=D*y,E[3]+=D*G,E[5]+=x*x,E[6]+=x*y,E[7]+=x*G,E[10]+=y*y,E[11]+=y*G,E[15]+=G*G}return E[4]=E[1],E[8]=E[2],E[9]=E[6],E[12]=E[3],E[13]=E[7],E[14]=E[11],{R:E,m:O,N:A}}function S(w){const{R:m}=w,{m:T}=w,{N:E}=w,O=T[0],A=T[1],_=T[2],D=T[3],x=E==0?0:1/E,y=[m[0]-O*O*x,m[1]-O*A*x,m[2]-O*_*x,m[3]-O*D*x,m[4]-A*O*x,m[5]-A*A*x,m[6]-A*_*x,m[7]-A*D*x,m[8]-_*O*x,m[9]-_*A*x,m[10]-_*_*x,m[11]-_*D*x,m[12]-D*O*x,m[13]-D*A*x,m[14]-D*_*x,m[15]-D*D*x],G=y,$=R;let K=[Math.random(),Math.random(),Math.random(),Math.random()],J=0,Y=0;if(E!=0)for(let Z=0;Z<16&&(K=$.multVec(G,K),Y=Math.sqrt($.dot(K,K)),K=$.sml(1/Y,K),!(Z!=0&&Math.abs(Y-J)<1e-9));Z++)J=Y;const ee=[O*x,A*x,_*x,D*x];return{Cov:y,q:ee,e:K,L:J,eMq255:$.dot($.sml(255,ee),K),eMq:$.dot(K,ee),rgba:(Math.round(255*ee[3])<<24|Math.round(255*ee[2])<<16|Math.round(255*ee[1])<<8|Math.round(255*ee[0])<<0)>>>0}}var R={multVec:(w,m)=>[w[0]*m[0]+w[1]*m[1]+w[2]*m[2]+w[3]*m[3],w[4]*m[0]+w[5]*m[1]+w[6]*m[2]+w[7]*m[3],w[8]*m[0]+w[9]*m[1]+w[10]*m[2]+w[11]*m[3],w[12]*m[0]+w[13]*m[1]+w[14]*m[2]+w[15]*m[3]],dot:(w,m)=>w[0]*m[0]+w[1]*m[1]+w[2]*m[2]+w[3]*m[3],sml:(w,m)=>[w*m[0],w*m[1],w*m[2],w*m[3]]};It.encode=function(m,T,E,O,A,_,D){O==null&&(O=0),D==null&&(D=!1);const x=c(m,T,E,O,[!1,!1,!1,0,D,!1]);return f(x,-1),d(x,T,E,A,_)},It.encodeLL=function(m,T,E,O,A,_,D,x){const y={ctype:0+(O==1?0:2)+(A==0?0:4),depth:_,frames:[]},G=(O+A)*_,$=G*T;for(let K=0;K<m.length;K++)y.frames.push({rect:{x:0,y:0,width:T,height:E},img:new Uint8Array(m[K]),blend:0,dispose:1,bpp:Math.ceil(G/8),bpl:Math.ceil($/8)});return f(y,0,!0),d(y,T,E,D,x)},It.encode.compress=c,It.encode.dither=i,It.quantize=v,It.quantize.getKDtree=C,It.quantize.getNearest=j})();const pc={toArrayBuffer(e,t){const a=e.width,n=e.height,r=a<<2,o=e.getContext("2d").getImageData(0,0,a,n),l=new Uint32Array(o.data.buffer),i=(32*a+31)/32<<2,d=i*n,f=122+d,c=new ArrayBuffer(f),p=new DataView(c),u=1<<20;let h,g,v,C,j=u,N=0,M=0,I=0;function F(w){p.setUint16(M,w,!0),M+=2}function S(w){p.setUint32(M,w,!0),M+=4}function R(w){M+=w}F(19778),S(f),R(4),S(122),S(108),S(a),S(-n>>>0),F(1),F(32),S(3),S(d),S(2835),S(2835),R(8),S(16711680),S(65280),S(255),S(4278190080),S(1466527264),function w(){for(;N<n&&j>0;){for(C=122+N*i,h=0;h<r;)j--,g=l[I++],v=g>>>24,p.setUint32(C+h,g<<8|v),h+=4;N++}I<l.length?(j=u,setTimeout(w,pc._dly)):t(c)}()},toBlob(e,t){this.toArrayBuffer(e,a=>{t(new Blob([a],{type:"image/bmp"}))})},_dly:9};var ht={CHROME:"CHROME",FIREFOX:"FIREFOX",DESKTOP_SAFARI:"DESKTOP_SAFARI",IE:"IE",IOS:"IOS",ETC:"ETC"},Fv={[ht.CHROME]:16384,[ht.FIREFOX]:11180,[ht.DESKTOP_SAFARI]:16384,[ht.IE]:8192,[ht.IOS]:4096,[ht.ETC]:8192};const go=typeof window<"u",fc=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,br=go&&window.cordova&&window.cordova.require&&window.cordova.require("cordova/modulemapper"),Bv=(go||fc)&&(br&&br.getOriginalSymbol(window,"File")||typeof File<"u"&&File),hc=(go||fc)&&(br&&br.getOriginalSymbol(window,"FileReader")||typeof FileReader<"u"&&FileReader);function bo(e,t,a=Date.now()){return new Promise(n=>{const r=e.split(","),o=r[0].match(/:(.*?);/)[1],l=globalThis.atob(r[1]);let i=l.length;const d=new Uint8Array(i);for(;i--;)d[i]=l.charCodeAt(i);const f=new Blob([d],{type:o});f.name=t,f.lastModified=a,n(f)})}function mc(e){return new Promise((t,a)=>{const n=new hc;n.onload=()=>t(n.result),n.onerror=r=>a(r),n.readAsDataURL(e)})}function gc(e){return new Promise((t,a)=>{const n=new Image;n.onload=()=>t(n),n.onerror=r=>a(r),n.src=e})}function vn(){if(vn.cachedResult!==void 0)return vn.cachedResult;let e=ht.ETC;const{userAgent:t}=navigator;return/Chrom(e|ium)/i.test(t)?e=ht.CHROME:/iP(ad|od|hone)/i.test(t)&&/WebKit/i.test(t)?e=ht.IOS:/Safari/i.test(t)?e=ht.DESKTOP_SAFARI:/Firefox/i.test(t)?e=ht.FIREFOX:(/MSIE/i.test(t)||document.documentMode)&&(e=ht.IE),vn.cachedResult=e,vn.cachedResult}function bc(e,t){const a=vn(),n=Fv[a];let r=e,o=t,l=r*o;const i=r>o?o/r:r/o;for(;l>n*n;){const d=(n+r)/2,f=(n+o)/2;d<f?(o=f,r=f*i):(o=d*i,r=d),l=r*o}return{width:r,height:o}}function Mr(e,t){let a,n;try{if(a=new OffscreenCanvas(e,t),n=a.getContext("2d"),n===null)throw new Error("getContext of OffscreenCanvas returns null")}catch{a=document.createElement("canvas"),n=a.getContext("2d")}return a.width=e,a.height=t,[a,n]}function xc(e,t){const{width:a,height:n}=bc(e.width,e.height),[r,o]=Mr(a,n);return t&&/jpe?g/.test(t)&&(o.fillStyle="white",o.fillRect(0,0,r.width,r.height)),o.drawImage(e,0,0,r.width,r.height),r}function ir(){return ir.cachedResult!==void 0||(ir.cachedResult=["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&typeof document<"u"&&"ontouchend"in document),ir.cachedResult}function xr(e,t={}){return new Promise(function(a,n){let r,o;var l=function(){try{return o=xc(r,t.fileType||e.type),a([r,o])}catch(d){return n(d)}},i=function(d){try{var f=function(c){try{throw c}catch(p){return n(p)}};try{let c;return mc(e).then(function(p){try{return c=p,gc(c).then(function(u){try{return r=u,function(){try{return l()}catch(h){return n(h)}}()}catch(h){return f(h)}},f)}catch(u){return f(u)}},f)}catch(c){f(c)}}catch(c){return n(c)}};try{if(ir()||[ht.DESKTOP_SAFARI,ht.MOBILE_SAFARI].includes(vn()))throw new Error("Skip createImageBitmap on IOS and Safari");return createImageBitmap(e).then(function(d){try{return r=d,l()}catch{return i()}},i)}catch{i()}})}function yr(e,t,a,n,r=1){return new Promise(function(o,l){let i;if(t==="image/png"){let f,c,p;return f=e.getContext("2d"),{data:c}=f.getImageData(0,0,e.width,e.height),p=It.encode([c.buffer],e.width,e.height,4096*r),i=new Blob([p],{type:t}),i.name=a,i.lastModified=n,d.call(this)}{let f=function(){return d.call(this)};if(t==="image/bmp")return new Promise(c=>pc.toBlob(e,c)).then((function(c){try{return i=c,i.name=a,i.lastModified=n,f.call(this)}catch(p){return l(p)}}).bind(this),l);{let c=function(){return f.call(this)};if(typeof OffscreenCanvas=="function"&&e instanceof OffscreenCanvas)return e.convertToBlob({type:t,quality:r}).then((function(p){try{return i=p,i.name=a,i.lastModified=n,c.call(this)}catch(u){return l(u)}}).bind(this),l);{let p;return p=e.toDataURL(t,r),bo(p,a,n).then((function(u){try{return i=u,c.call(this)}catch(h){return l(h)}}).bind(this),l)}}}function d(){return o(i)}})}function Rt(e){e.width=0,e.height=0}function ta(){return new Promise(function(e,t){let a,n,r,o;return ta.cachedResult!==void 0?e(ta.cachedResult):bo("data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAAAAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/xABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAAAAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==","test.jpg",Date.now()).then(function(l){try{return a=l,xr(a).then(function(i){try{return n=i[1],yr(n,a.type,a.name,a.lastModified).then(function(d){try{return r=d,Rt(n),xr(r).then(function(f){try{return o=f[0],ta.cachedResult=o.width===1&&o.height===2,e(ta.cachedResult)}catch(c){return t(c)}},t)}catch(f){return t(f)}},t)}catch(d){return t(d)}},t)}catch(i){return t(i)}},t)})}function yc(e){return new Promise((t,a)=>{const n=new hc;n.onload=r=>{const o=new DataView(r.target.result);if(o.getUint16(0,!1)!=65496)return t(-2);const l=o.byteLength;let i=2;for(;i<l;){if(o.getUint16(i+2,!1)<=8)return t(-1);const d=o.getUint16(i,!1);if(i+=2,d==65505){if(o.getUint32(i+=2,!1)!=1165519206)return t(-1);const f=o.getUint16(i+=6,!1)==18761;i+=o.getUint32(i+4,f);const c=o.getUint16(i,f);i+=2;for(let p=0;p<c;p++)if(o.getUint16(i+12*p,f)==274)return t(o.getUint16(i+12*p+8,f))}else{if((65280&d)!=65280)break;i+=o.getUint16(i,!1)}}return t(-1)},n.onerror=r=>a(r),n.readAsArrayBuffer(e)})}function vc(e,t){const{width:a}=e,{height:n}=e,{maxWidthOrHeight:r}=t;let o,l=e;return isFinite(r)&&(a>r||n>r)&&([l,o]=Mr(a,n),a>n?(l.width=r,l.height=n/a*r):(l.width=a/n*r,l.height=r),o.drawImage(e,0,0,l.width,l.height),Rt(e)),l}function wc(e,t){const{width:a}=e,{height:n}=e,[r,o]=Mr(a,n);switch(t>4&&t<9?(r.width=n,r.height=a):(r.width=a,r.height=n),t){case 2:o.transform(-1,0,0,1,a,0);break;case 3:o.transform(-1,0,0,-1,a,n);break;case 4:o.transform(1,0,0,-1,0,n);break;case 5:o.transform(0,1,1,0,0,0);break;case 6:o.transform(0,1,-1,0,n,0);break;case 7:o.transform(0,-1,-1,0,n,a);break;case 8:o.transform(0,-1,1,0,0,a)}return o.drawImage(e,0,0,a,n),Rt(e),r}function Qi(e,t,a=0){return new Promise(function(n,r){let o,l,i,d,f,c,p,u,h,g,v,C,j,N,M,I,F,S,R,w;function m(E=5){if(t.signal&&t.signal.aborted)throw t.signal.reason;o+=E,t.onProgress(Math.min(o,100))}function T(E){if(t.signal&&t.signal.aborted)throw t.signal.reason;o=Math.min(Math.max(E,o),100),t.onProgress(o)}return o=a,l=t.maxIteration||10,i=1024*t.maxSizeMB*1024,m(),xr(e,t).then((function(E){try{return[,d]=E,m(),f=vc(d,t),m(),new Promise(function(O,A){var _;if(!(_=t.exifOrientation))return yc(e).then((function(x){try{return _=x,D.call(this)}catch(y){return A(y)}}).bind(this),A);function D(){return O(_)}return D.call(this)}).then((function(O){try{return c=O,m(),ta().then((function(A){try{return p=A?f:wc(f,c),m(),u=t.initialQuality||1,h=t.fileType||e.type,yr(p,h,e.name,e.lastModified,u).then((function(_){try{{let x=function(){if(l--&&(M>i||M>j)){let G,$;return G=w?.95*R.width:R.width,$=w?.95*R.height:R.height,[F,S]=Mr(G,$),S.drawImage(R,0,0,G,$),u*=h==="image/png"?.85:.95,yr(F,h,e.name,e.lastModified,u).then(function(K){try{return I=K,Rt(R),R=F,M=I.size,T(Math.min(99,Math.floor((N-M)/(N-i)*100))),x}catch(J){return r(J)}},r)}return[1]},y=function(){return Rt(R),Rt(F),Rt(f),Rt(p),Rt(d),T(100),n(I)};if(g=_,m(),v=g.size>i,C=g.size>e.size,!v&&!C)return T(100),n(g);var D;return j=e.size,N=g.size,M=N,R=p,w=!t.alwaysKeepResolution&&v,(D=(function(G){for(;G;){if(G.then)return void G.then(D,r);try{if(G.pop){if(G.length)return G.pop()?y.call(this):G;G=x}else G=G.call(this)}catch($){return r($)}}}).bind(this))(x)}}catch(x){return r(x)}}).bind(this),r)}catch(_){return r(_)}}).bind(this),r)}catch(A){return r(A)}}).bind(this),r)}catch(O){return r(O)}}).bind(this),r)})}const Hv=`
let scriptImported = false
self.addEventListener('message', async (e) => {
  const { file, id, imageCompressionLibUrl, options } = e.data
  options.onProgress = (progress) => self.postMessage({ progress, id })
  try {
    if (!scriptImported) {
      // console.log('[worker] importScripts', imageCompressionLibUrl)
      self.importScripts(imageCompressionLibUrl)
      scriptImported = true
    }
    // console.log('[worker] self', self)
    const compressedFile = await imageCompression(file, options)
    self.postMessage({ file: compressedFile, id })
  } catch (e) {
    // console.error('[worker] error', e)
    self.postMessage({ error: e.message + '\\n' + e.stack, id })
  }
})
`;let Zr;function Rv(e,t){return new Promise((a,n)=>{Zr||(Zr=function(l){const i=[];return typeof l=="function"?i.push(`(${l})()`):i.push(l),URL.createObjectURL(new Blob(i))}(Hv));const r=new Worker(Zr);r.addEventListener("message",function(l){if(t.signal&&t.signal.aborted)r.terminate();else if(l.data.progress===void 0){if(l.data.error)return n(new Error(l.data.error)),void r.terminate();a(l.data.file),r.terminate()}else t.onProgress(l.data.progress)}),r.addEventListener("error",n),t.signal&&t.signal.addEventListener("abort",()=>{n(t.signal.reason),r.terminate()}),r.postMessage({file:e,imageCompressionLibUrl:t.libURL,options:{...t,onProgress:void 0,signal:void 0}})})}function it(e,t){return new Promise(function(a,n){let r,o,l,i,d,f;if(r={...t},l=0,{onProgress:i}=r,r.maxSizeMB=r.maxSizeMB||Number.POSITIVE_INFINITY,d=typeof r.useWebWorker!="boolean"||r.useWebWorker,delete r.useWebWorker,r.onProgress=h=>{l=h,typeof i=="function"&&i(l)},!(e instanceof Blob||e instanceof Bv))return n(new Error("The file given is not an instance of Blob or File"));if(!/^image/.test(e.type))return n(new Error("The file given is not an image"));if(f=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,!d||typeof Worker!="function"||f)return Qi(e,r).then((function(h){try{return o=h,u.call(this)}catch(g){return n(g)}}).bind(this),n);var c=(function(){try{return u.call(this)}catch(h){return n(h)}}).bind(this),p=function(h){try{return Qi(e,r).then(function(g){try{return o=g,c()}catch(v){return n(v)}},n)}catch(g){return n(g)}};try{return r.libURL=r.libURL||"https://cdn.jsdelivr.net/npm/browser-image-compression@2.0.2/dist/browser-image-compression.js",Rv(e,r).then(function(h){try{return o=h,c()}catch{return p()}},p)}catch{p()}function u(){try{o.name=e.name,o.lastModified=e.lastModified}catch{}try{r.preserveExif&&e.type==="image/jpeg"&&(!r.fileType||r.fileType&&r.fileType===e.type)&&(o=uc(e,o))}catch{}return a(o)}})}it.getDataUrlFromFile=mc,it.getFilefromDataUrl=bo,it.loadImage=gc,it.drawImageInCanvas=xc,it.drawFileInCanvas=xr,it.canvasToFile=yr,it.getExifOrientation=yc,it.handleMaxWidthOrHeight=vc,it.followExifOrientation=wc,it.cleanupCanvasMemory=Rt,it.isAutoOrientationInBrowser=ta,it.approximateBelowMaximumCanvasSizeOfBrowser=bc,it.copyExifWithoutOrientation=uc,it.getBrowserName=vn,it.version="2.0.2";const Uv=({selectors:e})=>{var O;const t=gt({defaultValues:{globalImageResizeScale:100,downloadEnabledAfterProcessing:!1},mode:"onChange",resolver:Lt(xp)}),{getValues:a,setValue:n,formState:{errors:r}}=t,o=En(A=>A.updateTumblerImage),{checkAndDeductUserCredits:l}=al(),i=b.useRef(null),d=b.useRef(null),[f,c]=b.useState(!1),[p,u]=b.useState(!0),[h,g]=b.useState(!1),[v,C]=b.useState(!1),j=b.useCallback(async()=>{const{"merch.globalImageResizeScale":A,"merch.downloadEnabledAfterProcessing":_}=await at.getStoreValue(["merch.globalImageResizeScale","merch.downloadEnabledAfterProcessing"]);n("globalImageResizeScale",+wt(A,50),{shouldValidate:!0}),n("downloadEnabledAfterProcessing",!!_),u(!1)},[]);b.useEffect(()=>{j()},[]),b.useEffect(()=>{const A=ye(e.uploader);if(!A)return;let _=null;const D=async()=>{const y=ye(e==null?void 0:e.uploaded_img);if(y&&y.src)try{const G=await fetch(y.src);if(G.ok){const $=await G.blob(),K=new FileReader;K.onload=()=>{o("front",K.result)},K.readAsDataURL($)}else console.error("Failed to fetch the image")}catch(G){console.error("Error fetching image:",G)}},x=new MutationObserver(()=>{const y=ye(e.artwork_remove_btn);y&&y!==_&&(D(),_=y)});return x.observe(A,{childList:!0,subtree:!1}),()=>{x.disconnect()}},[e.uploader,e.uploaded_img,o]);const N=()=>{i.current&&(i.current.value="")},M=A=>{A.preventDefault(),g(!1);const _=Array.from(A.dataTransfer.files);if(!_.length)return;const D=_.filter(y=>y.type==="image/png");if(!D.length){se("Only PNG images are allowed!");return}const x={target:{files:[D[0]]}};S(x)},I=A=>{A.preventDefault(),h||g(!0)},F=()=>{h&&g(!1)},S=async A=>{var K;if(f)return;let _=document.querySelector(e==null?void 0:e.artwork_uploading_container);if(_){se("Please wait until the current artwork upload is complete.");return}_=null;const{hasCredits:D}=await l(sa.DESIGN_UPSCALING,{shouldOnlyFetchCredits:!0});if(!D)return;const x=(K=A.target.files)==null?void 0:K[0];if(!x)return;c(!0);let y=document.querySelector(e==null?void 0:e.artwork_remove_btn);y&&(y.click(),await Me(500),y=null);const $=a().globalImageResizeScale;try{const J=await it(x,{maxSizeMB:25,maxWidthOrHeight:4500,useWebWorker:!0,initialQuality:$/100}),Y=new FileReader;Y.onload=()=>w(Y.result,x.name,$),Y.onerror=()=>{se("Failed to read image file. Please check the file format and try again."),c(!1),N()},Y.readAsDataURL(J)}catch{c(!1),N()}},R=b.useCallback(A=>{const _=document.createElement("canvas"),D=_.getContext("2d",{willReadFrequently:!0});_.width=A.width,_.height=A.height,D.drawImage(A,0,0);try{const x=A.width*A.height>4e6,y=x?4:1,$=D.getImageData(0,0,A.width,A.height).data;let K=A.width,J=A.height,Y=-1,ee=-1;for(let q=0;q<A.height;q+=y){const Q=q*A.width*4;let oe=!1;for(let de=0;de<A.width;de+=y)$[Q+de*4+3]>10&&(oe=!0,de<K&&(K=de),de>Y&&(Y=de),q<J&&(J=q),q>ee&&(ee=q));!oe&&q>ee+100&&q>A.height/2&&(q+=20)}if(Y<K||ee<J)return null;x&&(K=Math.max(0,K-y),J=Math.max(0,J-y),Y=Math.min(A.width-1,Y+y),ee=Math.min(A.height-1,ee+y));const Z={x:K,y:J,width:Y-K+1,height:ee-J+1};return _.width=1,_.height=1,Z}catch{return _.width=1,_.height=1,null}},[]),w=b.useCallback((A,_,D)=>{const x=new Image;x.crossOrigin="Anonymous";const y=a("downloadEnabledAfterProcessing");x.onload=()=>{try{const G=d.current,$=G.getContext("2d",{willReadFrequently:!0}),K=4500,J=5400,Y=Math.abs(x.width-K)<100&&Math.abs(x.height-J)<100;let ee=null,Z=!1,q=!1;if(!Y&&(ee=R(x),Z=!!ee&&ee.width>0&&ee.height>0,Z)){const B=((ee==null?void 0:ee.width)||0)*((ee==null?void 0:ee.height)||0),W=x.width*x.height;q=B/W<.8}G.width=K,G.height=J,$.clearRect(0,0,K,J),$.imageSmoothingEnabled=!0,$.imageSmoothingQuality="high";let Q=0,oe=0,de=x.width,pe=x.height;q&&Z&&ee&&(Q=ee.x,oe=ee.y,de=ee.width,pe=ee.height);const Ce=K/de,re=J/pe,ne=Math.min(Ce,re)*(D/100),H=de*ne,V=pe*ne,ce=(K-H)/2,k=0;if(ne>1.5&&de*pe<K*J*.5){const B=document.createElement("canvas"),W=B.getContext("2d",{willReadFrequently:!0});B.width=de,B.height=pe,W.imageSmoothingEnabled=!0,W.imageSmoothingQuality="high",W.drawImage(x,Q,oe,de,pe,0,0,de,pe);const z=document.createElement("canvas"),te=z.getContext("2d",{willReadFrequently:!0}),X=1+(ne-1)*.5;z.width=de*X,z.height=pe*X,te.imageSmoothingEnabled=!0,te.imageSmoothingQuality="high",te.drawImage(B,0,0,B.width,B.height,0,0,z.width,z.height),$.drawImage(z,ce,k,H,V),B.width=1,B.height=1,z.width=1,z.height=1}else $.drawImage(x,Q,oe,de,pe,ce,k,H,V);G.toBlob(async B=>{if(!B){se("Failed to process image"),c(!1),N();return}const W=new File([B],_,{type:"image/png"}),z=new DataTransfer;if(z.items.add(W),W.size>25*1e3*1e3){se("The file size is over 25MB after processing. Please try a lower scaling percentage to generate a smaller image."),fs(B,_),c(!1),N();return}const te=document.querySelector(e==null?void 0:e.uploader_input);if(!te){se("Unable to find artwork uploader. Please check and try uploading again."),c(!1),N();return}const{hasCredits:X}=await l(sa.DESIGN_UPSCALING);if(!X){c(!1),N();return}te.files=z.files,te.dispatchEvent(new Event("change",{bubbles:!0})),y&&fs(B,_),c(!1),N()},"image/png",1)}catch{se("Image processing failed. Please upload a valid image file."),c(!1),N()}},x.src=A,x.onerror=()=>{se("Invalid or corrupted image file. Please try uploading a different image."),c(!1),N()}},[]),m=()=>{if(!v){C(!0);try{if(ye(e.artwork_uploading_container)){se("Please wait until the current artwork upload is complete.");return}const A=ye(e.uploaded_img);if(!A){se("Please upload a design first.");return}const _=(A.getAttribute("alt")||"artwork").replace(/\./g,"-"),D=document.createElement("a");D.href=A.src,D.download=`${_}.png`,document.body.appendChild(D),D.click(),document.body.removeChild(D)}catch{}finally{C(!1)}}},T=b.useCallback(A=>{const _=+wt(A.target.value);n("globalImageResizeScale",_,{shouldValidate:!0}),at.setStoreValue({"merch.globalImageResizeScale":_})},[]),E=b.useCallback(A=>{const _=+wt(A.target.value,50);n("globalImageResizeScale",_,{shouldValidate:!0}),at.setStoreValue({"merch.globalImageResizeScale":_})},[]);return p?null:s.jsx(bt,{...t,children:s.jsxs("div",{className:"uploader-container",children:[s.jsxs("label",{className:`drop-zone w-full mb-3 ${h?"dragging":""}`,onDragOver:I,onDragLeave:F,onDrop:M,children:[s.jsx("input",{ref:i,type:"file",accept:"image/png",onChange:S,hidden:!0,disabled:f}),s.jsxs("div",{className:"drop-content d-flex flex-col gap-1 font-semibold",children:[s.jsx("div",{className:"upload-icon",children:f?s.jsx(na,{size:40}):s.jsx(sc,{size:40})}),s.jsx("div",{className:"upload-text",children:f?"Processing image...":"Upload and convert to 4500x5400px"}),s.jsx("div",{className:"upload-subtext",children:f?"Please wait while we process your image":"Drag and drop (PNG only)"})]})]}),s.jsxs("div",{className:"resize-options mb-3",children:[s.jsxs("div",{className:"d-flex gap-4 global-image-scale-actions",children:[[100,85,75].map(A=>s.jsx(fe,{buttonContent:`${A}%`,onclickHandler:()=>n("globalImageResizeScale",A),tooltipId:`${A}-global-percent-scale-selection`,extraClass:"outlined-btn"},A)),s.jsx("div",{className:"d-flex",children:s.jsx(ke,{name:"globalImageResizeScale",control:t.control,render:({field:{value:A}})=>s.jsx(nn,{onChange:T,onBlur:E,value:A,uniqueId:"mrdn-global-image-resize-scale_input",extraClass:"flex-fill h-31px",inputClassName:"mt-auto",hasPermission:!0,onDragStart:_=>_.preventDefault(),allowDecimal:!1,min:1,max:100})})})]}),s.jsx(At,{message:(O=r==null?void 0:r.globalImageResizeScale)==null?void 0:O.message})]}),s.jsxs("div",{className:"d-flex align-items-center justify-between mt-2",children:[s.jsx(ke,{control:t.control,name:"downloadEnabledAfterProcessing",render:({field:{onChange:A,value:_}})=>s.jsx(wr,{name:"isDownloadEnabled",checked:_,onChangeHandler:D=>{A(D),at.setStoreValue({"merch.downloadEnabledAfterProcessing":D.target.checked})},switchTitle:"Auto Download",extraClass:"small-switch global-image-download-switch",hasPermission:!0,title:"Automatically download the design after processing when enabled"})}),s.jsx(fe,{buttonContent:"Download Design",isLoading:v,isDisabled:f||v,onclickHandler:m,buttonIcon:s.jsx(Xc,{}),extraClass:"color-white border-none",buttonProps:{type:"submit"},tooltipId:"global-artwork-downloader"})]}),s.jsx("canvas",{ref:d,style:{display:"none"}})]})})},Vv=({pageConfigs:e,subscriptionModules:t,plan:a})=>{const{constants:n,xPaths:r}=e,o=nl,l=jr(we=>we.setMerchFieldsConfigs),i=fl(we=>we.updateVisibilityConfigs),d=Qc(we=>we.setPlan),f=Pt(we=>we.setMerchFeatureAccess),{updateScale:c,updateTumblerScale:p,updateTumblerTab:u,updateIsPattern:h}=En(we=>({updateScale:we.updateScale,updateTumblerScale:we.updateTumblerScale,updateTumblerTab:we.updateTumblerTab,updateIsPattern:we.updateIsPattern})),[g,v]=b.useState(null),[C,j]=b.useState(null),[N,M]=b.useState(null),[I,F]=b.useState(null),[S,R]=b.useState(null),[w,m]=b.useState(null),[T,E]=b.useState(null),[O,A]=b.useState(null),[_,D]=b.useState(null),[x,y]=b.useState(null),[G,$]=b.useState(null),{product_selection:K,global_uploader:J,product_editor:Y,global_templates:ee,product_edit:Z,plan_summary_append_section_identifier:q}=r||{},{product_selection_btn:Q,modal:oe,modal_header:de,modal_submit:pe}=K||{},{translation_options:Ce,product_text:re,listing_accordion:U,languages_expansion_container:ne,languages_expansion_controller:H,listing_marketplaces:V,listing_marketplace_header:ce}=Y||{},{edit_btn_postfix:k,card_postfix:L,product_editor_container:B,asset_container:W,fit_types_container:z,fit_types_checkbox:te,colors_container:X,colors_selection_label:ae,colors_checkbox:me,active_product_card:ge,price:he,image_upload:_e,bg_color:Oe}=Z||{},{helpers_tool_container:Ne,tumbler_price_container:le}=_e||{},{append_section_identifier:ie}=ee,ue=()=>{const we=ye(Q);we?we.addEventListener("click",()=>{Ze(oe).then(ve=>{if(ve){const{targetContainer:Ee,wrapperContainer:Pe}=Ot(ve,de,{id:"merch-dominator-product-selection",className:"merch-dominator-product-selection merch-dominator-style-container"});Pe&&(Ee.insertAdjacentElement("afterend",Pe),v(Pe))}})}):setTimeout(ue,500)},be=we=>{const{hasFitTypes:ve,hasColors:Ee,category:Pe,productEditorContainer:je,fitTypesContainer:Fe,colorsSelectionLabel:Xe,priceLabel:Re,imageUpload:Ae}=we;let ut=document.querySelectorAll(je).length>1?1e3:0;setTimeout(()=>{Ze(je,500,5e3).then(Qe=>{var Ue,$e;if(Qe){if(ve){const{targetContainer:Ve,wrapperContainer:ot}=Ot(Qe,Fe,{id:"mrdn-fit-types-container",className:"mrdn-fit-types-container merch-dominator-style-container"});Ve==null||Ve.appendChild(ot),j(ot)}if(Ee){Po(Xe,{id:"mrdn-colors-container",className:"mrdn-colors-container merch-dominator-style-container mrdn-primary-container"},Qe,0,0).then(pa=>{pa&&M(pa)});const{targetContainer:Ve,wrapperContainer:ot}=Ot(document.body,Xe,{id:`${Pe}-selected-color-count`,className:"mrdn-colors-container merch-dominator-style-container ml-auto d-flex"});Ve==null||Ve.appendChild(ot),F(ot)}if(Po(Re,{id:"mrdn-price-container",className:"mrdn-price-container merch-dominator-style-container mrdn-primary-container"},Qe,0,0).then(Ve=>{Ve&&R(Ve)}),Pe===Tr.TUMBLER){m(null);const{targetContainer:Ve,wrapperContainer:ot}=Ot(Qe,Ae==null?void 0:Ae.tumblerPriceContainer,{id:"mrdn-tumbler-container",className:"mrdn-tumbler-container merch-dominator-style-container"});ot&&((Ue=Ve==null?void 0:Ve.parentNode)==null||Ue.insertBefore(ot,Ve),E(ot))}else{E(null);const{targetContainer:Ve,wrapperContainer:ot}=Ot(Qe,Ae==null?void 0:Ae.helpersToolContainer,{id:`${Pe}-mrdn-scale-container`,className:"mrdn-scale-container merch-dominator-style-container"});ot&&(($e=Ve==null?void 0:Ve.parentNode)==null||$e.insertBefore(ot,Ve),m(ot))}}})},ut)},Te=()=>{at.getStoreValue(["merch.scale","merch.tumblerScale","merch.tumblerTab","merch.isPattern"]).then(we=>{const{"merch.scale":ve,"merch.tumblerScale":Ee,"merch.tumblerTab":Pe,"merch.isPattern":je}=we;ve&&c(+wt(ve)),Ee&&p(+wt(Ee)),Pe&&u(Pe,!0),h(!!je,!0)}),window.location.href.includes(Ns.EDIT)&&Ze(W).then(we=>{if(!we)return;const ve=Sr(we);be({hasFitTypes:qt[ve].fit_types,hasColors:qt[ve].colors,category:ve,productEditorContainer:B,fitTypesContainer:z,colorsSelectionLabel:ae,priceLabel:he==null?void 0:he.price_label,imageUpload:{helpersToolContainer:Ne,tumblerPriceContainer:le}})});for(const we in qt){const ve=qt[we].fit_types,Ee=qt[we].colors;Ze(`#${we}${L} .${we}${k}`,500).then(Pe=>{Pe&&Pe.addEventListener("click",je=>{je.stopPropagation(),be({hasFitTypes:ve,hasColors:Ee,category:we,productEditorContainer:B,fitTypesContainer:z,colorsSelectionLabel:ae,priceLabel:he==null?void 0:he.price_label,imageUpload:{helpersToolContainer:Ne,tumblerPriceContainer:le}})})})}},Le=()=>{Ze(re,500,3e4).then(we=>{var Pe;if(!we)return;const{targetContainer:ve,wrapperContainer:Ee}=Ot(we,ne,{id:"mrdn-product-listing-container",className:"mrdn-product-listing-container merch-dominator-style-container"});Ee&&(document.querySelectorAll(Ce).forEach((Fe,Xe)=>{Fe.addEventListener("change",()=>{setTimeout(()=>{const Re=ye("#languages-toggle-action");Re&&!Xe&&Fe.checked?Re.style.setProperty("display","none","important"):Re&&Xe&&Fe.checked&&(Re.style.display="flex")},0)})}),(Pe=ve==null?void 0:ve.parentNode)==null||Pe.insertBefore(Ee,ve),A(Ee))})},ze=()=>{Ze(ie,500,3e4).then(we=>{if(!we)return;const{targetContainer:ve,wrapperContainer:Ee}=Ot(document.body,ie,{id:"mrdn-global-templates-container",className:"mrdn-global-templates-container merch-dominator-style-container"});Ee&&(ve.insertAdjacentElement("afterend",Ee),D(Ee))})},Ke=()=>{Ze(J.wizzy_global_uploader,500,3e4).then(we=>{var Pe,je;if(!we)return;const{targetContainer:ve,wrapperContainer:Ee}=Ot(document.body,J.wizzy_global_uploader,{id:"mrdn-global-image-download-container",className:"mrdn-global-image-download-container merch-dominator-style-container"});Ee&&((Pe=ve==null?void 0:ve.parentElement)==null||Pe.style.setProperty("position","relative"),(je=ve==null?void 0:ve.parentElement)==null||je.appendChild(Ee),y(Ee))})},He=()=>{Ze(q,500,3e4).then(we=>{if(!we)return;const{targetContainer:ve,wrapperContainer:Ee}=Ot(document.body,q,{id:"mrdn-plan-summary-container",className:"mrdn-plan-summary-container merch-dominator-style-container"});Ee&&(ve.prepend(Ee),$(Ee))})};return b.useEffect(()=>{f(t),l(e),d(a),ue(),Te(),Le(),ze(),Ke(),He()},[]),b.useEffect(()=>{const we=new MutationObserver(()=>{ps(H),i(us(V,ce))});return Ze(U,500,3e4).then(ve=>{ve&&(ps(H),i(us(V,ce)),we.observe(ve,{childList:!0,subtree:!1}))}),()=>{we.disconnect()}},[]),s.jsxs(s.Fragment,{children:[g&&ft.createPortal(s.jsx(Bp,{selectors:K}),g),C&&ft.createPortal(s.jsx(zp,{selectors:{checkbox:te}}),C),N&&ft.createPortal(s.jsx(Xp,{selectors:{colorsContainer:X,checkbox:me,editBtnPostfix:k,activeProductCard:ge},productColors:n.PRODUCT_COLORS}),N),I&&ft.createPortal(s.jsx(Qp,{selectors:{colorCheckbox:`${X} ${me}`,assetContainer:W},colorPriority:new Map(n.COLOR_PRIORITY)}),I),S&&ft.createPortal(s.jsx(af,{selectors:{...he,assetContainer:W,activeProductCard:ge,editBtnPostfix:k},priceRanges:n.PRICE_RANGES,priceAdjustmentIncrement:n.PRICE_ADJUSTMENT_INCREMENT}),S),w&&ft.createPortal(s.jsx(Gy,{selectors:{..._e,assetContainer:W,globalUploader:J,bgColorSelectors:Oe}}),w),T&&ft.createPortal(s.jsx(ev,{selectors:{..._e,assetContainer:W,globalUploader:J,productEditorContainer:B}}),T),O&&ft.createPortal(s.jsx(_v,{emitter:o,selectors:{...Y,globalUploader:J}}),O),_&&ft.createPortal(s.jsx(Ov,{selectors:r,priceRanges:n.PRICE_RANGES,emitter:o}),_),x&&ft.createPortal(s.jsx(Uv,{selectors:J}),x),G&&ft.createPortal(s.jsx(_d,{displayModules:ju}),G)]})},zv=()=>{const{isLoading:e,title:t}=Na(a=>a);return e?s.jsx("div",{style:{position:"fixed",top:0,zIndex:1060,padding:".5rem",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",gap:"1rem",height:"100vh",width:"100%",backdropFilter:"blur(3px)",WebkitBackdropFilter:"blur(3px)",backgroundColor:"rgba(255, 255, 255, 0.4)"},children:s.jsxs("div",{className:"d-flex flex-col i-px-6 py-3 gap-2 bg-merch-dominator text-center",style:{borderRadius:"6px",padding:"1rem",fontSize:"16px",color:"#fff",textShadow:"0 1px 3px rgba(0, 0, 0, 0.3)"},children:[s.jsx("div",{className:"d-flex justify-center",children:s.jsx(Lu,{color:"var(--spinner-primary-bg)",loading:!0})}),s.jsx("span",{className:"text-sm opacity-80",children:"Please keep the tab active or open in a separate window"}),s.jsx("span",{className:"text-base font-medium",children:t})]})}):null},Kv=b.memo(zv),qv=({children:e})=>s.jsxs(s.Fragment,{children:[s.jsx(Zc,{position:"top-right",toastOptions:{className:"merch-toaster"}}),s.jsx(Kv,{}),s.jsx(Ad,{}),s.jsx(kd,{plan:""}),s.jsx(Pd,{children:s.jsx(Nd,{})}),e]}),Wv=qv,xo=".app-wrapper",Gv=(e,t,a)=>{var n,r,o;try{switch(e.type){case"UNAUTHENTICATED":return tl.error(e.payload,{id:"unauthorized"}),new Promise(d=>setTimeout(d,1e3)).then(()=>{window.location.reload()}),!0;case _u:case Eu:window.location.reload();break;case Su:const l=e.payload;document.querySelectorAll("[data-theme]").forEach(d=>{d.setAttribute("data-theme",l)});break;default:break}}catch{(o=(r=(n=rt)==null?void 0:n.runtime)==null?void 0:r.lastError)==null||o.message,a({success:!1})}};rt.runtime.onMessage.addListener(Gv);const Yv={create:Vv};let St=document.getElementById("mrdn-container"),Zi="light",lt=null;const Xv=async()=>{const e=await at.getStoreValue(["merchDominator.theme"]);Zi=(e==null?void 0:e["merchDominator.theme"])||"light",St||(St=document.createElement("div"),St.id="mrdn-container",St.className="merch-dominator-style-container montserrat-medium",document.body.appendChild(St),document.body.setAttribute("data-theme",Zi))},Qv=e=>{var n;const a=e.target.getAttribute("href");a!=null&&a.includes(Ns.CREATE)?Cc("create"):((n=lt==null?void 0:lt.unmount)==null||n.call(lt),Ss(xo,"montserrat-text",!1))},Cc=async e=>{var N,M,I;Ss(xo,"montserrat-text");let t=!1;const a=await at.checkValidToken(),{token_status:n,otp_status:r,stripe_status:o="",user_type:l=0,plan_name:i="",subscription_modules:d={}}=(a==null?void 0:a.result)||{};t=n&&r&&nd(l,o);const f=n?r?Nu:Iu:Ou;if(!t){St&&((N=St.remove)==null||N.call(St)),St=null,ed(f);return}lt&&((M=lt.unmount)==null||M.call(lt));const c=document.querySelector("#banner-container");if(c&&((I=c.remove)==null||I.call(c)),!(await at.fetchDisplayConfigs()).analyzeCreatePageEnabled||!St)return;const u=await at.processForFieldsConfigs(Au,ku.AMAZON),h=pl(Pu,(u==null?void 0:u.constants)||{}),g={xPaths:(u==null?void 0:u.xpaths)||ns,constants:h};lt=td(St);const{templates:v="0",export_templates:C="0",import_templates:j="0"}=d;lt.render(s.jsx(Wv,{children:P.createElement(Yv[e],{pageConfigs:g,subscriptionModules:{templates:v,export_templates:C,import_templates:j},plan:i})}))},Zv=async()=>{var r,o;const e=await Ze(ns.navigation_tabs,300,6e4);if(!e)return;e.addEventListener("click",Qv);const t=await Ze(ns.active_navigation_tab,300,6e4);if(!t)return;const a=t.getAttribute("href"),n=document.querySelector("#mrdn-banner-portal");n&&((r=n==null?void 0:n.remove)==null||r.call(n)),a!=null&&a.includes(Ns.CREATE)?Cc("create"):((o=lt==null?void 0:lt.unmount)==null||o.call(lt),Ss(xo,"montserrat-text",!1))};Xv().then(async()=>{Jc(),await Zv()});export{Qv as handleTabClick,Xv as initializeContainer,Cc as renderApp};
