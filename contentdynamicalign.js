!function(){function t(t){const e=t.querySelector("#center-vertical-btn");e&&e.click();const n=t.querySelector("#center-horizontal-btn");n&&n.click()}!function e(){if(!document.body.contains(document.querySelector("div.container-fluid")))return void setTimeout(e,500);const n=document.querySelector("div.container-fluid").querySelector(".row.mb-4 > .col-3:not(.p-base)");if(n){if(!n.querySelector("#dynamic-align-toggle-button")){const t=document.createElement("div");t.id="dynamic-align-toggle-button",t.style.cssText="\n                display: inline-flex;\n                align-items: center;\n                cursor: pointer;\n                margin-top: 5px;\n            ";const e=document.createElement("div");e.style.cssText="\n                width: 36px;\n                height: 18px;\n                border-radius: 18px;\n                background-color: rgb(207, 212, 212);\n                position: relative;\n                transition: background-color 0.3s;\n                margin-right: 10px;\n            ";const o=document.createElement("span");o.style.cssText="\n                width: 12px;\n                height: 12px;\n                border-radius: 50%;\n                background-color: white;\n                position: absolute;\n                top: 3px;\n                left: 3px;\n                transition: transform 0.3s;\n            ",e.appendChild(o);const r=document.createElement("span");r.style.cssText="\n                font-size: 14px;\n                color: rgb(100, 100, 100);\n            ",function(t,e,n){let o=localStorage.getItem("dynamicAlignOn");null===o&&(o="true",localStorage.setItem("dynamicAlignOn",o));"true"===o?(t.style.backgroundColor="#470CED",e.style.transform="translateX(18px)",n.textContent="Dynamic Align On",n.style.color="rgb(0, 0, 0)"):(t.style.backgroundColor="rgb(207, 212, 212)",e.style.transform="translateX(0)",n.textContent="Dynamic Align Off",n.style.color="rgb(100, 100, 100)")}(e,o,r),t.addEventListener("click",(function(){!function(t,e,n){let o="true"===localStorage.getItem("dynamicAlignOn");o=!o,localStorage.setItem("dynamicAlignOn",o),o?(t.style.backgroundColor="#470CED",e.style.transform="translateX(18px)",n.textContent="Dynamic Align On",n.style.color="rgb(0, 0, 0)"):(t.style.backgroundColor="rgb(207, 212, 212)",e.style.transform="translateX(0)",n.textContent="Dynamic Align Off",n.style.color="rgb(100, 100, 100)")}(e,o,r)})),t.appendChild(e),t.appendChild(r),n.appendChild(t)}window.dynamicAlignObserverInitialized||(!function(){const e=new MutationObserver((t=>{t.forEach((t=>{if("attributes"===t.type&&"aria-valuenow"===t.attributeName){const e=t.target;if("true"===e.dataset.isDragging){const t=parseFloat(e.getAttribute("aria-valuenow"));o(e,t)}}}))}));n();function n(){const n=document.querySelector("mat-slider.scale-slider");if(n&&!n.dataset.observing){e.observe(n,{attributes:!0,attributeFilter:["aria-valuenow"]}),n.dataset.observing="true";const o=t=>{"true"!==n.dataset.isDragging&&(n.dataset.isDragging="true")},r=()=>{if("true"===n.dataset.isDragging){n.dataset.isDragging="false";const e=n.closest("div.product-editor");if(e){"true"===localStorage.getItem("dynamicAlignOn")&&setTimeout((()=>{t(e)}),10)}}};n.addEventListener("mousedown",o),n.addEventListener("touchstart",o,{passive:!0}),window.addEventListener("mouseup",r),window.addEventListener("touchend",r)}}function o(e,n){if(!e||"true"!==e.dataset.isDragging)return;const o=e.closest("div.product-editor");if(!o)return;"true"===localStorage.getItem("dynamicAlignOn")&&(o.dataset.alignTimeout&&clearTimeout(parseInt(o.dataset.alignTimeout)),t(o))}new MutationObserver((t=>{t.forEach((t=>{t.addedNodes.length&&n()}))})).observe(document.body,{childList:!0,subtree:!0})}(),window.dynamicAlignObserverInitialized=!0)}else setTimeout(e,500)}()}();