- [ ] Task: After selecting the country from the dropdown in the reporting automation, simulate a human click on the last field filled (e.g., phone number input) to ensure the country dropdown loses focus and its value is retained. This prevents the dropdown from being cleared after a few seconds due to lingering focus.
    - Investigate the best field to click after country selection (likely the phone number input).
    - Implement the click right after the country selection logic and before filling the next field.
    - Test to ensure the dropdown value is not cleared and the automation proceeds smoothly.
